package com.maersk.sd1.sdg.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ResponseTruckDepartureDelete {
    private int responseState;
    private String responseMessage;
    private Boolean responseFlagRemoveWorkOrder;
}
