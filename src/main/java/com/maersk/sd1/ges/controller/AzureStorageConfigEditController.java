package com.maersk.sd1.ges.controller;

import com.maersk.sd1.ges.controller.dto.AzureStorageConfigEditInput;
import com.maersk.sd1.ges.controller.dto.AzureStorageConfigEditOutput;
import com.maersk.sd1.ges.service.AzureStorageConfigEditService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("ModuleADM/module/adm/azureStorageConfigService")
public class AzureStorageConfigEditController {

    private static final Logger logger = LogManager.getLogger(AzureStorageConfigEditController.class);

    private final AzureStorageConfigEditService azureStorageConfigEditService;

    public AzureStorageConfigEditController(AzureStorageConfigEditService azureStorageConfigEditService) {
        this.azureStorageConfigEditService = azureStorageConfigEditService;
    }

    @PostMapping("/editAzureStorageConfig")
    public ResponseEntity<ResponseController<AzureStorageConfigEditOutput>> editAzureStorageConfig(
            @RequestBody @Valid AzureStorageConfigEditInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure"));
            }
            AzureStorageConfigEditInput.Input input = request.getPrefix().getInput();
            if(input.getAzureStorageConfigId() == null || input.getModificationUserId() == null) {
                return ResponseEntity.status(400).body(new ResponseController<>("azureId and userId should not be null"));
            }
            AzureStorageConfigEditOutput output = azureStorageConfigEditService.editAzureStorageConfig(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the edit call.", e);
            AzureStorageConfigEditOutput output = new AzureStorageConfigEditOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}