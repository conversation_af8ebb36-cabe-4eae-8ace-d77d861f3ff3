package com.maersk.sd1.sde.controller;

import com.maersk.sd1.sde.controller.dto.InspectionMachineryInput;
import com.maersk.sd1.sde.controller.dto.InspectionMachineryOutput;
import com.maersk.sd1.sde.service.InspectionMachineryListService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEInspeccionServiceImp")
public class InspectionMachineryListController {

    private static final Logger logger = LogManager.getLogger(InspectionMachineryListController.class);

    private final InspectionMachineryListService inspectionMachineryService;

    @PostMapping("/sdeinspectionMachineryList")
    public ResponseEntity<ResponseController<InspectionMachineryOutput>> listInspectionMachinery(
            @RequestBody @Valid InspectionMachineryInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.badRequest().body(new ResponseController<>(new InspectionMachineryOutput()));
            }
            InspectionMachineryInput.Input input = request.getPrefix().getInput();
            InspectionMachineryOutput result = inspectionMachineryService.listInspectionMachinery(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception ex) {
            logger.error("An error occurred while processing the request.", ex);
            InspectionMachineryOutput empty = new InspectionMachineryOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(empty));
        }
    }
}