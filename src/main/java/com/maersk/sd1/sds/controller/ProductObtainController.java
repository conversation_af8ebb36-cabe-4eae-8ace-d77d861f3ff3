package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.ProductObtainInput;
import com.maersk.sd1.sds.controller.dto.ProductObtainOutput;
import com.maersk.sd1.sds.service.ProductObtainService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProductoServiceImp")
public class ProductObtainController {

    private static final Logger logger = LogManager.getLogger(ProductObtainController.class);

    private final ProductObtainService productObtainService;

    public ProductObtainController(ProductObtainService productObtainService) {
        this.productObtainService = productObtainService;
    }

    @PostMapping("/sdsproductoObtener")
    public ResponseEntity<ResponseController<ProductObtainOutput>> obtainProduct(@Valid @RequestBody ProductObtainInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure."));
            }
            Integer productId = request.getPrefix().getInput().getProductId();
            if(productId == null){
                return ResponseEntity.status(400).body(new ResponseController<>("productId cannot be null."));
            }
            ProductObtainOutput output = productObtainService.obtainProduct(productId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(e.getMessage()));
        }
    }
}