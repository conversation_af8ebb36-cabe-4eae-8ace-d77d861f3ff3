package com.maersk.sd1.sdg.controller.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Jacksonized
public class StockInventoryReportOutputDTO {

    @JsonProperty("totalCount")
    private List<Map<String, Integer>> totalCount;

    @JsonProperty("data")
    private List<StockInventory> dataList;

    public Object[] toResultArray() {
        return new Object[]{dataList, totalCount};
    }

    @Data
    @Builder
    public static class StockInventory {

        @JsonProperty("Depot")
        private String depot;
        @JsonProperty("Empty/Full")
        private String emptyFull;
        @JsonProperty("Equipment Number")
        private String equipmentNumber;
        @JsonProperty("Equipment Category")
        private String equipmentCategory;
        @JsonProperty("Equipment Size/Type")
        private String equipmentSizeType;
        @JsonProperty("Equipment Grade")
        private String equipmentGrade;
        @JsonProperty("Manufacture Year")
        private String manufactureYear;
        @JsonProperty("ISO Code")
        private String isoCode;
        @JsonProperty("Shipping Line/Chassis Owner")
        private String shippingLineOrChassisOwner;
        @JsonProperty("Shipper Name")
        private String shipperName;
        @JsonProperty("Commodity")
        private String commodity;
        @JsonProperty("Gate In Date")
        private String gateInDate;
        @JsonProperty("Dwell Time")
        private Integer dwellTime;
        @JsonProperty("Structure Condition (current)")
        private String structureCondition;
        @JsonProperty("Machinery condition (current)")
        private String machineryCondition;
        @JsonProperty("Equipment Restriction")
        private String equipmentRestriction;
        @JsonProperty("USDA Approved")
        private String usdaApproved;
        @JsonProperty("IMO information")
        private String imoInformation;
        @JsonProperty("Gate In - EIR Container")
        private String gateInEirContainer;
        @JsonProperty("Gate In - EIR Chassis")
        private String gateInEirChassis;
        @JsonProperty("Gate In - Chassis Stayed")
        private String chassisStayed;
        @JsonProperty("Inspection Status")
        private String inspectionStatus;
        @JsonProperty("Structure Condition (Inspection)")
        private String structureConditionInspection;
        @JsonProperty("Machinery condition (Inspection)")
        private String machineryConditionInspection;
        @JsonProperty("Gate In - Comment")
        private String gateInComment;
        @JsonProperty("Structure Inspecction Empty Container Comment")
        private String structureInspectionEmptyContainerComment;
        @JsonProperty("Booking (Pre-allocation)")
        private String bookingPreAllocation;
        @JsonProperty("Potential FGIS")
        private String potentialFGIS;
        @JsonProperty("Yard Location")
        private String yardLocation;
        @JsonProperty("Estimate Structure Status")
        private String estimateStructureStatus;
        @JsonProperty("Estimate Machinery Status")
        private String estimateMachineryStatus;
        @JsonProperty("Non Maersk Flag")
        private String nonMaerskFlag;
    }
}
