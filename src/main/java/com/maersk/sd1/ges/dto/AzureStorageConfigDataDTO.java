package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AzureStorageConfigDataDTO {
    @JsonProperty("azure_storage_config_id")
    private Integer azureStorageConfigId;

    @JsonProperty("id")
    private String id1;

    @JsonProperty("azure_container")
    private String azureContainer;

    @JsonProperty("azure_path")
    private String azurePath;

    @JsonProperty("azure_storage_name")
    private String azureStorageName;

    @JsonProperty("azure_storage_key")
    private String azureStorageKey;

    @JsonProperty("referencia_01")
    private String reference01;

    @JsonProperty("estado")
    private Boolean status;

    @JsonProperty("fecha_registro")
    private LocalDateTime registrationDate;

    @JsonProperty("fecha_modificacion")
    private LocalDateTime modificationDate;

    @JsonProperty("usuario_registro_id")
    private Integer registrationUserId;

    @JsonProperty("usuario_registro_nombres")
    private String registrationUserNames;

    @JsonProperty("usuario_registro_apellidos")
    private String registrationUserLastNames;
}
