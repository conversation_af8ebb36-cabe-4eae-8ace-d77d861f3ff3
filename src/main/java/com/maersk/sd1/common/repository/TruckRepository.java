package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Truck;
import com.maersk.sd1.sds.dto.GetVehicleCompanyOutput;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface TruckRepository extends JpaRepository<Truck, Integer> {

    @Query("""
    SELECT new com.maersk.sd1.sds.dto.GetVehicleCompanyOutput(
        v.id,
        v.plate,
        v.model,
        e.document,
        e.legalName,
        v.transportCompany.id
    )
    FROM Truck v
    INNER JOIN Company e ON v.transportCompany.id = e.id
    WHERE (:vehicleId IS NULL OR v.id = :vehicleId)
      AND (:vehiclePlate IS NULL OR v.plate = :vehiclePlate)
    """)
    List<GetVehicleCompanyOutput> findVehicleCompanyByCriteria(
            @Param("vehicleId") Integer vehicleId,
            @Param("vehiclePlate") String vehiclePlate,
            Pageable pageable
    );
    Page<Truck> findAll(Specification<Truck> spec, Pageable pageable);

    @Modifying
    @Query("UPDATE Truck t SET t.active = false, t.modificationUser.id = :userId, t.modificationDate = :modificationDate WHERE t.id = :vehicleId")
    void deactivateTruck(@Param("vehicleId") Integer vehicleId,
                         @Param("userId") Integer userId,
                         @Param("modificationDate") LocalDateTime modificationDate);

    boolean existsByPlateIgnoreCaseAndIdNot(String plate, Integer id);

    Optional<Truck> findByPlateIgnoreCaseAndIdNot(String plate, Integer id);

    Truck findFirstByPlateIgnoreCase(String plate);

    @Query("SELECT c.id FROM Truck c WHERE c.plate = :plate")
    Optional<Integer> findTopByPlate(@Param("plate") String plate);

}

