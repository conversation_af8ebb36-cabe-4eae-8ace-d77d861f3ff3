package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sde.controller.dto.BookingBlockCancellationEditInput;
import com.maersk.sd1.sde.controller.dto.BookingBlockCancellationEditOutput;
import com.maersk.sd1.common.repository.BookingBlockCancellationRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class BookingBlockCancellationEditService {

    private static final Logger logger = LogManager.getLogger(BookingBlockCancellationEditService.class);

    private final BookingBlockCancellationRepository bookingBlockCancellationRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    public BookingBlockCancellationEditService(BookingBlockCancellationRepository bookingBlockCancellationRepository,
                                               MessageLanguageRepository messageLanguageRepository) {
        this.bookingBlockCancellationRepository = bookingBlockCancellationRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public BookingBlockCancellationEditOutput editBookingBlockCancellation(BookingBlockCancellationEditInput.Input input) {
        BookingBlockCancellationEditOutput output = new BookingBlockCancellationEditOutput();
        try {
            int updated = bookingBlockCancellationRepository.updateBookBlockCommentAndUser(
                    input.getCancelBloqueoBookingId(),
                    input.getComments(),
                    LocalDateTime.now(),
                    input.getUserModificationId()
            );

            if (updated > 0) {
                output.setRespStatus(1);
                String translatedMessage = messageLanguageRepository.fnTranslatedMessage("GENERAL", 10, input.getLanguageId());
                output.setRespMessage(translatedMessage);
            } else {
                output.setRespStatus(0);
                output.setRespMessage("No record updated. Possibly invalid ID.");
            }
        } catch (Exception e) {
            logger.error("Error updating BookingBlockCancellation.", e);
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
        }
        return output;
    }
}