package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ContainerDetailProcessFileInput {

    private ContainerDetailProcessFileInput() {}
    @Data
    public static class Input {

        @JsonProperty("edi_coparn_id")
        @NotNull(message = "Booking EDI Id must not be null")
        private Integer bookingEdi;

        @JsonProperty("SituacionReserva")
        private String reservationStatus;

        @JsonProperty("unidad_negocio_id")
        @NotNull(message = "Business Unit Id must not be null")
        private Integer businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        @NotNull(message = "Sub Business Unit Id must not be null")
        private Integer subBusinessUnitId;

        @JsonProperty("programacion_nave_detalle_id")
        @NotNull(message = "Vessel Programming Detail Id must not be null")
        private Integer vesselProgrammingDetailId;

        @JsonProperty("Booking")   // need to check for not null
        private String booking;

        @JsonProperty("CntDimen_id")
        @NotNull(message = "Container Dimension Id must not be null")
        private Integer containerDimensionId;

        @JsonProperty("CntTipo_id")
        private Integer containerTypeId;

        @JsonProperty("CantidadReserva")
        private Integer reservedQuantity;

        @JsonProperty("CntDimen2_id")
        private Integer containerDimension2id;

        @JsonProperty("CntTipo2_id")
        private Integer containerType2id;

        @JsonProperty("CantidadReserva2")
        private Integer reservedQuantity2;

        @JsonProperty("Cliente_id")
        private Integer clientId;

        @JsonProperty("ClienteRS")
        private String clientReference;

        @JsonProperty("GrupoProductoDes")
        private String productGroupDescription;

        @JsonProperty("producto_id")
        private Integer productId;

        @JsonProperty("PtoEmbarque_id")
        private Integer loadingPortId;

        @JsonProperty("PtoDestino_id")
        private Integer destinationPortId;

        @JsonProperty("PtoDescarga_id")
        private Integer unloadingPortId;

        @JsonProperty("Temperatura")
        private String temperature;

        @JsonProperty("imo_id")
        private Integer imoId;

        @JsonProperty("LineaBK_id")
        private Integer bookingLineId;

        @JsonProperty("PesoBrutoEDI")
        private Integer grossWeightEDI;

        @JsonProperty("PesoBrutoEDI2")
        private Integer grossWeightEDI2;

        @JsonProperty("ColdTreatment")
        private Boolean coldTreatment = false;

        @JsonProperty("AtmosferaControlada")
        private Boolean controlledAtmosphere = false;

        @JsonProperty("usuario_registro_id")
        private Integer userRegistrationId;

        @JsonProperty("param_sequence_details")
        private String parameterSequenceDetails;

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix sds;

        public Input getInput() {
            return sds != null ? sds.getInput() : null;
        }
    }


}

