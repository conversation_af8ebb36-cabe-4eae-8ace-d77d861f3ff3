package com.maersk.sd1.common.dto;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class LinesWithSubLinesDTO {

    @SerializedName("sub_line_id")
    private Integer subLineId;
    @SerializedName("sub_line_name")
    private String subLineName;
    @SerializedName("line_main_id")
    private Integer lineMainId;
    @SerializedName("line_main_name")
    private String lineMainName;
}
