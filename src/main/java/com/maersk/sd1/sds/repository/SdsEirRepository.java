package com.maersk.sd1.sds.repository;

import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.repository.EirRepository;
import com.maersk.sd1.sds.dto.EIRFindStock;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SdsEirRepository extends EirRepository {

    @Query("SELECT DISTINCT new com.maersk.sd1.sds.dto.EIRFindStock(a.id, b.name, c.variable2, mf.longDescription) " +
            "FROM Eir a " +
            "JOIN BusinessUnit b ON a.subBusinessUnit.id = b.id " +
            "JOIN Catalog c ON a.catMovement.id = c.id " +
            "JOIN Catalog mf ON a.catEmptyFull.id = mf.id " +
            "WHERE a.container.id = :containerId " +
            "AND a.businessUnit.id = :businessUnitId " +
            "AND a.active = true " +
            "ORDER BY a.truckArrivalDate DESC")
    EIRFindStock findEIRStockGateIn(@Param("containerId") Integer containerId,
                                    @Param("businessUnitId") Integer businessUnitId);

    @Query("SELECT new com.maersk.sd1.sds.dto.EIRFindStock(a.id, b.name, c.variable2, mf.longDescription) " +
            "FROM Eir a " +
            "JOIN BusinessUnit b ON a.subBusinessUnit.id = b.id " +
            "JOIN Catalog c ON a.catMovement.id = c.id " +
            "JOIN Catalog mf ON a.catEmptyFull.id = mf.id " +
            "WHERE a.container.id = :containerId " +
            "AND a.active = true " +
            "ORDER BY a.truckArrivalDate DESC")
    EIRFindStock findEIRStockGateIn(@Param("containerId") Integer containerId);

    @Query(value = "select e from Eir e " +
            "inner join EirChassis ec on e.eirChassis.id = ec.id " +
            "where ec.chassis.id = :chassisId " +
            "and e.active = true " +
            "and ec.active = true " +
            "and e.subBusinessUnit.id = :subUnidadNegocioId " +
            "order by e.truckArrivalDate desc")
    Optional<Eir> findTop1ByChassisIdAndSubBusinessUnitId(@Param("chassisId") Integer chassisId, @Param("subUnidadNegocioId") Integer subUnidadNegocioId);

    @Query(value = "select e from Eir e " +
            "where e.container.id = :containerId " +
            "and e.active = true " +
            "and e.subBusinessUnit.id = :subUnidadNegocioId " +
            "order by e.truckArrivalDate desc")
    List<Eir> findTop1ByContainerIdAndSubBusinessUnitId(@Param("containerId") Integer containerId, @Param("subUnidadNegocioId") Integer subUnidadNegocioId);
}
