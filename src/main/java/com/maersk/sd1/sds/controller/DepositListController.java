package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.DepositListInput;
import com.maersk.sd1.sds.dto.DepositListOutput;
import com.maersk.sd1.sds.service.DepositListService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSDepositoService")
public class DepositListController {

    private static final Logger logger = LogManager.getLogger(DepositListController.class.getName());

    private final DepositListService depositListService;

    @PostMapping("/sdsdepositoListar")
    public ResponseEntity<ResponseController<DepositListOutput>> listDepots(@RequestBody @Valid DepositListInput.Root request) {
        try {
            DepositListInput.Input input = request.getPrefix().getInput();
            DepositListOutput output = depositListService.findDepots(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("Error listing depots.", e);
            DepositListOutput errorOutput = new DepositListOutput();
            // We won't fill in all data, just handle error scenario.
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}
