package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class BookingPreAllocationContainerListInput {
    @Data
    public static class Input {

        @JsonProperty("sub_business_unit_local_id")
        private Integer subBusinessUnitLocalId;

        @JsonProperty("language_id")
        private Integer languageId;

        @JsonProperty("container_size_id")
        private Integer containerSizeId;

        @JsonProperty("container_type_id")
        private Integer containerTypeId;

        @JsonProperty("documento_carga_id")
        private Integer documentoCargaId;

        @JsonProperty("container_grade_id")
        private Integer containerGradeId;

        @JsonProperty("container_shipping_line_id")
        private Integer containerShippingLineId;

        @JsonProperty("empty_full_id")
        private Integer emptyFullId;
    }
    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix prefix;
    }
}