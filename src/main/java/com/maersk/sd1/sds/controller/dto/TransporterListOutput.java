package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class TransporterListOutput {

    @JsonProperty("transporter_list")
    private List<TransporterOutputDetail> transporterList;

    @Data
    public static class TransporterOutputDetail {

        @JsonProperty("empresa_id")
        private Integer companyId;

        @JsonProperty("documento")
        private String document;

        @JsonProperty("razon_social")
        private String legalName;

        @JsonProperty("empresa")
        private String company;
    }
}