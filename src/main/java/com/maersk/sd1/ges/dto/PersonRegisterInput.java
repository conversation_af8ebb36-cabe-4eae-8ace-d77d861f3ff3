package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.PastOrPresent;
import lombok.Data;

import java.time.LocalDate;

@Data
public class PersonRegisterInput {

    private PersonRegisterInput() {}

    @Data
    public static class Input {

        @JsonProperty("tipo_documento_identidad_id")
        @NotNull(message = "tipo_documento_identidad_id cannot be null")
        private Integer tipoDocumentoIdentidadId;

        @JsonProperty("documento_identidad")
        @NotNull(message = "documento_identidad cannot be null")
        @Size(max = 15)
        private String documentoIdentidad;

        @JsonProperty("apellido_parterno")
        @NotNull(message = "apellido_parterno cannot be null")
        @Size(max = 100)
        private String apellidoParterno;

        @JsonProperty("apellido_materno")
        @Size(max = 100)
        private String apellidoMaterno;

        @JsonProperty("nombres")
        @NotNull(message = "nombres cannot be null")
        @Size(max = 100)
        private String nombres;

        @JsonProperty("fecha_nacimiento")
        @NotNull(message = "fecha_nacimiento cannot be null")
        @PastOrPresent
        private LocalDate fechaNacimiento;

        @JsonProperty("correo")
        @Size(max = 100)
        private String correo;

        @JsonProperty("telefono")
        @Size(max = 50)
        private String telefono;

        @JsonProperty("tipo_estado_persona_id")
        @NotNull(message = "tipo_estado_persona_id cannot be null")
        private Integer tipoEstadoPersonaId;

        @JsonProperty("tipo_area_trabajo_id")
        private Integer tipoAreaTrabajoId; // can be null if 0

        @JsonProperty("activo")
        @NotNull(message = "activo cannot be null")
        private Boolean activo;

        @JsonProperty("usuario_registro_id")
        @NotNull(message = "usuario_registro_id cannot be null")
        private Integer usuarioRegistroId;

        @JsonProperty("persona_rol")
        private Integer personaRol; // single role

        @JsonProperty("persona_rol_multiple")
        private String personaRolMultiple; // JSON string of roles

        @JsonProperty("empresa_id")
        private Integer empresaId;

        @JsonProperty("unidad_negocio_id")
        private Integer unidadNegocioId;

        @JsonProperty("licencia_conducir")
        @Size(max = 50)
        private String licenciaConducir;

        @JsonProperty("fecha_vigencia_licencia_conducir")
        private LocalDate fechaVigenciaLicenciaConducir;

        @JsonProperty("carnet")
        @Size(max = 50)
        private String carnet;

        @JsonProperty("fecha_vigencia_carnet")
        private LocalDate fechaVigenciaCarnet;

        @JsonProperty("genero")
        private Character genero;

        @JsonProperty("situacion_laboral")
        private Integer situacionLaboral;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}
