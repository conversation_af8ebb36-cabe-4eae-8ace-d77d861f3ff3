package com.apm.business.service.module.sde;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEGateInService {

	@RequestMapping(value = "/sdegateInBusquedaCntRhx", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdegateInBusquedaCntRhx(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.gate_in_busqueda_cnt_rhx","SDE");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_local_id", Jpo.DECIMAL);
			pResult.input("numero_contenedor", Jpo.STRING);
			pResult.input("cita_id", Jpo.INTEGER);
			pResult.input("ID_DocumentoQR", Jpo.BIGINTEGER);
			pResult.input("Local_DocumentoQR", Jpo.STRING);
			pResult.input("ImpoExpo_DocumentoQR", Jpo.STRING);
			pResult.input("Numero_DocumentoQR", Jpo.STRING);
			pResult.input("FechaCita", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdegateInValidacionMultipleRhxJson", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdegateInValidacionMultipleRhxJson(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.gate_in_validacion_multiple_rhx_json","SDE");
			pResult.input("Contenedores", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdegateInEmptyRegistrarRhx", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdegateInEmptyRegistrarRhx(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.gate_in_empty_registrar_rhx","SDE");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_local_id", Jpo.DECIMAL);
			pResult.input("programacion_nave_detalle_id", Jpo.INTEGER);
			pResult.input("contenedor_id", Jpo.INTEGER);
			pResult.input("cat_procedencia_id", Jpo.DECIMAL);
			pResult.input("vehiculo_id", Jpo.INTEGER);
			pResult.input("empresa_transporte_id", Jpo.DECIMAL);
			pResult.input("persona_conductor_id", Jpo.INTEGER);
			pResult.input("Observacion", Jpo.STRING);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("NumeroCita", Jpo.INTEGER);
			pResult.input("NumeroDocumentoExterno", Jpo.INTEGER);
			pResult.input("eir_id_agrupa", Jpo.INTEGER);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdesetYardResultEir", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdesetYardResultEir(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.set_yard_result_eir","SDE");
			pResult.input("eir_id", Jpo.INTEGER);
			pResult.input("yard_location", Jpo.STRING);
			pResult.input("usuario_modificacion_id", Jpo.INTEGER);
			pResult.output("estado", Jpo.INTEGER);
			pResult.output("mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdegateInBusquedaCnt", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdegateInBusquedaCnt(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.gate_in_busqueda_cnt","SDE");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_local_id", Jpo.DECIMAL);
			pResult.input("numero_contenedor", Jpo.STRING);
			pResult.input("cita_id", Jpo.INTEGER);
			pResult.input("ID_DocumentoQR", Jpo.STRING);
			pResult.input("Local_DocumentoQR", Jpo.DECIMAL);
			pResult.input("ImpoExpo_DocumentoQR", Jpo.STRING);
			pResult.input("Numero_DocumentoQR", Jpo.STRING);
			pResult.input("FechaCita", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("system_alias", Jpo.STRING);
			pResult.input("business_unit_alias", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdegateInEmptyRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdegateInEmptyRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.gate_in_empty_registrar","SDE");
			pResult.input("unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_local_id", Jpo.DECIMAL);
			pResult.input("programacion_nave_detalle_id", Jpo.INTEGER);
			pResult.input("contenedor_id", Jpo.INTEGER);
			pResult.input("cat_procedencia_id", Jpo.DECIMAL);
			pResult.input("vehiculo_id", Jpo.INTEGER);
			pResult.input("empresa_transporte_id", Jpo.DECIMAL);
			pResult.input("persona_conductor_id", Jpo.INTEGER);
			pResult.input("Observacion", Jpo.STRING);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.input("NumeroCita", Jpo.INTEGER);
			pResult.input("NumeroDocumentoExterno", Jpo.STRING);
			pResult.input("eir_id_agrupa", Jpo.INTEGER);
			pResult.output("resp_new_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}