package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.time.LocalDate;
import java.util.List;

@UtilityClass
public class RepairCleaningRequestChangeStatusInput {

    @Data
    public static class Input {
        @JsonProperty("estimates_list")
        private String estimatesList; // NVARCHAR(MAX)

        @JsonProperty("next_zone_id")
        private Integer nextZoneId; // NUMERIC(18)

        @JsonProperty("next_status_id")
        private Integer nextStatusId; // NUMERIC(18)

        @JsonProperty("user_registration_id")
        private Integer userRegistrationId; // NUMERIC(18)

        @JsonProperty("language_id")
        private Integer languageId; // INT

        @JsonProperty("is_cleaning_interface")
        private Integer isCleaningInterface; // INT

        @JsonProperty("sub_business_local_unit_id")
        private Integer subBusinessLocalUnitId; // NUMERIC(18)
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        @NotNull
        private Prefix prefix;
    }
}
