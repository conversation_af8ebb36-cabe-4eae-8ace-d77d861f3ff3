package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


@Data
public class EmailPendingProcessDetail {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("content")
    private String content;

    @JsonProperty("attachments")
    private String attachments;

    public EmailPendingProcessDetail(Integer id, String content, String attachments) {
        this.id = id;
        this.content = content;
        this.attachments = attachments;
    }
}
