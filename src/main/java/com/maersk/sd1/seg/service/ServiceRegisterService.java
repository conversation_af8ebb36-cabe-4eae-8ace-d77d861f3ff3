package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.Service;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.seg.dto.ServiceRegisterInput;
import com.maersk.sd1.seg.dto.ServiceRegisterOutput;
import com.maersk.sd1.common.repository.ServiceRepository;
import com.maersk.sd1.seg.exception.UserNotFoundException;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@org.springframework.stereotype.Service
@RequiredArgsConstructor
public class ServiceRegisterService {

    private static final Logger logger = LogManager.getLogger(ServiceRegisterService.class);

    private final ServiceRepository serviceRepository;
    private final UserRepository userRepository;

    @Transactional
    public ServiceRegisterOutput registerService(ServiceRegisterInput.Input input) {
        ServiceRegisterOutput output = new ServiceRegisterOutput();
        try {
            Service newService = new Service();

            newService.setName(input.getServiceName());
            newService.setProtectedIndicator(input.getServiceProtected());
            boolean isActive = (input.getServiceActive() == '1');
            newService.setStatus(isActive);

            Optional<User> user = userRepository.findById(input.getUserRegistrationId());

            if (user.isPresent()) {
                newService.setRegistrationUser(user.get());
            } else {
                throw new UserNotFoundException("User not found");
            }

            newService.setRegistrationDate(LocalDateTime.now());
            newService = serviceRepository.save(newService);

            output.setRespNewId(newService.getId());
            output.setRespResult(1);
            output.setRespMessage("Record successfully registered");

        } catch (UserNotFoundException e) {
            logger.error("Error registering service: User not found", e);
            output.setRespNewId(0);
            output.setRespResult(1);
            output.setRespMessage(e.getMessage());
        } catch (Exception e) {
            logger.error("Error registering service", e);
            output.setRespNewId(0);
            output.setRespResult(1);
            output.setRespMessage(e.getMessage());
        }
        return output;
    }
}