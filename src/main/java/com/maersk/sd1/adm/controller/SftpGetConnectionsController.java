package com.maersk.sd1.adm.controller;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/module/adm/ADMSftpConfigServiceImp")
public class SftpGetConnectionsController {

    @Getter
    @Value("${testcon.api.url}")
    private String baseUrl;

    @Setter
    private RestTemplate restTemplate = new RestTemplate();

    @PostMapping("/ftpGetConnection")
    public ResponseEntity<String> ftpGetConnection(@RequestBody FtpConnectionRequest request) {
        String response = getTestMethod(request, "ftpGetConnection");
        return ResponseEntity.ok(response);
    }

    @PostMapping("/sftpGetListDirectory")
    public ResponseEntity<String> sftpGetListDirectory(@RequestBody FtpConnectionRequest request) {
        String response = getTestMethod(request, "ftpGetListDirectory");
        return ResponseEntity.ok(response);
    }

    @PostMapping("/sftpGetFile")
    public ResponseEntity<String> sftpGetFile(@RequestBody FtpConnectionRequest request) {
        String response = getTestMethod(request, "ftpGetFile");
        return ResponseEntity.ok(response);
    }

    @PostMapping("/sftpSetFile")
    public ResponseEntity<String> sftpSetFile(@RequestBody FtpConnectionRequest request) {
        String response = getTestMethod(request, "ftpSetFile");
        return ResponseEntity.ok(response);
    }

    private String getTestMethod(FtpConnectionRequest request, String method) {
        String securePrefix = (request.getFlagFtp() != null && request.getFlagFtp().equals("1")) ? "" : "s";
        String url = baseUrl + securePrefix + method;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> requestBody = getStringObjectMap(request);

        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);

        return response.getBody();
    }

    static Map<String, Object> getStringObjectMap(FtpConnectionRequest request) {
        Map<String, Object> requestBody = new HashMap<>();
        Map<String, Object> innerMap = new HashMap<>();
        Map<String, String> dataMap = new HashMap<>();

        if (request.getId() != null) {
            dataMap.put("id", request.getId());
        }
        if (request.getFilename() != null) {
            dataMap.put("filename", request.getFilename());
        }
        if (request.getContent() != null) {
            dataMap.put("content", request.getContent());
        }
        if (request.getFlagDelete() != null) {
            dataMap.put("flag_delete", request.getFlagDelete());
        }

        innerMap.put("F", dataMap);
        requestBody.put("CON", innerMap);
        return requestBody;
    }

    @Getter
    @Setter
    public static class FtpConnectionRequest {
        private String id;
        private String flagFtp;
        private String filename;
        private String content;
        private String flagDelete;
    }
}