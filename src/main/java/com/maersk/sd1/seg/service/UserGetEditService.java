package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.seg.controller.dto.UserGetEditOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@RequiredArgsConstructor
@Service
public class UserGetEditService {

    private static final Logger logger = LogManager.getLogger(UserGetEditService.class);

    private final UserRepository userRepository;

    private final CompanyUserRepository companyUserRepository;

    private final CompanyRepository companyRepository;

    private final UserEmailTemplateRepository userEmailTemplateRepository;

    private final EmailTemplateRepository emailTemplateRepository;

    @Transactional(readOnly = true)
    public UserGetEditOutput getUserData(Integer userId) {
        logger.info("process started with, userId={}", userId);

        UserGetEditOutput output = new UserGetEditOutput();
        if (userId == null) {
            logger.error("userId cannot be null.");
            throw new IllegalArgumentException("userId cannot be null.");
        }

        User user = userRepository.findById(userId).orElse(null);
        if (user == null) {
            logger.error("user not found with Id: {}", userId);
            throw new UserNotFoundException("user not found");
        }

        output.setUserId(user.getId());
        output.setUserAlias(user.getAlias());
        output.setCorreo(user.getMail());
        output.setNames(user.getNames());
        output.setApellidoPaterno(user.getFirstLastName());
        output.setApellidoMaterno(user.getSecondLastName());
        output.setStatus(user.getStatus());
        output.setPersonId(user.getPerson() != null ? user.getPerson().getId() : null);
        output.setEmpresaId(user.getCompany() != null ? user.getCompany().getId() : null);
        output.setAdjuntoFotoId(user.getAttachmentPhoto() != null ? user.getAttachmentPhoto().getId() : null);

        if (user.getDateChangeKey() != null) {
            LocalDateTime now = LocalDateTime.now();
            long diffDays = ChronoUnit.DAYS.between(now.toLocalDate(), user.getDateChangeKey().toLocalDate());
            output.setCaducidadClave(diffDays);
        } else {
            output.setCaducidadClave(null);
        }

        List<Integer> companyIds = new ArrayList<>();
        var companyUserList = companyUserRepository.findByUserId(userId);
        if (companyUserList.isEmpty()) {
            if (user.getCompany() != null) {
                companyIds.add(user.getCompany().getId());
            }
        } else {
            for (var cu : companyUserList) {
                if(cu.getCompany() != null){
                    companyIds.add(cu.getCompany().getId());
                }
            }
        }

        if (!companyIds.isEmpty()) {
            List<UserGetEditOutput.AssociatedCompanyDTO> associatedCompanies =
                    companyRepository.findAssociatedCompaniesByIds(companyIds);
            output.setEmpresasAsociadas(associatedCompanies);
        } else {
            output.setEmpresasAsociadas(Collections.emptyList());
        }

        var userEmailTemplates = userEmailTemplateRepository.findUserEmailTemplatesByUserId(userId);
        output.setUserEmailTemplateDTO(userEmailTemplates);

        var emailTemplates = emailTemplateRepository.findEmailTemplatesForUser();
        output.setEmailTemplateDTOList(emailTemplates);

        logger.info("Process success with, userId={}", userId);
        return output;
    }

    public class UserNotFoundException extends RuntimeException {
        public UserNotFoundException(String message) {
            super(message);
        }
    }
}