package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.BusinessUnitDeleteInput;
import com.maersk.sd1.seg.dto.BusinessUnitDeleteOutput;
import com.maersk.sd1.seg.service.BusinessUnitDeleteService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMUnidadNegocioServiceImp")
public class BusinessUnitDeleteController {

    private static final Logger logger = LogManager.getLogger(BusinessUnitDeleteController.class.getName());

    private final BusinessUnitDeleteService businessUnitDeleteService;

    @PostMapping("/segunidadNegocioEliminar")
    public ResponseEntity<ResponseController<BusinessUnitDeleteOutput>> deleteBusinessUnit(@RequestBody @Valid BusinessUnitDeleteInput.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                logger.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
            }

            logger.info("Request received deleteBusinessUnit: {}", request);
            BusinessUnitDeleteInput.Input input = request.getPrefix().getInput();

            BusinessUnitDeleteOutput output = businessUnitDeleteService.deleteBusinessUnit(
                    input.getUnidadNegocioId(),
                    input.getUsuarioId()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            BusinessUnitDeleteOutput output = new BusinessUnitDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}