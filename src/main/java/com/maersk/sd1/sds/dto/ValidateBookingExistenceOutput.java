package com.maersk.sd1.sds.dto;

import lombok.Data;

import java.util.List;

@Data
public class ValidateBookingExistenceOutput {

    private List<DataItem> data;

    @Data
    public static class DataItem{
        private String bookingNumber;
        private String shipName;
        private String trip;
        private String operationName;
        private Integer bookingState;
        private String stateName;
        private Boolean isBookingApproved;
        private String bookingApprovalDesc;
        private String registrationDate;
    }

}
