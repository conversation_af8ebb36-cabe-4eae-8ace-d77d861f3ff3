package com.maersk.sd1.sde.service;


import com.maersk.sd1.common.repository.BusinessUnitRepository;
import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.common.repository.ContainerRepository;
import com.maersk.sd1.common.repository.EirRepository;
import com.maersk.sd1.sde.dto.GateOutEmptyExportReportInput;
import com.maersk.sd1.sde.dto.GateOutEmptyExportReportOutput;
import com.maersk.sd1.sde.dto.GateOutEmptyExportReportRow;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class GateOutEmptyExportReportServiceTest {

    @Mock
    private EirRepository eirRepository;

    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private ContainerRepository containerRepository;

    @InjectMocks
    private GateOutEmptyExportReportService gateOutEmptyExportReportService;

    private GateOutEmptyExportReportInput.Input input;

    @BeforeEach
    void setUp() {
        input = new GateOutEmptyExportReportInput.Input();
        input.setUserId(1);
        input.setSubBusinessUnitId(1);
        input.setLanguageId(1);
        input.setFromDate(LocalDate.of(2025, 1, 1));
        input.setToDate(LocalDate.of(2025, 12, 31));
        input.setPage(1);
        input.setSize(10);
    }

    @Test
    void Given_ValidInputs_WhenGetGateOutEmptyExportReport_ThenReturnSuccessResponse() {
        // Mock the repository methods
        Mockito.when(containerRepository.findContainerDummyId()).thenReturn(0);
        Mockito.when(containerRepository.getEquipmentNotApplicableId()).thenReturn(123L);

        // Create mock data for a single row
        Object[] row1 = new Object[51];
        row1[0] = 1; // gateType
        row1[1] = "MoveType1"; // moveType
        row1[2] = "LocalName1"; // localName
        row1[3] = 100; // eirNumber
        // Continue to fill the row with mock data for the remaining fields...

        Page<Object[]> page = new PageImpl<>(Collections.singletonList(row1), PageRequest.of(0, 10), 1);

        // Mock the repository's method call to return the page
        Mockito.when(eirRepository.findAllGateOutEmptyExport(
                        any(), any(), any(), any(), any(), any(), any(Pageable.class)))
                .thenReturn(page);

        // Call the service method
        GateOutEmptyExportReportOutput output = gateOutEmptyExportReportService.getGateOutEmptyExportReport(input);

        // Assertions
        assertEquals(1, output.getTotalRecords());
        assertEquals(1, output.getRows().size());

        // Check the row data
        GateOutEmptyExportReportRow row = output.getRows().get(0);
        assertEquals(1, row.getGateType());
        assertEquals("MoveType1", row.getMoveType());
        assertEquals("LocalName1", row.getLocalName());
        assertEquals(100, row.getEirNumber());
        // Add more assertions for the other fields...
    }


    @Test
    void Given_InvalidInputs_WhenGetGateOutEmptyExportReport_ThenReturnEmptyResultResponse() {
        // Mock repository for empty results
        Mockito.when(containerRepository.findContainerDummyId()).thenReturn(0);
        Mockito.when(containerRepository.getEquipmentNotApplicableId()).thenReturn(123L);

        Page<Object[]> page = new PageImpl<>(Arrays.asList(), PageRequest.of(0, 10), 0);
        Mockito.when(eirRepository.findAllGateOutEmptyExport(
                        any(), any(), any(), any(), any(), any(), any(Pageable.class)))
                .thenReturn(page);

        // Call the service method
        GateOutEmptyExportReportOutput output = gateOutEmptyExportReportService.getGateOutEmptyExportReport(input);

        // Assertions
        assertEquals(0, output.getTotalRecords());
        assertEquals(0, output.getRows().size());
    }

    @Test
    void Given_InvalidDates_WhenGetGateOutEmptyExportReport_ThenReturnEmptyResultResponse() {
        // Invalid date range where toDate is before fromDate
        input.setFromDate(LocalDate.of(2025, 12, 31));
        input.setToDate(LocalDate.of(2025, 1, 1));

        // Mock repository for empty results
        Mockito.when(containerRepository.findContainerDummyId()).thenReturn(0);
        Mockito.when(containerRepository.getEquipmentNotApplicableId()).thenReturn(123L);

        Page<Object[]> page = new PageImpl<>(Arrays.asList(), PageRequest.of(0, 10), 0);
        Mockito.when(eirRepository.findAllGateOutEmptyExport(
                        any(), any(), any(), any(), any(), any(), any(Pageable.class)))
                .thenReturn(page);

        // Call the service method
        GateOutEmptyExportReportOutput output = gateOutEmptyExportReportService.getGateOutEmptyExportReport(input);

        // Assertions: Should return empty result, and not throw an exception
        assertEquals(0, output.getTotalRecords());
        assertEquals(0, output.getRows().size());
    }
}
