package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class CancelEstimateStatusInput {

    @Data
    public static class Input {

        @JsonProperty("estimate_id")
        @NotNull
        private Integer estimateId;

        @JsonProperty("cat_equipment_category")
        @NotNull
        private Integer catEquipmentCategory;

        @JsonProperty("cat_estimate_cancel_reason")
        @NotNull
        private Integer catEstimateCancelReason;

        @JsonProperty("estimate_cancel_description")
        @Size(max = 200)
        private String estimateCancelDescription;

        @JsonProperty("modification_user_id")
        @NotNull
        private Integer modificationUserId;

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}