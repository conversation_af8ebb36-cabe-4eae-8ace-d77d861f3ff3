package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Output DTO that returns total records and a list of matched rows.
 */
@Data
public class BookingEdiSettingListOutputDTO {

    @JsonProperty("total_registros")
    private Long totalRegistros;

    @JsonProperty("resultados")
    private List<BookingEdiSettingRowDTO> resultados;

    /**
     * Inner DTO that represents each row from the query.
     */
    @Data
    public static class BookingEdiSettingRowDTO {

        @JsonProperty("seteo_edi_coparn_id")
        private Integer seteoEdiCoparnId;

        @JsonProperty("linea_naviera_id")
        private Integer lineaNavieraId;

        @JsonProperty("cat_canal_recepcion_coparn_id")
        private Long catCanalRecepcionCoparnId;

        @JsonProperty("cat_modo_procesar_coparn_id")
        private Long catModoProcesarCoparnId;

        @JsonProperty("edi_coparn_descripcion")
        private String bkEdiDescription;

        @JsonProperty("azure_id")
        private String azureId;

        @JsonProperty("sftp_coparn_id")
        private String bkEdiSftpId;

        @JsonProperty("ftp_coparn_id")
        private String bkEdiFtpId;

        @JsonProperty("carpeta_coparn_ruta")
        private String bkEdiFolderRoute;

        @JsonProperty("extension_archivo_descargar")
        private String downloadFileExtension;

        @JsonProperty("ruta_mover_edi")
        private String ediMoveRoute;

        @JsonProperty("permitir_crear_prog_nave_automatico")
        private Boolean allowCreateAutomaticVesselProgramming;

        @JsonProperty("permitir_crear_cliente_automatico")
        private Boolean allowCreateAutomaticCustomer;

        @JsonProperty("es_historico")
        private Boolean isHistorical;

        @JsonProperty("fecha_debaja")
        private LocalDateTime deactivationDate;

        @JsonProperty("motivo_debaja")
        private String deactivationReason;

        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("fecha_registro")
        private LocalDateTime registrationDate;

        @JsonProperty("fecha_modificacion")
        private LocalDateTime modificationDate;

        @JsonProperty("unidad_negocio_id")
        private Long businessUnitId;

        @JsonProperty("usuario_registro_id")
        private Integer registrationUserId;

        @JsonProperty("usuario_registro_nombres")
        private String registrationUserNames;

        @JsonProperty("usuario_registro_apellidos")
        private String registrationUserApellidos;

        @JsonProperty("usuario_modificacion_id")
        private Integer modificationUserId;

        @JsonProperty("usuario_modificacion_nombres")
        private String modificationUserNames;

        @JsonProperty("usuario_modificacion_apellidos")
        private String modificationUserApellidos;
    }
}

