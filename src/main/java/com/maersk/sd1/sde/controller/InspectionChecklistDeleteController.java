package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.InspectionChecklistDeleteInput;
import com.maersk.sd1.sde.dto.InspectionChecklistDeleteOutput;
import com.maersk.sd1.sde.service.InspectionChecklistDeleteService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEInspectionChecklistServiceImp")
public class InspectionChecklistDeleteController {

    private static final Logger logger = LogManager.getLogger(InspectionChecklistDeleteController.class);

    private final InspectionChecklistDeleteService inspectionChecklistDeleteService;

    @PostMapping("/sdeinspectionChecklistDelete")
    public ResponseEntity<ResponseController<InspectionChecklistDeleteOutput>> inspectionChecklistDelete(
            @RequestBody @Valid InspectionChecklistDeleteInput.Root request) {
        try {
            logger.info("Request received inspectionChecklistDelete: {}", request);
            InspectionChecklistDeleteInput.Input input = request.getPrefix().getInput();
            InspectionChecklistDeleteOutput output = inspectionChecklistDeleteService.deleteInspectionChecklist(
                    input.getInspectionChecklistId(),
                    input.getUserModificationId(),
                    input.getLanguageId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing inspectionChecklistDelete request.", e);
            InspectionChecklistDeleteOutput output = new InspectionChecklistDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}