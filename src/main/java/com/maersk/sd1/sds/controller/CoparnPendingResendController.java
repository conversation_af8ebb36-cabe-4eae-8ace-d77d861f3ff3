package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.CoparnPendingResendInput;
import com.maersk.sd1.sds.dto.CoparnPendingResendOutput;
import com.maersk.sd1.sds.service.CoparnPendingResendService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSCoparnResendServiceImp")
public class CoparnPendingResendController {

    private static final Logger logger = LogManager.getLogger(CoparnPendingResendController.class);

    private final CoparnPendingResendService coparnPendingResendService;

    @Autowired
    public CoparnPendingResendController(CoparnPendingResendService coparnPendingResendService) {
        this.coparnPendingResendService = coparnPendingResendService;
    }

    @PostMapping("/listarPendientesReenvio")
    public ResponseEntity<ResponseController<CoparnPendingResendOutput>> listarPendientesReenvio(
            @RequestBody @Valid CoparnPendingResendInput.Root request) {
        try {

            if(request.getPrefix().getInput().getSeteoEdiCoparnId() == null) {
                throw new IllegalArgumentException("Seteo EDI COPARN ID is required.");
            }

            Integer seteoEdiCoparnId = request.getPrefix().getInput().getSeteoEdiCoparnId();
            CoparnPendingResendOutput output = coparnPendingResendService.getPendingResends(seteoEdiCoparnId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while listing pending COPARN resend.", e);
            CoparnPendingResendOutput output = new CoparnPendingResendOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            output.setPendingResendRecords(null);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}