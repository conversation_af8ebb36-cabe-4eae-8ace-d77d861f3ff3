package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.System;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "SystemRule.rule_general_get_out",
        procedureName = "sds.rule_general_get_out",
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "sub_business_unit_local_alias", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "system_rule_id", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "type_rule", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "r_action", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "r_actionjson", type = String.class)
        }
)
@NamedStoredProcedureQuery(
        name = "SystemRule.gateinGeneralEquipmentAppointmentValidate",
        procedureName = "sdg.gatein_general_equipment_appointment_validate",
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "sub_business_unit_local_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "equipment_number", type = String.class)
        }
)
@Table(name = "regla_sistema", schema = "ges")
public class SystemRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "regla_sistema_id", nullable = false)
    private Integer id;

    @Size(max = 50)
    @NotNull
    @Column(name = "id", nullable = false, length = 50)
    private String alias;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sistema_id", nullable = false)
    private System system;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unidad_negocio_id")
    private BusinessUnit businessUnit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sub_unidad_negocio_id")
    private BusinessUnit subBusinessUnit;

    @Size(max = 100)
    @NotNull
    @Column(name = "descripcion", nullable = false, length = 100)
    private String description;

    @NotNull
    @Lob
    @Column(name = "regla", nullable = false)
    private String rule;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

}