package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.NotificationTemplate;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "notificacion_accion_plantilla", schema = "seg")
public class NotificationActionTemplate {
    @Id
    @Column(name = "notificacion_accion_plantilla_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "notificacion_plantilla_id", nullable = false)
    private NotificationTemplate notificationTemplate;

    @Size(max = 100)
    @NotNull
    @Column(name = "icono", nullable = false, length = 100)
    private String icon;

    @Size(max = 200)
    @Column(name = "titulo", length = 200)
    private String title;

    @Size(max = 200)
    @Column(name = "subtitulo", length = 200)
    private String subtitle;

    @NotNull
    @Lob
    @Column(name = "contenido", nullable = false)
    private String content;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @Size(max = 200)
    @Column(name = "url_referencia", length = 200)
    private String urlReference;

}