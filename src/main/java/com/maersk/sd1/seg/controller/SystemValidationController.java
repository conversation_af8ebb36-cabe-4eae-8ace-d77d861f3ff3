package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.SystemValidationInput;
import com.maersk.sd1.seg.controller.dto.SystemValidationOutput;
import com.maersk.sd1.seg.service.SystemValidationService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/Inlandnet/security")
public class SystemValidationController {

    private static final Logger logger = LogManager.getLogger(SystemValidationController.class.getName());

    private final SystemValidationService systemValidationService;

    @PostMapping("/SistemaServiceImp/segsistemaValidar")
    public ResponseEntity<ResponseController<SystemValidationOutput>> systemValidate(@RequestBody SystemValidationInput.Root request){
        try {
            if (request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.status(400).body(new ResponseController<>(Constants.INVALID_INPUT));
            }
            SystemValidationInput.Input input = request.getPrefix().getInput();
            if (input.getSystemId() == null || input.getUserId() == null) {
                return ResponseEntity.status(400).body(new ResponseController<>(Constants.INVALID_INPUT));
            }
            SystemValidationOutput systemValidationOutput = systemValidationService.validateSystem(input);
            return ResponseEntity.ok(new ResponseController<>(systemValidationOutput));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(null));
        }
    }
}