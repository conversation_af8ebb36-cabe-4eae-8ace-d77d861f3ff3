package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Service;
import com.maersk.sd1.seg.dto.ServicioListDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import com.maersk.sd1.seg.dto.ServiceGetServiceDto;
import org.springframework.data.jpa.repository.Modifying;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ServiceRepository extends JpaRepository<Service, Integer> {

    @Modifying
    @Query("UPDATE Service s " +
            "SET s.status = false, s.modificationDate = :modificationDate, s.modificationUser.id = :userModificationId " +
            "WHERE s.id = :serviceId")
    int disableService(
            @Param("serviceId") Integer serviceId,
            @Param("userModificationId") Integer userModificationId,
            @Param("modificationDate") LocalDateTime modificationDate);

    @Query("""
            SELECT COUNT(s.id)
            FROM Service s
            LEFT JOIN s.registrationUser ru
            WHERE (:servicioId IS NULL OR s.id = :servicioId)
              AND (:servicioNombre IS NULL OR s.name LIKE %:servicioNombre%)
              AND (:servicioIndicadorProtegido IS NULL OR s.protectedIndicator = :servicioIndicadorProtegido)
              AND (
                (:servicioEstado IS NULL) OR
                (:servicioEstado = '1' AND s.status = true) OR
                (:servicioEstado = '0' AND s.status = false)
              )
              AND (:usuarioRegistro IS NULL OR CONCAT(ru.names, ' ', ru.firstLastName, ' ', COALESCE(ru.secondLastName, '')) LIKE %:usuarioRegistro%)
           """)
    long countByFilters(
            @Param("servicioId") Integer servicioId,
            @Param("servicioNombre") String servicioNombre,
            @Param("servicioIndicadorProtegido") Character servicioIndicadorProtegido,
            @Param("servicioEstado") String servicioEstado,
            @Param("usuarioRegistro") String usuarioRegistro
    );

    @Query("""
            SELECT new com.maersk.sd1.seg.dto.ServicioListDTO(
                s.id,
                s.name,
                s.protectedIndicator,
                s.status,
                COALESCE(ru.id, 0),
                s.registrationDate,
                COALESCE(mu.id, 0),
                s.modificationDate,
                ru.names,
                CONCAT(ru.firstLastName, ' ', COALESCE(ru.secondLastName, ''))
            )
            FROM Service s
            LEFT JOIN s.registrationUser ru
            LEFT JOIN s.modificationUser mu
            WHERE (:servicioId IS NULL OR s.id = :servicioId)
              AND (:servicioNombre IS NULL OR s.name LIKE %:servicioNombre%)
              AND (:servicioIndicadorProtegido IS NULL OR s.protectedIndicator = :servicioIndicadorProtegido)
              AND (
                (:servicioEstado IS NULL) OR
                (:servicioEstado = '1' AND s.status = true) OR
                (:servicioEstado = '0' AND s.status = false)
              )
              AND (:usuarioRegistro IS NULL OR CONCAT(ru.names, ' ', ru.firstLastName, ' ', COALESCE(ru.secondLastName, '')) LIKE %:usuarioRegistro%)
            ORDER BY s.id ASC
           """)
    List<ServicioListDTO> findServicioList(
            @Param("servicioId") Integer servicioId,
            @Param("servicioNombre") String servicioNombre,
            @Param("servicioIndicadorProtegido") Character servicioIndicadorProtegido,
            @Param("servicioEstado") String servicioEstado,
            @Param("usuarioRegistro") String usuarioRegistro
    );
           
    @Query("""
            SELECT new com.maersk.sd1.seg.dto.ServiceGetServiceDto(
                s.id,
                s.name,
                s.protectedIndicator,
                s.status,
                s.registrationUser.id,
                s.registrationDate,
                COALESCE(s.modificationUser.id, 0),
                s.modificationDate
            )
            FROM Service s
            WHERE s.id = :id
            """)
    Optional<ServiceGetServiceDto> findServicioById(@Param("id") Integer id);

}