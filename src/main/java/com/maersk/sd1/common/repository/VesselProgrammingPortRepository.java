package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.VesselProgrammingPort;
import com.maersk.sd1.sds.dto.VesselProgrammingPortDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

import java.util.List;
import java.util.Optional;

@Repository
public interface VesselProgrammingPortRepository extends JpaRepository<VesselProgrammingPort, Integer> {

    @Query("SELECT new com.maersk.sd1.sds.dto.VesselProgrammingPortDTO(v.id, p.port, p.name, c.name) " +
            "FROM VesselProgrammingPort v " +
            "JOIN Port p on v.id = p.id " +
            "LEFT JOIN Country c on c.id = p.country.id " +
            "INNER JOIN VesselProgrammingDetail vd on v.vesselProgramming.id = vd.vesselProgramming.id " +
            "WHERE vd.id = :shipProgrammingDetailId " +
            "AND (:portIds IS NOT NULL OR v.active = true) " +
            "AND (:portIds IS NULL OR p.id IN :portIds) " +
            "AND (:portName IS NULL OR CONCAT(p.port, ' - ', p.name, COALESCE(' / ' || c.name, '')) LIKE %:portName%) " +
            "ORDER BY CONCAT(p.port, ' - ', p.name, COALESCE(' / ' || c.name, ''))")
    List<VesselProgrammingPortDTO> searchPorts(@Param("portIds") List<Integer> portIds,
                                               @Param("portName") String portName,
                                               @Param("shipProgrammingDetailId") Integer shipProgrammingDetailId);

    @Query("SELECT CASE WHEN EXISTS (SELECT 1 FROM VesselProgrammingPort vpp WHERE vpp.vesselProgramming.id = :vesselProgrammingId " +
            "AND vpp.port.id = :portId) THEN true ELSE false END")
    boolean existsByVesselProgrammingIdAndPortId(@Param("vesselProgrammingId") Integer vesselProgrammingId,
                                                 @Param("portId") Integer portId);

    @Modifying
    @Query("UPDATE VesselProgrammingPort v SET v.active = true, v.modificationUser.id = :userRegistrationId, v.modificationDate = CURRENT_TIMESTAMP " +
            "WHERE v.vesselProgramming.id = :vesselProgrammingId AND v.port.id = :destinationPortId AND v.active = false")
    void updateVesselProgrammingPort(@Param("userRegistrationId") Integer userRegistrationId,
                                     @Param("vesselProgrammingId") Integer vesselProgrammingId,
                                     @Param("destinationPortId") Integer destinationPortId);

    @Modifying
    @Transactional
    @Query("UPDATE VesselProgrammingPort SET active = true, modificationUser.id = :registrationUserId, modificationDate = :modificationDate " +
            "WHERE vesselProgramming.id = :vesselProgrammingId AND port.id = :portId AND active = false")
    void updateVesselProgrammingPortByIdAndPortAndUserAndDate(int vesselProgrammingId, int portId, int registrationUserId, LocalDateTime modificationDate);

    @Query("SELECT vp FROM VesselProgrammingPort vp " +
            "WHERE vp.vesselProgramming.id = :vesselProgrammingId " +
            "AND vp.port.id = :portid")
    Optional<VesselProgrammingPort> findByVesselProgrammingIdAndPortId(
            @Param("vesselProgrammingId") Integer vesselProgrammingId,
            @Param("portid") Integer portid);
  
    @Query("SELECT vpp FROM VesselProgrammingPort vpp "
            + "JOIN FETCH vpp.port p "
            + "LEFT JOIN FETCH p.country c "
            + "WHERE vpp.vesselProgramming.id = :vesselProgrammingId "
            + "AND vpp.active = true "
            + "ORDER BY p.port")
    List<VesselProgrammingPort> findActiveByVesselProgrammingId(@Param("vesselProgrammingId") Integer vesselProgrammingId);

    @Query("SELECT vpp FROM VesselProgrammingPort vpp WHERE vpp.vesselProgramming.id = :programmingId AND vpp.port.id = :portId")
    List<VesselProgrammingPort> findVesselProgrammingPort(@Param("programmingId") Integer programmingId,
                                                          @Param("portId") Integer portId);

    @Modifying
    @Query("UPDATE VesselProgrammingPort vpp " +
            "SET vpp.active = true, vpp.modificationUser.id = 1, vpp.modificationDate = CURRENT_TIMESTAMP " +
            "WHERE vpp.vesselProgramming.id = :vesselProgrammingId " +
            "AND vpp.port.id = :portId " +
            "AND vpp.active = false")
    int activateVesselProgrammingPort(Integer vesselProgrammingId, Integer portId);
}