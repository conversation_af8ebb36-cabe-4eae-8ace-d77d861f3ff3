package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.CompanyContact;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface CompanyContactRepository extends JpaRepository<CompanyContact, Integer> {

    @Query("SELECT DISTINCT econ.mail " +
            "FROM Company emp " +
            "INNER JOIN CompanyContact econ ON emp.id = econ.company.id " +
            "WHERE emp.id = :empresaTransporteId " +
            "AND econ.catContactType.id = :tipoContactoId " +
            "AND emp.status = true")
    List<String> findDistinctEmailsByCompanyAndContactType(@Param("empresaTransporteId") Integer empresaTransporteId,
                                                           @Param("tipoContactoId") Integer tipoContactoId);

    List<CompanyContact> findByCompanyId(Integer empresaId);

    @Modifying
    @Transactional
    @Query("DELETE FROM CompanyContact c WHERE c.company.id = :companyId")
    void deleteAllByCompanyId(@Param("companyId") Integer companyId);
  
    @Query("DELETE FROM CompanyContact cc WHERE cc.company.id = :companyId")
    void deleteByCompanyId(@Param("companyId") Integer companyId);

}