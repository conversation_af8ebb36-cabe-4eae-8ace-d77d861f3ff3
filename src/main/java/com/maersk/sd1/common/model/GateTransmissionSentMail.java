package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.PendingSendEmail;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.Nationalized;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "edi_codeco_envio_correo", schema = "sde")
public class GateTransmissionSentMail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "edi_codeco_envio_correo_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "email_pendiente_enviar_id")
    private PendingSendEmail pendingSendEmail;

    @Nationalized
    @Lob
    @Column(name = "lista_edi_codeco_envio_id")
    private String listGateTransmissionSentId;

}