package com.maersk.sd1.ges.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class ConsultarServicioOutputDTO {
    private List<Map<String, Object>> objetoConsulta;
    private Map<String, Object> objetoTitulo;
    private int total;

    public ConsultarServicioOutputDTO(List<Map<String, Object>> objetoConsulta, Map<String, Object> objetoTitulo, int total) {
        this.objetoConsulta = objetoConsulta;
        this.objetoTitulo = objetoTitulo;
        this.total = total;
    }
}