package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.CargoDocumentDetailRepository;
import com.maersk.sd1.sds.dto.DocumentCargoDetailObtainOutput;

import com.maersk.sd1.sds.repository.DocumentCargoDetailObtainProjection;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class DocumentCargoDetailObtainService {

    private static final Logger logger = LogManager.getLogger(DocumentCargoDetailObtainService.class);

    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;

    public DocumentCargoDetailObtainOutput obtenerDetalleCarga(Integer cargoDocumentDetailId) {
        logger.info("Fetching CargoDocumentDetail with ID: {}", cargoDocumentDetailId);
        if (cargoDocumentDetailId == null) {
            throw new IllegalArgumentException("documento_carga_detalle_id cannot be null");
        }

        DocumentCargoDetailObtainProjection projection = cargoDocumentDetailRepository
                .findCargoDocumentDetail(cargoDocumentDetailId)
                .orElseThrow(() -> new RuntimeException("No CargoDocumentDetail found for the given ID."));

        DocumentCargoDetailObtainOutput output = new DocumentCargoDetailObtainOutput();
        output.setCargoDocumentId(projection.getCargoDocumentId());
        output.setCargoDocument(projection.getCargoDocument());
        output.setVesselId(projection.getVesselId());
        output.setVesselName(projection.getVesselName());
        output.setVoyage(projection.getVoyage());
        output.setCargoDocumentDetailId(projection.getCargoDocumentDetailId());
        output.setCargoDocumentDetailMotherId(projection.getCargoDocumentDetailMotherId());
        output.setProductId(projection.getProductId());
        output.setProductName(projection.getProductName());
        output.setContainerNumber(projection.getContainerNumber());
        output.setManifestedQuantity(projection.getManifestedQuantity());
        output.setManifestedWeight(projection.getManifestedWeight());
        output.setManifestedVolume(projection.getManifestedVolume());
        output.setReceivedQuantity(projection.getReceivedQuantity());
        output.setReceivedWeight(projection.getReceivedWeight());
        output.setReceivedVolume(projection.getReceivedVolume());
        output.setDispatchedQuantity(projection.getDispatchedQuantity());
        output.setDispatchedWeight(projection.getDispatchedWeight());
        output.setDispatchedVolume(projection.getDispatchedVolume());
        output.setImmobilizedQuantity(projection.getImmobilizedQuantity());
        output.setImmobilizedWeight(projection.getImmobilizedWeight());
        output.setImmobilizedVolume(projection.getImmobilizedVolume());
        output.setCatPackagingId(projection.getCatPackagingId());
        output.setCatPackagingDescription(projection.getCatPackagingDescription());
        output.setCatWeightMeasureId(projection.getCatWeightMeasureId());
        output.setCatWeightMeasureDescription(projection.getCatWeightMeasureDescription());
        output.setSaysContain(projection.getSaysContain());
        output.setBalanceQuantity(projection.getBalanceQuantity());
        output.setBalanceWeight(projection.getBalanceWeight());
        output.setBalanceVolume(projection.getBalanceVolume());
        output.setIsDangerousCargo(projection.getIsDangerousCargo());
        output.setIsRefrigeratedCargo(projection.getIsRefrigeratedCargo());
        output.setBrands(projection.getBrands());
        output.setCommodity(projection.getCommodity());
        output.setManifestedChassis(projection.getManifestedChassis());
        output.setReceivedChassis(projection.getReceivedChassis());
        output.setCatCargoConditionId(projection.getCatCargoConditionId());
        output.setCatCargoConditionDescription(projection.getCatCargoConditionDescription());
        output.setInventoriedQuantity(projection.getInventoriedQuantity());
        output.setInventoriedWeight(projection.getInventoriedWeight());
        output.setInventoriedVolume(projection.getInventoriedVolume());
        output.setEmptyPickUpBusinessUnitId(projection.getEmptyPickUpBusinessUnitId());
        output.setEmptyPickUpBusinessUnitName(projection.getEmptyPickUpBusinessUnitName());
        output.setGateoutEmptySettled(projection.getGateoutEmptySettled());
        output.setCatManifestedContainerTypeId(projection.getCatManifestedContainerTypeId());
        output.setCatManifestedContainerTypeDescription(projection.getCatManifestedContainerTypeDescription());
        output.setCatCreationOriginId(projection.getCatCreationOriginId());
        output.setCatCreationOriginDescription(projection.getCatCreationOriginDescription());
        output.setCatManifestedSizeId(projection.getCatManifestedSizeId());
        output.setCatManifestedSizeDescription(projection.getCatManifestedSizeDescription());
        output.setCatMeasureUnitQuantityId(projection.getCatMeasureUnitQuantityId());
        output.setCatMeasureUnitQuantityDescription(projection.getCatMeasureUnitQuantityDescription());
        output.setBookingDetailId(projection.getBookingDetailId());
        output.setTraceCargoDocumentDetail(projection.getTraceCargoDocumentDetail());

        logger.info("CargoDocumentDetail fetched successfully for ID: {}", cargoDocumentDetailId);
        return output;
    }
}
