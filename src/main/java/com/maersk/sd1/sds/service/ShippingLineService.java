package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.dto.CatalogFindDTO;
import com.maersk.sd1.common.dto.ShippingLineDTO;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.sds.controller.dto.ShippingLineInput;
import com.maersk.sd1.sds.controller.dto.ShippingLineOutput;
import com.maersk.sd1.sds.dto.ShippingLineCountFilterDTO;
import com.maersk.sd1.sds.dto.ShippingLineFilterDTO;
import com.maersk.sd1.sds.dto.ShippingLineProcessDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
public class ShippingLineService {

    private static final Logger logger = LogManager.getLogger(ShippingLineService.class);

    private final ShippingLineRepository shippingLineRepository;

    public ShippingLineService(ShippingLineRepository shippingLineRepository) {
        this.shippingLineRepository = shippingLineRepository;
    }

    @Transactional(readOnly = true)
    public ShippingLineOutput listShippingLines(ShippingLineInput.Input input) {
        logger.info("Entering listShippingLines method");

        ShippingLineCountFilterDTO countFilter = new ShippingLineCountFilterDTO();
        countFilter.setShippingLineId(input.getShippingLineId());
        countFilter.setName(input.getName());
        countFilter.setActive(input.getActive());
        countFilter.setRegistrationDateMin(input.getRegistrationDateMin());
        countFilter.setRegistrationDateMax(input.getRegistrationDateMax() != null ? input.getRegistrationDateMax().plusDays(1) : null);
        countFilter.setModificationDateMin(input.getModificationDateMin());
        countFilter.setModificationDateMax(input.getModificationDateMax() != null ? input.getModificationDateMax().plusDays(1) : null);
        countFilter.setShippingLineCompany(input.getShippingLine());

        Long totalRecords = shippingLineRepository.countFiltered(countFilter);

        Pageable pageable;
        if (input.getPage() == null || input.getSize() == null || input.getPage() <= 0 || input.getSize() <= 0) {
            pageable = PageRequest.of(0, Integer.MAX_VALUE);
        } else {
            pageable = PageRequest.of(input.getPage() - 1, input.getSize());
        }

        ShippingLineFilterDTO filter = new ShippingLineFilterDTO();

        filter.setShippingLineId(input.getShippingLineId());
        filter.setName(input.getName());
        filter.setActive(input.getActive());
        filter.setRegistrationDateMin(input.getRegistrationDateMin());
        filter.setRegistrationDateMax(input.getRegistrationDateMax() != null ? input.getRegistrationDateMax().plusDays(1) : null);
        filter.setModificationDateMin(input.getModificationDateMin());
        filter.setModificationDateMax(input.getModificationDateMax() != null ? input.getModificationDateMax().plusDays(1) : null);
        filter.setShippingLine(input.getShippingLine());
        filter.setBusinessUnitId(input.getBusinessUnitId());

        Page<ShippingLineProcessDTO> shippingLineProcess = shippingLineRepository.findFiltered(
                filter,
                pageable
        );

        return generateOutput(shippingLineProcess.getContent(), totalRecords.intValue());
    }

    private ShippingLineOutput generateOutput(List<ShippingLineProcessDTO> lines, int records) {
        logger.info("Using generateOutput method");

        List<ShippingLineOutput.ShippingLineResult> results = new ArrayList<>();

        for (ShippingLineProcessDTO line : lines) {
            ShippingLineOutput.ShippingLineResult result = createShippingLineResult(line);
            results.add(result);
        }

        List<Long> count = List.of((long) records);
        return ShippingLineOutput.builder()
                .totalRecords(List.of(count))
                .shippingLineResultList(results)
                .build();
    }

    private ShippingLineOutput.ShippingLineResult createShippingLineResult(ShippingLineProcessDTO line) {
        ShippingLineOutput.ShippingLineResult result = new ShippingLineOutput.ShippingLineResult();

        result.setShippingLineId(getShippingLineId(line));
        result.setName(getShippingLineName(line));
        result.setActive(getShippingLineActive(line));
        result.setRegistrationDate(line.getRegistrationDate());
        result.setModificationDate(line.getModificationDate());
        result.setShippingLine(getShippingLineCompany(line));
        result.setColor(getShippingLineColor(line));
        result.setRegistrationUserId(getRegistrationUserId(line));
        result.setModificationUserId(getModificationUserId(line));
        result.setRegistrationUserNames(getRegistrationUserNames(line));
        result.setRegistrationUserLastNames(getRegistrationUserLastNames(line));
        result.setModificationUserNames(getModificationUserNames(line));
        result.setModificationUserLastNames(getModificationUserLastNames(line));

        return result;
    }

    private Integer getShippingLineId(ShippingLineProcessDTO line) {
        return line.getShippingLineClass() != null ? line.getShippingLineClass().getId() : null;
    }

    private String getShippingLineName(ShippingLineProcessDTO line) {
        return line.getShippingLineClass() != null ? line.getShippingLineClass().getName() : null;
    }

    private Boolean getShippingLineActive(ShippingLineProcessDTO line) {
        return line.getShippingLineClass() != null ? line.getShippingLineClass().getActive() : null;
    }

    private String getShippingLineCompany(ShippingLineProcessDTO line) {
        return line.getShippingLineClass() != null ? line.getShippingLineClass().getShippingLineCompany() : null;
    }

    private String getShippingLineColor(ShippingLineProcessDTO line) {
        return line.getShippingLineClass() != null ? line.getShippingLineClass().getColor() : null;
    }

    private Integer getRegistrationUserId(ShippingLineProcessDTO line) {
        return line.getShippingLineClass() != null && line.getShippingLineClass().getRegistrationUser() != null ? line.getShippingLineClass().getRegistrationUser().getId() : null;
    }

    private Integer getModificationUserId(ShippingLineProcessDTO line) {
        return line.getShippingLineClass() != null && line.getShippingLineClass().getModificationUser() != null ? line.getShippingLineClass().getModificationUser().getId() : null;
    }

    private String getRegistrationUserNames(ShippingLineProcessDTO line) {
        return line.getShippingLineClass() != null && line.getShippingLineClass().getRegistrationUser() != null ? line.getShippingLineClass().getRegistrationUser().getNames() : null;
    }

    private String getRegistrationUserLastNames(ShippingLineProcessDTO line) {
        return line.getShippingLineClass() != null && line.getShippingLineClass().getRegistrationUser() != null ? line.getShippingLineClass().getRegistrationUser().getFirstLastName() + " " + line.getShippingLineClass().getRegistrationUser().getSecondLastName() : null;
    }

    private String getModificationUserNames(ShippingLineProcessDTO line) {
        return line.getShippingLineClass() != null && line.getShippingLineClass().getModificationUser() != null ? line.getShippingLineClass().getModificationUser().getNames() : null;
    }

    private String getModificationUserLastNames(ShippingLineProcessDTO line) {
        return line.getShippingLineClass() != null && line.getShippingLineClass().getModificationUser() != null ? line.getShippingLineClass().getModificationUser().getFirstLastName() + " " + line.getShippingLineClass().getModificationUser().getSecondLastName() : null;
    }

    public String getNameById(Integer shippingLineId){
        return shippingLineRepository.getNameById(shippingLineId);
    }

    public HashMap<String, Integer> findIdsByShippingLineCompanies(List<String> shippingLineNames){
        HashMap<String, Integer> result = new HashMap<>();
        List<ShippingLineDTO> rawResult = shippingLineRepository.findIdsByShippingLineCompanies(shippingLineNames);
        rawResult.forEach(rr -> result.put(rr.getShippingLineCompany(), rr.getShippingLineId()));
        return result;
    }

}