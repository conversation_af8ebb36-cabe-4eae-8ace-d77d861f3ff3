package com.maersk.sd1.sds.dto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ValidateCargaBlInput {
    @Data
    public static class Input{
        @JsonProperty("documentos")
        private String documents;

        @JsonProperty("usuario_id")
        private Integer UserId;

        @JsonProperty("unidad_negocio_id")
        private Integer BusinessUnitId;
    }
    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ValidateCargaBlInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private ValidateCargaBlInput.Prefix prefix;
    }
}
