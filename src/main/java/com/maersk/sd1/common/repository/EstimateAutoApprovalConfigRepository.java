package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EstimateAutoApprovalConfig;
import com.maersk.sd1.sde.dto.EstimateAutoApprovalConfigGetOutput;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

import java.math.BigDecimal;
import java.util.Optional;

public interface EstimateAutoApprovalConfigRepository extends JpaRepository<EstimateAutoApprovalConfig, Integer> {


    @Modifying
    @Query("UPDATE EstimateAutoApprovalConfig e " +
            " SET e.active = :active, e.modificationUser = (SELECT u FROM User u WHERE u.id = :userId), " +
            " e.modificationDate = :modificationDate " +
            " WHERE e.id IN :ids")
    int updateConfigActiveAndModification(
            @Param("ids") List<Integer> ids,
            @Param("active") Boolean active,
            @Param("userId") Integer userId,
            @Param("modificationDate") LocalDateTime modificationDate
    );

    List<EstimateAutoApprovalConfig> findByLocalSubBusinessUnitIdAndStructureEstimateFlagAndMachineryEstimateFlagAndCatEquipmentCategoryIdAndCatEquipmentTypeIdAndShippingLineIdAndIdNot(
            Integer localSubBusinessUnitId,
            Boolean structureEstimateFlag,
            Boolean machineryEstimateFlag,
            Integer catEquipmentCategoryId,
            Integer catEquipmentTypeId,
            Integer shippingLineId,
            Integer excludedId
    );

    List<EstimateAutoApprovalConfig> findByLocalSubBusinessUnitIdAndStructureEstimateFlagAndMachineryEstimateFlagAndCatEquipmentCategoryIdAndCatEquipmentTypeIdAndShippingLineIdAndIdNotOrderById(
            Integer localSubBusinessUnitId,
            Boolean structureEstimateFlag,
            Boolean machineryEstimateFlag,
            Integer catEquipmentCategoryId,
            Integer catEquipmentTypeId,
            Integer shippingLineId,
            Integer excludedId
    );

    List<EstimateAutoApprovalConfig> findByLocalSubBusinessUnitIdAndStructureEstimateFlagAndCatEquipmentCategoryIdAndCatEquipmentTypeIdAndCustomerIdAndIdNot(
            Integer localSubBusinessUnitId,
            Boolean structureEstimateFlag,
            Integer catEquipmentCategoryId,
            Integer catEquipmentTypeId,
            Integer customerId,
            Integer excludedId
    );

    @Query("SELECT new com.maersk.sd1.sde.dto.EstimateAutoApprovalConfigGetOutput( "
            + " e.id, "
            + " e.approvalId, "
            + " e.localSubBusinessUnit.id, "
            + " e.structureEstimateFlag, "
            + " e.machineryEstimateFlag, "
            + " e.catEquipmentCategory.id, "
            + " e.catEquipmentType.id, "
            + " e.thresholdValue, "
            + " e.active, "
            + " sl.id, "
            + " sl.name, "
            + " cr.id, "
            + " comp.id, "
            + " comp.legalName ) "
            + "FROM EstimateAutoApprovalConfig e "
            + "LEFT JOIN e.shippingLine sl "
            + "LEFT JOIN e.customer comp "
            + "LEFT JOIN e.currency cr "
            + "WHERE e.id = :autoApprovalId")
    Optional<EstimateAutoApprovalConfigGetOutput> findOutputById(@Param("autoApprovalId") Integer autoApprovalId);

    @Query("SELECT e FROM EstimateAutoApprovalConfig e " +
            "WHERE e.localSubBusinessUnit.id = :subBusinessLocalId " +
            "AND (:catEstimateTypeId IS NULL OR ( :catEstimateTypeId = :isStructure AND e.structureEstimateFlag = true ) " +
            "OR ( :catEstimateTypeId = :isMachinery AND e.machineryEstimateFlag = true )) " +
            "AND (:estimateAutoApprovalConfigId IS NULL OR e.id = :estimateAutoApprovalConfigId) " +
            "AND (:catEquipmentCategoryId IS NULL OR e.catEquipmentCategory.id = :catEquipmentCategoryId) " +
            "AND (:catEquipmentType IS NULL OR (e.catEquipmentType is not null and e.catEquipmentType.id = :catEquipmentType)) " +
            "AND (:active IS NULL OR e.active = :active) " +
            "AND (:thresholdValue IS NULL OR e.thresholdValue = :thresholdValue)")
    Page<EstimateAutoApprovalConfig> findAllByFilters(
            @Param("subBusinessLocalId") Long subBusinessLocalId,
            @Param("estimateAutoApprovalConfigId") Integer estimateAutoApprovalConfigId,
            @Param("catEquipmentCategoryId") Integer catEquipmentCategoryId,
            @Param("catEquipmentType") Integer catEquipmentType,
            @Param("active") Boolean active,
            @Param("thresholdValue") BigDecimal thresholdValue,
            @Param("catEstimateTypeId") Long catEstimateTypeId,
            @Param("isStructure") Long isStructure,
            @Param("isMachinery") Long isMachinery,
            Pageable pageable
    );

    @Query("""
    SELECT e.thresholdValue
    FROM EstimateAutoApprovalConfig e
    WHERE e.localSubBusinessUnit.id = :subBusinessUnitId
    AND e.catEquipmentType.id = :equipmentTypeId
    AND e.shippingLine.id = :shippingLineId
    AND e.active = true
    ORDER BY e.id ASC
""")
    Optional<BigDecimal> findAutoApprovalAmount(@Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                @Param("equipmentTypeId") Integer equipmentTypeId,
                                                @Param("shippingLineId") Integer shippingLineId);
}