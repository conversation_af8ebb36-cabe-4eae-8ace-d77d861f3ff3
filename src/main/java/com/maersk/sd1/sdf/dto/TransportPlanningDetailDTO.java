package com.maersk.sd1.sdf.dto;

import lombok.Data;
import lombok.Value;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public interface TransportPlanningDetailDTO {

    Integer getTransportPlanningDetailId();        // Integer
    String getContainer();                         // String
    Integer getTruckerCompanyId();                // Int
    String getTruckerCompany();                   // String
    LocalDateTime getTransportDate();             // LocalDateTime
    Integer getIsoCodeId();                       // Int
    String getIsoCode();                          // String
    BigDecimal getManifestWeight();               // BigDecimal
    Integer getCatUnitMeasureWeightId();          // Int
    String getCatUnitMeasureWeight();             // String
    String getManifestSeal1();                    // String
    String getManifestSeal2();                    // String
    String getManifestSeal3();                    // String
    String getManifestSeal4();                    // String
    String getCatStateTrkPlanningAlias();         // String
    String getImoIds();                     // List<String>
}

