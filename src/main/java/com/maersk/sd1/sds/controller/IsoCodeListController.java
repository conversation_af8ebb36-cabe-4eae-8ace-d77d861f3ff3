package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.IsoCodeListInputDTO;
import com.maersk.sd1.sds.dto.IsoCodeListOutputDTO;
import com.maersk.sd1.sds.service.IsoCodeListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSCodigoIsoServiceImp")
public class IsoCodeListController {

    private final IsoCodeListService isoCodeListService;

    @Autowired
    public IsoCodeListController(IsoCodeListService isoCodeListService) {
        this.isoCodeListService = isoCodeListService;
    }

    @PostMapping("/sdscodigoIsoListar")
    public ResponseEntity<ResponseController<IsoCodeListOutputDTO>> isoCodeListService(@RequestBody IsoCodeListInputDTO.Root input) {
        return isoCodeListService.isoCodeListService(input);
    }
}
