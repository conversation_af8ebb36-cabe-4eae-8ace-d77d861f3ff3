package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class NotificationJobEmailBrandId implements Serializable {
    private static final long serialVersionUID = 8596165850975103373L;
    @NotNull
    @Column(name = "notificacion_job_id", nullable = false)
    private Integer notificationJobId;

    @NotNull
    @Column(name = "id", nullable = false)
    private Integer id;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        NotificationJobEmailBrandId entity = (NotificationJobEmailBrandId) o;
        return Objects.equals(this.notificationJobId, entity.notificationJobId) &&
                Objects.equals(this.id, entity.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(notificationJobId, id);
    }

}