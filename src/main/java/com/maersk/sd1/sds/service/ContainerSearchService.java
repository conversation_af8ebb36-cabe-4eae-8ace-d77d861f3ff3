package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sds.controller.dto.ContainerSearchInput;
import com.maersk.sd1.sds.controller.dto.ContainerSearchOutput;
import com.maersk.sd1.common.repository.ContainerRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class ContainerSearchService {

    private static final Logger logger = LogManager.getLogger(ContainerSearchService.class);

    private final ContainerRepository containerRepository;

    public ContainerSearchService(ContainerRepository containerRepository) {
        this.containerRepository = containerRepository;
    }

    @Transactional(readOnly = true)
    public ContainerSearchOutput searchContainers(ContainerSearchInput.Input input) {
        ContainerSearchOutput output = new ContainerSearchOutput();
        try {
            int page = (input.getPage() == null) ? 1 : input.getPage();
            int size = (input.getSize() == null) ? 10 : input.getSize();

            PageRequest pageRequest = PageRequest.of(page - 1, size);

            Page<Container> pageResult = containerRepository.searchContainers(
                    input.getContainerId(),
                    input.getContainerNumber(),
                    input.getCatSizeId(),
                    input.getCatContainerTypeId(),
                    input.getShippingLineName(),
                    input.getActive(),
                    pageRequest
            );

            long totalElements = pageResult.getTotalElements();
            output.setTotalRecords(totalElements);

            List<ContainerSearchOutput.ContainerData> dataList = new ArrayList<>();

            for (Container c : pageResult.getContent()) {
                ContainerSearchOutput.ContainerData data = new ContainerSearchOutput.ContainerData();
                data.setContainerId(c.getId());
                data.setContainerNumber(c.getContainerNumber());
                data.setCatFamilyDescription((c.getCatFamily() != null) ? c.getCatFamily().getDescription() : null);
                data.setCatSizeDescription((c.getCatSize() != null) ? c.getCatSize().getDescription() : null);
                data.setCatContainerTypeDescription((c.getCatContainerType() != null) ? c.getCatContainerType().getDescription() : null);
                data.setShippingLineName((c.getShippingLine() != null) ? c.getShippingLine().getName() : null);
                data.setTara(c.getContainerTare());
                data.setCargaMaxima(c.getMaximunPayload());
                data.setIsoCode((c.getIsoCode() != null) ? c.getIsoCode().getIsoCode() : null);
                data.setCatGradeDescription((c.getCatGrade() != null) ? c.getCatGrade().getDescription() : null);
                data.setCatReeferTypeDescription((c.getCatReeferType() != null) ? c.getCatReeferType().getDescription() : null);
                data.setCatEngineBrandDescription((c.getCatEngineBrand() != null) ? c.getCatEngineBrand().getLongDescription() : null);

                data.setManufactureDate(c.getManufactureDate());

                data.setShipperOwn(c.getShipperOwn());
                data.setActive(c.getActive());
                data.setForSale(c.getForSale());

                data.setRegistrationDate(c.getRegistrationDate());
                data.setModificationDate(c.getModificationDate());

                User regUser = c.getRegistrationUser();
                if (regUser != null) {
                    data.setUserRegistrationId(regUser.getId());
                    data.setUserRegistrationNames(regUser.getNames());
                    String regLastNames = (regUser.getFirstLastName() == null ? "" : regUser.getFirstLastName()) +
                            (regUser.getSecondLastName() == null ? "" : (" " + regUser.getSecondLastName()));
                    data.setUserRegistrationLastNames(regLastNames);
                }

                User modUser = c.getModificationUser();
                if (modUser != null) {
                    data.setUserModificationId(modUser.getId());
                    data.setUserModificationNames(modUser.getNames());
                    String modLastNames = (modUser.getFirstLastName() == null ? "" : modUser.getFirstLastName()) +
                            (modUser.getSecondLastName() == null ? "" : (" " + modUser.getSecondLastName()));
                    data.setUserModificationLastNames(modLastNames);
                }

                dataList.add(data);
            }

            output.setContainerList(dataList);
        } catch (Exception e) {
            logger.error("Error in searchContainers: ", e);
        }
        return output;
    }
}