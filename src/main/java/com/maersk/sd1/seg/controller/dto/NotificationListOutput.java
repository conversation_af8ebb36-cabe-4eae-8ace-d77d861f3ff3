package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Builder
@Data
public class NotificationListOutput<T> {

    private List<T> result;

    public List<T> toResultArray() {
        return result;
    }

    @Builder
    @Data
    public static class NotificationItem {

        @JsonProperty("notificacion_id")
        private Integer notificationId;

        @JsonProperty("titulo")
        private String title;

        @JsonProperty("subtitulo")
        private String subtitle;

        @JsonProperty("icono")
        private String icon;

        @JsonProperty("nivel")
        private String level;

        @JsonProperty("unica_vez")
        private String onlyOnce;

        @JsonProperty("unica_alerta")
        private String onlyAlert;

        @JsonProperty("fecha_caducidad")
        private LocalDateTime expirationDate;

        @JsonProperty("estado")
        private String status;

        @JsonProperty("business_unit_id")
        private Integer businessUnitId;

        @JsonProperty("roles")
        private String roles;
    }
}
