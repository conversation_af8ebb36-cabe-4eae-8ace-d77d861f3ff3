package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.BusinessPersonalListInput;
import com.maersk.sd1.ges.dto.BusinessPersonalListOutput;
import com.maersk.sd1.ges.service.BusinessPersonalListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMPersonaServiceImp")
public class BusinessPersonalListController {

    private static final Logger logger = LogManager.getLogger(BusinessPersonalListController.class);

    private final BusinessPersonalListService empresaPersonaListarService;

    @Autowired
    public BusinessPersonalListController(BusinessPersonalListService empresaPersonaListarService) {
        this.empresaPersonaListarService = empresaPersonaListarService;
    }

    @PostMapping("/gesempresaPersonaListar")
    public ResponseEntity<ResponseController<BusinessPersonalListOutput>> listarEmpresaPersona(@RequestBody @Valid BusinessPersonalListInput.Root request) {
        try {
            BusinessPersonalListInput.Input input = request.getPrefix().getInput();
            BusinessPersonalListOutput output = empresaPersonaListarService.listarEmpresaPersona(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while listing Company-Person records.", e);
            BusinessPersonalListOutput output = new BusinessPersonalListOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}