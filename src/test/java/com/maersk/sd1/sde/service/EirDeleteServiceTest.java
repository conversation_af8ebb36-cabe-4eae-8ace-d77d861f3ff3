package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sde.controller.dto.EirDeleteInput;
import com.maersk.sd1.sde.controller.dto.EirDeleteOutput;
import com.maersk.sd1.sdg.dto.ResponseEirDeleteBeforeYard;
import com.maersk.sd1.sdg.service.EirDeleteBeforeYardService;
import com.maersk.sd1.sds.dto.TbChaEstimateDTO;
import com.maersk.sd1.sds.dto.TbCntEstimateDTO;
import com.maersk.sd1.sds.repository.SdsEirRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class EirDeleteServiceTest {

    @Mock
    private EirDeleteBeforeYardService eirDeleteBeforeYardService;
    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private ContainerRepository containerRepository;
    @Mock
    private SdsEirRepository eirRepository;
    @Mock
    private MessageLanguageService messageService;
    @Mock
    private EstimateEmrRepository estimateEmrRepository;
    @Mock
    private EirChassisRepository eirChassisRepository;
    @Mock
    private ChassisEstimateRepository chassisEstimateRepository;
    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;
    @Mock
    private UserRepository userRepository;
    @Mock
    private VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    @Mock
    private ContainerPreassignmentRepository containerPreassignmentRepository;
    @Mock
    private BookingDetailRepository bookingDetailRepository;
    @Mock
    private TransportPlanningDetailRepository transportPlanningDetailRepository;
    @Mock
    private EstimateEmrDetailRepository estimateEmrDetailRepository;
    @Mock
    private ChassisDocumentDetailRepository chassisDocumentDetailRepository;
    @Mock
    private ChassisBookingDocumentRepository chassisBookingDocumentRepository;
    @Mock
    private ChassisEstimateDetailRepository chassisEstimateDetailRepository;
    @Mock
    private ActivityLogRepository activityLogRepository;

    @InjectMocks
    private EirDeleteService eirDeleteService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }


    @Test
    void eirDeleteProcess_invalidInput_throwsException() throws Exception {
        EirDeleteInput.Input input = new EirDeleteInput.Input();
        input.setEirId(1);
        input.setIdiomaId(1);
        input.setUsuarioModificacionId(1);

        ResponseEirDeleteBeforeYard response = new ResponseEirDeleteBeforeYard();
        response.setResponseResult(0);
        when(eirDeleteBeforeYardService.eirDeleteBeforeYard(any())).thenReturn(response);

        assertThrows(RuntimeException.class, () -> eirDeleteService.eirDeleteProcess(input));
    }

    @Test
    void eirDeleteProcess_requiresPlanApprove_executesSuccessfully() throws Exception {
        EirDeleteInput.Input input = new EirDeleteInput.Input();
        input.setEirId(1);
        input.setIdiomaId(1);
        input.setUsuarioModificacionId(1);

        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS)).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).thenReturn(2);

        ResponseEirDeleteBeforeYard response = new ResponseEirDeleteBeforeYard();
        response.setResponseResult(1);
        response.setResponseFlagRequirePlanApprove('1');
        response.setSubBusinessUnitLocalId(1);
        response.setResponseGateinEirId(1);
        when(eirDeleteBeforeYardService.eirDeleteBeforeYard(any())).thenReturn(response);

        EirDeleteOutput eirDeleteOutput = new EirDeleteOutput();
        eirDeleteOutput.setRespStatus(1);
        eirDeleteOutput.setRespMessage("");
        when(containerRepository.findById(any())).thenReturn(Optional.of(new Container()));
        when(containerRepository.findByContainerNumber(any())).thenReturn(new Container());

        Eir mockEir = new Eir();
        mockEir.setId(1);
        Catalog mockCatalog = new Catalog();
        mockCatalog.setId(2);
        mockEir.setCatMovement(mockCatalog);
        mockEir.setCatEmptyFull(mockCatalog);
        Container container = new Container();
        container.setId(1);
        mockEir.setContainer(container);
        mockEir.setBusinessUnit(new BusinessUnit());
        mockEir.setSubBusinessUnit(new BusinessUnit());
        mockEir.setVesselProgrammingDetail(new VesselProgrammingDetail());
        mockEir.setActive(true);
        TransportPlanningDetail transportPlanningDetail = new TransportPlanningDetail();
        transportPlanningDetail.setId(1);
        mockEir.setTransportPlanningDetailFull(transportPlanningDetail);
        when(eirRepository.findById(any())).thenReturn(Optional.of(mockEir));

        when(eirRepository.findTop1ByContainerIdAndSubBusinessUnitId(any(), any())).thenReturn(List.of(mockEir));


        EirDocumentCargoDetail eirDocumentCargoDetail = new EirDocumentCargoDetail();
        ContainerPreassignment containerPreassignment = new ContainerPreassignment();
        containerPreassignment.setId(1);
        containerPreassignment.setCatOriginPreassignment(mockCatalog);
        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        cargoDocumentDetail.setId(1);
        BookingDetail bookingDetail = new BookingDetail();
        cargoDocumentDetail.setBookingDetail(bookingDetail);
        eirDocumentCargoDetail.setCargoDocumentDetail(cargoDocumentDetail);

        List<Object[]> list = new ArrayList<>();
        list.add(new Object[]{eirDocumentCargoDetail, containerPreassignment});

        when(eirDocumentCargoDetailRepository.findByEirIdJoinDocumentCargoDetail(any())).thenReturn(list);


        when(eirDocumentCargoDetailRepository.findTop1ByEirId(any())).thenReturn(Optional.of(eirDocumentCargoDetail));
        when(cargoDocumentDetailRepository.findById(any())).thenReturn(Optional.of(cargoDocumentDetail));
        when(transportPlanningDetailRepository.findById(any())).thenReturn(Optional.of(transportPlanningDetail));
        EirDeleteOutput result = eirDeleteService.eirDelete(input);

        assertEquals(1, result.getRespStatus());
    }

    @Test
    void eirDeleteProcess_requiresPlanApprove_executesSuccessfully_gate_in() throws Exception {
        EirDeleteInput.Input input = new EirDeleteInput.Input();
        input.setEirId(1);
        input.setIdiomaId(1);
        input.setUsuarioModificacionId(1);

        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).thenReturn(2);

        ResponseEirDeleteBeforeYard response = new ResponseEirDeleteBeforeYard();
        response.setResponseResult(1);
        response.setResponseFlagRequirePlanApprove('1');
        response.setSubBusinessUnitLocalId(1);
        response.setResponseGateinEirId(1);
        when(eirDeleteBeforeYardService.eirDeleteBeforeYard(any())).thenReturn(response);

        EirDeleteOutput eirDeleteOutput = new EirDeleteOutput();
        eirDeleteOutput.setRespStatus(1);
        eirDeleteOutput.setRespMessage("");
        when(containerRepository.findById(any())).thenReturn(Optional.of(new Container()));
        when(containerRepository.findByContainerNumber(any())).thenReturn(new Container());

        Eir mockEir = new Eir();
        mockEir.setId(1);
        Catalog mockCatalog = new Catalog();
        mockCatalog.setId(2);
        mockEir.setCatMovement(mockCatalog);
        mockEir.setCatEmptyFull(mockCatalog);
        Container container = new Container();
        container.setId(1);
        mockEir.setContainer(container);
        mockEir.setBusinessUnit(new BusinessUnit());
        mockEir.setSubBusinessUnit(new BusinessUnit());
        mockEir.setVesselProgrammingDetail(new VesselProgrammingDetail());
        mockEir.setActive(true);
        TransportPlanningDetail transportPlanningDetail = new TransportPlanningDetail();
        transportPlanningDetail.setId(1);
        mockEir.setTransportPlanningDetailFull(transportPlanningDetail);
        when(eirRepository.findById(any())).thenReturn(Optional.of(mockEir));

        when(eirRepository.findTop1ByContainerIdAndSubBusinessUnitId(any(), any())).thenReturn(List.of(mockEir));


        EirDocumentCargoDetail eirDocumentCargoDetail = new EirDocumentCargoDetail();
        ContainerPreassignment containerPreassignment = new ContainerPreassignment();
        containerPreassignment.setId(1);
        containerPreassignment.setCatOriginPreassignment(mockCatalog);
        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        cargoDocumentDetail.setId(1);
        BookingDetail bookingDetail = new BookingDetail();
        cargoDocumentDetail.setBookingDetail(bookingDetail);
        eirDocumentCargoDetail.setCargoDocumentDetail(cargoDocumentDetail);

        List<Object[]> list = new ArrayList<>();
        list.add(new Object[]{eirDocumentCargoDetail, containerPreassignment});

        when(eirDocumentCargoDetailRepository.findByEirIdJoinDocumentCargoDetail(any())).thenReturn(list);


        when(eirDocumentCargoDetailRepository.findTop1ByEirId(any())).thenReturn(Optional.of(eirDocumentCargoDetail));
        when(cargoDocumentDetailRepository.findById(any())).thenReturn(Optional.of(cargoDocumentDetail));
        when(transportPlanningDetailRepository.findById(any())).thenReturn(Optional.of(transportPlanningDetail));

        List<TbCntEstimateDTO> tbCntEstimateDTOS = new ArrayList<>();
        TbCntEstimateDTO tbCntEstimateDTO = new TbCntEstimateDTO();
        tbCntEstimateDTO.setEstimateId(1);
        tbCntEstimateDTO.setEstadoEstimado("1");
        tbCntEstimateDTO.setTipoEstimado("1");
        tbCntEstimateDTOS.add(tbCntEstimateDTO);
        when(estimateEmrRepository.findEstimatesByEirStatusIn(any(), any(), any(), any())).thenReturn(tbCntEstimateDTOS);

        EstimateEmrDetail estimateEmrDetail = new EstimateEmrDetail();
        when(estimateEmrDetailRepository.findByEstimateIds(any())).thenReturn(List.of(estimateEmrDetail));

        EstimateEmr estimateEmr = new EstimateEmr();
        when(estimateEmrRepository.findByIds(any())).thenReturn(List.of(estimateEmr));

        EirDeleteOutput result = eirDeleteService.eirDelete(input);

        assertEquals(1, result.getRespStatus());
    }


    @Test
    void eirDeleteProcess_requiresPlanApprove_executesSuccessfully_gate_in_with_chassis() throws Exception {
        EirDeleteInput.Input input = new EirDeleteInput.Input();
        input.setEirId(1);
        input.setIdiomaId(1);
        input.setUsuarioModificacionId(1);

        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).thenReturn(2);

        ResponseEirDeleteBeforeYard response = new ResponseEirDeleteBeforeYard();
        response.setResponseResult(1);
        response.setResponseFlagRequirePlanApprove('1');
        response.setSubBusinessUnitLocalId(1);
        response.setResponseGateinEirId(1);
        when(eirDeleteBeforeYardService.eirDeleteBeforeYard(any())).thenReturn(response);

        EirDeleteOutput eirDeleteOutput = new EirDeleteOutput();
        eirDeleteOutput.setRespStatus(1);
        eirDeleteOutput.setRespMessage("");
        when(containerRepository.findById(any())).thenReturn(Optional.of(new Container()));
        when(containerRepository.findByContainerNumber(any())).thenReturn(new Container());

        Eir mockEir = new Eir();
        mockEir.setId(1);
        Catalog mockCatalog = new Catalog();
        mockCatalog.setId(2);
        mockEir.setCatMovement(mockCatalog);
        mockEir.setCatEmptyFull(mockCatalog);
        Container container = new Container();
        container.setId(1);
        mockEir.setContainer(container);
        mockEir.setBusinessUnit(new BusinessUnit());
        mockEir.setSubBusinessUnit(new BusinessUnit());
        mockEir.setVesselProgrammingDetail(new VesselProgrammingDetail());
        mockEir.setActive(true);
        EirChassis eirChassis = new EirChassis();
        eirChassis.setId(1);
        Chassis chassis = new Chassis();
        chassis.setId(1);
        eirChassis.setChassis(chassis);

        mockEir.setEirChassis(eirChassis);
        TransportPlanningDetail transportPlanningDetail = new TransportPlanningDetail();
        transportPlanningDetail.setId(1);
        mockEir.setTransportPlanningDetailFull(transportPlanningDetail);
        when(eirRepository.findById(any())).thenReturn(Optional.of(mockEir));

        when(eirRepository.findTop1ByContainerIdAndSubBusinessUnitId(any(), any())).thenReturn(List.of(mockEir));


        EirDocumentCargoDetail eirDocumentCargoDetail = new EirDocumentCargoDetail();
        ContainerPreassignment containerPreassignment = new ContainerPreassignment();
        containerPreassignment.setId(1);
        containerPreassignment.setCatOriginPreassignment(mockCatalog);
        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        cargoDocumentDetail.setId(1);
        BookingDetail bookingDetail = new BookingDetail();
        cargoDocumentDetail.setBookingDetail(bookingDetail);
        eirDocumentCargoDetail.setCargoDocumentDetail(cargoDocumentDetail);

        List<Object[]> list = new ArrayList<>();
        list.add(new Object[]{eirDocumentCargoDetail, containerPreassignment});

        when(eirDocumentCargoDetailRepository.findByEirIdJoinDocumentCargoDetail(any())).thenReturn(list);


        when(eirDocumentCargoDetailRepository.findTop1ByEirId(any())).thenReturn(Optional.of(eirDocumentCargoDetail));
        when(cargoDocumentDetailRepository.findById(any())).thenReturn(Optional.of(cargoDocumentDetail));
        when(transportPlanningDetailRepository.findById(any())).thenReturn(Optional.of(transportPlanningDetail));

        List<TbCntEstimateDTO> tbCntEstimateDTOS = new ArrayList<>();
        TbCntEstimateDTO tbCntEstimateDTO = new TbCntEstimateDTO();
        tbCntEstimateDTO.setEstimateId(1);
        tbCntEstimateDTO.setEstadoEstimado("1");
        tbCntEstimateDTO.setTipoEstimado("1");
        tbCntEstimateDTOS.add(tbCntEstimateDTO);
        when(estimateEmrRepository.findEstimatesByEirStatusIn(any(), any(), any(), any())).thenReturn(tbCntEstimateDTOS);

        EstimateEmrDetail estimateEmrDetail = new EstimateEmrDetail();
        when(estimateEmrDetailRepository.findByEstimateIds(any())).thenReturn(List.of(estimateEmrDetail));

        EstimateEmr estimateEmr = new EstimateEmr();
        when(estimateEmrRepository.findByIds(any())).thenReturn(List.of(estimateEmr));


        EirChassis eirChassis1 = new EirChassis();
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(3);
        eirChassis1.setChassisDocumentDetail(chassisDocumentDetail);
        eirChassis1.setChassis(chassis);
        when(eirChassisRepository.findById(any())).thenReturn(Optional.of(eirChassis1));



        when(chassisDocumentDetailRepository.findById(any())).thenReturn(Optional.of(chassisDocumentDetail));

        TbChaEstimateDTO tbChaEstimateDTO = new TbChaEstimateDTO();
        tbChaEstimateDTO.setEstimateId(1);
        tbChaEstimateDTO.setStatusEstimate("");
        when(chassisEstimateRepository.findEstimatesByEirStatusIn(any(),any(),any(),any())).thenReturn(List.of(tbChaEstimateDTO));

        ChassisEstimateDetail chassisEstimateDetail = new ChassisEstimateDetail();
        when(chassisEstimateDetailRepository.findByEstimateIds(any())).thenReturn(List.of(chassisEstimateDetail));

        ChassisEstimate chassisEstimate = new ChassisEstimate();
        when(chassisEstimateRepository.findByIds(any())).thenReturn(List.of(chassisEstimate));


        ActivityLog activityLog = new ActivityLog();
        when(activityLogRepository.findByEir(any())).thenReturn(Optional.of(activityLog));

        EirDeleteOutput result = eirDeleteService.eirDelete(input);

        assertEquals(1, result.getRespStatus());
    }

    @Test
    void eirDeleteProcess_requiresPlanApprove_executesSuccessfully_gate_out_with_chassis() throws Exception {
        EirDeleteInput.Input input = new EirDeleteInput.Input();
        input.setEirId(1);
        input.setIdiomaId(1);
        input.setUsuarioModificacionId(1);

        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS)).thenReturn(2);
        when(catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)).thenReturn(2);

        ResponseEirDeleteBeforeYard response = new ResponseEirDeleteBeforeYard();
        response.setResponseResult(1);
        response.setResponseFlagRequirePlanApprove('1');
        response.setSubBusinessUnitLocalId(1);
        response.setResponseGateinEirId(1);
        when(eirDeleteBeforeYardService.eirDeleteBeforeYard(any())).thenReturn(response);

        EirDeleteOutput eirDeleteOutput = new EirDeleteOutput();
        eirDeleteOutput.setRespStatus(1);
        eirDeleteOutput.setRespMessage("");
        when(containerRepository.findById(any())).thenReturn(Optional.of(new Container()));
        when(containerRepository.findByContainerNumber(any())).thenReturn(new Container());

        Eir mockEir = new Eir();
        mockEir.setId(1);
        Catalog mockCatalog = new Catalog();
        mockCatalog.setId(2);
        mockEir.setCatMovement(mockCatalog);
        mockEir.setCatEmptyFull(mockCatalog);
        Container container = new Container();
        container.setId(1);
        mockEir.setContainer(container);
        mockEir.setBusinessUnit(new BusinessUnit());
        mockEir.setSubBusinessUnit(new BusinessUnit());
        mockEir.setVesselProgrammingDetail(new VesselProgrammingDetail());
        mockEir.setActive(true);
        EirChassis eirChassis = new EirChassis();
        eirChassis.setId(1);
        Chassis chassis = new Chassis();
        chassis.setId(1);
        eirChassis.setChassis(chassis);

        mockEir.setEirChassis(eirChassis);
        TransportPlanningDetail transportPlanningDetail = new TransportPlanningDetail();
        transportPlanningDetail.setId(1);
        mockEir.setTransportPlanningDetailFull(transportPlanningDetail);
        when(eirRepository.findById(any())).thenReturn(Optional.of(mockEir));

        when(eirRepository.findTop1ByContainerIdAndSubBusinessUnitId(any(), any())).thenReturn(List.of(mockEir));


        EirDocumentCargoDetail eirDocumentCargoDetail = new EirDocumentCargoDetail();
        ContainerPreassignment containerPreassignment = new ContainerPreassignment();
        containerPreassignment.setId(1);
        containerPreassignment.setCatOriginPreassignment(mockCatalog);
        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        cargoDocumentDetail.setId(1);
        BookingDetail bookingDetail = new BookingDetail();
        cargoDocumentDetail.setBookingDetail(bookingDetail);
        eirDocumentCargoDetail.setCargoDocumentDetail(cargoDocumentDetail);

        List<Object[]> list = new ArrayList<>();
        list.add(new Object[]{eirDocumentCargoDetail, containerPreassignment});

        when(eirDocumentCargoDetailRepository.findByEirIdJoinDocumentCargoDetail(any())).thenReturn(list);


        when(eirDocumentCargoDetailRepository.findTop1ByEirId(any())).thenReturn(Optional.of(eirDocumentCargoDetail));
        when(cargoDocumentDetailRepository.findById(any())).thenReturn(Optional.of(cargoDocumentDetail));
        when(transportPlanningDetailRepository.findById(any())).thenReturn(Optional.of(transportPlanningDetail));

        List<TbCntEstimateDTO> tbCntEstimateDTOS = new ArrayList<>();
        TbCntEstimateDTO tbCntEstimateDTO = new TbCntEstimateDTO();
        tbCntEstimateDTO.setEstimateId(1);
        tbCntEstimateDTO.setEstadoEstimado("1");
        tbCntEstimateDTO.setTipoEstimado("1");
        tbCntEstimateDTOS.add(tbCntEstimateDTO);
        when(estimateEmrRepository.findEstimatesByEirStatusIn(any(), any(), any(), any())).thenReturn(tbCntEstimateDTOS);

        EstimateEmrDetail estimateEmrDetail = new EstimateEmrDetail();
        when(estimateEmrDetailRepository.findByEstimateIds(any())).thenReturn(List.of(estimateEmrDetail));

        EstimateEmr estimateEmr = new EstimateEmr();
        when(estimateEmrRepository.findByIds(any())).thenReturn(List.of(estimateEmr));


        EirChassis eirChassis1 = new EirChassis();
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(3);
        eirChassis1.setChassisDocumentDetail(chassisDocumentDetail);
        eirChassis1.setChassis(chassis);
        when(eirChassisRepository.findById(any())).thenReturn(Optional.of(eirChassis1));


        ChassisBookingDocument chassisBookingDocument = new ChassisBookingDocument();
        chassisBookingDocument.setId(4);
        chassisDocumentDetail.setChassisBookingDocument(chassisBookingDocument);
        when(chassisDocumentDetailRepository.findById(any())).thenReturn(Optional.of(chassisDocumentDetail));


        when(chassisBookingDocumentRepository.findByBookingChassisId(any())).thenReturn(Optional.of(chassisBookingDocument));

        VesselProgrammingContainer vesselProgrammingContainer = new VesselProgrammingContainer();
        when( vesselProgrammingContainerRepository.findByContainer_IdAndVesselProgrammingDetail_Id(any(),any())).thenReturn(vesselProgrammingContainer);
        EirDeleteOutput result = eirDeleteService.eirDelete(input);

        assertEquals(1, result.getRespStatus());
    }

    @Test
    void eirDeleteProcess_requiresPlanApprove_failsExecution() throws Exception {
        EirDeleteInput.Input input = new EirDeleteInput.Input();
        input.setEirId(1);
        input.setIdiomaId(1);
        input.setUsuarioModificacionId(1);

        ResponseEirDeleteBeforeYard response = new ResponseEirDeleteBeforeYard();
        response.setResponseResult(1);
        response.setResponseFlagRequirePlanApprove('1');
        response.setSubBusinessUnitLocalId(1);
        response.setResponseGateinEirId(1);
        when(eirDeleteBeforeYardService.eirDeleteBeforeYard(any())).thenReturn(response);

        EirDeleteOutput eirDeleteOutput = new EirDeleteOutput();
        eirDeleteOutput.setRespStatus(1);
        when(containerRepository.findById(any())).thenReturn(Optional.of(new Container()));
        when(containerRepository.findByContainerNumber(any())).thenReturn(new Container());

        EirDeleteOutput result = eirDeleteService.eirDeleteProcess(input);

        assertEquals(2, result.getRespStatus());
    }

    @Test
    void actualizarEstadoCitaJSON_validInput_returnsExpectedJson() {
        String citaId = "123";
        String currentStatus = "1";
        String newStatus = "2";
        String usuarioModificacionId = "456";

        String expectedJson = (new StringBuilder().append("{\n").append("  \"APS\": {\n").append("    \"F\": {\n").append("      \"cita_id\": \"123\",\n").append("      \"current_status\": \"1\",\n").append("      \"new_status\": \"2\",\n").append("      \"usuario_modificacion_id\": \"456\"\n").append("    }\n").append("  }\n").append("}").toString()).formatted();

        String result = eirDeleteService.actualizarEstadoCitaJSON(citaId, currentStatus, newStatus, usuarioModificacionId);

        assertEquals(expectedJson, result);
    }


    @Test
    void actualizarEstadoCitaJSON_emptyCitaId_returnsJsonWithEmptyCitaId() {
        String citaId = "";
        String currentStatus = "1";
        String newStatus = "2";
        String usuarioModificacionId = "456";

        String expectedJson = new StringBuilder().append("{\n").append("  \"APS\": {\n").append("    \"F\": {\n").append("      \"cita_id\": \"\",\n").append("      \"current_status\": \"1\",\n").append("      \"new_status\": \"2\",\n").append("      \"usuario_modificacion_id\": \"456\"\n").append("    }\n").append("  }\n").append("}").toString();

        String result = eirDeleteService.actualizarEstadoCitaJSON(citaId, currentStatus, newStatus, usuarioModificacionId);

        assertEquals(expectedJson, result);
    }
}
