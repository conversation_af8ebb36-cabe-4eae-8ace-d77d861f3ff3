package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Ubigeo;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "terminal", schema = "ges")
public class Terminal {
    @Id
    @Column(name = "terminal_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "ubigeo_departamento_id", nullable = false)
    private Ubigeo departmentUbigeo;

    @Size(max = 50)
    @NotNull
    @Column(name = "codigo_puerto", nullable = false, length = 50)
    private String portCode;

    @Size(max = 50)
    @NotNull
    @Column(name = "nombre_puerto", nullable = false, length = 50)
    private String portName;

    @NotNull
    @ColumnDefault("'1'")
    @Column(name = "estado", nullable = false)
    private Character status;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

}