package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "eir_chassis_zone_activity", schema = "sdh", indexes = {
        @Index(name = "chazoneactx_chassis_id", columnList = "eir_chassis_id"),
        @Index(name = "CHAZONEACTX_END_ACTIVITY_DATE", columnList = "end_date"),
        @Index(name = "CHAZONEACTX_PARTIAL_INSP_DATE", columnList = "started_inspection_date"),
        @Index(name = "CHAZONEACTX_EIRCHA_ID_ACTIVITY_ID", columnList = "eir_chassis_id, cat_chassis_zone_activity_id"),
        @Index(name = "CHAZONEACTX_ZONA_ID", columnList = "eir_chassis_zone_id")
})
public class EirChassisZoneActivity {

    @Id
    @Column(name = "eir_chassis_zone_activity_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "eir_chassis_id", nullable = false)
    private EirChassis eirChassis;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_chassis_zone_id")
    private EirChassisZone eirChassisZone;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_chassis_zone_activity_id", nullable = false)
    private Catalog catChassisZoneActivity;

    @NotNull
    @Column(name = "start_date", nullable = false)
    private LocalDateTime startDate;

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Column(name = "structure_chassis_damaged")
    private Boolean structureChassisDamaged;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_chassis_zone_activity_result_id")
    private Catalog catChassisZoneActivityResult;

    @NotNull
    @Column(name = "completed", nullable = false)
    private Boolean completed = false;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "start_user_id", nullable = false)
    private User startUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "end_user_id")
    private User endUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @Size(max = 20)
    @Column(name = "trace_chassis_zone_activity", length = 20)
    private String traceChassisZoneActivity;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_createsource_eircha_zoneact_id")
    private Catalog catCreateSourceEIRChassisZoneAct;

    @Column(name = "is_parcial_inspection")
    private Boolean isPartialInspection;

    @Size(max = 250)
    @Column(name = "inspector_comment", length = 250)
    private String inspectorComment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chassis_estimate_id")
    private ChassisEstimate chassisEstimate;

    @Column(name = "started_inspection_date")
    private LocalDateTime startedInspectionDate;

    @Column(name = "started_inspection_user")
    private Integer startedInspectionUser;

    @Size(max = 250)
    @Column(name = "inspection_observation_comment", length = 250)
    private String inspectionObservationComment;

}