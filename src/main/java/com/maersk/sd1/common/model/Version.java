package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.System;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "version", schema = "seg")
public class Version {
    @Id
    @Column(name = "version_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sistema_id", nullable = false)
    private System system;

    @Size(max = 30)
    @NotNull
    @Column(name = "version", nullable = false, length = 30)
    private String version;

    @NotNull
    @Lob
    @Column(name = "descripcion", nullable = false)
    private String description;

    @NotNull
    @Column(name = "fecha_inicio", nullable = false)
    private LocalDateTime beginningDate;

    @Column(name = "fecha_fin")
    private LocalDateTime endingDate;

    @Column(name = "fecha_despliegue")
    private LocalDateTime deploymentDate;

    @NotNull
    @ColumnDefault("'1'")
    @Column(name = "estado", nullable = false)
    private Character status;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @NotNull
    @ColumnDefault("'1'")
    @Column(name = "nivel", nullable = false)
    private Character level;

}