package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.*;

import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class PortMasterEntityPropertyPK implements Serializable {
    @Column(name = "CodigoActual", length = 10,insertable=false, updatable=false)
    private String code;

    @Column(name = "Pais", length = 10,insertable=false, updatable=false)
    private String country;
}
