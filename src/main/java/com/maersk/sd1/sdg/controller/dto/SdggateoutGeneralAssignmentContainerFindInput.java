package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class SdggateoutGeneralAssignmentContainerFindInput {

    @Data
    public static class Input {
        @JsonProperty("eir_id")
        private int eirId;

        @JsonProperty("container_number")
        private String containerNumber;

        @JsonProperty("list_container_date")
        private boolean listContainerDate;

        @JsonProperty("languaje_id")
        private int languageId;

        @JsonProperty("confirm_container")
        private int confirmContainer;

        @JsonProperty("list_container_date")
        public void setListContainerDate(String value) {
            this.listContainerDate = "1".equals(value);
        }
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix sdg;
    }

}