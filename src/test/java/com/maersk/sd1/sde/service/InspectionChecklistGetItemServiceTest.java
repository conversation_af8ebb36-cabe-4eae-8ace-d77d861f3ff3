package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.InspectionChecklist;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sde.controller.dto.InspectionChecklistGetItemOutput;
import com.maersk.sd1.common.repository.InspectionChecklistRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class InspectionChecklistGetItemServiceTest {

    @Mock
    private InspectionChecklistRepository inspectionChecklistRepository;

    @InjectMocks
    private InspectionChecklistGetItemService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_ValidId_When_GetItem_Then_ReturnsEntityOutput() {

        InspectionChecklist checklist = new InspectionChecklist();
        checklist.setId(100);

        BusinessUnit bu = new BusinessUnit();
        bu.setId(10);
        checklist.setSubBusinessUnit(bu);

        Catalog catalog = new Catalog();
        catalog.setId(20);
        checklist.setCatInspectionType(catalog);

        checklist.setOrderNumber(1);
        checklist.setDescription("Test Description");
        User regUser = new User();
        regUser.setId(777);
        checklist.setRegistrationUser(regUser);
        checklist.setRegistrationDate(LocalDateTime.now());
        checklist.setActive(true);

        when(inspectionChecklistRepository.findById(anyInt())).thenReturn(Optional.of(checklist));

        InspectionChecklistGetItemOutput output = service.getInspectionChecklistItem(100);
        assertNotNull(output);
    }

    @Test
    void given_ValidEntity_When_MapEntityToOutput_Then_ReturnsCorrectOutput() {
        InspectionChecklist checklist = new InspectionChecklist();
        checklist.setId(200);

        BusinessUnit bu = new BusinessUnit();
        bu.setId(15);
        checklist.setSubBusinessUnit(bu);

        Catalog catalog = new Catalog();
        catalog.setId(25);
        checklist.setCatInspectionType(catalog);

        checklist.setOrderNumber(2);
        checklist.setDescription("Another Test Description");
        User regUser = new User();
        regUser.setId(888);
        checklist.setRegistrationUser(regUser);
        checklist.setRegistrationDate(LocalDateTime.now());
        checklist.setActive(false);

        InspectionChecklistGetItemOutput output = service.mapEntityToOutput(checklist);

        assertNotNull(output);
        assertEquals(200, output.getInspectionChecklistId());
        assertEquals(15, output.getSubBusinessUnitId());
        assertEquals(25, output.getCatInspectionTypeId());
        assertEquals(2, output.getOrderNumber());
        assertEquals("Another Test Description", output.getDescription());
        assertEquals(888, output.getUserRegistrationId());
    }
}
