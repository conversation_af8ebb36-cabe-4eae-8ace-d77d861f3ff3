package com.maersk.sd1.sde.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

@Service
public class GatetransmissionService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    public void edi322UpdateDamageForActivity(Integer eirId, Integer eirZoneActivityId, Integer estimateEmrId, Integer userId) {
        jdbcTemplate.update("EXEC sde.edi322_update_damage_for_activity ?, ?, ?, ?",
                eirId, eirZoneActivityId, estimateEmrId, userId);
    }

}
