package com.maersk.sd1.ges.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class SftpConfigListOutput {

    @JsonProperty("total_registros")
    private long totalRegistros;

    @JsonProperty("registros")
    private List<SftpConfigData> registros;

    @Data
    public static class SftpConfigData {

        @JsonProperty("sftp_config_id")
        private Integer sftpConfigId;

        @JsonProperty("es_ftp")
        private Boolean isFtp;

        @JsonProperty("alias")
        private String alias;

        @JsonProperty("sftp_host")
        private String sftpHost;

        @JsonProperty("sftp_name")
        private String sftpName;

        @JsonProperty("sftp_pass")
        private String sftpPass;

        @JsonProperty("sftp_port")
        private String sftpPort;

        @JsonProperty("sftp_path")
        private String sftpPath;

        @JsonProperty("evento_despues_subir")
        private String eventAfterUpload;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("fecha_registro")
        private LocalDateTime registrationDate;

        @JsonProperty("fecha_modificacion")
        private LocalDateTime modificationDate;

        @JsonProperty("usuario_registro_id")
        private Integer userRegistrationId;

        @JsonProperty("usuario_registro_nombres")
        private String userRegistrationNames;

        @JsonProperty("usuario_registro_apellidos")
        private String userRegistrationLastNames;

        @JsonProperty("requiere_private_key")
        private Boolean requiresPrivateKey;
    }
}