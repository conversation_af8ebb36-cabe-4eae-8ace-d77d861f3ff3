package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.dto.RuleListFullOutput;
import com.maersk.sd1.common.repository.RuleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class RuleFullListService {

    private final RuleRepository ruleRepository;

    @Autowired
    public RuleFullListService(RuleRepository ruleRepository) {
        this.ruleRepository = ruleRepository;
    }

    /**
     * Fetch the list of rules based on the provided filters and pagination.
     *
     * @param ruleId              The ID of the rule.
     * @param systemId            The ID of the system.
     * @param parameters          Filter by parameters.
     * @param status              The status of the rule.
     * @param registrationDateMin The minimum registration date.
     * @param registrationDateMax The maximum registration date.
     * @param modificationDateMin The minimum modification date.
     * @param modificationDateMax The maximum modification date.
     * @param categoryRuleId      The category ID of the rule.
     * @param page                The page number for pagination.
     * @param size                The size of the page for pagination.
     * @return Output containing the list of rules and total count.
     */
    public RuleListFullOutput.Output getRuleList(
            Integer ruleId,
            Long systemId,
            String parameters,
            Character status,
            LocalDateTime registrationDateMin,
            LocalDateTime registrationDateMax,
            LocalDateTime modificationDateMin,
            LocalDateTime modificationDateMax,
            Long categoryRuleId,
            Integer page,
            Integer size
    ) {


        Integer totalCount = ruleRepository.countReglas(
                ruleId,
                systemId,
                parameters,
                status,
                registrationDateMin,
                registrationDateMax,
                modificationDateMin,
                modificationDateMax,
                categoryRuleId
        );

        List<Object[]> ruleData = ruleRepository.findReglas1(
                ruleId,
                systemId,
                parameters,
                status,
                registrationDateMin,
                registrationDateMax,
                modificationDateMin,
                modificationDateMax,
                categoryRuleId
        );

        List<RuleListFullOutput.ListResult> listResult = ruleData.stream()
                .map(row -> RuleListFullOutput.ListResult.builder()
                        .reglaId((Integer) row[0])  // regla_id
                        .parametros((String) row[1]) // parametros
                        .sistemaId(row[3] != null ? ((BigDecimal) row[3]).longValue() : null) // sistema_id
                        .estado(row[4] != null ? row[4].toString() : null) // estado
                        .fechaRegistro(row[5] != null ? row[5].toString() : null) // fecha_registro
                        .fechaModificacion(row[6] != null ? row[6].toString() : null) // fecha_modificacion
                        .catReglaId(row[7] != null ? ((BigDecimal) row[7]).longValue() : null) // cat_regla_id
                        .usuarioRegistroId(row[8] != null ? ((BigDecimal) row[8]).intValue() : null) // usuario_registro_id
                        .usuarioModificacionId(row[9] != null ? ((BigDecimal) row[9]).intValue() : null) // usuario_modificacion_id
                        .usuarioRegistroNombres((String) row[10]) // usuario_registro_nombres
                        .usuarioRegistroApellidos((String) row[11]) // usuario_registro_apellidos
                        .usuarioModificacionNombres((String) row[12]) // usuario_modificacion_nombres
                        .usuarioModificacionApellidos((String) row[13]) // usuario_modificacion_apellidos
                        .build())
                .toList();

        RuleListFullOutput.ListCount listCount = new RuleListFullOutput.ListCount(totalCount);

        return RuleListFullOutput.Output.builder()
                .listResult(listResult)
                .listCount(listCount)
                .build();
    }
}