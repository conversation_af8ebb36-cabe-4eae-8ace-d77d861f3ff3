package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EirReject;
import com.maersk.sd1.sdg.dto.TruckArrivalRejectProcessDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface EirRejectRepository extends JpaRepository<EirReject, Integer> {

    @Query("SELECT TER.id AS eirRejectNumber, " +
            "ISNULL(TER.containerNumber, '') AS containerNumber, " +
            "ISNULL(TER.containerBooking, '') AS containerBooking, " +
            "TCEF.longDescription AS emptyFull, " +
            "ISNULL(TER.chassisNumber, '') AS chassisNumber, " +
            "ISNULL(TER.chassisBooking, '') AS chassisBooking, " +
            "TCTR.description AS typeRejection, " +
            "sds.fn_CatalogoTraducidoDesLarga(TER.catTypeMovement.id, :#{#dto.pfIdiomaId}) AS typeMovement, " +
            "TVH.plate AS truckPlate, " +
            "CONCAT(TEM.document, ' - ', TEM.legalName) AS truckCompany, " +
            "CONCAT(TPE.driversLicense, ' - ', TPE.firstLastName, ' ', TPE.names) AS driver, " +
            "ISNULL(TER.comment, '') AS comment, " +
            "CONVERT(VARCHAR(10), TER.registrationDate, 111) AS registrationDate, " +
            "IIF(TER.catRejectOrigin IS NOT NULL, sds.fn_CatalogoTraducidoDes(TER.catRejectOrigin.id, :#{#dto.pfIdiomaId}), UPPER(CAST(sds.fn_CatalogoTraducidoDesLarga(TER.catTypeMovement.id, :#{#dto.pfIdiomaId}) AS string))) AS originReject, " +
            "TER.eir.id AS eirNumber " +
            "FROM EirReject TER " +
            "JOIN TER.companyTransport TEM " +
            "JOIN TER.personDriver TPE " +
            "INNER JOIN Truck TVH ON TVH.id = TER.truck.id " +
            "INNER JOIN Catalog TCEF ON TCEF.id = TER.catEmptyFull.id " +
            "INNER JOIN Catalog TCTR ON TCTR.id = TER.catTypeReject.id " +
            "WHERE TER.localSubBusinessUnit.id = :#{#dto.subUnidadNegocioId} " +
            "AND (:#{#dto.containerNumber} IS NULL OR TER.containerNumber LIKE %:#{#dto.containerNumber}%) " +
            "AND (:#{#dto.chassisNumber} IS NULL OR TER.chassisNumber LIKE %:#{#dto.chassisNumber}%) " +
            "AND (:#{#dto.catTypeMovementId} IS NULL OR TER.catTypeMovement.id = :#{#dto.catTypeMovementId}) " +
            "AND (:#{#dto.emptyFullProcessId} IS NULL OR TER.catEmptyFull.id = :#{#dto.emptyFullProcessId}) " +
            "AND (:#{#dto.truckingCompany} IS NULL OR TEM.legalName LIKE %:#{#dto.truckingCompany}%) " +
            "AND (:#{#dto.driverName} IS NULL OR CONCAT(TPE.firstLastName, ' ', COALESCE(TPE.secondLastName, ''), ' ', TPE.names) LIKE %:#{#dto.driverName}%) " +
            "AND ((:#{#dto.dateRegistrationDesde} IS NULL AND :#{#dto.dateRegistrationHasta} IS NULL) " +
            "    OR seg.fn_datetime_get(:#{#dto.subUnidadNegocioId}, TER.registrationDate) BETWEEN :#{#dto.dateRegistrationDesde} " +
            "    AND :#{#dto.dateRegistrationHasta}) " +
            "ORDER BY TER.id DESC")
    Page<Object[]> processData(
            @Param("dto") TruckArrivalRejectProcessDTO dto,
            Pageable pageable);
}