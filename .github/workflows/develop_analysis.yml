name: Develop Branch Analysis
on:
  push:
    branches:
      - Develop

jobs:
  sonar:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Important for Sonar to get full SCM history
      
      - name: Set up JDK 23
        uses: actions/setup-java@v3
        with:
          distribution: 'adopt'
          java-version: '23'
          
      - name: maven-settings-xml-action
        uses: whelk-io/maven-settings-xml-action@v20
        with:
          repositories: '[{ "id": "mvn-repo", "url": "https://maven.pkg.github.com/Maersk-Global/ohRest" }]'
          servers: '[{ "id": "mvn-repo", "username": "${{ secrets.GIT_USER }}", "password": "${{ secrets.GIT_PASSWORD }}" }]'
          output_file: maven/settings.xml

      - name: Create local Maven repository
        run: mkdir -p $HOME/.m2/repository

      - name: Copy Maven settings
        run: cp maven/settings.xml $HOME/.m2

      - name: Analyze Develop branch
        run: mvn clean verify --file pom.xml sonar:sonar
          -Dsonar.host.url=${{ secrets.MDN_SONARQUBE_HOST }}
          -Dsonar.login=${{ secrets.MDN_SONARQUBE_TOKEN }}
          -Dsonar.branch.name=Develop 