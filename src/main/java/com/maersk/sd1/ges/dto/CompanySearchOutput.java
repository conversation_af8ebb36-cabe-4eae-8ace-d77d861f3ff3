package com.maersk.sd1.ges.dto;

import java.util.ArrayList;
import java.util.List;

public class CompanySearchOutput {
    private String respMensaje;
    private int respEstado;
    private List<CompanySearchData> companies = new ArrayList<>();

    public String getRespMensaje() {
        return respMensaje;
    }

    public void setRespMensaje(String respMensaje) {
        this.respMensaje = respMensaje;
    }

    public int getRespEstado() {
        return respEstado;
    }

    public void setRespEstado(int respEstado) {
        this.respEstado = respEstado;
    }

    public List<CompanySearchData> getCompanies() {
        return companies;
    }

    public void setCompanies(List<CompanySearchData> companies) {
        this.companies = companies;
    }

    public static class CompanySearchData {
        private Integer id;
        private String document;
        private String companyInfo;

        public CompanySearchData(Integer id, String document, String companyInfo) {
            this.id = id;
            this.document = document;
            this.companyInfo = companyInfo;
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getDocument() {
            return document;
        }

        public void setDocument(String document) {
            this.document = document;
        }

        public String getCompanyInfo() {
            return companyInfo;
        }

        public void setCompanyInfo(String companyInfo) {
            this.companyInfo = companyInfo;
        }
    }
}