package com.maersk.sd1.sdg.service;


import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;



import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.sdg.controller.dto.ResponseTruckDepartureRegisterBeforeYard;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureRegisterBeforeYardInput;

import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.json.JSONArray;
import org.json.JSONObject;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureRegisterInput;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureRegisterOutput;
import com.maersk.sd1.sdg.dto.TbEirChassisDto;
import com.maersk.sd1.sdg.repository.*;


@Service
@NoArgsConstructor(force = true)
@RequiredArgsConstructor
public class TruckDepartureRegisterService {

    private static final String PRC_FULL_GI_GO = "PRC_FULL_GI_GO";
    private static final String EIRX_PLACEHOLDER = "{EIRX}";
    private static final String INS_CHASSIS = "INS_CHASSIS";
    private static final String CNTX_PLACEHOLDER = "{CNTX}";
    private static final String RSTX_PLACEHOLDER = "{RSTX}";
    private static final String BOOKING_NUMBER_PLACEHOLDER = "{BOOKING_NUMBER}";
    private static final String SALIDA_CAMION = "SALIDA_CAMION";
    private static final String TRUCKDEPA_RECH_CHGO1 = "truckdepa_rech_chgo1";
    private static final String TRUCKDEPA_RECH_CHGO2 = "truckdepa_rech_chgo2";
    private static final String TRACE_GRALTRUCKDEPA_RECH2 = "graltruckdepa_rech2";
    private static final String RESULT_KEY = "result";
    private static final String INTEGRATION_DATA = "integration_data";
    private static final String IS_CORRECT = "isCorrect";
    private static final String BAD_RESPONSE = "Bad Response";


    private final  EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    private final  ContainerRepository containerRepository;

    private final  CatalogRepository catalogRepository;

    private final  SdgEirRepository eirRepository;

    private final  EirMultipleRepository eirMultipleRepository;

    private final  BusinessUnitRepository businessUnitRepository;

    private final  SdgStockEmptyRepository sdgStockEmptyRepository;

    private final  SdgStockFullRepository sdgStockFullRepository;

    private final  MessageLanguageService messageLanguageService;

    private final  SdgEirChassisRepository eirChassisRepository;

    private final  SdgChassisRepository chassisRepository;

    private final  SdgStockChassisRepository stockChassisRepository;

    private final  UserRepository userRepository;

    private final  SystemRuleRepository systemRuleRepository;

    private final  ContainerRestrictionRepository containerRestrictionRepository;

    private final  FgisInspectionRepository fgisInspectionRepository;

    private final  EirDocumentCargoDetailRepository eirCargoDetailRepository;

    private final  AttachmentRepository attachmentRepository;

    private final  ContainerPreassignmentRepository containerPreassignmentRepository;

    private final  SdsEirPhotoRepository sdsEirPhotoRepository;

    private final  SdfEirPhotoRepository sdfEirPhotoRepository;

    private final  ChassisDocumentDetailRepository chassisDocumentDetailRepository;

    private final  ChassisBookingDocumentRepository chassisBookingDocumentRepository;

    private final  CargoDocumentDetailRepository cargoDocumentDetailRepository;

    private final  VesselProgrammingContainerRepository vesselProgrammingContainerRepository;

    private final  TransportPlanningDetailRepository transportPlanningDetailRepository;

    private final  CompanyRepository companyRepository;

    private final  EirSendAppeirRepository eirSendAppeirRepository;

    private final  EirNotificationRepository eirNotificationRepository;

    private final TruckDepartureRegisterBeforeYardService truckDepartureRegisterBeforeYardService;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${msk.api.apiUserLogin.sdy.loginUrl}")
    private String loginUrl;

    @Value("${msk.api.apiUserLogin.sdy.user}")
    private String user;

    @Value("${msk.api.apiUserLogin.sdy.password}")
    private String password;

    @Value("${msk.api.apiUserLogin.sdy.system}")
    private String system;

    @Value("${msk.api.apiUserLogin.sdy.gateInUrl}")
    private String gateInUrl;

    @Value("${msk.api.apiUserLogin.sdy.gateOutUrl}")
    private String gateOutUrl;

    @Value("${msk.api.apiUserLogin.sdy.searchContainer}")
    private String searchContainer;

    @Value("${msk.api.apiUserLogin.sdy.plannigGateInAssignmentAndAprrove}")
    private String plannigGateInAssignmentAndAprrove;

    @Value("${msk.api.apiUserLogin.sdy.afterTruckDepartureCreateWorkOrder}")
    private String afterTruckDepartureCreateWorkOrder;


    @Transactional
    public SdgTruckDepartureRegisterOutput register(SdgTruckDepartureRegisterInput.Root inputRoot) {


        SdgTruckDepartureRegisterOutput response = new SdgTruckDepartureRegisterOutput();
        if (inputRoot == null) {
            return response;
        }

        LocalDateTime truckOutDateZh = null;
        Integer isEirManual = catalogRepository.findIdByAlias(Parameter.IS_EIR_MANUAL);
        Integer isGateIn = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS);
        Integer isGateOut = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
        Integer isFullId = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS);
        Integer isEmpty = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);
        Integer isContainer = catalogRepository.findIdByAlias(Parameter.EQUIPMENT_CATEGORY_CONTAINER);
        Integer isChassis = catalogRepository.findIdByAlias(Parameter.EQUIPMENT_CATEGORY_CHASSIS);
        Integer isPlanningPending = catalogRepository.findIdByAlias(Parameter.IS_PLANNING_PENDING);
        Integer isPlanningDone = catalogRepository.findIdByAlias(Parameter.IS_PLANNING_DONE);
        Integer isMeasureWeightKg = catalogRepository.findIdByAlias(Parameter.CATALOG_MEASURE_WEIGHT_KG_ALIAS);
        Integer isDocChassisCompleted = catalogRepository.findIdByAlias(Parameter.IS_DOC_CHASSIS_COMPLETED);
        Integer isDocChassisPending = catalogRepository.findIdByAlias(Parameter.CATALOG_CHASSIS_DOCUMENT_PENDING);
        Integer isPreAllocation = catalogRepository.findIdByAlias(Parameter.CATALOG_PRE_ALLOCATED_ORIGIN_PRE_ALLOCATED_ALIAS);
        Integer isOperativeContainer = catalogRepository.findIdByAlias(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OK);
        Integer isOperativeChassis = catalogRepository.findIdByAlias(Parameter.IS_OPERATIVE_CHASSIS);
        Integer isOperativeDirtyContainer = catalogRepository.findIdByAlias(Parameter.IS_OPERATIVE_DIRTY_CONTAINER);
        Integer isGoCustomDelivery = catalogRepository.findIdByAlias(Parameter.CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS);

        Integer containerNoCntId = containerRepository.findByContainerNumber(Parameter.CONTAINER_NO_CNT).getId();
        Integer containerNotApplicableId = containerRepository.findByContainerNumber(Parameter.CONTAINER_NOT_APPLICA).getId();
        Integer eirNotificationPending = catalogRepository.findIdByAlias(Parameter.EIR_NOTIFICATION_PENDING);

        SdgTruckDepartureRegisterInput.Input input = inputRoot.getSdg().getInput();

        User userRegistration = userRepository.findById(input.getUserRegistrationId()).get();
        String remarks = formatString(input.getRemarks());
        String seal1 = formatString(input.getSeal1());
        String seal2 = formatString(input.getSeal2());
        String seal3 = formatString(input.getSeal3());
        String seal4 = formatString(input.getSeal4());

        Integer containerId = input.getContainerId();
        Integer chassisId = input.getChassisId();

        Integer subBusinessUnitId = businessUnitRepository.findParentBusinessUnitId(input.getSubBusinessUnitLocalId());

        Integer eirMultipleId = null;
        Integer catMovementId;
        Integer catEmptyFullId = null;
        Integer eirChassisId = null;
        Integer bookingId = null;
        String bookingNumber = null;
        Integer containerEirId = null;
        Integer vehicleId = null;
        Integer driverId = null;
        LocalDateTime truckArrivalDate = null;
        Boolean flagChassisStayed = null;
        Integer catOriginId = null;
        Integer businessUnitId = null;

        Integer eirAuxId = null;
        List<EirMultiple> eirMultipleList = eirMultipleRepository.findByEirIdAndActiveIsTrue(input.getEirId());
        if (!eirMultipleList.isEmpty()) {
            eirMultipleId = eirMultipleList.getFirst().getId().getEirMultipleId();

            Eir eir;
            eir = eirMultipleList.getFirst().getEir();
            catMovementId = eir.getCatMovement().getId();
            catEmptyFullId = eir.getCatEmptyFull().getId();
            eirChassisId = eir.getEirChassis().getId();
            bookingNumber = eir.getBookingGout().getBookingNumber();
            containerEirId = eir.getContainer().getId();
            vehicleId = eir.getTruck().getId();
            driverId = eir.getDriverPerson().getId();
            truckArrivalDate = eir.getTruckArrivalDate();
            flagChassisStayed = eir.getFlagChassisStayed();
            catOriginId = eir.getCatOrigin().getId();
            businessUnitId = eir.getBusinessUnit().getId();

        } else {
            Eir eir;
            eir = eirRepository.findOneById(input.getEirId());
            if (eir != null) {
                catMovementId = eir.getCatMovement().getId();
                catEmptyFullId = eir.getCatEmptyFull().getId();
                if (eir.getEirChassis() != null) {
                    eirChassisId = eir.getEirChassis().getId();
                }
                if (eir.getBookingGout() != null) {
                    bookingNumber = eir.getBookingGout().getBookingNumber();
                }


                containerEirId = eir.getContainer().getId();
                vehicleId = eir.getTruck().getId();
                driverId = eir.getDriverPerson().getId();
                truckArrivalDate = eir.getTruckArrivalDate();
                flagChassisStayed = eir.getFlagChassisStayed();
                catOriginId = eir.getCatOrigin().getId();
                businessUnitId = eir.getBusinessUnit().getId();
            } else {
                catMovementId = null;
            }
        }

        if (containerId == null) {
            containerId = containerEirId;
        }

        if (containerId != null
                && !List.of(containerNotApplicableId, containerNoCntId).contains(containerId)
                && Objects.equals(catMovementId, isGateOut)) {

            if (Objects.equals(catEmptyFullId, isEmpty)) {
                eirAuxId = sdgStockEmptyRepository.getEirAuxByEirId(input.getEirId());
            }

            if (Objects.equals(catEmptyFullId, isFullId)) {
                eirAuxId = sdgStockFullRepository.getEirAuxByEirId(input.getEirId());
            }

            if (eirAuxId != null) {
                response.setRespResult(2);
                response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 34, input.getLanguageId(),
                        Map.of(EIRX_PLACEHOLDER, eirAuxId.toString(), "EIRY{}", input.getEirId().toString())));
                return response;
            }
        }

        Integer documentChassisDetailId = null;
        Integer chassis2Id = null;
        Integer eirChassisAuxId = null;
        Integer documentChassisDetailAuxId = null;

        if (eirChassisId != null) {
            eirAuxId = null;
            Optional<EirChassis> eirChassis = eirChassisRepository.findById(eirChassisId);
            if (eirChassis.isPresent()) {
                documentChassisDetailId = eirChassis.get().getChassisDocumentDetail().getId();
                chassis2Id = eirChassis.get().getChassis().getId();
            }

            if (chassis2Id != null && catMovementId.equals(isGateIn) && Boolean.FALSE.equals(flagChassisStayed)) {
                LocalDateTime startDate = truckArrivalDate.minusMinutes(5);
                LocalDateTime endDate = truckArrivalDate.plusMinutes(5);
                Eir eir = eirRepository.findEirByChassisId(containerNotApplicableId,
                        input.getSubBusinessUnitLocalId(),
                        isGateOut, vehicleId, driverId, startDate, endDate, chassis2Id);

                if (eir != null) {
                    eirAuxId = eir.getId();
                    eirChassisAuxId = eir.getEirChassis().getId();
                    documentChassisDetailAuxId = eir.getEirChassis().getChassisDocumentDetail().getId();
                }

            }

            if (chassis2Id != null && catMovementId.equals(isGateOut)) {
                eirAuxId = eirRepository.findEirIdByEirChassisId(eirChassisId);

                if (eirAuxId != null) {
                    response.setRespResult(2);
                    response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 34, input.getLanguageId(),
                            Map.of(EIRX_PLACEHOLDER, eirAuxId.toString(), "EIRY{}", input.getEirId().toString())));
                    return response;
                }

            }


            String chassisNumberANT = "";
            if (chassis2Id != null && response.getResultMessage() == null) {
                Optional<Chassis> chassisOptional = chassisRepository.findById(chassis2Id);
                if (chassisOptional.isPresent()) {
                    chassisNumberANT = chassisOptional.get().getChassisNumber();
                }

                Integer eirChassisReferences = 0;
                if (!chassisNumberANT.equalsIgnoreCase(input.getNewChassisNumber())) {
                    Integer chassisIdAux = null;
                    chassisIdAux = chassisRepository.findChassisId(chassis2Id, input.getNewChassisNumber());
                    eirChassisReferences = eirChassisRepository.countEirChassisReferencesByChassisId(chassisId);

                    if (chassisIdAux != null) {
                        response.setRespResult(2);
                        response.setResultMessage(messageLanguageService.getMessage(INS_CHASSIS, 1, input.getLanguageId(),
                                Map.of("{IDX}", chassisIdAux.toString())));
                        return response;
                    }

                    if (Objects.nonNull(eirChassisReferences) && eirChassisReferences > 2) {
                        response.setRespResult(2);
                        response.setResultMessage(messageLanguageService.getMessage(INS_CHASSIS, 2, input.getLanguageId()));
                        return response;
                    } else {
                        Integer eirChassisValidateId = 0;
                        if (Objects.equals(catMovementId, isGateIn)) {
                            eirChassisValidateId = stockChassisRepository.findEirChassisGateOutIdByEirChassisId(eirChassisId);
                        }
                        if (Objects.equals(catMovementId, isGateOut)) {
                            eirChassisValidateId = stockChassisRepository.findEirChassisGateIntIdByEirChassisId(eirChassisId);
                        }

                        Integer chassisIdTemp = eirChassisRepository.findChassisIdByEirChassisId(eirChassisValidateId);

                        if (eirChassisReferences == 2 && eirChassisValidateId != null && eirChassisValidateId != 0 && chassis2Id != chassisIdTemp) {
                            response.setRespResult(2);
                            response.setResultMessage(messageLanguageService.getMessage(INS_CHASSIS, 2, input.getLanguageId()));
                            return response;

                        }
                    }

                    if (response.getResultMessage() == null && chassisOptional.isPresent()) {
                        Chassis chassisEdit = chassisOptional.get();
                        chassisEdit.setChassisNumber(input.getNewChassisNumber());
                        chassisEdit.setTraceChassis("upd_chass_truck_dep");
                        chassisEdit.setModificationDate(LocalDateTime.now());
                        Optional<User> userUpdate = userRepository.findById(input.getUserRegistrationId());
                        chassisEdit.setModificationUser(userUpdate.get());
                        chassisRepository.save(chassisEdit);
                    }
                }
            }
        }


        String containerNumber = containerRepository.findContainerNumberById(containerId);

        Optional<BusinessUnit> optionalBusinessUnit = businessUnitRepository.findOneById(businessUnitId);
        String businessUnitAlias = null;
        if (optionalBusinessUnit.isPresent()) {
            businessUnitAlias = optionalBusinessUnit.get().getBusinesUnitAlias();
        }


        Map<String, Object> resultRuleGeneralGetOut = systemRuleRepository.ruleGeneralGetOut(
                businessUnitAlias,
                "sd1_general_rule_by_business_unit",
                "[\"restriction_truck_departure_by_inspection\"]");

        String rAction = resultRuleGeneralGetOut.get("r_action").toString();


        if ("true".equalsIgnoreCase(rAction) && response.getResultMessage() == null) {
            String statusInspectionBoxPending = "";
            Integer eirGateIn;
            Integer catContainerStatus;
            String statusInspectionBox;
            Integer catChassisStatus;
            if (containerId != null
                    && (!Objects.equals(containerId, containerNoCntId) && !containerId.equals(containerNotApplicableId))
                    && Objects.equals(catMovementId, isGateOut)) {

                statusInspectionBoxPending = catalogRepository.findByAlias("sd1_statusinspectiongral_pending").getDescription();
                eirGateIn = sdgStockEmptyRepository.findGateInEirByContainerIdAndEirId(containerId, input.getEirId());

                if (eirGateIn == null) {
                    eirGateIn = sdgStockEmptyRepository.findGateInEirByContainerIdAndSubBusinessUnitId(containerId, subBusinessUnitId);
                }
                catContainerStatus = eirRepository.fn_GetEquipmentConditionID(eirGateIn, isContainer, "", "GRAL");
                statusInspectionBox = catalogRepository.findById(catContainerStatus).get().getLongDescription();

                if (Objects.equals(catOriginId, isGoCustomDelivery)
                        && (!Objects.equals(catContainerStatus, isOperativeContainer) && !Objects.equals(catContainerStatus, isOperativeDirtyContainer))) {

                    response.setRespResult(3);
                    response.setResultMessage(messageLanguageService.getMessage("TRUCK_DEPARTURE", 8, input.getLanguageId(),
                            Map.of("{status}", catContainerStatus == null ? statusInspectionBoxPending : statusInspectionBox)));
                    return response;
                }

            }

            if (eirChassisId != null
                    && (Objects.equals(containerId, containerNoCntId) || Objects.equals(containerId, containerNotApplicableId))
                    && Objects.equals(catMovementId, isGateOut)
                    && response.getResultMessage() == null) {

                statusInspectionBoxPending = catalogRepository.findByAlias("sd1_chassis_structurecondition_nc").getDescription();

                if (chassisId == null) {
                    chassisId = eirRepository.findChassisIdByEirIdAndEirChassisId(input.getEirId(), eirChassisId);
                }
                TbEirChassisDto tbEirChassisDto = eirRepository.findEirChassisGateInByChassisIdAndEirChassisId(chassisId, eirChassisId);
                Integer eirChassisGateinId = tbEirChassisDto.getEirId();
                Boolean flagChassisStayedGi = tbEirChassisDto.getFlagChassisStayed();
                Boolean flagOnSiteInspection = tbEirChassisDto.getFlagOnSiteInspection();

                catChassisStatus = eirRepository.fn_GetEquipmentConditionID(eirChassisGateinId, isChassis, "", "GRAL");
                statusInspectionBox = catalogRepository.findById(catChassisStatus).get().getLongDescription();

                if (flagChassisStayedGi && flagOnSiteInspection && Objects.equals(catOriginId, isGoCustomDelivery)
                        && !catChassisStatus.equals(isOperativeChassis)) {
                    response.setRespResult(3);
                    response.setResultMessage(messageLanguageService.getMessage("TRUCK_DEPARTURE", 9, input.getLanguageId(),
                            Map.of("{status}", catChassisStatus == null ? statusInspectionBoxPending : statusInspectionBox)));
                    return response;
                }

            }
        }

        Integer controlRevision = null;
        BigDecimal reportedWeight = null;
        Catalog catReportedWeight = null;
        Integer vesselProgrammingDetailId = null;
        String sealGif1 = null;
        String sealGif2 = null;
        String sealGif3 = null;
        String sealGif4 = null;
        Boolean activo = null;
        Integer catOriginCreationId = null;
        Integer transportPlanningDetailFullId = null;

        LocalDateTime truckOutDate = null;
        if (response.getResultMessage() == null) {

            if (containerId != null &&
                    !Objects.equals(containerId, containerNotApplicableId) &&
                    Objects.equals(catMovementId, isGateOut)) {
                ContainerRestriction containerRestriction = containerRestrictionRepository.findContainerRestrictionByParameters(containerId, subBusinessUnitId, catEmptyFullId);
                Integer containerRestrictionId = null;
                if (containerRestriction != null) {
                    containerRestrictionId = containerRestriction.getId();
                }

                String restriction = "";
                if (containerRestriction != null
                        && containerRestriction.getContainerRestrictionDetails() != null
                        && !containerRestriction.getContainerRestrictionDetails().isEmpty()) {
                    restriction = containerRestriction.getContainerRestrictionDetails().stream()
                            .filter(obj -> obj.getActive())
                            .map(obj -> obj.getCatRestrictionReason().getDescription())
                            .collect(Collectors.joining(", "));


                }
                restriction = restriction.trim();


                if (Objects.equals(catEmptyFullId, isFullId) && containerRestrictionId != null) {
                    response.setRespResult(2);
                    response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 29, input.getLanguageId(),
                            Map.of(CNTX_PLACEHOLDER, containerNumber, RSTX_PLACEHOLDER, restriction)));
                    return response;
                }

                if (Objects.equals(catEmptyFullId, isEmpty)
                        && (input.getPassRestriction() == null || input.getPassRestriction() == 0)
                        && response.getResultMessage() == null) {

                    Integer eirGateinContainerId = sdgStockEmptyRepository.findEirGateInIdByEirGateOutId(input.getEirId());
                    FgisInspection fgisInspection = fgisInspectionRepository.findByEirGateInAndContainerId(eirGateinContainerId, containerId);
                    Integer fgisInspectionId = fgisInspection != null ? fgisInspection.getId() : null;
                    Boolean usdaApproved = fgisInspection != null ? fgisInspection.getUsdaApproved() : null;
                    EirDocumentCargoDetail eirDocumentCargoDetail = eirCargoDetailRepository.findByEirAndActiveTrue(input.getEirId());
                    Integer preallocationContainerId = null;
                    if (eirDocumentCargoDetail == null) {
                        List<ContainerPreassignment> containerPreassignmentList = containerPreassignmentRepository.findByCargoDocumentDetailIdAndCatOriginPreassignment(eirDocumentCargoDetail.getId(), isPreAllocation);
                        if (containerPreassignmentList != null && !containerPreassignmentList.isEmpty()) {

                            preallocationContainerId = containerPreassignmentList.getFirst().getId();
                        }
                    }

                    if (preallocationContainerId != null) {
                        if (Boolean.TRUE.equals(containerRestrictionId != null && fgisInspectionId != null && usdaApproved)
                                && (input.getPassRestriction() == null || input.getPassRestriction() == 0)) {

                            response.setRespResult(4);
                            response.setRestrictionContainerId(containerRestrictionId);
                            response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 33, input.getLanguageId(),
                                    Map.of(CNTX_PLACEHOLDER, containerNumber, RSTX_PLACEHOLDER, restriction, BOOKING_NUMBER_PLACEHOLDER, bookingNumber)));
                            return response;
                        }

                        if (containerRestrictionId != null && fgisInspectionId == null) {
                            response.setRespResult(4);
                            response.setRestrictionContainerId(containerRestrictionId);
                            response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 30, input.getLanguageId(),
                                    Map.of(CNTX_PLACEHOLDER, containerNumber, RSTX_PLACEHOLDER, restriction, BOOKING_NUMBER_PLACEHOLDER, bookingNumber)));
                            return response;

                        }

                        if (containerRestrictionId == null && fgisInspectionId != null && Boolean.TRUE.equals(usdaApproved)) {
                            response.setRespResult(6);
                            response.setRestrictionContainerId(containerRestrictionId);
                            response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 32, input.getLanguageId(),
                                    Map.of(CNTX_PLACEHOLDER, containerNumber, BOOKING_NUMBER_PLACEHOLDER, bookingNumber)));
                            return response;

                        }

                    } else {
                        if (containerRestrictionId == null && fgisInspectionId != null && Boolean.TRUE.equals(usdaApproved)) {
                            response.setRespResult(6);
                            response.setRestrictionContainerId(containerRestrictionId);
                            response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 31, input.getLanguageId(),
                                    Map.of(CNTX_PLACEHOLDER, containerNumber)));
                            return response;

                        }
                    }
                }
            }

            if (eirMultipleId != null && Objects.equals(catMovementId, isGateOut)
                    && (Objects.equals(catEmptyFullId, isFullId) || Objects.equals(catEmptyFullId, isEmpty))) {
                eirMultipleId = null;
            }

            List<Eir> eirList = new ArrayList<>();
            Eir gateInEir = eirRepository.findEirGateInByEirGateOutId(input.getEirId());
            if (eirMultipleId == null && gateInEir != null) {

                truckOutDate = gateInEir.getTruckDepartureDate();
                activo = gateInEir.getActive();
                sealGif1 = gateInEir.getSeal1();
                sealGif2 = gateInEir.getSeal2();
                sealGif3 = gateInEir.getSeal3();
                sealGif4 = gateInEir.getSeal4();
                catOriginCreationId = gateInEir.getCatCreationOrigin().getId();
                if (gateInEir.getTransportPlanningDetailFull() != null) {
                    transportPlanningDetailFullId = gateInEir.getTransportPlanningDetailFull().getId();
                }

                reportedWeight = gateInEir.getWeightGoods();

                Catalog catIsMesassureWeight = catalogRepository.findById(isMeasureWeightKg).get();
                catReportedWeight = gateInEir.getCatMeasureWeight() == null ? catIsMesassureWeight : gateInEir.getCatMeasureWeight();
                vesselProgrammingDetailId = gateInEir.getVesselProgrammingDetail().getId();
                if (gateInEir.getControlRevision() != null) {
                    controlRevision = Integer.valueOf(gateInEir.getControlRevision());
                } else {
                    controlRevision = null;
                }


                if (Objects.equals(catMovementId, isGateOut) && Objects.equals(catEmptyFullId, isFullId)
                        && (!Objects.equals(gateInEir.getContainer().getId(), containerNoCntId)
                        && !Objects.equals(gateInEir.getContainer().getId(), containerNotApplicableId))) {
                    if (!Objects.equals(gateInEir.getSeal1(), seal1)
                            || !Objects.equals(gateInEir.getSeal2(), seal2)
                            || !Objects.equals(gateInEir.getSeal3(), seal3)
                            || !Objects.equals(gateInEir.getSeal4(), seal4)) {


                        String seals1 = Stream.of(gateInEir.getSeal1(), gateInEir.getSeal2(), gateInEir.getSeal3(), gateInEir.getSeal4())
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(", "));

                        String seals2 = Stream.of(seal1, seal2, seal3, seal4)
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(", "));

                        response.setRespResult(2);
                        response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 4, input.getLanguageId(),
                                Map.of("{sealx1}", seals1, "{sealx2}", seals2)));
                    } else {
                        response.setRespResult(2);
                        response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 5, input.getLanguageId()));
                    }
                }

                Eir gateOutFullEir = eirRepository.findOneById(input.getEirId());
                if (response.getResultMessage() == null) {

                    if (Boolean.TRUE.equals(gateOutFullEir.getActive()) && truckOutDate == null
                            && (!Objects.equals(gateOutFullEir.getCatCreationOrigin().getId(), isEirManual)
                            || (Objects.equals(gateOutFullEir.getCatCreationOrigin().getId(), isEirManual) && input.getTruckOutDate() == null))) {

                        if (input.getTruckOutDate() != null) {
                            if (gateOutFullEir.getTruckArrivalDate().isAfter(input.getTruckOutDate())) {
                                response.setRespResult(2);
                                response.setResultMessage(messageLanguageService.getMessage("PRC_EIR_MANUAL", 6, input.getLanguageId()));
                            } else {
                                truckOutDateZh = input.getTruckOutDate();
                            }
                        } else {
                            truckOutDateZh = LocalDateTime.now();
                        }

                        Eir eir = eirRepository.findEirGateInByEirGateOutId(input.getEirId());

                        eir.setTruckDepartureDate(truckOutDateZh);

                        eir.setTruckDepartureUser(userRegistration);
                        eir.setTraceEir("graltrkdeparture1");
                        eir.setModificationDate(LocalDateTime.now());
                        if (Objects.equals(catMovementId, isGateOut)) {
                            eir.setSeal1(seal1);
                            eir.setSeal1(seal2);
                            eir.setSeal1(seal3);
                            eir.setSeal1(seal4);
                        }

                        eir.setObservation(remarks);
                        if (Objects.equals(catMovementId, isGateOut) && Objects.equals(catEmptyFullId, isFullId)) {
                            eir.setControlAssignmentLight((short) 3);
                        }

                        if (Objects.equals(catMovementId, isGateIn) && (Objects.equals(controlRevision, 2) || Boolean.TRUE.equals(input.getReject()))) {
                            eir.setActive(false);
                        }

                        if (Objects.equals(catMovementId, isGateIn) &&
                                Objects.equals(controlRevision, 0) && Boolean.TRUE.equals(input.getReject())) {
                            eir.setControlRevision((short) 2);
                        }

                        if (Objects.equals(catMovementId, isGateOut) && input.getTemperature() != null) {
                            eir.setTemperature(input.getTemperature());
                        }

                        if (Objects.equals(catMovementId, isGateOut) && input.getTemperature() != null) {
                            Catalog catTemperatur = catalogRepository.findById(input.getTemperatureUnit()).get();
                            eir.setCatTemperatureMeasure(catTemperatur);

                        }

                        eirRepository.save(eir);

                        List<Attachment> listAttachmentId = new ArrayList<>();
                        if (input.getPhotos() != null) {

                            JSONArray jsonArray = new JSONArray(input.getPhotos());
                            for (int i = 0; i < jsonArray.length(); i++) {
                                JSONObject jsonObject = jsonArray.getJSONObject(i);

                                String name = jsonObject.getString("nombre");
                                String weight = jsonObject.getString("peso");
                                String format = jsonObject.getString("formato");
                                String path = jsonObject.getString("ubicacion");
                                String url = jsonObject.getString("url");
                                String id = jsonObject.getString("id");
                                int attachmentType = jsonObject.getInt("tipoAdjunto");
                                Catalog catAttachmentType = catalogRepository.findById(attachmentType).get();
                                Attachment attachment = new Attachment();
                                attachment.setName(name);
                                attachment.setWeight(Integer.valueOf(weight));
                                attachment.setFormat(format);
                                attachment.setLocation(path);

                                attachment.setUrl(url);
                                attachment.setId1(id);
                                attachment.setCatAttachmentType(catAttachmentType);
                                attachment = attachmentRepository.save(attachment);
                                listAttachmentId.add(attachment);
                            }
                        }
                        if (!listAttachmentId.isEmpty()) {
                            if (Objects.equals(catEmptyFullId, isFullId)) {
                                listAttachmentId.forEach(attachment -> {
                                    SdfEirPhoto sdfEirPhoto = new SdfEirPhoto();
                                    sdfEirPhoto.setAttached(attachment);
                                    sdfEirPhoto.setRegistrationDate(LocalDateTime.now());
                                    sdfEirPhoto.setUserRegistration(userRegistration);
                                    sdfEirPhoto.setActive(true);
                                    sdfEirPhoto.setEir(eir);
                                    sdfEirPhotoRepository.save(sdfEirPhoto);
                                });

                            }
                            if (Objects.equals(catEmptyFullId, isEmpty)) {

                                listAttachmentId.forEach(attachment -> {
                                    SdsEirPhoto sdsEirPhoto = new SdsEirPhoto();
                                    sdsEirPhoto.setAttachment(attachment);
                                    sdsEirPhoto.setRegistrationDate(LocalDateTime.now());
                                    sdsEirPhoto.setUserRegistration(userRegistration);
                                    sdsEirPhoto.setActive(true);
                                    sdsEirPhoto.setEir(eir);
                                    sdsEirPhotoRepository.save(sdsEirPhoto);
                                });
                            }
                        }

                        if (Objects.equals(catMovementId, isGateIn) && (Objects.equals(controlRevision, 2) || Boolean.TRUE.equals(input.getReject()))) {
                            if (eirChassisAuxId != null) {
                                Integer bookingChassisAuxId = chassisDocumentDetailRepository.findDocumentChassisBookingIdByDocumentChassisDetailId(documentChassisDetailAuxId);

                                ChassisDocumentDetail chassisDocumentDetail = chassisDocumentDetailRepository.findById(documentChassisDetailAuxId).get();
                                Catalog catStatuChassis = catalogRepository.findById(isDocChassisPending).get();
                                chassisDocumentDetail.setCatStatusChassis(catStatuChassis);
                                chassisDocumentDetail.setChassis(null);
                                chassisDocumentDetail.setModificationUser(userRegistration);
                                chassisDocumentDetail.setModificationDate(LocalDateTime.now());
                                chassisDocumentDetail.setTraceChassisDocDetail(TRUCKDEPA_RECH_CHGO1);
                                chassisDocumentDetailRepository.save(chassisDocumentDetail);

                                Integer qchaAttendedAux = chassisDocumentDetailRepository.countDocumentChassisDetailByBookingChassisId(bookingChassisAuxId);
                                ChassisBookingDocument chassisBookingDocument = chassisBookingDocumentRepository.findById(bookingChassisAuxId).get();

                                chassisBookingDocument.setAttendedQuantity(qchaAttendedAux == null ? 0 : qchaAttendedAux);
                                chassisBookingDocument.setModificationUser(userRegistration);
                                chassisBookingDocument.setModificationDate(LocalDateTime.now());
                                chassisBookingDocumentRepository.save(chassisBookingDocument);

                                Eir eirEdit = eirRepository.findByEirIdAndActive(eirAuxId);
                                eirEdit.setActive(false);
                                if (controlRevision != null) {
                                    eirEdit.setControlRevision((short) (Objects.equals(controlRevision, 0) ? 4 : controlRevision));
                                }

                                eirEdit.setTraceEir(TRUCKDEPA_RECH_CHGO1);
                                eirEdit.setModificationUser(userRegistration);
                                eirEdit.setModificationDate(LocalDateTime.now());
                                eirEdit.setDeleteDate(LocalDateTime.now());
                                eirEdit.setDeleteUser(userRegistration);
                                eirRepository.save(eirEdit);


                                EirChassis eirChassis = eirChassisRepository.findEirChassisByIdAndActive(eirChassisAuxId);
                                eirChassis.setActive(false);
                                eirChassis.setModificationUser(userRegistration);
                                eirChassis.setModificationDate(LocalDateTime.now());
                                eirChassis.setTraceEirChassis(TRUCKDEPA_RECH_CHGO1);
                                eirChassisRepository.save(eirChassis);
                            }

                            Optional<Eir> eirOptional = eirRepository.findById(input.getEirId());
                            if (eirOptional.isPresent()) {
                                Eir eirEdit = eirOptional.get();
                                eirEdit.setActive(false);
                                eirEdit.setControlRevision((short) (controlRevision == 0 ? 4 : controlRevision));
                                eirEdit.setDeleteUser(userRegistration);
                                eirEdit.setDeleteDate(LocalDateTime.now());
                                eirRepository.save(eirEdit);
                            }

                            if (eirChassisId != null) {
                                EirChassis eirChassis = eirChassisRepository.findById(eirChassisId).get();
                                eirChassis.setActive(false);
                                eirChassis.setModificationUser(userRegistration);
                                eirChassis.setModificationDate(LocalDateTime.now());
                                eirChassis.setTraceEirChassis("graltruckdepa_rech1");
                                eirChassisRepository.save(eirChassis);
                            }
                        }

                        if (Objects.equals(catEmptyFullId, isFullId)) {
                            if (Objects.equals(catMovementId, isGateIn) && Boolean.FALSE.equals(input.getReject())) {
                                CargoDocumentDetail cargoDocumentDetail = cargoDocumentDetailRepository.findByEirId(input.getEirId());
                                cargoDocumentDetail.setReceivedWeight(reportedWeight);
                                cargoDocumentDetail.setCatReceivedWeightMeasure(catReportedWeight);
                                cargoDocumentDetail.setReceivedQuantity(BigDecimal.valueOf(1));
                                cargoDocumentDetail.setReceivedVolume(BigDecimal.valueOf(0));
                                cargoDocumentDetail.setBalanceQuantity(BigDecimal.valueOf(1));
                                cargoDocumentDetail.setBalanceWeight(reportedWeight);
                                cargoDocumentDetail.setModificationUser(userRegistration);
                                cargoDocumentDetail.setModificationDate(LocalDateTime.now());
                                cargoDocumentDetail.setTraceCargoDocumentDetail("ggi_trkdeparture2");

                                cargoDocumentDetailRepository.save(cargoDocumentDetail);


                                VesselProgrammingContainer vesselProgrammingContainer =
                                        vesselProgrammingContainerRepository.findByIdAndContainerIdAndActiveTrue(vesselProgrammingDetailId, containerId);
                                vesselProgrammingContainer.setReceivedQuantity(0);
                                vesselProgrammingContainer.setReceivedWeight(reportedWeight);
                                vesselProgrammingContainer.setCatReceivedWeightMeasure(catReportedWeight);
                                vesselProgrammingContainer.setReceivedSeal1(sealGif1);
                                vesselProgrammingContainer.setReceivedSeal2(sealGif2);
                                vesselProgrammingContainer.setReceivedSeal3(sealGif3);
                                vesselProgrammingContainer.setReceivedSeal4(sealGif4);
                                vesselProgrammingContainer.setModificationUser(userRegistration);
                                vesselProgrammingContainer.setModificationDate(LocalDateTime.now());
                                vesselProgrammingContainer.setTraceProgVesCnt("ggi_trkdeparture2");
                                vesselProgrammingContainerRepository.save(vesselProgrammingContainer);


                            }

                            if (Objects.equals(catMovementId, isGateOut)) {
                                CargoDocumentDetail cargoDocumentDetail = cargoDocumentDetailRepository.findByEirId(input.getEirId());
                                cargoDocumentDetail.setDispatchedQuantity(BigDecimal.valueOf(1));
                                cargoDocumentDetail.setDispatchedWeight(reportedWeight);
                                cargoDocumentDetail.setDispatchedVolume(BigDecimal.valueOf(0));
                                cargoDocumentDetail.setBalanceQuantity(BigDecimal.valueOf(0));

                                if (cargoDocumentDetail.getBalanceWeight() != null) {
                                    if (cargoDocumentDetail.getBalanceWeight().subtract(reportedWeight).compareTo(BigDecimal.ZERO) < 0) {
                                        cargoDocumentDetail.setBalanceWeight(BigDecimal.valueOf(0));
                                    } else {
                                        cargoDocumentDetail.setBalanceWeight(cargoDocumentDetail.getBalanceWeight().subtract(reportedWeight));
                                    }
                                } else {
                                    cargoDocumentDetail.setBalanceWeight(BigDecimal.valueOf(0));
                                }
                                cargoDocumentDetail.setBalanceVolume(BigDecimal.valueOf(0));

                                cargoDocumentDetail.setCatDispatchedWeightMeasure(catReportedWeight);

                                cargoDocumentDetail.setModificationUser(userRegistration);
                                cargoDocumentDetail.setModificationDate(LocalDateTime.now());
                                cargoDocumentDetail.setTraceCargoDocumentDetail("ggo_trkdeparture3");

                                cargoDocumentDetailRepository.save(cargoDocumentDetail);


                                VesselProgrammingContainer vesselProgrammingContainer =
                                        vesselProgrammingContainerRepository.findByIdAndContainerIdAndActiveTrue(vesselProgrammingDetailId, containerId);

                                vesselProgrammingContainer.setReceivedSeal1(seal1);
                                vesselProgrammingContainer.setReceivedSeal2(seal2);
                                vesselProgrammingContainer.setReceivedSeal3(seal3);
                                vesselProgrammingContainer.setReceivedSeal4(seal4);
                                vesselProgrammingContainer.setModificationUser(userRegistration);
                                vesselProgrammingContainer.setModificationDate(LocalDateTime.now());
                                vesselProgrammingContainer.setTraceProgVesCnt("ggo_trkdeparture3");
                                vesselProgrammingContainer.setDispatchedTemperatureC(input.getTemperature());
                                Catalog catDispatchedTemperatureMeasure = catalogRepository.findById(input.getTemperatureUnit()).get();
                                vesselProgrammingContainer.setCatDispatchedTemperatureMeasure(catDispatchedTemperatureMeasure);
                                vesselProgrammingContainerRepository.save(vesselProgrammingContainer);

                            }

                            if (transportPlanningDetailFullId != null) {
                                TransportPlanningDetail transportPlanningDetail = transportPlanningDetailRepository.findOneById(transportPlanningDetailFullId);
                                if (Objects.equals(catMovementId, isGateIn) && input.getReject()) {
                                    Catalog catIsPlanningPending = catalogRepository.findById(isPlanningPending).get();
                                    transportPlanningDetail.setCatTrkPlanningState(catIsPlanningPending);
                                } else {
                                    Catalog catIsPlanningDone = catalogRepository.findById(isPlanningDone).get();
                                    transportPlanningDetail.setCatTrkPlanningState(catIsPlanningDone);
                                }
                                transportPlanningDetail.setModificationUser(userRegistration);
                                transportPlanningDetail.setModificationDate(LocalDateTime.now());
                                transportPlanningDetailRepository.save(transportPlanningDetail);

                            }


                        }

                        if (documentChassisDetailId != null) {
                            ChassisDocumentDetail chassisDocumentDetail = chassisDocumentDetailRepository.findById(documentChassisDetailId).get();
                            if (Objects.equals(catMovementId, isGateIn) && Boolean.TRUE.equals(input.getReject())) {
                                Catalog catIsDocChassisPending = catalogRepository.findById(isDocChassisPending).get();
                                chassisDocumentDetail.setCatStatusChassis(catIsDocChassisPending);
                            } else {
                                Catalog catIsDocChassisCompleted = catalogRepository.findById(isDocChassisCompleted).get();
                                chassisDocumentDetail.setCatStatusChassis(catIsDocChassisCompleted);
                            }
                            chassisDocumentDetail.setModificationUser(userRegistration);
                            chassisDocumentDetail.setModificationDate(LocalDateTime.now());

                            if (Objects.equals(catMovementId, isGateIn) && Boolean.TRUE.equals(input.getReject())) {
                                chassisDocumentDetail.setTraceChassisDocDetail("graltruckdepa_rech1");
                            } else {
                                chassisDocumentDetail.setTraceChassisDocDetail("graltrkdeparture6");
                            }
                            chassisDocumentDetailRepository.save(chassisDocumentDetail);
                        }

                        if (input.getNewTruckCompanyId() != null) {
                            Eir eirEdit = eirRepository.findById(input.getEirId()).get();
                            Company company = companyRepository.findById(input.getNewTruckCompanyId()).get();
                            eirEdit.setTransportCompany(company);
                            eirEdit.setModificationUser(userRegistration);
                            eirEdit.setModificationDate(LocalDateTime.now());
                            eirEdit.setTraceEir("upd_truck_truck_dep");
                            eirRepository.save(eirEdit);
                        }
                        response.setRespResult(1);
                        return response;

                    }
                } else {
                    if (Boolean.FALSE.equals(activo)) {
                        response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 1, input.getLanguageId(),
                                Map.of(EIRX_PLACEHOLDER, input.getEirId().toString())));
                    }
                    if (response.getResultMessage() == null && Objects.equals(catOriginCreationId, isEirManual) && input.getTruckOutDate() == null) {
                        response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 3, input.getLanguageId()));
                    }
                    if (response.getResultMessage() == null && truckOutDate != null) {

                        response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 2, input.getLanguageId(),
                                Map.of(EIRX_PLACEHOLDER, input.getEirId().toString(), "{FECHAX}", gateOutFullEir.getTruckDepartureDate().toString())));
                    }
                    response.setRespResult(2);
                    return response;
                }

            } else {
                if (input.getTruckOutDate() != null) {
                    truckOutDateZh = input.getTruckOutDate();
                } else {
                    truckOutDateZh = LocalDateTime.now();
                }
                eirList = eirRepository.findByEirMultipleId(eirMultipleId);
                List<Eir> eirTruckDepartureDateNotNull = eirList.stream().filter(eir -> eir.getTruckDepartureDate() != null).toList();
                if (eirList.stream().filter(eir -> eir.getTruckDepartureDate() == null).toList().isEmpty() && !eirTruckDepartureDateNotNull.isEmpty()) {


                    eirAuxId = eirTruckDepartureDateNotNull.getFirst().getId();
                    truckOutDate = eirTruckDepartureDateNotNull.getFirst().getTruckDepartureDate();

                    response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 2, input.getLanguageId(),
                            Map.of(EIRX_PLACEHOLDER, eirAuxId.toString(), "{FECHAX}", truckOutDate.toString())));
                    response.setRespResult(2);
                    return response;


                } else {
                    LocalDateTime finalTruckOutDateZh = truckOutDateZh;
                    eirList.forEach(eir -> {
                        eir.setTruckDepartureDate(finalTruckOutDateZh);
                        eir.setTruckDepartureUser(userRegistration);
                        eir.setTraceEir("graltrkdeparture4");
                        eir.setModificationDate(LocalDateTime.now());
                        eir.setModificationUser(userRegistration);
                        eir.setObservation(remarks);

                        if (Objects.equals(catMovementId, isGateIn) && Boolean.TRUE.equals(input.getReject())) {
                            eir.setActive(false);
                        }
                        eirRepository.save(eir);

                    });


                    if (Objects.equals(catMovementId, isGateIn)
                            && (!eirList.stream().filter(eir ->
                            eir.getControlRevision() == 2).toList().isEmpty() || input.getReject())) {
                        if (eirChassisAuxId != null) {
                            ChassisDocumentDetail chassisDocumentDetail = chassisDocumentDetailRepository.findById(documentChassisDetailAuxId).get();
                            Integer bookingChassisAuxId = chassisDocumentDetail.getChassisBookingDocument().getId();
                            Catalog catIsDocChassisPending = catalogRepository.findById(isDocChassisPending).get();
                            chassisDocumentDetail.setCatStatusChassis(catIsDocChassisPending);
                            chassisDocumentDetail.setChassis(null);
                            chassisDocumentDetail.setModificationUser(userRegistration);
                            chassisDocumentDetail.setModificationDate(LocalDateTime.now());
                            chassisDocumentDetail.setTraceChassisDocDetail(TRUCKDEPA_RECH_CHGO2);
                            chassisDocumentDetailRepository.save(chassisDocumentDetail);


                            Integer qchaAttendedAux =
                                    chassisDocumentDetailRepository.countDocumentChassisDetailByBookingChassisId(bookingChassisAuxId);

                            ChassisBookingDocument chassisBookingDocument = chassisBookingDocumentRepository.findById(bookingChassisAuxId).get();
                            chassisBookingDocument.setAttendedQuantity(qchaAttendedAux);
                            chassisBookingDocument.setModificationUser(userRegistration);
                            chassisBookingDocument.setModificationDate(LocalDateTime.now());

                            chassisBookingDocumentRepository.save(chassisBookingDocument);

                            Eir eirEdit = eirRepository.findByEirIdAndActive(eirAuxId);
                            eirEdit.setActive(false);
                            if (controlRevision != null && controlRevision == 4) {
                                eirEdit.setControlRevision((short) 4);
                            }
                            eirEdit.setDeleteUser(userRegistration);
                            eirEdit.setDeleteDate(LocalDateTime.now());
                            eirEdit.setTraceEir(TRUCKDEPA_RECH_CHGO2);
                            eirEdit.setModificationUser(userRegistration);
                            eirEdit.setModificationDate(LocalDateTime.now());
                            eirRepository.save(eirEdit);

                            EirChassis eirChassis = eirChassisRepository.findEirChassisByIdAndActive(eirChassisAuxId);
                            eirChassis.setActive(false);
                            eirChassis.setModificationUser(userRegistration);
                            eirChassis.setModificationDate(LocalDateTime.now());
                            eirChassis.setTraceEirChassis(TRUCKDEPA_RECH_CHGO2);
                            eirChassisRepository.save(eirChassis);

                        }

                        eirList.forEach(eir -> {
                            eir.setTraceEir(TRACE_GRALTRUCKDEPA_RECH2);
                            eir.setActive(false);
                            if (eir.getControlRevision() != null && eir.getControlRevision() == 0) {
                                eir.setControlRevision((short) 4);
                            }
                            eir.setDeleteUser(userRegistration);
                            eir.setDeleteDate(LocalDateTime.now());
                            eirRepository.save(eir);
                        });

                        if (eirChassisId != null) {
                            EirChassis eirChassis = eirChassisRepository.findEirChassisByIdAndActive(eirChassisId);
                            eirChassis.setActive(false);
                            eirChassis.setModificationUser(userRegistration);
                            eirChassis.setModificationDate(LocalDateTime.now());
                            eirChassis.setTraceEirChassis(TRACE_GRALTRUCKDEPA_RECH2);
                            eirChassisRepository.save(eirChassis);
                        }

                    }

                    if (Objects.equals(catMovementId, isGateIn)
                            && Objects.equals(catEmptyFullId, isFullId) && Boolean.FALSE.equals(input.getReject())) {

                        List<Integer> eirIdsList = new ArrayList<>();
                        eirList.forEach(eir ->
                                eirIdsList.add(eir.getId())
                        );

                        List<EirDocumentCargoDetail> eirDocumentCargoDetailList = eirDocumentCargoDetailRepository.findManyByEirId(eirIdsList);

                        eirDocumentCargoDetailList.forEach(eirDocumentCargoDetail -> {
                            CargoDocumentDetail cargoDocumentDetail = eirDocumentCargoDetail.getCargoDocumentDetail();
                            cargoDocumentDetail.setReceivedQuantity(BigDecimal.valueOf(1));
                            cargoDocumentDetail.setReceivedWeight(eirDocumentCargoDetail.getEir().getWeightGoods());
                            cargoDocumentDetail.setCatWeightMeasureUnit(eirDocumentCargoDetail.getEir().getCatMeasureWeight());
                            cargoDocumentDetail.setReceivedVolume(BigDecimal.valueOf(1));
                            cargoDocumentDetail.setBalanceQuantity(BigDecimal.valueOf(1));
                            cargoDocumentDetail.setBalanceWeight(eirDocumentCargoDetail.getEir().getWeightGoods());
                            cargoDocumentDetail.setBalanceVolume(BigDecimal.valueOf(0));
                            cargoDocumentDetail.setModificationUser(userRegistration);
                            cargoDocumentDetail.setModificationDate(LocalDateTime.now());
                            cargoDocumentDetail.setTraceCargoDocumentDetail("ggi_trkdeparture5");
                            cargoDocumentDetailRepository.save(cargoDocumentDetail);
                        });


                        List<VesselProgrammingContainer> vesselProgrammingContainers = vesselProgrammingContainerRepository.findByEirIdList(eirIdsList);
                        List<Eir> finalEirList = eirList;
                        vesselProgrammingContainers.forEach(vesselProgrammingContainer -> {
                            vesselProgrammingContainer.setReceivedQuantity(0);
                            Eir eirSearch = null;
                            for (Eir eir : finalEirList) {
                                if (Objects.equals(eir.getVesselProgrammingDetail().getId(), vesselProgrammingContainer.getVesselProgrammingDetail().getId())) {
                                    eirSearch = eir;
                                }
                            }
                            if (eirSearch != null) {
                                vesselProgrammingContainer.setReceivedWeight(eirSearch.getWeightGoods());
                                vesselProgrammingContainer.setCatReceivedWeightMeasure(eirSearch.getCatMeasureWeight());
                                vesselProgrammingContainer.setManifestedSeal1(eirSearch.getSeal1());
                                vesselProgrammingContainer.setManifestedSeal2(eirSearch.getSeal2());
                                vesselProgrammingContainer.setManifestedSeal3(eirSearch.getSeal3());
                                vesselProgrammingContainer.setManifestedSeal4(eirSearch.getSeal4());
                                vesselProgrammingContainer.setModificationUser(userRegistration);
                                vesselProgrammingContainer.setModificationDate(LocalDateTime.now());
                                vesselProgrammingContainer.setTraceProgVesCnt("ggi_trkdeparture5");
                            }
                            vesselProgrammingContainerRepository.save(vesselProgrammingContainer);


                        });
                    }

                    if (Objects.equals(catEmptyFullId, isFullId)) {
                        for (Eir eir : eirList) {
                            TransportPlanningDetail transportPlanningDetail = eir.getTransportPlanningDetailFull();

                            if (Objects.equals(catMovementId, isGateIn) && Boolean.TRUE.equals(input.getReject())) {
                                Catalog catIsPlanningPending = catalogRepository.findById(isPlanningPending).get();
                                transportPlanningDetail.setCatTrkPlanningState(catIsPlanningPending);
                            } else {
                                Catalog catIsPlanningDone = catalogRepository.findById(isPlanningDone).get();
                                transportPlanningDetail.setCatTrkPlanningState(catIsPlanningDone);
                            }
                            transportPlanningDetail.setModificationUser(userRegistration);
                            transportPlanningDetail.setModificationDate(LocalDateTime.now());
                            transportPlanningDetailRepository.save(transportPlanningDetail);

                        }
                    }

                    if (documentChassisDetailId != null) {
                        ChassisDocumentDetail chassisDocumentDetail = chassisDocumentDetailRepository.findById(documentChassisDetailId).get();
                        if (Objects.equals(catMovementId, isGateIn) && Boolean.TRUE.equals(input.getReject())) {
                            Catalog catIsDocChassisPending = catalogRepository.findById(isDocChassisPending).get();
                            chassisDocumentDetail.setCatStatusChassis(catIsDocChassisPending);
                            chassisDocumentDetail.setTraceChassisDocDetail(TRACE_GRALTRUCKDEPA_RECH2);
                        } else {
                            Catalog catIsDocChassisCompleted = catalogRepository.findById(isDocChassisCompleted).get();
                            chassisDocumentDetail.setCatStatusChassis(catIsDocChassisCompleted);
                            chassisDocumentDetail.setTraceChassisDocDetail("graltrkdeparture7");
                        }
                        chassisDocumentDetail.setModificationUser(userRegistration);
                        chassisDocumentDetail.setModificationDate(LocalDateTime.now());
                        chassisDocumentDetailRepository.save(chassisDocumentDetail);
                    }

                    response.setRespResult(1);


                }
            }

            Eir eirEdit = eirRepository.findOneById(input.getEirId());
            if (eirEdit != null) {
                eirEdit.setConfirmAddContainerNotMatch(input.getConfirmAddContainerNotMatch());
                eirRepository.save(eirEdit);
            }

            String pContainerNumber;
            String pEquipmentNotApplicable = "NOT APPLICA";
            if (Objects.equals(catEmptyFullId, isEmpty) && Objects.equals(catMovementId, isGateIn)) {
                if (response.getRespResult() != null && response.getRespResult() == 1) {
                    if (eirMultipleId == null) {
                        Eir eir = eirRepository.findOneById(input.getEirId());
                        pContainerNumber = eir.getContainer() != null ? eir.getContainer().getContainerNumber() : null;
                        if (pContainerNumber != null && !Objects.equals(pContainerNumber, pEquipmentNotApplicable)) {
                            EirSendAppeir eirSendAppeir = new EirSendAppeir();
                            eirSendAppeir.setEir(eir);
                            eirSendAppeir.setFlagSend('0');
                            eirSendAppeir.setRegistrationDate(LocalDateTime.now());
                            eirSendAppeirRepository.save(eirSendAppeir);
                        }
                    } else {

                        for (Eir eir : eirList.stream().filter(eir -> !Objects.equals(eir.getContainer().getContainerNumber(), pEquipmentNotApplicable)).toList()) {
                            EirSendAppeir eirSendAppeir = new EirSendAppeir();
                            eirSendAppeir.setEir(eir);
                            eirSendAppeir.setFlagSend('0');
                            eirSendAppeir.setRegistrationDate(LocalDateTime.now());
                            eirSendAppeirRepository.save(eirSendAppeir);
                        }
                    }
                }


            }


            if (containerId != null
                    && (!Objects.equals(containerId, containerNotApplicableId) && !Objects.equals(containerId, containerNoCntId))
                    && Objects.equals(catMovementId, isGateOut)) {
                List<Map<String, Object>> tRuleAps = eirRepository.gateGeneralAppointmentValidate(input.getSubBusinessUnitLocalId(), "gateout", null);

                Optional<Map<String, Object>> topRecord = tRuleAps.stream()
                        .filter(rule -> "aps".equals(rule.get("type_product_integration")))
                        .findFirst();
                if (topRecord.isPresent()) {
                    Eir eir = eirRepository.findOneById(input.getEirId());
                    eir.setAppointmentFlagOndepupdcnt(false);
                    eirRepository.save(eir);
                }
            }

        }


        if (response.getRespResult() != null && response.getRespResult() == 0) {
            Map<String, Object> resultRuleGeneralGetOut2 = systemRuleRepository.ruleGeneralGetOut(
                    businessUnitAlias,
                    "sd1_general_rule_by_business_unit",
                    "[\"truck_departure_ticket_notification\"]");

            String rAction2 = resultRuleGeneralGetOut2.get("r_action").toString();
            Eir eir = eirRepository.findOneById(input.getEirId());
            if (Objects.equals(rAction2, "true")) {
                EirNotification eirNotification = new EirNotification();
                eirNotification.setEir(eir);
                Catalog catEirNotificationPending = catalogRepository.findById(eirNotificationPending).get();
                eirNotification.setStatus(catEirNotificationPending);
                eirNotification.setActive(true);
                eirNotification.setRegistrationUser(userRegistration);
                eirNotification.setRegistrationDate(LocalDateTime.now());
                eirNotificationRepository.save(eirNotification);
            }
        }
        return response;
    }


    private String formatString(String input) {
        return input == null ? "" : input.trim().toUpperCase();
    }

    @Transactional
    public List<Map<String, Object>> validateYardIntegration(Integer subBusinessUnitLocalId, String subBusinessUnitLocalAlias, String typeProcess) {
        return eirRepository.validateYardIntegration(subBusinessUnitLocalId, subBusinessUnitLocalAlias, typeProcess);
    }

    @Transactional
    public SdgTruckDepartureRegisterOutput truckDepartureRegisterProcess(SdgTruckDepartureRegisterInput.Root inputRoot) throws Exception {
        SdgTruckDepartureRegisterInput.Input input = inputRoot.getSdg().getInput();
        List<Map<String, Object>> checkInt = validateYardIntegration(input.getSubBusinessUnitLocalId(), null, null);

        Map<String, Object> checkIntResponse = checkInt.getFirst();
        boolean flagRegister = false;
        boolean flagAssigment = false;

        Eir eir = eirRepository.findOneById(input.getEirId());

        if (eir != null && Objects.equals(eir.getCatMovement().getAlias(), Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS)) {
            flagAssigment = true;
            if (checkIntResponse.get(RESULT_KEY) == "true") {
                if (input.getContainerId() != null) {
                    String json = objectMapper.writeValueAsString(inputRoot);
                    SdgTruckDepartureRegisterBeforeYardInput.Root registerBeforeYardInput = objectMapper.readValue(json, SdgTruckDepartureRegisterBeforeYardInput.Root.class);
                    ResponseTruckDepartureRegisterBeforeYard responseTruckDepartureRegisterBeforeYard = truckDepartureRegisterBeforeYardService.registerBeforeYard(registerBeforeYardInput);

                    if (responseTruckDepartureRegisterBeforeYard.getRespResult() == 1) {
                        flagRegister = true;
                    } else {
                        throw new Exception();

                    }
                } else {
                    flagRegister = true;
                }
            } else {
                flagRegister = true;
            }
        } else {
            flagAssigment = false;
            flagRegister = true;
        }

        boolean rptaAssigment = false;

        if (flagRegister && flagAssigment) {

            rptaAssigment = this.implementAssigmentGateOut(inputRoot);

            if (!rptaAssigment) {
                throw new Exception();
            }

        } else {
            rptaAssigment = true;
        }


        if (flagRegister && rptaAssigment) {
            SdgTruckDepartureRegisterOutput sdgTruckDepartureRegisterOutput = register(inputRoot);
            return sdgTruckDepartureRegisterOutput;
        } else {
            throw new Exception();
        }
    }


    public boolean implementAssigmentGateOut(SdgTruckDepartureRegisterInput.Root inputRoot) throws Exception {
        SdgTruckDepartureRegisterInput.Input input = inputRoot.getSdg().getInput();
        Integer isForGateOutAssignation = input.getIsForGateOutAssignation();

        Map<String, Object> responseAssignGout;
        if (isForGateOutAssignation == 1) {
            responseAssignGout = gateoutGeneralAssignmentRegister(inputRoot, false, true);

            if (responseAssignGout != null) {
                if (responseAssignGout.get("result_state").equals(1)) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return true;
        }
    }

    public Map<String, Object> gateoutGeneralAssignmentRegister(SdgTruckDepartureRegisterInput.Root inputRoot, boolean autocommit, boolean avoidYard) throws Exception {
        SdgTruckDepartureRegisterInput.Input input = inputRoot.getSdg().getInput();
        Map<String, Object> responseAssignGout = eirRepository.gateoutGeneralAssignmentRegisterV2(input.getSubBusinessUnitLocalId(), input.getEirId(),
                input.getContainerId(), input.getChassisId(), input.getDocumentoCargaDetalleId(), input.getPlanningDetailId(),
                input.getPhotos(), input.getSeal1(), input.getSeal2(), input.getSeal3(), input.getSeal4(), input.getUserRegistrationId(),
                input.getLanguageId(), input.getRemarks(), "sd1_rule_integration_sdy", "gateout", input.getOperationCode());

        if (!avoidYard) {
            yardIntegrationRule(responseAssignGout, inputRoot);
        }

        return responseAssignGout;
    }

    public Object yardIntegrationRule(Map<String, Object> responseAssignGout, SdgTruckDepartureRegisterInput.Root inputRoot) throws Exception {
        SdgTruckDepartureRegisterInput.Input input = inputRoot.getSdg().getInput();
        // VALIDATE BUSINESS RULE
        if (responseAssignGout != null) {
            if (responseAssignGout.get(INTEGRATION_DATA) != null) {
                JSONArray integrationDataArray = new JSONArray(responseAssignGout.get(INTEGRATION_DATA).toString());
                JSONObject integrationData = (JSONObject) integrationDataArray.getJSONObject(0);
                if (integrationData.get("type_product_integration").equals("sdy")) {
                    String sdyToken = getSdyToken(loginUrl, user, password, system);
                    try {
                        // CREATE JSON OBJECT TO SEND TO SDY RESTPOINT
                        JSONObject bodySdy = new JSONObject();
                        JSONObject bodyF = new JSONObject();
                        JSONObject body = new JSONObject();
                        JSONArray containersArray = new JSONArray().put(new JSONObject()
                                .put("numero_contenedor", integrationData.get("container_number"))
                                .put("eir_numero", input.getEirId()));

                        body.put("usuario_id", input.getUserRegistrationId())
                                .put("unidad_negocio_id", input.getSubBusinessUnitLocalId())
                                .put("tipo_operacion", input.getOperationCode())
                                .put("proceso_realizado", "GO")
                                .put("contenedores", containersArray);
                        bodyF.put("F", body);
                        bodySdy.put("SDY", bodyF);

                        // CREATE HTTP CONNECTION
                        URL url = new URL(gateOutUrl);
                        url.toURI();
                        HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
                        httpConn.setRequestMethod("POST");
                        httpConn.setRequestProperty("Authorization", "Bearer " + sdyToken);
                        httpConn.setRequestProperty("Content-Type", "application/json");
                        httpConn.setDoOutput(true);

                        // SET REQUEST BODY
                        try (OutputStream os = httpConn.getOutputStream()) {
                            byte[] inputStream = bodySdy.toString().getBytes("utf-8");
                            os.write(inputStream, 0, inputStream.length);
                        }

                        // READ RESPONSE
                        try (BufferedReader br = new BufferedReader(new InputStreamReader(httpConn.getInputStream(), "utf-8"))) {
                            StringBuilder response = new StringBuilder();
                            String responseLine;
                            while ((responseLine = br.readLine()) != null) {
                                response.append(responseLine.trim());
                            }
                            JSONObject jsonResponse = new JSONObject(response.toString());

                            if (jsonResponse.getBoolean(IS_CORRECT) &&
                                    "Success".equals(jsonResponse.getString("status")) &&
                                    jsonResponse.get(RESULT_KEY) != null) {
                                JSONObject jsonResult = jsonResponse.getJSONObject(RESULT_KEY);
                                if (jsonResult.getBoolean(IS_CORRECT)) {
                                    integrationData.put("message", "SDY - Container successfully assigned to exit");
                                    integrationDataArray.put(0, integrationData);
                                    responseAssignGout.put(INTEGRATION_DATA, integrationDataArray.toString());
                                    return responseAssignGout;
                                } else {
                                    return BAD_RESPONSE;
                                }
                            } else {
                                return BAD_RESPONSE;
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        return BAD_RESPONSE;
                    }
                }
            }
        }
        return BAD_RESPONSE;
    }

    private String getSdyToken(String loginUrl, String username, String password, String system) throws Exception {

        JSONObject body = new JSONObject();
        body.put("username", username);
        body.put("password", password);
        body.put("system", system);

        // Configurar conexión HTTP
        URL url = new URL(loginUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        try {
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setDoOutput(true);

            // Escribir datos en el cuerpo de la solicitud
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = body.toString().getBytes("UTF-8");
                os.write(input, 0, input.length);
            }

            // Leer la respuesta
            int status = connection.getResponseCode();
            if (status >= HttpURLConnection.HTTP_OK && status < HttpURLConnection.HTTP_MULT_CHOICE) {
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                    JSONObject jsonResponse = new JSONObject(response.toString());

                    // Validar la respuesta y extraer el token
                    if (jsonResponse.getBoolean(IS_CORRECT) && jsonResponse.has("token")) {
                        return jsonResponse.getString("token");
                    } else {
                        throw new RuntimeException("Invalid response: " + jsonResponse.toString());
                    }
                }
            } else {
                throw new RuntimeException("HTTP Request Failed with status code: " + status);
            }
        } finally {
            connection.disconnect();
        }
    }
}
