package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.InspectionRegisterV2Input;
import com.maersk.sd1.sde.controller.dto.InspectionRegisterV2Output;
import com.maersk.sd1.sde.service.InspectionRegisterV2Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEInspeccionServiceImp")
public class InspectionRegisterV2Controller {

    private final InspectionRegisterV2Service inspectionRegisterV2Service;

    @Autowired
    public InspectionRegisterV2Controller(InspectionRegisterV2Service inspectionRegisterV2Service) {
        this.inspectionRegisterV2Service = inspectionRegisterV2Service;
    }

    @PostMapping("/sderegistrarInspeccionJsonV2")
    public ResponseEntity<ResponseController<InspectionRegisterV2Output>> registerInspection(@RequestBody InspectionRegisterV2Input.Root input) {
        if(input == null || input.getPrefix() == null || input.getPrefix().getInput() == null) {
            return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
        }
        try {
            InspectionRegisterV2Output output = inspectionRegisterV2Service.registerInspectionV2(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(new ResponseController<>(e.getMessage()));
        }
     }
}