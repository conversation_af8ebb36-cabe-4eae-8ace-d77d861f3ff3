package com.maersk.sd1.sde.dto;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TbDanoTmpDto {

    private int x; // IDENTITY

    private Integer item;

    private boolean esNuevo;

    private int estimadoEmrDetalleId;

    private Integer catEstimadoUbicacionDanoId;

    private Integer catEstimadoTipoDanoId;

    private Integer catEstimadoComponenteId;

    private Integer catEstimadoMetodoRepId;

    private Integer catEstimadoDimensionTipoId;

    private BigDecimal largo;

    private BigDecimal ancho;

    private Integer piezasReparar;

    private Integer catEstimadoAsumeCostoId;

    private String observacion;

    private String cedexMercCodigo;

    private String cedexMercDescripcion;

    private BigDecimal costoMaterialXPieza;

    private BigDecimal hhXPieza;

}
