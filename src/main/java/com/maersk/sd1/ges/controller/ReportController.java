package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.controller.dto.ReportListInput;
import com.maersk.sd1.ges.controller.dto.ReportListOutput;
import com.maersk.sd1.ges.service.ReportService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMReporteServiceImp")
public class ReportController {

    private static final Logger logger = LogManager.getLogger(ReportController.class.getName());

    private final ReportService reportService;

    @PostMapping("/gesreporteListar")
    public ResponseEntity<ResponseController<ReportListOutput>> listarReports(@Valid @RequestBody ReportListInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid payload structure."));
            }
            ReportListInput.Input input = request.getPrefix().getInput();
            ReportListOutput output = reportService.listReports(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while listing reports.", e);
            ReportListOutput output = new ReportListOutput();
            output.setRespMessage(e.toString());
            output.setRespStatus(0);
            output.setReports(null);
            output.setTotalRecords(0L);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}