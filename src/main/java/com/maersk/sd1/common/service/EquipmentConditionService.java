package com.maersk.sd1.common.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.dto.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.ges.service.GESCatalogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Service
public class EquipmentConditionService {

    private final EirRepository eirRepository;
    private final EirActivityZoneRepository eirActivityZoneRepository;
    private final GESCatalogService gesCatalogService;
    private final StockEmptyRepository stockEmptyRepository;
    private final StockFullRepository stockFullRepository;
    private final EmrInspectionRepository emrInspectionRepository;
    private final EirChassisZoneActivityRepository eirChassisZoneActivityRepository;
    private final EirChassisRepository eirChassisRepository;
    private final StockChassisRepository stockChassisRepository;

    @Autowired
    public EquipmentConditionService(EirRepository eirRepository,
                                     EirActivityZoneRepository eirActivityZoneRepository,
                                     GESCatalogService gesCatalogService,
                                     StockEmptyRepository stockEmptyRepository,
                                     StockFullRepository stockFullRepository,
                                     EmrInspectionRepository emrInspectionRepository,
                                     EirChassisZoneActivityRepository eirChassisZoneActivityRepository,
                                     EirChassisRepository eirChassisRepository,
                                     StockChassisRepository stockChassisRepository) {
        this.eirRepository = eirRepository;
        this.eirActivityZoneRepository = eirActivityZoneRepository;
        this.gesCatalogService = gesCatalogService;
        this.stockEmptyRepository = stockEmptyRepository;
        this.stockFullRepository = stockFullRepository;
        this.emrInspectionRepository = emrInspectionRepository;
        this.eirChassisZoneActivityRepository = eirChassisZoneActivityRepository;
        this.eirChassisRepository = eirChassisRepository;
        this.stockChassisRepository = stockChassisRepository;
    }

    public Integer getEquipmentCondition(Integer eirId,
                                         Integer catEquipmentCategoryId,
                                         String structureMachinery,
                                         String specificTask) {

        Integer catConditionId = null;

        List<String> catalogAliases = new ArrayList<>();

        catalogAliases.add(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS);
        catalogAliases.add(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
        catalogAliases.add(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);
        catalogAliases.add(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS);
        catalogAliases.add(Parameter.EQUIPMENT_CATEGORY_CHASSIS);
        catalogAliases.add(Parameter.EQUIPMENT_CATEGORY_CONTAINER);
        catalogAliases.add(Parameter.EQUIPMENT_CATEGORY_GENSET);
        catalogAliases.add(Parameter.EQUIPMENT_CATEGORY_UNDERSLUNG);
        catalogAliases.add(Parameter.STATUS_INSPECTION_FULL_PENDING);
        catalogAliases.add(Parameter.STATUS_INSPECTION_FULL_COMPLETED);
        catalogAliases.add(Parameter.STATUS_INSPECTION_FULL_CANCELED);
        catalogAliases.add(Parameter.CONDITION_EQUIPMENT_FULL_INSP);
        catalogAliases.add(Parameter.CONDITION_EQUIPMENT_FULL_OK);
        catalogAliases.add(Parameter.CONDITION_EQUIPMENT_FULL_DAM);
        catalogAliases.add(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_INSP);
        catalogAliases.add(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_DAM);
        catalogAliases.add(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_REP);
        catalogAliases.add(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OD);
        catalogAliases.add(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OW);
        catalogAliases.add(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OK);

        catalogAliases.add(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_SC);
        catalogAliases.add(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_DD);
        catalogAliases.add(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_DM);
        catalogAliases.add(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_DH);
        catalogAliases.add(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OS);
        catalogAliases.add(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OW);
        catalogAliases.add(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OH);
        catalogAliases.add(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OK);

        catalogAliases.add(Parameter.ACTIVITY_ZONE_EMPTY_INSP);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_EMPTY_PTI);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_EMPTY_REP);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_EMPTY_REPM);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_EMPTY_LAV);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_EMPTY_DD);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_EMPTY_DM);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_EMPTY_OK);

        catalogAliases.add(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_SINP);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_PTI);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_DD);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_DM);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_REP);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_REPM);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_LAV);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_OK);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_VEN);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_DRCH);

        catalogAliases.add(Parameter.STATUS_REPAIR_EMPTY_CONTAINER_PENDING);
        catalogAliases.add(Parameter.STATUS_REPAIR_EMPTY_CONTAINER_APPROVED);
        catalogAliases.add(Parameter.STATUS_REPAIR_EMPTY_CONTAINER_REJECTED);
        catalogAliases.add(Parameter.STATUS_REPAIR_EMPTY_CONTAINER_REPAIRED);
        catalogAliases.add(Parameter.STATUS_REPAIR_EMPTY_CONTAINER_REPAIRED_PROGRESS);

        catalogAliases.add(Parameter.STATUS_CLEANING_EMPTY_CONTAINER_CLEANED);
        catalogAliases.add(Parameter.STATUS_CLEANING_EMPTY_CONTAINER_PENDING);
        catalogAliases.add(Parameter.STATUS_CLEANING_EMPTY_CONTAINER_DIRTY);
        catalogAliases.add(Parameter.STATUS_CLEANING_EMPTY_CONTAINER_WET);
        catalogAliases.add(Parameter.STATUS_CLEANING_EMPTY_CONTAINER_CLEANING_PROGRESS);

        catalogAliases.add(Parameter.STRUCTURE_CONDITION_CHASSIS_INSP);
        catalogAliases.add(Parameter.STRUCTURE_CONDITION_CHASSIS_OK);
        catalogAliases.add(Parameter.STRUCTURE_CONDITION_CHASSIS_DAM);

        catalogAliases.add(Parameter.ZONE_ACTIVITY_CHASSIS_CHINSP);
        catalogAliases.add(Parameter.ZONE_ACTIVITY_CHASSIS_CHREP);
        catalogAliases.add(Parameter.ZONE_ACTIVITY_CHASSIS_CHOK);
        catalogAliases.add(Parameter.ZONE_ACTIVITY_CHASSIS_CHDAM);

        HashMap<String, Integer> catalogs = gesCatalogService.findIdsByAliasesIn(catalogAliases);

        EquipmentConditionEIRContainerDTO eirContainer = eirRepository.findEquipmentConditionEirContainerDetails(eirId);

        if (eirContainer.getCatEmptyFullId() != null) {

            if (catEquipmentCategoryId.equals(catalogs.get(Parameter.EQUIPMENT_CATEGORY_CONTAINER))) {

                if (eirContainer.getCatEmptyFullId().equals(catalogs.get(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))) {

                    if (specificTask.equals("INS") && eirContainer.getCatMovementId().equals(catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS))) {

                        if (structureMachinery.equals("S")) {

                            EquipmentConditionEIRActivityZoneDTO zoneActivityInsp = eirActivityZoneRepository.findEirActivityZoneByEirIdAndCatZoneActivity(eirId, catalogs.get(Parameter.ACTIVITY_ZONE_EMPTY_INSP));

                            if (zoneActivityInsp.getEirActivityZoneId() == null || (!zoneActivityInsp.getConcluded() && !zoneActivityInsp.getIsPartialInspection())) {
                                catConditionId = catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_SC);
                            } else {
                                catConditionId = zoneActivityInsp.getStructureDamagedResult() ? catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_DD) : catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OK);
                            }

                        } else if (structureMachinery.equals("M") && eirContainer.isReefer()) {

                            EquipmentConditionEIRActivityZoneDTO zoneActivityPTI = eirActivityZoneRepository.findEirActivityZoneByEirIdAndCatZoneActivity(eirId, catalogs.get(Parameter.ACTIVITY_ZONE_EMPTY_PTI));

                            if (zoneActivityPTI.getEirActivityZoneId() == null || (!zoneActivityPTI.getConcluded() && !zoneActivityPTI.getIsPartialInspection())) {
                                catConditionId = catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_SC);
                            } else {
                                catConditionId = zoneActivityPTI.getStructureDamagedResult() ? catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_DM) : catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OK);
                            }

                        }

                    } else if (specificTask.equals("DEL") && eirContainer.getCatMovementId().equals(catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS))) {

                        Integer eIRIn = stockEmptyRepository.findEIRInByEIROut(eirId);

                        if (structureMachinery.equals("S")) {

                            if (eirContainer.isStructureWithDamage()) {

                                catConditionId = catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_DD);

                            } else {

                                EquipmentConditionEIRActivityZoneDTO zoneActivityInsp = eirActivityZoneRepository.findEirActivityZoneByEirIdAndCatZoneActivity(eIRIn, catalogs.get(Parameter.ACTIVITY_ZONE_EMPTY_INSP));

                                catConditionId = (!zoneActivityInsp.getConcluded() && !zoneActivityInsp.getIsPartialInspection()) ? catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_SC) : catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OK);

                            }

                        } else if (structureMachinery.equals("M") && eirContainer.isReefer()) {

                            if (eirContainer.isMachineryWithDamage()) {

                                catConditionId = catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_DM);

                            } else {

                                EquipmentConditionEIRActivityZoneDTO zoneActivityPTI = eirActivityZoneRepository.findEirActivityZoneByEirIdAndCatZoneActivity(eIRIn, catalogs.get(Parameter.ACTIVITY_ZONE_EMPTY_PTI));

                                catConditionId = (!zoneActivityPTI.getConcluded() && !zoneActivityPTI.getIsPartialInspection()) ? catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_SC) : catalogs.get(Parameter.STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OK);
                            }

                        }

                    } else if (specificTask.equals("CUR") || specificTask.equals("GRAL")) {

                        if (eirContainer.getCatMovementId().equals(catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS))) {

                            eirId = stockEmptyRepository.findEIRInByEIROut(eirId);

                        }

                        EquipmentConditionEIRDTO eir = eirRepository.findEquipmentConditionEirDetails(eirId);

                        EquipmentConditionEIRActivityZoneDTO zoneActivityInsp = eirActivityZoneRepository.findEirActivityZoneByEirIdAndCatZoneActivity(eirId, catalogs.get(Parameter.ACTIVITY_ZONE_EMPTY_INSP));

                        EquipmentConditionEIRActivityZoneDTO zoneActivityPTI = eirActivityZoneRepository.findEirActivityZoneByEirIdAndCatZoneActivity(eirId, catalogs.get(Parameter.ACTIVITY_ZONE_EMPTY_PTI));

                        Integer catBoxConditionId = null;
                        Integer catMachineConditionId = null;

                        if (structureMachinery.equals("S") || specificTask.equals("GRAL")) {
                            if (!zoneActivityInsp.getConcluded()) {
                                catBoxConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_INSP);
                            } else if (!eir.getStructureWithDamage()) {
                                if (eir.getCatCleaningStatusId() != 0 && !eir.getCatCleaningStatusId().equals(catalogs.get(Parameter.STATUS_CLEANING_EMPTY_CONTAINER_CLEANED))) {
                                    catBoxConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OD);
                                } else {
                                    catBoxConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OK);
                                }
                            } else {
                                catBoxConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_DAM);
                            }
                        }

                        if (structureMachinery.equals("M") || specificTask.equals("GRAL")) {
                            if (!zoneActivityPTI.getConcluded()) {
                                if (!eirContainer.isReefer()) {
                                    catMachineConditionId = null;
                                } else {
                                    catMachineConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_INSP);
                                }
                            } else {
                                if (!eir.getMachineryWithDamage()) {
                                    if (specificTask.equals("GRAL") && eir.getCatCleaningStatusId() != 0 && !eir.getCatCleaningStatusId().equals(catalogs.get(Parameter.STATUS_CLEANING_EMPTY_CONTAINER_CLEANED))) {
                                        catMachineConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OD);
                                    } else {
                                        catMachineConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OK);
                                    }
                                } else {
                                    catMachineConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_DAM);
                                }
                            }
                        }

                        if (specificTask.equals("CUR")) {
                            catConditionId = structureMachinery.equals("S") ? catBoxConditionId : catMachineConditionId;
                        }

                        if (specificTask.equals("GRAL")) {
                            if (catBoxConditionId.equals(catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_INSP)) || (eirContainer.isReefer() && Objects.equals(catMachineConditionId, catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_INSP)))) {
                                catConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_INSP);
                            } else if (catBoxConditionId.equals(catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_DAM)) || (eirContainer.isReefer() && Objects.equals(catMachineConditionId, catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_DAM)))) {
                                catConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_DAM);
                            } else if (catBoxConditionId.equals(catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OD)) || (eirContainer.isReefer() && Objects.equals(catMachineConditionId, catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OD)))) {
                                catConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OD);
                            } else if (catBoxConditionId.equals(catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OK)) && (!eirContainer.isReefer() || Objects.equals(catMachineConditionId, catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OK)))) {
                                catConditionId = catalogs.get(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OK);
                            }
                        }
                    }

                } else if (eirContainer.getCatEmptyFullId().equals(catalogs.get(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS))) {

                    Integer eirIdSearch = eirId;

                    if (eirContainer.getCatMovementId().equals(catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS))) {

                        eirIdSearch = stockFullRepository.findEirInIdByEirOutId(eirId);

                    }

                    EquipmentConditionEMRInspectionDTO emrInspection = emrInspectionRepository.findTopByEirIdOrderByDateRegistrationDesc(eirIdSearch); //Validate EIR

                    if (emrInspection.getEmrInspectionId() == null || emrInspection.getCatStatusId().equals(catalogs.get(Parameter.STATUS_INSPECTION_FULL_CANCELED))) {
                        catConditionId = catalogs.get(Parameter.CONDITION_EQUIPMENT_FULL_INSP);
                    } else {
                        if (structureMachinery.equals("S")) {
                            if (emrInspection.getCatStatusId().equals(catalogs.get(Parameter.STATUS_INSPECTION_FULL_COMPLETED))) {
                                if (emrInspection.getFlagDamageBox()) {
                                    catConditionId = catalogs.get(Parameter.CONDITION_EQUIPMENT_FULL_DAM);
                                } else {
                                    catConditionId = catalogs.get(Parameter.CONDITION_EQUIPMENT_FULL_OK);
                                }
                            } else {
                                catConditionId = catalogs.get(Parameter.CONDITION_EQUIPMENT_FULL_INSP);
                            }
                        } else if (structureMachinery.equals("M") && eirContainer.isReefer()) {
                            if (emrInspection.getCatStatusId().equals(catalogs.get(Parameter.STATUS_INSPECTION_FULL_COMPLETED))) {
                                if (emrInspection.getFlagDamageMachine()) {
                                    catConditionId = catalogs.get(Parameter.CONDITION_EQUIPMENT_FULL_DAM);
                                } else {
                                    catConditionId = catalogs.get(Parameter.CONDITION_EQUIPMENT_FULL_OK);
                                }
                            } else {
                                catConditionId = catalogs.get(Parameter.CONDITION_EQUIPMENT_FULL_INSP);
                            }
                        }
                    }

                }

            } else if (catEquipmentCategoryId.equals(catalogs.get(Parameter.EQUIPMENT_CATEGORY_CHASSIS)) && eirContainer.getEirChassisId() != null) {

                if (specificTask.equals("INS") && eirContainer.getCatMovementId().equals(catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS))) {

                    EquipmentConditionEIRDChassisZoneActivityTO eirChassisZoneActivity = eirChassisZoneActivityRepository.findTopEirChassisZoneActivityByEirChassisIdAndCatChassisZoneActivityId(eirId, catalogs.get(Parameter.ZONE_ACTIVITY_CHASSIS_CHINSP));

                    if (eirChassisZoneActivity.getEirChassisZoneActivityId() == null || (!eirChassisZoneActivity.getCompleted() && !eirChassisZoneActivity.getIsPartialInspection())) {
                        catConditionId = catalogs.get(Parameter.STRUCTURE_CONDITION_CHASSIS_INSP);
                    } else {
                        catConditionId = eirChassisZoneActivity.getStructureChassisDamaged() ? catalogs.get(Parameter.STRUCTURE_CONDITION_CHASSIS_DAM) : catalogs.get(Parameter.STRUCTURE_CONDITION_CHASSIS_OK);
                    }
                } else if (specificTask.equals("DEL") && eirContainer.getCatMovementId().equals(catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS))) {

                    Boolean structureWithDamage = eirChassisRepository.findStructureWithDamageByEirChassisId(eirContainer.getEirChassisId());
                    Integer eirChassisGateinId = stockChassisRepository.findEirChassisGateinIdByEirChassisGateoutId(eirContainer.getEirChassisId());

                    if (structureWithDamage) {
                        catConditionId = catalogs.get(Parameter.STRUCTURE_CONDITION_CHASSIS_DAM);
                    } else {
                        EquipmentConditionEIRDChassisZoneActivityTO eirChassisZoneActivity = eirChassisZoneActivityRepository.findTopEirChassisZoneActivityByEirChassisIdAndCatChassisZoneActivityId(eirChassisGateinId, catalogs.get(Parameter.ZONE_ACTIVITY_CHASSIS_CHINSP));
                        catConditionId = (!eirChassisZoneActivity.getCompleted() && !eirChassisZoneActivity.getIsPartialInspection()) ? catalogs.get(Parameter.STRUCTURE_CONDITION_CHASSIS_INSP) : catalogs.get(Parameter.STRUCTURE_CONDITION_CHASSIS_OK);
                    }

                } else if (specificTask.equals("CUR") || specificTask.equals("GRAL")) {

                    if (eirContainer.getCatMovementId().equals(catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS))) {
                        eirContainer.setEirChassisId(stockChassisRepository.findEirChassisGateinIdByEirChassisGateoutId(eirId));
                    }

                    Boolean structureWithDamage = eirChassisRepository.findStructureWithDamageByEirChassisId(eirContainer.getEirChassisId());
                    EquipmentConditionEIRDChassisZoneActivityTO eirChassisZoneActivity = eirChassisZoneActivityRepository.findTopEirChassisZoneActivityByEirChassisIdAndCatChassisZoneActivityId(eirContainer.getEirChassisId(), catalogs.get(Parameter.ZONE_ACTIVITY_CHASSIS_CHINSP));
                    if (!eirChassisZoneActivity.getCompleted()) {
                        catConditionId = catalogs.get(Parameter.STRUCTURE_CONDITION_CHASSIS_INSP);
                    } else if (structureWithDamage) {
                        catConditionId = catalogs.get(Parameter.STRUCTURE_CONDITION_CHASSIS_DAM);
                    } else {
                        catConditionId = catalogs.get(Parameter.STRUCTURE_CONDITION_CHASSIS_OK);
                    }
                }

            }
        }

        return catConditionId;

    }

}
