package com.apm.business.service.external;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONObject;

import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;

import kong.unirest.Header;
import kong.unirest.HttpResponse;
import kong.unirest.RequestBodyEntity;
import kong.unirest.Unirest;

import lombok.Getter;
import lombok.Setter;
import ohSolutions.ohJpo.dao.JpoUtil;

@Getter
@Setter
public class APSUser {
	private String user;
	private String password;
	private String token;
	
	public APSUser(String user, String password) {
		this.user = user;
		this.password = password;
		this.token = getTokenAPI();
	}
	
	@SuppressWarnings("unchecked")
	private String getTokenAPI() {
		// Valores del SEG
		Map<String, Object> body_t = new HashMap<String, Object>();
		Map<String, Object> f = new HashMap<String, Object>();
		Map<String, String> data = new HashMap<String, String>();

		data.put("Usuario", this.user);
		data.put("ClaveMD5", this.password);
		data.put("Sistema_id", "11");

		f.put("F", data);
		body_t.put("SEG", f);

		// Valores del AUT
		Map<String, Object> f_aut = new HashMap<String, Object>();
		Map<String, String> data_aut = new HashMap<String, String>();

		data_aut.put("browser", "Chrome 96.0.4664.45");
		data_aut.put("clientId", "API_APM_INLANDNET");
		data_aut.put("clientSecret", "33caa750333af31d49d39e9251ecb592");
		data_aut.put("latitude", "");
		data_aut.put("longitude", "");
		data_aut.put("so", "Windows NT 4.0");

		f_aut.put("F", data_aut);
		body_t.put("AUT", f_aut);

		// Armar el JSON de envio
		Gson prettyGson = new GsonBuilder().setPrettyPrinting().create();
		String body_f = prettyGson.toJson(body_t);

		RequestBodyEntity resp;
		try {
			resp = Unirest.post(JpoUtil.getPropertie("SDE", "aps.api.token.url")).body(body_f);
			HttpResponse<String> response = resp.asString();
			if (response.isSuccess()) {
				JSONObject body = new JSONObject(response.getBody());
				if (!body.isEmpty()) {
					JSONArray result = body.getJSONArray("result");
					if (!result.isEmpty()) {
						List<Object> res = result.toList();
						res = (List<Object>) res.get(0);
						// Reasginar el valor para obtener el resultado
						res = (List<Object>) res.get(0);

						return res.get(res.size() - 1).toString();
					}
				}
			}
			return null;
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		return null;
	}
}
