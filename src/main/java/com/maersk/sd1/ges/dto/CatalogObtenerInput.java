package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class CatalogObtenerInput {

    @Data
    public static class Input {
        @JsonProperty("catalog_id")
        @NotNull(message = "catalog_id cannot be null")
        private Integer catalogId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}

