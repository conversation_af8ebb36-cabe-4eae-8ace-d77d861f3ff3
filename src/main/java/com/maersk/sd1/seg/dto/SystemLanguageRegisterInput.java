package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class SystemLanguageRegisterInput {
    @Data
    public static class Input {
        @JsonProperty("user_id")
        private Integer userId;

        @JsonProperty("language_id")
        private Integer languageId;

        @JsonProperty("system_id")
        private Integer systemId;
    }
    @Data
    public static class Prefix {
        @JsonProperty("F")
        private SystemLanguageRegisterInput.Input input;
    }
    @Data
    public static class Root {
        @JsonProperty("SEG")
        private SystemLanguageRegisterInput.Prefix prefix;
    }
}
