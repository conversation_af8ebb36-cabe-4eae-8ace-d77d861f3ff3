package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.BookingBlockCancellationRegisterInput;
import com.maersk.sd1.sde.dto.BookingBlockCancellationRegisterOutput;
import com.maersk.sd1.sde.service.BookingBlockCancellationRegisterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BookingBlockCancellationRegisterControllerTest {

    @Mock
    private BookingBlockCancellationRegisterService bookingBlockCancellationRegisterService;

    @InjectMocks
    private BookingBlockCancellationRegisterController bookingBlockCancellationRegisterController;

    private BookingBlockCancellationRegisterInput.Root request;

    @BeforeEach
    public void setUp() {
        request = new BookingBlockCancellationRegisterInput.Root();
        BookingBlockCancellationRegisterInput.Prefix prefix = new BookingBlockCancellationRegisterInput.Prefix();
        BookingBlockCancellationRegisterInput.Input input = new BookingBlockCancellationRegisterInput.Input();
        input.setBusinessUnitId(1);
        input.setSubBusinessUnitId(1);
        input.setCatType(1);
        input.setCatReason(1);
        input.setComment("Test comment");
        input.setUserRegistrationId(1);
        input.setDocumentCargoId(1);
        input.setLanguageId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);
    }

    @Test
    void testRegisterCancelBlockSuccess() {
        BookingBlockCancellationRegisterOutput output = new BookingBlockCancellationRegisterOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");
        when(bookingBlockCancellationRegisterService.registerCancelBookingBlock(any(BookingBlockCancellationRegisterInput.Input.class)))
                .thenReturn(output);

        ResponseEntity<ResponseController<BookingBlockCancellationRegisterOutput>> response =
                bookingBlockCancellationRegisterController.registerCancelBlock(request);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testRegisterCancelBlockBadRequest() {
        ResponseEntity<ResponseController<BookingBlockCancellationRegisterOutput>> response =
                bookingBlockCancellationRegisterController.registerCancelBlock(null);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals("Invalid request received", Objects.requireNonNull(response.getBody()).getResult().getRespMensaje());
    }

    @Test
    void testRegisterCancelBlockInternalServerError() {
        when(bookingBlockCancellationRegisterService.registerCancelBookingBlock(any(BookingBlockCancellationRegisterInput.Input.class)))
                .thenThrow(new RuntimeException("Internal Server Error"));

        ResponseEntity<ResponseController<BookingBlockCancellationRegisterOutput>> response =
                bookingBlockCancellationRegisterController.registerCancelBlock(request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals("java.lang.RuntimeException: Internal Server Error", Objects.requireNonNull(response.getBody()).getResult().getRespMensaje());
    }
}