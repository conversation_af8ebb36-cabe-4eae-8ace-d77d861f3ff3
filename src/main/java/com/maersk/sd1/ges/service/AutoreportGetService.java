package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.AutoreportGetDataDTO;
import com.maersk.sd1.ges.controller.dto.AutoreportGetOutput;
import com.maersk.sd1.common.repository.AutomaticReportRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Service
public class AutoreportGetService {

    private static final Logger logger = LogManager.getLogger(AutoreportGetService.class);

    private final AutomaticReportRepository automaticReportRepository;

    public AutoreportGetService(AutomaticReportRepository automaticReportRepository) {
        this.automaticReportRepository = automaticReportRepository;
    }

    @Transactional(readOnly = true)
    public AutoreportGetOutput getAutoreport(Integer autoreportId) {
        AutoreportGetOutput output = new AutoreportGetOutput();
        try {
            Optional<AutoreportGetDataDTO> optionalData = automaticReportRepository.findAutoreportDetailsById(autoreportId);
            if (optionalData.isPresent()) {
                AutoreportGetDataDTO data = optionalData.get();

                String startDateConverted = (data.getReportStartDate() == null) ? null
                        : customFnDateTimeGet(data.getReportStartDate());
                String endDateConverted = (data.getReportEndDate() == null) ? null
                        : customFnDateTimeGet(data.getReportEndDate());

                output.setRespStatus(1);
                output.setRespMessage("Data fetched successfully.");
                output.setAutomaticReportId(data.getAutomaticReportId());
                output.setSubBusinessUnitId(data.getSubBusinessUnitId());
                output.setReportStartDateConverted(startDateConverted);
                output.setReportEndDateConverted(endDateConverted);
                output.setRecurrency(data.getRecurrency());
                output.setColumns(data.getColumns());
                output.setFilterParams(data.getFilterParams());
                output.setAlias(data.getAlias());
                output.setEmailTemplateId(data.getEmailTemplateId());
                output.setReportId(data.getReportId());
                output.setDepotId(data.getDepotId());
                output.setAzureStorageConfigId(data.getAzureStorageConfigId());
                output.setAzureStorageConfigUniqueId(data.getAzureStorageConfigUniqueId());
                output.setAzurePath(data.getAzurePath());
                output.setActive(data.getActive());
                output.setEmailSubject(data.getEmailSubject());
            } else {
                output.setRespStatus(0);
                output.setRespMessage("No AutomaticReport found for the given ID.");
            }
        } catch (Exception e) {
            logger.error("Error fetching autoreport data", e);
            output.setRespStatus(0);
            output.setRespMessage("An error occurred: " + e.getMessage());
        }
        return output;
    }

    private String customFnDateTimeGet(LocalDateTime dateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return (dateTime != null) ? dateTime.format(formatter) : null;
    }
}