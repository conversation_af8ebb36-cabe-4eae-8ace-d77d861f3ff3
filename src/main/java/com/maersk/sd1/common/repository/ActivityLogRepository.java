package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.ActivityLog;
import com.maersk.sd1.sds.dto.ActivityLogSubListItemDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface ActivityLogRepository extends JpaRepository<ActivityLog, Integer> {
    ActivityLog findByEirNumber(Integer eirNumber);//ask about this

    @Query(value = "SELECT new com.maersk.sd1.sds.dto.ActivityLogSubListItemDTO( " +
            "a.id, " +
            "a.activityAlias, " +
            "a.catStatus.id, " +
            "a.catStatus.description, " +
            "a.catStatus.alias, " +
            "a.inputData, " +
            "a.outputData, " +
            "a.registrationUser.id, " +
            "a.registrationDate, " +
            "a.registrationUser.names, " +
            "a.registrationUser.firstLastName) " +
            "FROM ActivityLog a " +
            "WHERE a.activityLogReference.id = :activityLogId AND a.active = '1'",
            countQuery = "SELECT count(a) FROM ActivityLog a WHERE a.activityLogReference.id = :activityLogId AND a.active = '1'")
    Page<ActivityLogSubListItemDTO> findActivityLogSubList(@Param("activityLogId") Integer activityLogId, Pageable pageable);

    @Query("SELECT COUNT(a) FROM ActivityLog a WHERE a.activityLogReference = :reference AND a.active = '0'")
    Integer countReferences(@Param("reference") ActivityLog reference);

    @Query("SELECT a FROM ActivityLog a "
            + " JOIN a.subBusinessUnit un "
            + " JOIN a.catStatus cat "
            + " JOIN a.registrationUser USUC "
            + " LEFT JOIN a.modificationUser USUD "
            + " WHERE a.active = '1' "
            + "   AND a.activityLogReference IS NULL "
            + "   AND (:activityLogId IS NULL OR a.id = :activityLogId) "
            + "   AND (:activityAlias IS NULL OR a.activityAlias = :activityAlias) "
            + "   AND (:subBusinessUnitId IS NULL OR un.id = :subBusinessUnitId) "
            + "   AND (:eirNumber IS NULL OR a.eirNumber = :eirNumber) "
            + "   AND (:containerNumber IS NULL OR a.containerNumber LIKE CONCAT('%', :containerNumber, '%')) "
            + "   AND (:chassisNumber IS NULL OR a.chassisNumber LIKE CONCAT('%', :chassisNumber, '%')) "
            + "   AND (:catStatusId IS NULL OR cat.id = :catStatusId) "
            + "   AND (:dataInput IS NULL OR a.inputData LIKE CONCAT('%', :dataInput, '%')) "
            + "   AND (:dataOutput IS NULL OR a.outputData LIKE CONCAT('%', :dataOutput, '%')) "
            + "   AND (:userRegistration IS NULL OR CONCAT(USUC.names, ' ', USUC.firstLastName) LIKE CONCAT('%', :userRegistration, '%')) "
            + "   AND (:userModification IS NULL OR CONCAT(USUD.names, ' ', USUD.firstLastName) LIKE CONCAT('%', :userModification, '%')) "
            + "   AND (a.activityAlias IN :allowedAliases) "
            + " ORDER BY a.id DESC")
    List<ActivityLog> search(@Param("activityLogId") Integer activityLogId,
                             @Param("activityAlias") String activityAlias,
                             @Param("subBusinessUnitId") Integer subBusinessUnitId,
                             @Param("eirNumber") Integer eirNumber,
                             @Param("containerNumber") String containerNumber,
                             @Param("chassisNumber") String chassisNumber,
                             @Param("catStatusId") Integer catStatusId,
                             @Param("dataInput") String dataInput,
                             @Param("dataOutput") String dataOutput,
                             @Param("userRegistration") String userRegistration,
                             @Param("userModification") String userModification,
                             @Param("allowedAliases") Iterable<String> allowedAliases);

    @Query("SELECT a FROM ActivityLog a " +
            "WHERE a.id = :activityLogId " +
            "OR a.activityLogReference.id = :activityLogId " +
            "OR (a.containerNumber IS NOT NULL AND a.containerNumber <> 'NO-CNT' AND a.containerNumber = :containerNumber " +
            "   AND a.eirNumber IS NOT NULL AND a.eirNumber = :eirNumber " +
            "   AND a.activityAlias = :activityAlias " +
            "   AND a.subBusinessUnit.id = :subBusinessUnitId " +
            "   AND a.moduleAlias = :moduleAlias)")
    List<ActivityLog> findActivityLogsForUpdate(
            @Param("activityLogId") Integer activityLogId,
            @Param("containerNumber") String containerNumber,
            @Param("eirNumber") Integer eirNumber,
            @Param("activityAlias") String activityAlias,
            @Param("subBusinessUnitId") Long subBusinessUnitId,
            @Param("moduleAlias") String moduleAlias);


    @Modifying
    @Transactional
    @Query("UPDATE ActivityLog al " +
            "SET al.modificationUser.id = :userModificationId, " +
            "    al.modificationDate = :modificationDate, " +
            "    al.catStatus.id = :disabledLogStatus, " +
            "    al.retryable = false, " +
            "    al.active = '0' " +
            "WHERE al.id = :activityLogId OR al.activityLogReference.id = :activityLogId")
    int disableActivityLog(@Param("userModificationId") Integer userModificationId,
                           @Param("modificationDate") LocalDateTime modificationDate,
                           @Param("disabledLogStatus") Integer disabledLogStatus,
                           @Param("activityLogId") Integer activityLogId);

    @Query("select a from ActivityLog a where a.eirNumber = :eirId")
    Optional<ActivityLog> findByEir(@Param("eirId") Integer eirId);
}