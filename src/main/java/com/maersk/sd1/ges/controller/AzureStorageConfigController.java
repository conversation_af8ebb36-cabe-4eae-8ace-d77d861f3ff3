package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.controller.dto.AzureStorageConfigGetInput;
import com.maersk.sd1.ges.controller.dto.AzureStorageConfigGetOutput;
import com.maersk.sd1.ges.service.AzureStorageConfigService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMAzureStorageConfigServiceImp")
public class AzureStorageConfigController {

    private static final Logger logger = LogManager.getLogger(AzureStorageConfigController.class);

    private final AzureStorageConfigService azureStorageConfigService;

    public AzureStorageConfigController(AzureStorageConfigService azureStorageConfigService) {
        this.azureStorageConfigService = azureStorageConfigService;
    }

    @PostMapping("/gesazureStorageConfigObtener")
    public ResponseEntity<ResponseController<AzureStorageConfigGetOutput>> getAzureStorageConfig(@RequestBody @Valid AzureStorageConfigGetInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure"));
            }
            Integer azureStorageConfigId = request.getPrefix().getInput().getAzureStorageConfigId();
            if(azureStorageConfigId == null) {
                return ResponseEntity.status(400).body(new ResponseController<>("azureStorageConfigId should not be null"));
            }
            AzureStorageConfigGetOutput output = azureStorageConfigService.getAzureStorageConfig(azureStorageConfigId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while fetching AzureStorageConfig.", e);
            AzureStorageConfigGetOutput output = new AzureStorageConfigGetOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}