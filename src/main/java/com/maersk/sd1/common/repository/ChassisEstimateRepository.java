package com.maersk.sd1.common.repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import com.maersk.sd1.sds.dto.TbChaEstimateDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import com.maersk.sd1.common.model.ChassisEstimate;

public interface ChassisEstimateRepository extends JpaRepository<ChassisEstimate, Integer> {
    @Query("select c from ChassisEstimate c where c.eirChassis.id = :eirChassisId and c.catChaestimStatus.id NOT IN :statusIdsList and c.active = true ")
    List<ChassisEstimate> findByEirChassisIdAndNotInChaestimStatusList(@Param("eirChassisId") Integer eirChassisId, @Param("statusIdsList") List<Integer> statusIdsList);

    @Query("select c from ChassisEstimate c where c.eirChassis.id = :eirChassisId and c.catChaestimStatus.id IN :statusIdsList and c.active = true ")
    List<ChassisEstimate> findByEirChassisIdAndInChaestimStatusList(@Param("eirChassisId") Integer eirChassisId, @Param("statusIdsList") List<Integer> statusIdsList);

    @Query("select c from ChassisEstimate c where c.id = :id and c.active = true")
    ChassisEstimate findByIdAndActiveTrue(@Param("id") Integer id);

    @Query("""
    SELECT ce FROM ChassisEstimate ce
    WHERE ce.eirChassis.id IN :eirChassisIds
    AND ce.active = true
    AND ce.chassisEstimateIssueDate = (
        SELECT MAX(ce1.chassisEstimateIssueDate)
        FROM ChassisEstimate ce1
        WHERE ce1.eirChassis.id = ce.eirChassis.id
        AND ce1.active = true
    )
""")
    List<ChassisEstimate> findLatestChassisEstimates(@Param("eirChassisIds") Set<Integer> eirChassisIds);

    Optional<ChassisEstimate> findByIdAndActive(Integer id, boolean active);

    @Query(value = "select new com.maersk.sd1.sds.dto.TbChaEstimateDTO(" +
            "e.id, " +
            "e.catChaestimStatus.description) " +
            "from ChassisEstimate e where e.eirChassis.id = :eirChassisId " +
            "and e.catChaestimStatus.id not in (:chaestStatusRejected, :chaestStatusCreated, :chaestStatusFinalized ) " +
            "and e.active = true")
    List<TbChaEstimateDTO> findEstimatesByEirStatusNotIn(@Param("eirChassisId") Integer eirChassisId, @Param("chaestStatusRejected") Integer chaestStatusRejected,
                                                         @Param("chaestStatusCreated") Integer chaestStatusCreated, @Param("chaestStatusFinalized") Integer chaestStatusFinalized);

    @Query(value = "select new com.maersk.sd1.sds.dto.TbChaEstimateDTO(" +
            "e.id, " +
            "null) " +
            "from ChassisEstimate e where e.eirChassis.id = :eirChassisId " +
            "and e.catChaestimStatus.id  in (:chaestStatusRejected, :chaestStatusCreated, :chaestStatusFinalized ) " +
            "and e.active = true")
    List<TbChaEstimateDTO> findEstimatesByEirStatusIn(@Param("eirChassisId") Integer eirChassisId, @Param("chaestStatusRejected") Integer chaestStatusRejected,
                                                      @Param("chaestStatusCreated") Integer chaestStatusCreated, @Param("chaestStatusFinalized") Integer chaestStatusFinalized);

    @Query("select c from ChassisEstimate c where c.id in :estimatesIds and c.active = true")
    List<ChassisEstimate> findByIds(@Param("estimatesIds") List<Integer> estimatesIds);
}