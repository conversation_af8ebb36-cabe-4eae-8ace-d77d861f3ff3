package com.maersk.sd1.common.integration;

import org.springframework.stereotype.Component;

@Component
public class SD1IntegrationConnectionFactoryImpl implements SD1IntegrationConnectionFactory {
    @Override
    public SD1IntegrationConnection createConnection(String loginUrl, String user, String password, String system) {
        return new SD1IntegrationConnection(loginUrl, user, password, system);
    }
}
