package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Term;
import org.springframework.data.jpa.repository.JpaRepository;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface TermRepository extends JpaRepository<Term, Integer> {

    @Query("select concat(m.title, ' v', t.version) from Term t join t.menu m join t.businessUnit bu where bu.id = :businessUnitId")
    List<String> findTermInfoByBusinessUnitId(@Param("businessUnitId") Integer businessUnitId);

    @Modifying
    @Query("DELETE FROM Term t WHERE t.businessUnit.id = :unidadNegocioId")
    void deleteTermByBusinessUnit(@Param("unidadNegocioId") Integer unidadNegocioId);

    @Query("SELECT DISTINCT t.businessUnit.id FROM Term t " +
            "JOIN Menu m ON m.id = t.menu.id " +
            "JOIN RoleMenu rm ON rm.id.menuId = m.id " +
            "JOIN Role ro ON ro.id = rm.id.roleId " +
            "JOIN UserRole ur ON ur.id.roleId = ro.id " +
            "JOIN User uu ON uu.id = ur.id.userId " +
            "WHERE m.parentMenu IS NULL AND t.status = '1' AND m.status = true AND ro.status = true AND uu.id = :userId")
    List<Integer> findBusinessUnitsForTerms(Long userId);
}