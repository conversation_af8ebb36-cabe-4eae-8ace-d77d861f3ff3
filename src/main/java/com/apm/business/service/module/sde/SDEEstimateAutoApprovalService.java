package com.apm.business.service.module.sde;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEEstimateAutoApprovalService {

	@RequestMapping(value = "/sdeestimateAutoApprovalConfigList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeestimateAutoApprovalConfigList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.estimate_auto_approval_config_list","SDE");
			pResult.input("sub_business_unit_id", Jpo.DECIMAL);
			pResult.input("estimate_auto_approval_config_id", Jpo.INTEGER);
			pResult.input("sub_business_local_id", Jpo.DECIMAL);
			pResult.input("cat_estimate_type_id", Jpo.DECIMAL);
			pResult.input("cat_equipment_category_id", Jpo.DECIMAL);
			pResult.input("cat_equipment_type", Jpo.DECIMAL);
			pResult.input("threshold_value", Jpo.DECIMAL);
			pResult.input("active", Jpo.CHARACTER);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.input("page", Jpo.INTEGER);
			pResult.input("size", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeestimateAutoApprovalConfigRegister", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeestimateAutoApprovalConfigRegister(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.estimate_auto_approval_config_register","SDE");
			pResult.input("sub_business_local_id", Jpo.DECIMAL);
			pResult.input("structure_estimate_flag", Jpo.CHARACTER);
			pResult.input("machinery_estimate_flag", Jpo.CHARACTER);
			pResult.input("cat_equipment_category_id", Jpo.DECIMAL);
			pResult.input("cat_equipment_type", Jpo.DECIMAL);
			pResult.input("threshold_value", Jpo.DECIMAL);
			pResult.input("shipping_line_id", Jpo.INTEGER);
			pResult.input("currency_id", Jpo.INTEGER);
			pResult.input("customer_id", Jpo.INTEGER);
			pResult.input("user_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.output("resp_state", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeestimateAutoApprovalConfigEdit", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeestimateAutoApprovalConfigEdit(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.estimate_auto_approval_config_edit","SDE");
			pResult.input("estimate_auto_approval_config_id", Jpo.INTEGER);
			pResult.input("sub_business_local_id", Jpo.DECIMAL);
			pResult.input("structure_estimate_flag", Jpo.CHARACTER);
			pResult.input("machinery_estimate_flag", Jpo.CHARACTER);
			pResult.input("cat_equipment_category_id", Jpo.DECIMAL);
			pResult.input("cat_equipment_type", Jpo.DECIMAL);
			pResult.input("threshold_value", Jpo.DECIMAL);
			pResult.input("shipping_line_id", Jpo.INTEGER);
			pResult.input("currency_id", Jpo.INTEGER);
			pResult.input("customer_id", Jpo.INTEGER);
			pResult.input("active", Jpo.CHARACTER);
			pResult.input("user_id", Jpo.DECIMAL);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.output("resp_state", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeestimateAutoApprovalConfigGet", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeestimateAutoApprovalConfigGet(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.estimate_auto_approval_config_get","SDE");
			pResult.input("auto_approval_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeestimateAutoApprovalConfigChangeStatus", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeestimateAutoApprovalConfigChangeStatus(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.estimate_auto_approval_config_change_status","SDE");
			pResult.input("estimate_auto_approval_config_ids", Jpo.STRING);
			pResult.input("active", Jpo.CHARACTER);
			pResult.input("user_id", Jpo.INTEGER);
			pResult.input("language_id", Jpo.INTEGER);
			pResult.output("resp_state", Jpo.INTEGER);
			pResult.output("resp_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}