package com.maersk.sd1.sde.controller;

import com.maersk.sd1.sde.service.EirNotificationService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/notifications")
public class EirNotificationController {


    private final EirNotificationService service;

//    @GetMapping
//    public List<EirNotification> getAllNotifications() {
//        return service.getAllNotifications();
//    }
//
//    @GetMapping("/{id}")
//    public ResponseEntity<EirNotification> getNotificationById(@PathVariable int id) {
//        return service.getNotificationById(id)
//                .map(ResponseEntity::ok)
//                .orElse(ResponseEntity.notFound().build());
//    }
//
//    @PostMapping
//    public EirNotification createNotification(@RequestBody EirNotification notification) {
//        return service.createNotification(notification);
//    }
//
//    @PutMapping("/{id}")
//    public ResponseEntity<EirNotification> updateNotification(@PathVariable int id, @RequestBody EirNotification notificationDetails) {
//        return ResponseEntity.ok(service.updateNotification(id, notificationDetails));
//    }
//
//    @DeleteMapping("/{id}")
//    public ResponseEntity<Void> deleteNotification(@PathVariable int id) {
//        service.deleteNotification(id);
//        return ResponseEntity.noContent().build();
//    }

}
