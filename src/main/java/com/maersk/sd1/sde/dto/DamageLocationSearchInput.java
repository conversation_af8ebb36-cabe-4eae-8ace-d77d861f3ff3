package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

public class DamageLocationSearchInput {

    @Data
    public static class Input {

        @JsonProperty("damage_location")
        @Size(max = 100, message = "Damage location must be at most 100 characters long.")
        private String damageLocation;

        @JsonProperty("type_container")
        @NotNull(message = "type_container must not be null.")
        private String typeContainer;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
