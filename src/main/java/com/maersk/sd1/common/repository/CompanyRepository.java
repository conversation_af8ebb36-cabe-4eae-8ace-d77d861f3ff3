package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.seg.controller.dto.UserGetEditOutput;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface CompanyRepository extends JpaRepository<Company, Integer> {

    @Query("SELECT c.legalName FROM Company c WHERE c.id = :shipperNameId")
    String findLegalNameById(@Param("shipperNameId") Integer shipperNameId);

    @Query("SELECT COUNT(c) > 0 FROM Company c WHERE c.document = :document")
    Boolean existsByDocument(@Param("document") String document);

    @Query("SELECT c FROM Company c WHERE c.id = :companyId")
    Company findByCompanyId(Integer companyId);

    @Query("SELECT c.id FROM Company c WHERE c.document = :document")
    Optional<Integer> findTopByDocument(@Param("document") String document);

    String findDocumentById(Integer id);

    @Query(value = "select count(c) from Company c where c.companyAlias = :alias and c.status = true ")
    Integer countByAliasAndActiveTrue(@Param("alias") String alias);

    @Query(value = "select c from Company c where c.companyAlias = :alias and c.status = true ")
    Optional<Company> findByAliasAndActiveTrue(@Param("alias") String alias);

    @Query(value = "select count(c) from Company c where c.legalName = :legalName and c.status = true ")
    Integer countByLegalNameAndActiveTrue(@Param("legalName") String legalName);

    @Query(value = "select c from Company c where c.legalName = :legalName and c.status = true ")
    Optional<Company> findByLegalNameAndActiveTrue(@Param("legalName") String legalName);

    @Query(value = "select count(c) from Company c where c.legalName = :legalName and c.companyAlias= :alias and c.status = true ")
    Integer countByLegalNameAndAliasAndActiveTrue(@Param("legalName") String legalName, @Param("alias") String alias);

    @Query(value = "select c from Company c where c.legalName = :legalName and c.companyAlias= :alias and c.status = true ")
    Optional<Company> findByLegalNameAndAliasAndActiveTrue(@Param("legalName") String legalName, @Param("alias") String alias);

    @Query(value = "select count(c) from Company c where c.legalName = :legalName and c.companyAlias= :alias and c.status = true and c.document= :document ")
    Integer countByLegalNameAndAliasAndDocumentAndActiveTrue(@Param("legalName") String legalName, @Param("alias") String alias,  @Param("document") String document);

    @Query(value = "select c from Company c where c.legalName = :legalName and c.companyAlias= :alias and c.status = true and c.document= :document ")
    Optional<Company> findByLegalNameAndAliasAndDocumentAndActiveTrue(@Param("legalName") String legalName, @Param("alias") String alias,  @Param("document") String document);

    @Query("SELECT COUNT(e) FROM Company e WHERE " +
            "UPPER(TRIM(REPLACE(e.legalName, ' S.A.C.', ''))) = :name OR " +
            "UPPER(TRIM(REPLACE(e.legalName, ' S A C', ''))) = :name OR " +
            "UPPER(TRIM(REPLACE(e.legalName, ' SAC', ''))) = :name OR " +
            "UPPER(TRIM(REPLACE(e.legalName, ' SOCIEDAD ANONIMA CERRADA', ''))) = :name AND e.status = true")
    Integer countByNombreFiltrado(@Param("name") String name);

    @Query("SELECT e FROM Company e WHERE " +
            "UPPER(TRIM(REPLACE(e.legalName, ' S.A.C.', ''))) = :name OR " +
            "UPPER(TRIM(REPLACE(e.legalName, ' S A C', ''))) = :name OR " +
            "UPPER(TRIM(REPLACE(e.legalName, ' SAC', ''))) = :name OR " +
            "UPPER(TRIM(REPLACE(e.legalName, ' SOCIEDAD ANONIMA CERRADA', ''))) = :name AND e.status = true")
    Optional<Company> findByFilteredName(@Param("name") String name);

    @Query("SELECT COUNT(e) FROM Company e WHERE " +
            "UPPER(TRIM(REPLACE(e.legalName, ' S.A.', ''))) = :name AND e.companyAlias = :alias AND e.status = true")
    Integer countByNameAndAlias(@Param("name") String name, @Param("alias") String alias);

    @Query("SELECT e FROM Company e WHERE " +
            "UPPER(TRIM(REPLACE(e.legalName, ' S.A.', ''))) = :name AND e.companyAlias = :alias AND e.status = true")
    Optional<Company> findByNameAndAlias(@Param("name") String name, @Param("alias") String alias);

    @Query("SELECT COUNT(e) FROM Company e WHERE " +
            "UPPER(TRIM(REPLACE(e.legalName, '.', ''))) = :name AND e.status = true")
    Integer countByExactName(@Param("name") String name);

    @Query("SELECT e FROM Company e WHERE " +
            "UPPER(TRIM(REPLACE(e.legalName, '.', ''))) = :name AND e.status = true")
    Optional<Company> findByExactName(@Param("name") String name);

    @Query("SELECT COUNT(e) FROM Company e WHERE e.document LIKE CONCAT(:prefix, '%')")
    Integer countByDocumentoPrefix(@Param("prefix") String prefix);

    @Query("SELECT e.id FROM Company e WHERE e.document IN :documents")
    Integer findDummyCustomerId(@Param("documents") List<String> documents);

    @Modifying
    @Query("UPDATE Company e SET e.companyAlias = :alias WHERE e.id = :id AND (e.companyAlias IS NULL OR e.companyAlias = '')")
    void updateAliasIfEmpty(@Param("alias") String alias, @Param("id") Integer id);

    Company findFirstByDocumentIgnoreCase(String document);

    @Query("select comp.legalName from Company comp join comp.businessUnit b where b.id = :businessUnitId")
    List<String> findCompanyLegalNamesByBusinessUnitId(@Param("businessUnitId") Integer businessUnitId);

    @Query("SELECT new com.maersk.sd1.seg.controller.dto.UserGetEditOutput$AssociatedCompanyDTO(" +
            " c.id, c.status, c.legalName, c.document, c.catDocumentType.description, c.businessUnit.name ) " +
            "FROM Company c WHERE c.id IN :ids")
    List<UserGetEditOutput.AssociatedCompanyDTO> findAssociatedCompaniesByIds(@Param("ids") List<Integer> ids);

    @Query(value = "exec ges.empresa_listar_cliente :businessUnitId", nativeQuery = true)
    List<Object[]> getCompanyList(@Param("businessUnitId") Integer businessUnitId);

    @Query("SELECT emp FROM Company emp " +
            "LEFT JOIN emp.businessUnit uni " +
            "LEFT JOIN emp.catDocumentType catd " +
            "LEFT JOIN CompanyRole er ON er.company.id = emp.id " +
            "LEFT JOIN Catalog ctc ON ctc.id = er.catRoleType.id " +
            "WHERE (:empresa_id IS NULL OR emp.id = :empresa_id) " +
            "AND (:documento IS NULL OR emp.document LIKE %:documento%) " +
            "AND (:razon_social IS NULL OR emp.legalName LIKE %:razon_social%) " +
            "AND (:razon_comercial IS NULL OR emp.commercialName LIKE %:razon_comercial%) " +
            "AND (:direccion IS NULL OR emp.address LIKE %:direccion%) " +
            "AND (:longitud IS NULL OR emp.longitude LIKE %:longitud%) " +
            "AND (:latitud IS NULL OR emp.latitude LIKE %:latitud%) " +
            "AND (:estado IS NULL OR emp.status = :estado) " +
            "AND ((:fecha_registro_min IS NULL AND :fecha_registro_max IS NULL) OR emp.registrationDate BETWEEN :fecha_registro_min AND :fecha_registro_max) " +
            "AND ((:fecha_modificacion_min IS NULL AND :fecha_modificacion_max IS NULL) OR emp.modificationDate BETWEEN :fecha_modificacion_min AND :fecha_modificacion_max) " +
            "AND (:unidad_negocio_id IS NULL OR emp.businessUnit.id = :unidad_negocio_id) " +
            "AND (:tipo_documento IS NULL OR emp.catDocumentType.id = :tipo_documento) " +
            "AND (:suspendido IS NULL OR emp.suspended = :suspendido) " +
            "AND (:telefono IS NULL OR emp.phone LIKE %:telefono%) " +
            "AND (:correo IS NULL OR emp.mail LIKE %:correo%) " +
            "AND (:abreviatura IS NULL OR emp.abbreviation LIKE %:abreviatura%) " +
            "AND (:roles IS NULL OR emp.id IN (SELECT er.company.id FROM CompanyRole er WHERE er.catRoleType.id IN :roles)) " +
            "ORDER BY emp.legalName ASC")
    Page<Company> searchCompanies(@Param("empresa_id") Integer empresaId,
                                  @Param("documento") String documento,
                                  @Param("razon_social") String razonSocial,
                                  @Param("razon_comercial") String razonComercial,
                                  @Param("direccion") String direccion,
                                  @Param("longitud") String longitud,
                                  @Param("latitud") String latitud,
                                  @Param("estado") Boolean estado,
                                  @Param("fecha_registro_min") LocalDateTime fechaRegistroMin,
                                  @Param("fecha_registro_max") LocalDateTime fechaRegistroMax,
                                  @Param("fecha_modificacion_min") LocalDateTime fechaModificacionMin,
                                  @Param("fecha_modificacion_max") LocalDateTime fechaModificacionMax,
                                  @Param("unidad_negocio_id") Integer unidadNegocioId,
                                  @Param("tipo_documento") Integer tipoDocumento,
                                  @Param("suspendido") Boolean suspendido,
                                  @Param("telefono") String telefono,
                                  @Param("correo") String correo,
                                  @Param("abreviatura") String abreviatura,
                                  @Param("roles") String roles,
                                  Pageable pageable);

    @Query("SELECT CASE WHEN c.document IS NULL THEN NULL ELSE CONCAT(c.document, ' - ', c.legalName) END " +
            "FROM Company c WHERE c.id = :companyId")
    String findCompanyNameWithDocument(@Param("companyId") Integer companyId);
}