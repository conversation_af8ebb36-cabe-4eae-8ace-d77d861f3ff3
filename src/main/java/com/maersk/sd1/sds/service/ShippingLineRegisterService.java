package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.sds.dto.ShippingLineRegisterInput;
import com.maersk.sd1.sds.dto.ShippingLineRegisterOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class ShippingLineRegisterService {

    private static final Logger logger = LogManager.getLogger(ShippingLineRegisterService.class);

    private final ShippingLineRepository shippingLineRepository;
    private final UserRepository userRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Autowired
    public ShippingLineRegisterService(ShippingLineRepository shippingLineRepository, UserRepository userRepository, MessageLanguageRepository messageLanguageRepository) {
        this.shippingLineRepository = shippingLineRepository;
        this.userRepository = userRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public ShippingLineRegisterOutput registerShippingLine(ShippingLineRegisterInput.Input input) {
        ShippingLineRegisterOutput output = new ShippingLineRegisterOutput();
        output.setRespNewId(0);
        output.setRespEstado(0);
        output.setRespMensaje("");

        try {
            Optional<ShippingLine> existingLine = shippingLineRepository.findByShippingLineCompanyIgnoreCase(input.getLineaNaviera());
            if (existingLine.isPresent()) {
                ShippingLine line = existingLine.get();
                String existMessage = messageLanguageRepository.fnTranslatedMessage("INS_LINEA_NAVIERA", 1, input.getIdiomaId());
                if (line.getId() != null) {
                    existMessage = existMessage.replace("{IDX}", line.getId().toString());
                }
                output.setRespMensaje(existMessage);
                output.setRespNewId(0);
                output.setRespEstado(2);
            } else {
                ShippingLine newLine = new ShippingLine();
                newLine.setId(null);
                newLine.setName(input.getNombre());
                newLine.setActive(input.getActivo());

                User registeringUser = userRepository.findById(input.getUsuarioRegistroId())
                        .orElseThrow(() -> new RuntimeException("User not found"));
                newLine.setRegistrationUser(registeringUser);
                newLine.setRegistrationDate(LocalDateTime.now());
                newLine.setShippingLineCompany(input.getLineaNaviera());
                newLine.setColor(input.getColor());

                ShippingLine savedLine = shippingLineRepository.save(newLine);

                output.setRespNewId(savedLine.getId());
                output.setRespEstado(1);
                String successMsg = messageLanguageRepository.fnTranslatedMessage("GENERAL", 9, input.getIdiomaId());
                output.setRespMensaje(successMsg);
            }
        } catch (Exception e) {
            logger.error("Error occurred during shipping line registration.", e);
            output.setRespMensaje(e.getMessage());
            output.setRespEstado(0);
            output.setRespNewId(0);
        }
        return output;
    }
}
