package com.maersk.sd1.sde.service;


import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.GateTransmissionSetting;
import com.maersk.sd1.common.repository.AzureStorageConfigRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.GateTransmissionSettingRepository;
import com.maersk.sd1.sde.dto.AzureStorageConfigProjection;
import com.maersk.sd1.sde.dto.ConfigureEDISystemToGetSendingParameterOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

class ConfigureEDISystemToGetSendingParameterServiceTest {

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private GateTransmissionSettingRepository gateTransmissionSettingRepository;

    @Mock
    private AzureStorageConfigRepository azureStorageConfigRepository;

    @InjectMocks
    private ConfigureEDISystemToGetSendingParameterService service;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_NonTelexFormat_When_ObtainingParams_Then_ReturnSuccess() {
        // Given
        Integer seteoEdiCodecoId = 100;
        Integer tipoEstructuraId = 200; // not telex
        Catalog telexCatalog = new Catalog();
        telexCatalog.setId(999); // mock TELEX id
        when(catalogRepository.findByAlias("sd1_messagetype_gatetrans_telex")).thenReturn(telexCatalog);

        GateTransmissionSetting setting = new GateTransmissionSetting();
        setting.setAzureIdGateTransmission("AZUREID_CODECO");
        when(gateTransmissionSettingRepository.findById(seteoEdiCodecoId)).thenReturn(Optional.of(setting));

        AzureStorageConfigProjection projection = mockProjection();
        when(azureStorageConfigRepository.findProjectionByAzureId("AZUREID_CODECO")).thenReturn(Optional.of(projection));

        // When
        ConfigureEDISystemToGetSendingParameterOutput output = service.configureEDISystemToGetSendingParameter(seteoEdiCodecoId, tipoEstructuraId);

        // Then
        assertNotNull(output);
        assertEquals(Integer.valueOf(1), output.getRespEstado());
        assertEquals("Success", output.getRespMensaje());
        assertEquals(projection.getAzureStorageConfigId(), output.getAzureStorageConfigId());
        assertEquals(projection.getAzureId(), output.getAzureId());
    }

    @Test
    void given_TelexFormat_When_ObtainingParams_Then_ReturnSuccess() {
        // Given
        Integer seteoEdiCodecoId = 100;
        Integer telexId = 999;
        Catalog telexCatalog = new Catalog();
        telexCatalog.setId(telexId);
        when(catalogRepository.findByAlias("sd1_messagetype_gatetrans_telex")).thenReturn(telexCatalog);

        GateTransmissionSetting setting = new GateTransmissionSetting();
        setting.setAzureIdTelex("AZUREID_TELEX");
        when(gateTransmissionSettingRepository.findById(seteoEdiCodecoId)).thenReturn(Optional.of(setting));

        AzureStorageConfigProjection projection = mockProjection();
        when(azureStorageConfigRepository.findProjectionByAzureId("AZUREID_TELEX")).thenReturn(Optional.of(projection));

        // When
        ConfigureEDISystemToGetSendingParameterOutput output = service.configureEDISystemToGetSendingParameter(seteoEdiCodecoId, telexId);

        // Then
        assertNotNull(output);
        assertEquals(Integer.valueOf(1), output.getRespEstado());
        assertEquals("Success", output.getRespMensaje());
        assertEquals(projection.getAzureId(), output.getAzureId());
    }

    @Test
    void given_AzureIdNotFound_When_ObtainingParams_Then_ReturnError() {
        // Given
        Integer seteoEdiCodecoId = 100;
        Integer tipoEstructuraId = 200;
        Catalog telexCatalog = new Catalog();
        telexCatalog.setId(999);
        when(catalogRepository.findByAlias("sd1_messagetype_gatetrans_telex")).thenReturn(telexCatalog);

        GateTransmissionSetting setting = new GateTransmissionSetting();
        setting.setAzureIdGateTransmission("AZUREID_CODECO");
        when(gateTransmissionSettingRepository.findById(seteoEdiCodecoId)).thenReturn(Optional.of(setting));

        when(azureStorageConfigRepository.findProjectionByAzureId("AZUREID_CODECO")).thenReturn(Optional.empty());

        // When
        ConfigureEDISystemToGetSendingParameterOutput output = service.configureEDISystemToGetSendingParameter(seteoEdiCodecoId, tipoEstructuraId);

        // Then
        assertNotNull(output);
        assertEquals(Integer.valueOf(0), output.getRespEstado());
        assertTrue(output.getRespMensaje().contains("No se encontró"));
    }

    private AzureStorageConfigProjection mockProjection() {
        return new AzureStorageConfigProjection() {
            @Override
            public Integer getAzureStorageConfigId() {
                return 777;
            }

            @Override
            public String getAzureId() {
                return "AZURE_ID_MOCK";
            }

            @Override
            public String getAzureContainer() {
                return "containerMock";
            }

            @Override
            public String getAzurePath() {
                return "pathMock";
            }

            @Override
            public String getAzureStorageKey() {
                return "storageKeyMock";
            }

            @Override
            public String getAzureStorageName() {
                return "storageNameMock";
            }

            @Override
            public String getEmailPlantilla() {
                return "emailPlantillaMock";
            }
        };
    }
}