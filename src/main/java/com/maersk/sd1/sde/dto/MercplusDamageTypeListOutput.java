package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Output DTO to hold damage type data (mirroring the stored procedure result).
 */
@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class MercplusDamageTypeListOutput {

    @JsonProperty("cat_damage_type_id")
    private Integer catDamageTypeId;

    @JsonProperty("damage_type")
    private String damageType;
}
