package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.dto.RepairApprovalListInput;
import com.maersk.sd1.sde.dto.RepairApprovalListOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class RepairApprovalListService {

    private static final Logger logger = LogManager.getLogger(RepairApprovalListService.class);

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public RepairApprovalListService(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public RepairApprovalListOutput listRepairApprovals(RepairApprovalListInput.Input input) {
        RepairApprovalListOutput output = new RepairApprovalListOutput();

        try {
            // Check for valid repair type ID first
            if (input.getRepairTypeId() != 48030 && input.getRepairTypeId() != 48031) {
                logger.warn("Invalid repair type ID: {}", input.getRepairTypeId());
                return output; // Return empty result
            }

            // Determine which stored procedure to call based on repair type
            String procedureName = input.getRepairTypeId() == 48030 ?
                    "sde.listar_aprobacion_rep_estructura" :
                    "sde.listar_aprobacion_rep_maquinaria";

            // Format the stored procedure call
            String storedProcedureCall = String.format(
                    "EXEC %s @sub_unidad_negocio_id = %d, @sub_unidad_negocio_local_id = %d, " +
                            "@estado_aprobacion_id = %d, @lista_contenedores = '%s', @linea_naviera_id = %d, " +
                            "@eir_id = %d, @en_stock = %d, @idioma_id = %d, @page = %d, @size = %d",
                    procedureName,
                    input.getSubBusinessUnitId(),
                    input.getSubBusinessUnitLocalId(),
                    input.getApprovalStatusId() != null ? input.getApprovalStatusId() : null,
                    input.getContainerList() != null ? input.getContainerList() : "",
                    input.getShippingLineId() != null ? input.getShippingLineId() : null,
                    input.getEirId() != null ? input.getEirId() : null,
                    input.getInStock() != null ? (input.getInStock() ? 1 : 0) : 0,
                    input.getLanguageId(),
                    (input.getPage()), // Adjust for 0-based indexing in database
                    input.getSize()
            );

            logger.info("Executing stored procedure: {}", storedProcedureCall);

            // Execute the stored procedure
            List<Map<String, Object>> resultSet = jdbcTemplate.queryForList(storedProcedureCall);

            // Process the results
            List<List<Object>> resultadoLista = new ArrayList<>();

            for (Map<String, Object> row : resultSet) {
                List<Object> rowData = new ArrayList<>();
                // Add each column in the order expected by the frontend
                rowData.add(row.get("TipoReparacion"));
                rowData.add(row.get("eir_id"));
                rowData.add(row.get("linea_naviera"));
                rowData.add(row.get("contenedor"));
                rowData.add(row.get("TipoCnt"));
                rowData.add(row.get("Venta"));
                rowData.add(row.get("ZonaActual"));
                rowData.add(row.get("Check1_Seleccionable"));
                rowData.add(row.get("SiguienteZona_Check1"));
                rowData.add(row.get("Check2_Seleccionable"));
                rowData.add(row.get("SiguienteZona_Check2"));
                rowData.add(row.get("ObsAprobacion"));
                rowData.add(row.get("Nave"));
                rowData.add(row.get("Viaje"));
                rowData.add(row.get("FechaAprobacion"));
                rowData.add(row.get("UsuarioAprobacion"));
                rowData.add(row.get("usuario_registro_nombres"));
                rowData.add(row.get("usuario_registro_apellidos"));
                rowData.add(row.get("fecha_eir"));
                rowData.add(row.get("NombreLocal"));

                resultadoLista.add(rowData);
            }

            output.setResultadoLista(resultadoLista);

            // Count the total number of records from the result set
            List<List<Long>> resultado = new ArrayList<>();
            List<Long> countRow = new ArrayList<>();
            countRow.add((long) resultadoLista.size());
            resultado.add(countRow);

            output.setResultado(resultado);

            return output;
        } catch (Exception e) {
            logger.error("Error calling stored procedure for repair approvals", e);
            return output; // Return empty result instead of throwing exception
        }
    }
}
