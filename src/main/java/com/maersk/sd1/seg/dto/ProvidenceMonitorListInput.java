package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ProvidenceMonitorListInput {

        @Data
        public static class Input {
                @JsonProperty("providencia_monitoreo_id")
                private Integer providenceMonitoringId;

                @JsonProperty("numero_manifiesto")
                private String manifestNumber;

                @JsonProperty("numero_din")
                private String dinNumber;

                @JsonProperty("retiro_parcial")
                private String partialDelivery;

                @JsonProperty("fecha_retiro_min")
                private LocalDateTime pickupDateMin;

                @JsonProperty("fecha_retiro_max")
                private LocalDateTime pickupDateMax;

                @JsonProperty("ultimo_retiro")
                private String lastPickup;

                @JsonProperty("numero_papeleta_entrega")
                private String ballotNumber;

                @JsonProperty("fecha_papeleta_entrega_min")
                private LocalDateTime ballotDateMin;

                @JsonProperty("fecha_papeleta_entrega_max")
                private LocalDateTime ballotDateMax;

                @JsonProperty("estado_codigo")
                private String statusCode;

                @JsonProperty("estado_glosa")
                private String statusGloss;

                @JsonProperty("error_codigo")
                private String errorCode;

                @JsonProperty("error_glosa")
                private String errorGloss;

                @JsonProperty("consulta_xml")
                private String xmlRequest;

                @JsonProperty("respuesta_xml")
                private String xmlResponse;

                @JsonProperty("fecha_registro_min")
                private LocalDateTime registrationDateMin;

                @JsonProperty("fecha_registro_max")
                private LocalDateTime registrationDateMax;

                @JsonProperty("fecha_modificacion_min")
                private LocalDateTime modificationDateMin;

                @JsonProperty("fecha_modificacion_max")
                private LocalDateTime modificationDateMax;

                @JsonProperty("page")
                private Integer page;

                @JsonProperty("size")
                private Integer size;
        }

        @Data
        public static class Prefix {
                @JsonProperty("F")
                private ProvidenceMonitorListInput.Input input;
        }

        @Data
        public static class Root {
                @JsonProperty("SEG")
                private ProvidenceMonitorListInput.Prefix prefix;
        }
}
