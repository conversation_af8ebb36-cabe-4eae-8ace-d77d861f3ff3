package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class EditRoleInput {

    @Data
    public static class Input {

        @JsonProperty("rol_id")
        private Integer roleId;

        @JsonProperty("id")
        private String id;

        @JsonProperty("nombre")
        private String name;

        @JsonProperty("estadoRol")
        private String statusRole;

        @JsonProperty("menu_ids")
        private String menuIds;

        @JsonProperty("unidad_negocio_ids")
        private String businessUnitIds;

        @JsonProperty("menu_proyecto_defecto_id")
        private Integer menuProjectDefaultId;

        @JsonProperty("role_services")
        private String roleServices;

        @JsonProperty("Usuario_id")
        private Integer userId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private EditRoleInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("ROL")
        private EditRoleInput.Prefix rol;

        public EditRoleInput.Input getInput() {
            return rol != null ? rol.getInput() : null;
        }
    }


}
