package com.maersk.sd1.common.model;

import com.maersk.sd1.sde.dto.NextZoneDTO;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "GetNextZone",
        procedureName = "[sde].[obtener_siguiente_zona]",
        resultSetMappings = "NextZoneMapping",
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "eir_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "Actividad", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "ConDano", type = Boolean.class)
        }
)
@SqlResultSetMapping(
        name = "NextZoneMapping",
        classes = @ConstructorResult(
                targetClass = NextZoneDTO.class,
                columns = {
                        @ColumnResult(name = "SiguienteZona", type = String.class),
                        @ColumnResult(name = "SiguienteZonaALT", type = String.class),
                        @ColumnResult(name = "Comentario", type = String.class),
                        @ColumnResult(name = "ComentarioALT", type = String.class)
                }
        )
)
@Table(name = "eir_actividad_zona", schema = "sde", indexes = {
        @Index(name = "EAZX_EIR", columnList = "eir_id"),
        @Index(name = "EAZX_PARTIAL_INSP_DATE", columnList = "fecha_insp_parcial"),
        @Index(name = "EAZX_END_ACTIVITY_DATE", columnList = "fecha_termino"),
        @Index(name = "EAZX_EIR_ID_ACTIVITY_ID", columnList = "eir_id, cat_actividad_zona_id"),
        @Index(name = "EAZX_ZONA_ID", columnList = "eir_zona_id"),
        @Index(name = "nci_msft_1_eir_actividad_zona_CA40748C2C3F03C642F978BD8E5565E6", columnList = "activo, cat_actividad_zona_id, concluido, es_inspeccion_parcial")
})
public class EirActivityZone {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "eir_actividad_zona_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "eir_id", nullable = false)
    private Eir eir;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_actividad_zona_id", nullable = false)
    private Catalog catZoneActivity;

    @NotNull
    @Column(name = "fecha_inicio", nullable = false)
    private LocalDateTime startDate;

    @Column(name = "fecha_termino")
    private LocalDateTime endDate;

    @Column(name = "con_sensor")
    private Boolean withSensor;

    @Column(name = "con_sensor_danado")
    private Boolean withSensorDamaged;

    @Column(name = "resultado_estructura_danada")
    private Boolean structureDamagedResult;

    @Column(name = "resultado_maquinaria_danada")
    private Boolean machineryDamagedResult;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_actividad_zona_resultado_id")
    private Catalog catZoneActivityResult;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_zona_id")
    private EirZone eirZone;

    @Column(name = "esta_limpio")
    private Boolean isClean;

    @Column(name = "rcd")
    private Boolean rcd;

    @Column(name = "inspeccion_compleja")
    private Boolean complexInspection;

    @NotNull
    @Column(name = "concluido", nullable = false)
    private Boolean concluded = false;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_inicio_id", nullable = false)
    private User startUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_termino_id")
    private User finalUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @NotNull
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_complejidad_dano_id")
    private Catalog catDamageComplex;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_insp_parcial_id")
    private User partialInspectionUser;

    @Column(name = "fecha_insp_parcial")
    private LocalDateTime partialInspectionDate;

    @NotNull
    @Column(name = "es_inspeccion_parcial", nullable = false)
    private Boolean isPartialInspection = false;

    @Size(max = 20)
    @Column(name = "trace_actividad_zona", length = 20)
    private String tracezoneActivity;

    @Column(name = "requiere_lavado_especial")
    private Boolean requireSpecialWash;

    @Column(name = "bloquear_contenedor")
    private Boolean blockContainer;

    @Size(max = 20)
    @Column(name = "precinto_bloqueo", length = 20)
    private String blockedSeal;

    @Size(max = 20)
    @Column(name = "precinto_preasignado", length = 20)
    private String preAssignSeal;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_origen_creacion_eiract_id")
    private Catalog catCreacionEIROrigin;

    @Size(max = 300)
    @Column(name = "inspector_comment", length = 300)
    private String inspectorComment;

    @Column(name = "potencial_food_aid")
    private Boolean potentialFoodAid;

    @Column(name = "flag_cleaning_section_interior")
    private Boolean flagCleaningSectionInterior;

    @Column(name = "flag_cleaning_section_bottom")
    private Boolean flagCleaningSectionBottom;

    @Column(name = "flag_cleaning_section_right")
    private Boolean flagCleaningSectionRight;

    @Column(name = "flag_cleaning_section_left")
    private Boolean flagCleaningSectionLeft;

    @Column(name = "flag_cleaning_section_top")
    private Boolean flagCleaningSectionTop;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_cleaning_type_id")
    private Catalog catCleaningType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "estimado_emr_id")
    private EstimateEmr estimateEmr;

    @Column(name = "result_flag_ca_damage")
    private Boolean resultFlagAcDamage;

    @Size(max = 250)
    @Column(name = "inspection_observation_comment", length = 250)
    private String inspectionObservationComment;

}