package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReportDTO {

    @JsonProperty("reporte_id")
    private Integer reportId;

    @JsonProperty("name")
    private String nombre;

    @JsonProperty("description")
    private String descripcion;

    @JsonProperty("storeName")
    private String nombreStore;

    @JsonProperty("parameters")
    private String parametros;

    @JsonProperty("currentDatetime")
    private String currentDatetime;

    public ReportDTO(Integer id, String nombre, String descripcion, String nombreStore, String parametros) {
        this.reportId = id;
        this.nombre = nombre;
        this.descripcion = descripcion;
        this.nombreStore = nombreStore;
        this.parametros = parametros;
    }

}
