package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class DepositListOutput {

    @JsonProperty("total_registros")
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    private List<List<Long>> totalRegistros;

    @JsonProperty("depots")
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    private List<DepotData> depots;

    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class DepotData {

        @JsonProperty("deposito_id")
        private Integer depositoId;

        @JsonProperty("unidad_negocio_id")
        private Integer unidadNegocioId;

        @JsonProperty("codigo_deposito")
        private String codigoDeposito;

        @JsonProperty("nombre_deposito")
        private String nombreDeposito;

        @JsonProperty("direccion_deposito")
        private String direccionDeposito;

        @JsonProperty("activo")
        private Boolean activo;

        @JsonProperty("fecha_registro")
        private LocalDateTime fechaRegistro;

        @JsonProperty("fecha_modificacion")
        private LocalDateTime fechaModificacion;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subUnidadNegocioId;

        @JsonProperty("deposito_default")
        private Boolean depositoDefault;

        @JsonProperty("cat_codigo_aduana_id")
        private Integer catCodigoAduanaId;

        @JsonProperty("cat_clase_operador_aduana_id")
        private Integer catClaseOperadorAduanaId;

        @JsonProperty("usuario_registro_id")
        private Integer usuarioRegistroId;

        @JsonProperty("usuario_modificacion_id")
        private Integer usuarioModificacionId;

        @JsonProperty("usuario_registro_nombres")
        private String usuarioRegistroNombres;

        @JsonProperty("usuario_registro_apellidos")
        private String usuarioRegistroApellidos;

        @JsonProperty("usuario_modificacion_nombres")
        private String usuarioModificacionNombres;

        @JsonProperty("usuario_modificacion_apellidos")
        private String usuarioModificacionApellidos;
    }
}
