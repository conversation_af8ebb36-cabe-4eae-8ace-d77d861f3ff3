package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EstimateEmr;
import com.maersk.sd1.sde.dto.*;
import com.maersk.sd1.sds.dto.TbCntEstimateDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface EstimateEmrRepository extends JpaRepository<EstimateEmr, Integer> {

    @Query("SELECT e.id FROM EstimateEmr e WHERE e.eir.id = :eirId AND e.catEstimateType.id = :estimateTypeId AND e.active = true ORDER BY e.registrationDate DESC")
    Integer findLatestActiveEstimateIdByEirIdAndEstimateTypeId(@Param("eirId") Integer eirId, @Param("estimateTypeId") Integer estimateTypeId);

    @Query("""
            SELECT e FROM EstimateEmr e
            WHERE e.eir.id IN :eirIds
            AND e.catEstimateType.id = :estimateTypeId
            AND e.active = true
            AND e.estimateDateIssue = (
                SELECT MAX(e1.estimateDateIssue)
                FROM EstimateEmr e1
                WHERE e1.eir.id = e.eir.id
                AND e1.catEstimateType.id = :estimateTypeId
                AND e1.active = true
            )
            """)
    List<EstimateEmr> findLatestEstimatesForEirIds(@Param("eirIds") Set<Integer> eirIds,
                                                   @Param("estimateTypeId") Integer estimateTypeId);

    @Query("""
            SELECT e.id FROM EstimateEmr e
            JOIN e.eir eirx
            WHERE e.eir.id = :eirId
            AND e.catEstimateType.id = :isTypeEstimateStructure
            AND e.catEstimateStatus.id IN :estimateStatus
            AND e.active = true
            AND eirx.active = true
            AND COALESCE(e.isReplica, false) = false
            ORDER BY e.estimateDateIssue DESC
            """)
    Integer findTopEstimateIdByEirIdAndTypeAndStatus(
            @Param("eirId") Integer eirId,
            @Param("isTypeEstimateStructure") Integer isTypeEstimateStructure,
            @Param("estimateStatus") List<Integer> estimateStatus);

    @Query(value = """
                SELECT ROW_NUMBER() OVER(ORDER BY AA.estimado_emr_detalle_id ASC) AS item,
                       AA.estimado_emr_detalle_id,
                       aa.cat_estimado_ubicacion_dano_id,
                       ubic.descripcion as ubicacion_dano,
                       aa.cat_estimado_tipo_dano_id,
                       tipo_dano.descripcion as tipo_dano,
                       aa.cat_estimado_componente_id,
                       componente.descripcion as componente,
                       aa.cat_estimado_metodo_rep_id,
                       metodo_rep.descripcion as metodo,
                       aa.cedex_merc_codigo,
                       aa.cedex_merc_descripcion,
                       aa.cat_estimado_dimension_tipo_id,
                       sds.fn_CatalogoTraducidoDes(aa.cat_estimado_dimension_tipo_id, :idiomaId) as tipo_dimension_dano,
                       aa.dimension_largo,
                       aa.dimension_ancho,
                       iif(aa.cat_estimado_dimension_tipo_id in (47479,47480), format(aa.dimension_largo, '0.0'),
                           format(aa.dimension_largo, '0.0') + 'x' + format(aa.dimension_ancho, '0.0')) as dimension,
                       aa.cat_estimado_asume_costo_id,
                       asumecosto.descripcion as asume_costo,
                       aa.piezas_reparar,
                       aa.hh_xpieza,
                       ROUND(aa.hh_xpieza * a.estimado_costo_hh, 2) as costo_trabajo_xpieza,
                       aa.costo_material_xpieza,
                       (SELECT estimado_emr_repuesto_id, estimado_emr_detalle_id, piezas_repuesto, parte_repuesto, resp.serial_anterior, resp.serial_nuevo, costo_repuesto_xpieza
                        FROM sde.estimado_emr_repuesto as resp (nolock)
                        WHERE resp.estimado_emr_detalle_id = aa.estimado_emr_detalle_id
                          AND resp.activo = 1 FOR JSON PATH) as repuesto,
                       isnull((SELECT sum(resp.piezas_repuesto * resp.costo_repuesto_xpieza)
                               FROM sde.estimado_emr_repuesto as resp (nolock)
                               WHERE resp.estimado_emr_detalle_id = aa.estimado_emr_detalle_id
                                 AND resp.activo = 1), 0) as total_repuesto,
                       aa.costo_total_item,
                       aa.observacion,
                       a.es_reefer,
                       a.linea_naviera_id,
                       aa.cat_estimado_unit_meassure_id,
                       ubic.descripcion + ' - ' + ubic.descricion_larga as repairLocationDesc,
                       tipo_dano.descripcion + ' - ' + tipo_dano.descricion_larga as damageTypeDesc,
                       componente.descripcion + ' - ' + componente.descricion_larga as componentDesc,
                       metodo_rep.descripcion + ' - ' + metodo_rep.descricion_larga as repairMethodDesc
                FROM sde.estimado_emr as a (nolock)
                INNER JOIN sde.estimado_emr_detalle AS aa (nolock) on a.estimado_emr_id = aa.estimado_emr_id
                INNER JOIN ges.moneda as moneda (nolock) on a.moneda_id = moneda.moneda_id
                INNER JOIN sds.contenedor AS cnt (nolock) ON a.contenedor_id = cnt.contenedor_id
                INNER JOIN ges.catalogo as tipocntx (nolock) ON cnt.cat_tipo_contenedor_id = tipocntx.catalogo_id
                INNER JOIN ges.catalogo as tnocntx (nolock) ON cnt.cat_tamano_id = tnocntx.catalogo_id
                INNER JOIN seg.unidad_negocio AS ea (nolock) ON a.sub_unidad_negocio_id = ea.unidad_negocio_id
                LEFT OUTER JOIN ges.catalogo as clasecnt (nolock) ON cnt.cat_clase_id = clasecnt.catalogo_id
                INNER JOIN ges.catalogo as asumecosto (nolock) ON aa.cat_estimado_asume_costo_id = asumecosto.catalogo_id
                INNER JOIN ges.catalogo as ubic (nolock) ON aa.cat_estimado_ubicacion_dano_id = ubic.catalogo_id
                INNER JOIN ges.catalogo as tipo_dano (nolock) ON aa.cat_estimado_tipo_dano_id = tipo_dano.catalogo_id
                INNER JOIN ges.catalogo as componente (nolock) ON aa.cat_estimado_componente_id = componente.catalogo_id
                INNER JOIN ges.catalogo as metodo_rep (nolock) ON aa.cat_estimado_metodo_rep_id = metodo_rep.catalogo_id
                WHERE aa.estimado_emr_id = :emrNumber AND aa.activo = 1
                ORDER BY aa.estimado_emr_detalle_id
            """, nativeQuery = true)
    List<Object[]> findEstimateEmrDetails(@Param("emrNumber") Integer emrNumber, @Param("idiomaId") Integer idiomaId);

    @Query("SELECT new com.maersk.sd1.sde.dto.EstimateEmrDTO(eir.id, ee.id) " +
            "FROM EstimateEmr ee " +
            "INNER JOIN Eir eir ON ee.eir.id = eir.id " +
            "WHERE ee.eir.id IN :eirIds " +
            "AND ee.catEstimateType.id = :structureEstimate " +
            "AND ee.active = true " +
            "AND eir.active = true " +
            "ORDER BY ee.estimateDateIssue DESC")
    List<EstimateEmrDTO> findEstimateEmrByEirIds(
            @Param("eirIds") List<Integer> eirIds,
            @Param("structureEstimate") Integer structureEstimate);

    @Query(value = "select new com.maersk.sd1.sds.dto.TbCntEstimateDTO(" +
            "e.catEstimateType.description, " +
            "e.id, " +
            "e.catEstimateStatus.description) " +
            "from EstimateEmr e where e.eir.id = :eirId " +
            "and e.catEstimateStatus.id not in (:isEstimateRejected, :isEstimateFinalized, :isEstimateCreated ) " +
            "and e.active = true")
    List<TbCntEstimateDTO> findEstimatesByEirStatusNotIn(@Param("eirId") Integer eirId, @Param("isEstimateRejected") Integer isEstimateRejected,
                                                         @Param("isEstimateFinalized") Integer isEstimateFinalized, @Param("isEstimateCreated") Integer isEstimateCreated);

    @Query(value = "select new com.maersk.sd1.sds.dto.TbCntEstimateDTO(" +
            "null, " +
            "e.id, " +
            "null) " +
            "from EstimateEmr e where e.eir.id = :eirId " +
            "and e.catEstimateStatus.id in (:isEstimateRejected, :isEstimateFinalized, :isEstimateCreated ) " +
            "and e.active = true")
    List<TbCntEstimateDTO> findEstimatesByEirStatusIn(@Param("eirId") Integer eirId, @Param("isEstimateRejected") Integer isEstimateRejected,
                                                      @Param("isEstimateFinalized") Integer isEstimateFinalized, @Param("isEstimateCreated") Integer isEstimateCreated);


    @Query("select e from EstimateEmr e where e.id in :estimatesIds and e.active = true ")
    List<EstimateEmr> findByIds(@Param("estimatesIds") List<Integer> estimatesIds);

    @Query("""
            SELECT ee.id
            FROM EstimateEmr ee
            JOIN ee.eir e
            WHERE ee.eir.id = :eirId
              AND ee.catEstimateType.id = :isTypeEstimateMachinery
              AND ee.catEstimateStatus.id IN :estimateStatuses
              AND ee.active = true
              AND e.active = true
              AND COALESCE(ee.isReplica, false) = false
            ORDER BY ee.estimateDateIssue DESC
            """)
    Integer findLatestEmrId(
            @Param("eirId") Integer eirId,
            @Param("isTypeEstimateMachinery") Integer isTypeEstimateMachinery,
            @Param("estimateStatuses") List<Integer> estimateStatuses
    );

    @Query("""
                SELECT new com.maersk.sd1.sde.dto.DamagePhotoDTO(
                    est.id, det.id, detf.id, adj.id, adj.id1, adj.url,
                    det.catStandardInspection.id, det.catCaInspection.id)
                FROM EstimateEmr est
                JOIN EstimateEmrDetail det ON det.estimateEmr.id = est.id
                JOIN EstimateEmrDetailPhoto detf ON detf.estimateEmrDetail.id = det.id
                JOIN Attachment adj ON adj.id = detf.attachment.id
                WHERE est.id = :emrNumber
                AND det.active = true
                AND detf.active = true
            """)
    List<DamagePhotoDTO> findEstimateEmrDamagePhotoDetails(@Param("emrNumber") Integer emrNumber);

    @Query("""
                SELECT new com.maersk.sd1.sde.dto.EmrPhotoDTO(
                    e.id, ef.id, a.id, a.id1, a.url
                )
                FROM EstimateEmr e
                JOIN EstimateEmrEirPhoto ef ON ef.estimadoEmr.id = e.id
                JOIN Attachment a ON ef.attachment.id = a.id
                WHERE e.id = :emrNumber AND ef.active = true
            """)
    List<EmrPhotoDTO> findEstimateEmrPhotoByEmrId(@Param("emrNumber") Integer emrNumber);

    @Query("""
                SELECT new com.maersk.sd1.sde.dto.DamageEstimateDetailsDTO(
                    NULL,
                    eed.id,
                    eed.catDamageLocationEstimate.id,
                    ubic.description,
                    eed.catDamageTypeEstimate.id,
                    tipoDano.description,
                    eed.catComponentEstimate.id,
                    componente.description,
                    eed.catRepairMethodEstimate.id,
                    metodo.description,
                    eed.cedexMercCode,
                    eed.cedexMercDescription,
                    eed.catDimensionTypeEstimate.id,
                    NULL,
                    eed.dimensionLong,
                    eed.dimensionWide,
                    NULL,
                    eed.catAssumeCostEstimate.id,
                    asumeCosto.description,
                    eed.partsRepair,
                    eed.hhXPiece,
                    (eed.hhXPiece * ee.estimateCostHh),
                    eed.costMaterialXPiece,
                    NULL,
                    (SELECT COALESCE(SUM(r.partsSpare * r.costSpareXpiece), 0)
                     FROM EstimateEmrSpare r WHERE r.estimadoEmrDetalle.id = eed.id AND r.active = true),
                    eed.itemTotalCost,
                    UPPER(eed.observation),
                    ee.isReefer,
                    ee.shippingLine.id,
                    eed.catUnitMeassureEstimate.id,
                    CONCAT(ubic.description, ' - ', ubic.longDescription),
                    CONCAT(tipoDano.description, ' - ', tipoDano.longDescription),
                    CONCAT(componente.description, ' - ', componente.longDescription),
                    CONCAT(metodo.description, ' - ', metodo.longDescription),
                    eed.catStandardInspection.id,
                    eed.catCaInspection.id,
                    NULL
                )
                FROM EstimateEmr ee
                JOIN EstimateEmrDetail eed ON eed.estimateEmr.id = ee.id
                JOIN Currency currency ON ee.currency.id = currency.id
                JOIN Container cnt ON ee.container.id = cnt.id
                JOIN Catalog tipocntx ON cnt.catContainerType.id = tipocntx.id
                JOIN Catalog tnocntx ON cnt.catSize.id = tnocntx.id
                JOIN BusinessUnit bu ON ee.subBusinessUnit.id = bu.id
                LEFT JOIN Catalog clasecnt ON cnt.catGrade.id = clasecnt.id
                JOIN Catalog asumeCosto ON eed.catAssumeCostEstimate.id = asumeCosto.id
                JOIN Catalog ubic ON eed.catDamageLocationEstimate.id = ubic.id
                JOIN Catalog tipoDano ON eed.catDamageTypeEstimate.id = tipoDano.id
                JOIN Catalog componente ON eed.catComponentEstimate.id = componente.id
                JOIN Catalog metodo ON eed.catRepairMethodEstimate.id = metodo.id
                WHERE eed.estimateEmr.id = :emrNumber
                AND eed.active = true
                AND eed.catCleaningType IS NULL
                ORDER BY eed.id
            """)
    List<DamageEstimateDetailsDTO> findDamageEstimateDetailsByEmrId(@Param("emrNumber") Integer emrNumber);


    @Query(value = " SELECT sde.is_allow_change_to_submitted_emr_reefer(:eirId,:languageId)", nativeQuery = true)
    Boolean fnIsAllowChangeToSubmittedEmrReefer(
            @Param("eirId") Integer eirId,
            @Param("languageId") Integer languageId
    );
    
    @Query("SELECT e.id FROM EstimateEmr e " +
            "JOIN Container c ON e.container.id = c.id " +
            "WHERE e.active = true " +
            "AND (:containerNumberList IS NULL OR c.containerNumber IN :containerNumberList) " +
            "AND (:estimatedEmrId IS NULL OR e.id = :estimatedEmrId) " +
            "AND (:eirId IS NULL OR e.eir.id = :eirId) " +
            "AND e.subBusinessUnit.id = :subBusinessUnitId " +
            "AND (:catEstimatedTypeId IS NULL OR e.catEstimateType.id = :catEstimatedTypeId) " +
            "AND (:catEstimatedStatusId IS NULL OR e.catEstimateStatus.id = :catEstimatedStatusId) " +
            "AND ((:estimatedInspectionDateFrom IS NULL AND :estimatedInspectionDateTo IS NULL) OR e.estimateDateInspection BETWEEN :estimatedInspectionDateFrom AND :estimatedInspectionDateTo) " +
            "AND (:catEstimatedModeId IS NULL OR e.catEstimateEMRMode.id = :catEstimatedModeId) " +
            "AND (:estimatedWithoutGatein IS NULL OR COALESCE(e.withoutGateinEstimate, 0) = :estimatedWithoutGatein) " +
            "AND (:mercPlusEstimateNumberList IS NULL OR e.mercPlusEstimateNumber IN :mercPlusEstimateNumberList) ")
    List<Integer> findEstimatedEmrIdsByListEstimates(  @Param("containerNumberList") List<String> containerNumberList,
                                                       @Param("estimatedEmrId") Integer estimatedEmrId,
                                                       @Param("eirId") Integer eirId,
                                                       @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                       @Param("catEstimatedTypeId") Integer catEstimatedTypeId,
                                                       @Param("catEstimatedStatusId") Integer catEstimatedStatusId,
                                                       @Param("estimatedInspectionDateFrom") LocalDateTime estimatedInspectionDateFrom,
                                                       @Param("estimatedInspectionDateTo") LocalDateTime estimatedInspectionDateTo,
                                                       @Param("catEstimatedModeId") Integer catEstimatedModeId,
                                                       @Param("estimatedWithoutGatein") Boolean estimatedWithoutGatein,
                                                       @Param("mercPlusEstimateNumberList") List<String> mercPlusEstimateNumberList);

    @Query("SELECT new com.maersk.sd1.sde.dto.EMREstimateListDTO(e.id, " +
            "e.catEstimateType.id, " +
            "cnt.containerNumber, " +
            "catcnt.description, " +
            "catsiz.description, " +
            "sl.shippingLineCompany, " +
            "e.eir.id, " +
            "catmod.description, " +
            "e.catEstimateStatus.id, " +
            "c.abbreviation, " +
            "e.estimateTotalHours, " +
            "e.estimateTotalCost, " +
            "e.estimateDateInspection, " +
            "e.approveRejectEstimateDate, " +
            "e.catRejectCodeEstimate.id, " +
            "e.rejectionObsEstimate, " +
            "e.completedEstimateDate, " +
            "ei.truckArrivalDate, " +
            "ee.truckArrivalDate, " +
            "CONCAT(inspector.names, ' ', inspector.firstLastName), " +
            "e.estimatorUser.id, " +
            "COALESCE(estimator.names, ''), " +
            "CONCAT(COALESCE(estimator.firstLastName, ''), ' ', COALESCE(estimator.secondLastName, '')), " +
            "e.approveRejectUser.id, " +
            "COALESCE(approver.names, ''), " +
            "CONCAT(COALESCE(approver.firstLastName, ''), ' ', COALESCE(approver.secondLastName, '')), " +
            "e.completedUser.id, " +
            "COALESCE(completedBy.names, ''), " +
            "CONCAT(COALESCE(completedBy.firstLastName, ''), ' ', COALESCE(completedBy.secondLastName, '')), " +
            "lbu.name, " +
            "e.shippingLine.id, " +
            "cnt.shippingLine.id, " +
            "e.withoutGateinEstimate, " +
            "e.blockSendingCtrl, " +
            "e.blockSending, " +
            "e.registrationDate, " +
            "e.registrationUser.id, " +
            "COALESCE(registeredBy.names, ''), " +
            "CONCAT(COALESCE(registeredBy.firstLastName, ''), ' ', COALESCE(registeredBy.secondLastName, '')), " +
            "e.modificationDate, " +
            "e.modificationUser.id, " +
            "COALESCE(modifiedBy.names, ''), " +
            "CONCAT(COALESCE(modifiedBy.firstLastName, ''), ' ', COALESCE(modifiedBy.secondLastName, '')), " +
            "e.catApprovalSendEstimateStatus.id, " +
            "e.catCreationOriginEstimate.id, " +
            "e.estimateSubmissionDate, " +
            "ei.catApprovalRepBox.id, " +
            "ei.catApprovalRepMachine.id, " +
            "submissionUser.id, " +
            "COALESCE(submissionUser.names, ''), " +
            "CONCAT(COALESCE(submissionUser.firstLastName, ''), ' ', COALESCE(submissionUser.secondLastName, '')), " +
            "e.flagAutoApproval, " +
            "e.mercPlusEstimateNumber, " +
            "ei.catCleaningStatus.id, " +
            "e.registrationCancelEstimateDate, " +
            "e.catReasonCancelEstimate.id, " +
            "e.reasonCancelEstimateDescription, " +
            "cancelUser.id, " +
            "COALESCE(cancelUser.names, ''), " +
            "CONCAT(COALESCE(cancelUser.firstLastName, ''), ' ', COALESCE(cancelUser.secondLastName, ''))) " +
            "FROM EstimateEmr e " +
            "JOIN e.currency c " +
            "JOIN e.eir ei " +
            "JOIN e.container cnt " +
            "JOIN Catalog catcnt on catcnt.id = cnt.catContainerType.id " +
            "JOIN Catalog catsiz on catsiz.id = cnt.catSize.id " +
            "JOIN ei.localSubBusinessUnit lbu " +
            "JOIN Catalog catmod on catmod.id = e.catEstimateEMRMode.id " +
            "LEFT JOIN e.shippingLine sl on sl.id = e.shippingLine.id " +
            "LEFT JOIN StockEmpty es on es.gateInEir.id = e.eir.id " +
            "LEFT JOIN es.gateOutEir ee " +
            "LEFT JOIN e.inspectorPerson inspector " +
            "LEFT JOIN e.estimatorUser estimator " +
            "LEFT JOIN e.approveRejectUser approver " +
            "LEFT JOIN e.completedUser completedBy " +
            "LEFT JOIN e.registrationUser registeredBy " +
            "LEFT JOIN e.modificationUser modifiedBy " +
            "LEFT JOIN e.estimateSubmissionUser submissionUser " +
            "LEFT JOIN e.cancelEstimateUser cancelUser " +
            "WHERE e.id IN :estimatesFound " +
            "ORDER BY e.estimateDateInspection DESC ")
    Page<EMREstimateListDTO> findEstimadoEmrById(
            @Param("estimatesFound") List<Integer> estimatesFound,
            Pageable pageable);

    @Query("SELECT e FROM EstimateEmr e " +
            "WHERE e.id IN :estimateIds AND e.active = true")
    List<EstimateEmr> findAllHeadersByIdIn(@Param("estimateIds") List<Integer> estimateIds);

    @Query(value = "SELECT " +
            "E.estimado_emr_id AS estimateId, " +
            "E.cat_tipo_estimado_id AS estimateType, " +
            "ges.fn_CatalogTranslationDesc(E.cat_tipo_estimado_id, :languageId) AS estimateTypeDesc, " +
            "E.eir_id AS eirId, " +
            "E.contenedor_id AS containerId, " +
            "C.numero_contenedor AS containerNumber, " +
            "ges.fn_CatalogTranslationDesc(C.cat_tipo_contenedor_id, :languageId) AS containerType, " +
            "CAST(C.cat_tamano_id AS INTEGER) AS containerSize, " +
            "P.nombres + ' ' + P.apellido_parterno + ' ' + P.apellido_materno AS inspector, " +
            "UE.nombres + ' ' + UE.apellido_paterno + ' ' + UE.apellido_materno AS estimator, " +
            "E.cat_estimado_causa_dano_id AS damageCauseId, " +
            "ges.fn_CatalogTranslationDescLong(E.cat_estimado_causa_dano_id, :languageId) AS damageCause, " +
            "E.moneda_id AS currencyId, " +
            "MON.abreviatura AS currencyAbbreviation, " +
            "MON.simbolo AS currencySymbol, " +
            "seg.fn_datetime_get(E.sub_unidad_negocio_id, E.estimado_fecha_inspeccion) AS inspectionDate, " +
            "E.estimado_fecha_aprueba_rechaza AS approvalDate, " +
            "E.estimado_total_horas AS totalHours, " +
            "E.estimado_total_costo AS totalCost, " +
            "ges.fn_CatalogTranslationDesc(E.cat_estado_estimado_id, :languageId) AS estimateStatus, " +
            "LIN.nombre AS shippingLine, " +
            "seg.fn_datetime_get(E.sub_unidad_negocio_id, EIR.fecha_ingreso_camion) AS gateInDate " +
            "FROM sde.estimado_emr E " +
            "INNER JOIN sds.contenedor C ON C.contenedor_id = E.contenedor_id " +
            "LEFT JOIN ges.persona P ON P.persona_id = E.persona_inspector_id " +
            "LEFT JOIN seg.usuario UE ON UE.usuario_id = E.usuario_estimador_id " +
            "INNER JOIN ges.moneda MON ON MON.moneda_id = E.moneda_id " +
            "LEFT JOIN sds.linea_naviera LIN ON C.linea_naviera_id = LIN.linea_naviera_id " +
            "INNER JOIN sde.eir EIR ON EIR.eir_id = E.eir_id " +
            "WHERE E.estimado_emr_id = :estimateId", nativeQuery = true)
    List<EstimateDtoInterface> findEstimateDtoById(@Param("estimateId") Integer estimateId,
                                                   @Param("languageId") Integer languageId);

    @Query(value = "SELECT " +
            "EST.estimado_emr_id AS estimateEmrId, " +
            "DET.estimado_emr_detalle_id AS estimateEmrDetailId, " +
            "DETF.estimate_emr_detail_repair_photo_id AS estimateEmrDetailPhotoId, " +
            "ADJ.adjunto_id AS attachedId, " +
            "ADJ.id AS id, " +
            "ADJ.url AS url " +
            "FROM sde.estimado_emr EST " +
            "INNER JOIN sde.estimado_emr_detalle DET (NOLOCK) ON DET.estimado_emr_id = EST.estimado_emr_id " +
            "INNER JOIN sde.estimate_emr_detail_repair_photo DETF (NOLOCK) ON DETF.estimate_emr_detail_id = DET.estimado_emr_detalle_id " +
            "INNER JOIN ges.adjunto ADJ (NOLOCK) ON ADJ.adjunto_id = DETF.attached_id " +
            "WHERE EST.estimado_emr_id = :estimateId AND DET.activo = 1 AND DETF.active = 1",
            nativeQuery = true)
    List<EstimateRepairPhotoDTOInterface> findEstimateRepairPhotoByEstimateId(@Param("estimateId") Integer estimateId);

    Optional<EstimateEmr> findByIdAndActive(Integer id, boolean active);

    List<EstimateEmr> findByEirIdAndActiveTrueOrderByEstimateDateInspectionAsc(Integer eirId);

    // Retrieves all active estimates (without gate-in) by container number and sub business unit, ordered by inspection date
    List<EstimateEmr> findByContainerContainerNumberAndSubBusinessUnitIdAndWithoutGateinEstimateTrueAndActiveTrueOrderByEstimateDateInspectionAsc(
            String containerNumber,
            Integer subBusinessUnitId
    );

    @Query(value = """
        SELECT
            E.estimado_emr_id AS estimadoEmrId,
            E.merc_plus_estimate_number AS mercPlusEstimateNumber,
            E.cat_tipo_estimado_id AS catTipoEstimadoId,
            E.eir_id AS eirId,
            seg.fn_datetime_get(EIR.sub_unidad_negocio_id, EIR.fecha_ingreso_camion) AS gateInDate,
            E.linea_naviera_id AS lineaNavieraId,
            E.contenedor_id AS contenedorId,
            CON.numero_contenedor AS numeroContenedor,
            CON.cat_tamano_id AS catTamanoId,
            CON.cat_tipo_contenedor_id AS catTipoContenedorId,
            '' AS viaje, -- Voyage column as empty
            seg.fn_datetime_get(EIR.sub_unidad_negocio_id, E.fecha_registro_aprueba_rechaza) AS approvalDate,
            E.usuario_aprueba_rechaza_id AS usuarioApruebaRechazaId,
            EIR.cat_aprobacion_rep_caja AS catRepairBoxStatusId,
            EIR.cat_aprobacion_rep_maquina AS catRepairMachineryStatusId,
            E.cat_estado_estimado_id AS catEstadoEstimadoId,
            E.flag_auto_approval AS flagAutoApproval,
            EIR.cat_cleaning_status_id AS catCleaningStatusId,
            E.estimado_total_horas AS estimadoTotalHoras
        FROM sde.estimado_emr E (NOLOCK)
        INNER JOIN sde.eir EIR (NOLOCK) ON EIR.eir_id = E.eir_id
        INNER JOIN sds.contenedor CON (NOLOCK) ON E.contenedor_id = CON.contenedor_id
        WHERE EIR.eir_id = IIF(:eirId IS NULL, EIR.eir_id, :eirId)
          AND EIR.sub_unidad_negocio_local_id = :subBusinessUnitLocalId
          AND (
                (ISNULL(E.flag_auto_approval, 0) = 0 AND E.cat_estado_estimado_id = :isEstimateApproved) OR
                (ISNULL(E.flag_auto_approval, 0) = 1 AND E.cat_estado_estimado_id IN (:isEstimateFinalized, :isEstimateSubmitted, :isEstimateApproved, :isEstimateRepairComplete))
              )
          AND E.cat_tipo_estimado_id = IIF(:estimateTypeId IS NULL, E.cat_tipo_estimado_id, :estimateTypeId)
          AND EIR.cat_cleaning_status_id = IIF(:repairStatus IS NULL, EIR.cat_cleaning_status_id, :repairStatus)
          AND E.activo = 1 AND EIR.activo = 1
          AND EIR.cat_cleaning_status_id IS NOT NULL
          AND EXISTS (
              SELECT 1 FROM sde.estimado_emr_detalle ED (NOLOCK)
              WHERE E.estimado_emr_id = ED.estimado_emr_id
              AND ED.cat_cleaning_type_id IS NOT NULL
              AND ED.activo = 1
          )
    """, nativeQuery = true)
    List<EstimateDataInterface> fetchEstimateData(
            @Param("eirId") Integer eirId,
            @Param("subBusinessUnitLocalId") Long subBusinessUnitLocalId,
            @Param("isEstimateApproved") Integer isEstimateApproved,
            @Param("isEstimateFinalized") Integer isEstimateFinalized,
            @Param("isEstimateSubmitted") Integer isEstimateSubmitted,
            @Param("isEstimateRepairComplete") Integer isEstimateRepairComplete,
            @Param("estimateTypeId") Long estimateTypeId,
            @Param("repairStatus") Long repairStatus

    );

    @Query(value = """
        SELECT 
            LIN.nombre AS shippingLine,
            USU.nombres AS userApproveName,
            CONCAT(USU.apellido_paterno, ' ', USU.apellido_materno) AS userApproveLastname
        FROM  sds.linea_naviera LIN 
        LEFT JOIN seg.usuario AS USU ON USU.usuario_id = :usuario_aprueba_rechaza_id 
        WHERE LIN.linea_naviera_id = :linea_naviera_id
    """, nativeQuery = true)
    List<Object[]> findAllEstimates(
            @Param("linea_naviera_id") Integer lineaNavieraId,
            @Param("usuario_aprueba_rechaza_id") Long usuarioApruebaRechazaId
    );

    @Query(value = """
    SELECT 
        E.estimado_emr_id AS estimadoEmrId,
        E.merc_plus_estimate_number AS mercPlusEstimateNumber,
        E.cat_tipo_estimado_id AS catTipoEstimadoId,
        E.eir_id AS eirId,
        seg.fn_datetime_get(EIR.sub_unidad_negocio_id, EIR.fecha_ingreso_camion) AS gateInDate,
        E.linea_naviera_id AS lineaNavieraId,
        E.contenedor_id AS contenedorId,
        CON.numero_contenedor AS numeroContenedor,
        CON.cat_tamano_id AS catTamanoId,
        CON.cat_tipo_contenedor_id AS catTipoContenedorId,
        '' AS viaje,
        seg.fn_datetime_get(EIR.sub_unidad_negocio_id, E.fecha_registro_aprueba_rechaza) AS approvalDate,
        E.usuario_aprueba_rechaza_id AS usuarioApruebaRechazaId,
        EIR.cat_aprobacion_rep_caja AS catRepairBoxStatusId,
        EIR.cat_aprobacion_rep_maquina AS catRepairMachineryStatusId,
        E.cat_estado_estimado_id AS catEstadoEstimadoId,
        E.flag_auto_approval AS flagAutoApproval,
        EIR.cat_cleaning_status_id AS catCleaningStatusId,
        E.estimado_total_horas AS estimadoTotalHoras
    FROM sde.estimado_emr E WITH (NOLOCK)
    INNER JOIN sde.eir EIR WITH (NOLOCK) ON EIR.eir_id = E.eir_id
    INNER JOIN sds.contenedor CON WITH (NOLOCK) ON E.contenedor_id = CON.contenedor_id
    WHERE 
        EIR.eir_id = IIF(:eirId IS NULL, EIR.eir_id, :eirId)
        AND EIR.sub_unidad_negocio_local_id = :subBusinessUnitLocalId
        AND (
            (ISNULL(E.flag_auto_approval, 0) = 0 AND E.cat_estado_estimado_id = :catEstimateApproved) OR 
            (ISNULL(E.flag_auto_approval, 0) = 1 AND E.cat_estado_estimado_id IN (:isEstimateFinalized, :isEstimateSubmitted, :isEstimateApproved))
        )
        AND IIF(E.cat_tipo_estimado_id = :catEstimateStructure, EIR.cat_aprobacion_rep_caja, EIR.cat_aprobacion_rep_maquina) = 
            IIF(:repairStatus IS NULL, 
                IIF(E.cat_tipo_estimado_id = :catEstimateStructure, EIR.cat_aprobacion_rep_caja, EIR.cat_aprobacion_rep_maquina), 
                :repairStatus
            )
        AND E.cat_tipo_estimado_id = IIF(:estimateTypeId IS NULL, E.cat_tipo_estimado_id, :estimateTypeId)
        AND (:containersListEmpty = 1 OR CON.numero_contenedor IN :containersList)
        AND E.activo = 1 AND EIR.activo = 1
        AND EXISTS (
            SELECT 1 
            FROM sde.estimado_emr_detalle ED WITH (NOLOCK) 
            WHERE E.estimado_emr_id = ED.estimado_emr_id 
              AND ED.cat_cleaning_type_id IS NULL 
              AND ED.activo = 1
        )
    """, nativeQuery = true)
    List<EstimateTempDataInterface> getApprovedOrAutoApprovedEstimates(
            @Param("eirId") Integer eirId,
            @Param("subBusinessUnitLocalId") Long subBusinessUnitLocalId,
            @Param("catEstimateApproved") Integer catEstimateApproved,
            @Param("isEstimateFinalized") Integer isEstimateFinalized,
            @Param("isEstimateSubmitted") Integer isEstimateSubmitted,
            @Param("isEstimateApproved") Integer isEstimateApproved,
            @Param("catEstimateStructure") Integer catEstimateStructure,
            @Param("repairStatus") Long repairStatus,
            @Param("estimateTypeId") Long estimateTypeId,
            @Param("containersListEmpty") Integer containersListEmpty,
            @Param("containersList") List<String> containersList
    );

    @Modifying
    @Query("""
            UPDATE EstimateEmr ee
            SET ee.catEstimateStatus.id = :newStatusId,
            ee.traceEstimate = :trace,
            ee.modificationUser.id = :modificationUserId,
            ee.modificationDate = CURRENT_TIMESTAMP
            WHERE ee.id = :estimateId
            AND ee.catEstimateStatus.id IN :currentStatus
            AND ee.active = TRUE
            """)
    void updateStatusByIdAndCurrentStatus(Integer estimateId, Integer newStatusId, String trace, Integer modificationUserId, List<Integer> currentStatus);

    @Query("SELECT e " +
            "FROM EstimateEmr e " +
            "WHERE e.eir.id = :eirId AND e.active = true " +
            "ORDER BY e.estimateDateInspection")
    List<EstimateEmr> findAllActiveByEirOrdered(@Param("eirId") Integer eirId);

    @Query("SELECT e " +
            "FROM EstimateEmr e " +
            "WHERE e.container.id = :containerId " +
            "  AND e.subBusinessUnit.id = :subBusinessUnitId " +
            "  AND e.withoutGateinEstimate = true " +
            "  AND e.active = true " +
            "ORDER BY e.estimateDateInspection")
    List<EstimateEmr> findAllActiveWithoutGateInByContainerAndSubBusinessUnit(@Param("containerId") Integer containerId,
                                                                              @Param("subBusinessUnitId") Integer subBusinessUnitId);

    @Query("SELECT CASE WHEN COUNT(e) > 0 THEN true ELSE false END " +
            "FROM EstimateEmr e " +
            "WHERE e.eir.id = :eirId " +
            "  AND e.active = true " +
            "  AND e.catEstimateStatus.id NOT IN (:rejected, :canceled, :completed)")
    boolean existsActiveEstimatesByEirAndNotInRejectedCanceledCompleted(@Param("eirId") Integer eirId,
                                                                        @Param("rejected") Integer isEstimateRejected,
                                                                        @Param("canceled") Integer isEstimateCanceled,
                                                                        @Param("completed") Integer isRepairComplete);

    @Query("SELECT CASE WHEN COUNT(e) > 0 THEN true ELSE false END " +
            "FROM EstimateEmr e " +
            "WHERE e.container.id = :containerId " +
            "  AND e.subBusinessUnit.id = :subBusinessUnitId " +
            "  AND e.active = true " +
            "  AND e.withoutGateinEstimate = true " +
            "  AND e.catEstimateStatus.id NOT IN (:rejected, :canceled, :completed)")
    boolean existsActiveEstimatesWithoutGateInByContainerAndSubBusinessUnitAndNotInRejectedCanceledCompleted(
            @Param("containerId") Integer containerId,
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("rejected") Integer isEstimateRejected,
            @Param("canceled") Integer isEstimateCanceled,
            @Param("completed") Integer isRepairComplete);
}