package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class RoleUnitBusinessId implements Serializable {
    private static final long serialVersionUID = -2350551842105865991L;
    @NotNull
    @Column(name = "rol_id", nullable = false)
    private Integer roleId;

    @NotNull
    @Column(name = "unidad_negocio_id", nullable = false)
    private Integer unitBusinessId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        RoleUnitBusinessId entity = (RoleUnitBusinessId) o;
        return Objects.equals(this.unitBusinessId, entity.unitBusinessId) &&
                Objects.equals(this.roleId, entity.roleId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(unitBusinessId, roleId);
    }

}