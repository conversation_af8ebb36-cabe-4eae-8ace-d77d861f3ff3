package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.controller.dto.RoleObtainerVariousOutput;
import com.maersk.sd1.common.repository.RoleRepository;
import com.maersk.sd1.common.repository.RoleMenuRepository;
import com.maersk.sd1.common.repository.RoleBusinessUnitRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
@Service
public class RoleObtainerVariousService {

    private static final Logger logger = LogManager.getLogger(RoleObtainerVariousService.class);

    private final RoleRepository roleRepository;

    private final RoleMenuRepository roleMenuRepository;

    private final RoleBusinessUnitRepository roleBusinessUnitRepository;

    @Transactional(readOnly = true)
    public RoleObtainerVariousOutput obtenerVariosRoles(List<Integer> roleIds) {
        RoleObtainerVariousOutput output = new RoleObtainerVariousOutput();
        try {
            List<RoleObtainerVariousOutput.RoleDTO> roles = roleRepository.findRoleDTOByIdIn(roleIds);
            List<RoleObtainerVariousOutput.RoleMenuDTO> roleMenus = roleMenuRepository.findRoleMenuDTOByRoleIdIn(roleIds);
            List<RoleObtainerVariousOutput.RoleBusinessUnitDTO> roleBusinessUnits = roleBusinessUnitRepository.findRoleBusinessUnitDTOByRoleIdIn(roleIds);

            output.setRoles(roles);
            output.setRoleMenus(roleMenus);
            output.setRoleBusinessUnits(roleBusinessUnits);

            output.setRespEstado(1);
            output.setRespMensaje("Success");
        } catch (Exception e) {
            logger.error("Error during obtenerVariosRoles", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}