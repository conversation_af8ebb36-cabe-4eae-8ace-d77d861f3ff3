package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class InspectionRegisterV2Output {
    private Integer respNewId;
    private Integer respEstado;
    private String respMensaje;
    private String siguienteZonaGuardada;
    private String typeProductIntegration;
    private Integer yardId;
    private String yardCode;
    private Integer containerId;
    private String yardLocation;
}
