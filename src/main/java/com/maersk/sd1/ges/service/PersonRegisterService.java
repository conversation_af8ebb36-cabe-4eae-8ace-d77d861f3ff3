package com.maersk.sd1.ges.service;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.ges.dto.PersonRegisterInput;
import com.maersk.sd1.ges.dto.PersonRegisterOutput;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Log4j2
public class PersonRegisterService {

    private final PersonRepository personRepository;

    private final PersonRoleRepository personRoleRepository;

    private final CompanyPersonRepository companyPersonRepository;

    private final CatalogRepository catalogRepository; // Assuming you have a CatalogRepository

    private final UserRepository userRepository; // Assuming you have a UserRepository

    @Autowired
    public PersonRegisterService(PersonRepository personRepository,
                                 PersonRoleRepository personRoleRepository,
                                 CompanyPersonRepository companyPersonRepository,
                                 CatalogRepository catalogRepository,
                                 UserRepository userRepository)
    {
        this.personRepository = personRepository;
        this.personRoleRepository = personRoleRepository;
        this.companyPersonRepository = companyPersonRepository;
        this.catalogRepository = catalogRepository;
        this.userRepository = userRepository;
    }
    @Transactional
    public PersonRegisterOutput registerPerson(PersonRegisterInput.Input input)
    {
        Integer tipoDocumentoIdentidadId = input.getTipoDocumentoIdentidadId();
        String documentoIdentidad = input.getDocumentoIdentidad();
        String apellidoParterno = input.getApellidoParterno();
        String apellidoMaterno = input.getApellidoMaterno();
        String nombres = input.getNombres();
        LocalDate fechaNacimiento = input.getFechaNacimiento();
        String correo = input.getCorreo();
        String telefono = input.getTelefono();
        Integer tipoEstadoPersonaId = input.getTipoEstadoPersonaId();
        Integer tipoAreaTrabajoId = input.getTipoAreaTrabajoId();

        Integer usuarioRegistroId = input.getUsuarioRegistroId();
        Integer personaRol = input.getPersonaRol();
        String personaRolMultiple = input.getPersonaRolMultiple();
        Integer empresaId = input.getEmpresaId();

        String licenciaConducir = input.getLicenciaConducir();
        LocalDate fechaVigenciaLicenciaConducir = input.getFechaVigenciaLicenciaConducir();
        String carnet = input.getCarnet();
        LocalDate fechaVigenciaCarnet = input.getFechaVigenciaCarnet();
        Character genero = input.getGenero();
        Integer situacionLaboral = input.getSituacionLaboral();

        PersonRegisterOutput output = new PersonRegisterOutput();
        output.setRespNewId(0);
        output.setRespEstado(0);
        output.setRespMensaje("Error in processing");

        try {
            // 1) Check whether the person is already working for a company (active) with the same doc type & doc
            List<String> activeCompanyNames = companyPersonRepository.findActiveCompanyNameByDocTypeAndDocNumber(
                    tipoDocumentoIdentidadId, documentoIdentidad);
            if (activeCompanyNames != null && !activeCompanyNames.isEmpty()) {
                // means there's at least one active company for this person
                String companyName = activeCompanyNames.get(0);
                output.setRespEstado(2);
                output.setRespMensaje("The person is working at the company " + companyName);
                return output;
            }

            // 2) Check if there's already a person with matching doc or license as indicated by the procedure logic
            boolean licenseIsEmpty = (licenciaConducir == null || licenciaConducir.trim().isEmpty());
            boolean docIsEmpty = (documentoIdentidad == null || documentoIdentidad.trim().isEmpty());

            Long existingCount = personRepository.countExistingPerson(
                    licenciaConducir,
                    documentoIdentidad,
                    tipoDocumentoIdentidadId,
                    licenseIsEmpty,
                    docIsEmpty
            );

            if (existingCount != null && existingCount > 0) {
                // already exist
                output.setRespEstado(2);
                output.setRespMensaje("There's already a record with the same document number. Check deactivated records.");
                return output;
            }

            // 3) Insert Person
            Person person = new Person();

            // set the ID by letting JPA generate it if it's an identity, or if needed, use a sequence.
            // For demonstration, we assume database identity:
            // person.setId(...) not needed if auto-generated. If the DB requires or uses identity, mark @GeneratedValue.

            Catalog docType = catalogRepository.findById(tipoDocumentoIdentidadId)
                    .orElseThrow(() -> new RuntimeException("Invalid doc type catalog ID: " + tipoDocumentoIdentidadId));
            person.setCatIdentificationDocumentType(docType);

            Catalog catStatus = catalogRepository.findById(tipoEstadoPersonaId)
                    .orElseThrow(() -> new RuntimeException("Invalid status catalog ID: " + tipoEstadoPersonaId));
            person.setCatPersonStatus(catStatus);

            if (tipoAreaTrabajoId != null && tipoAreaTrabajoId != 0) {
                Catalog catWorkArea = catalogRepository.findById(tipoAreaTrabajoId)
                        .orElse(null); // might be null if the ID doesn't exist or is optional
                person.setCatWorkArea(catWorkArea);
            } else {
                person.setCatWorkArea(null);
            }

            User regUser = userRepository.findById(usuarioRegistroId)
                    .orElseThrow(() -> new RuntimeException("Invalid user ID: " + usuarioRegistroId));

            person.setIdentificationDocument(documentoIdentidad);
            person.setFirstLastName(apellidoParterno);
            person.setSecondLastName(apellidoMaterno);
            person.setNames(nombres);
            // In the entity, birthDate is a LocalDateTime. We'll convert date to startOfDay:
            if (fechaNacimiento != null) {
                person.setBirthDate(fechaNacimiento.atStartOfDay());
            }

            person.setMail(correo);
            person.setPhone(telefono);
            person.setActive(true); // procedure sets it to 1 if we insert
            person.setRegistrationUser(regUser);
            person.setRegistrationDate(LocalDateTime.now());

            person.setDriversLicense(licenciaConducir);
            if (fechaVigenciaLicenciaConducir != null) {
                person.setDriversLicenseValidityDate(fechaVigenciaLicenciaConducir.atStartOfDay());
            }

            person.setCard(carnet);
            if (fechaVigenciaCarnet != null) {
                person.setCardValidityDate(fechaVigenciaCarnet.atStartOfDay());
            }

            person.setGender(genero);
            person.setEmploymentStatus(situacionLaboral);

            // save Person
            Person savedPerson = personRepository.save(person);

            // Store the new ID
            output.setRespNewId(savedPerson.getId());

            // 4) Insert single PersonRole if persona_rol is not null
            if (personaRol != null) {
                Catalog cat = catalogRepository.findById(personaRol)
                        .orElseThrow(() -> new RuntimeException("Invalid role: " + personaRol));

                PersonRole personRole = new PersonRole();
                PersonRoleId roleId = new PersonRoleId();
                roleId.setPersonId(savedPerson.getId());
                roleId.setTypeRolePersonId(cat.getId());
                personRole.setId(roleId);
                personRole.setPerson(savedPerson);
                personRole.setCatPersonRole(cat);
                personRole.setActive(true);
                personRole.setRegistrationUser(regUser);
                personRole.setRegistrationDate(LocalDateTime.now());

                personRoleRepository.save(personRole);
            }

            // 5) Insert multiple PersonRole from JSON if persona_rol_multiple is not null
            if (personaRolMultiple != null && !personaRolMultiple.trim().isEmpty()) {
                try {
                    ObjectMapper mapper = new ObjectMapper();
                    JobRole[] roles = mapper.readValue(personaRolMultiple, JobRole[].class);
                    for (JobRole rol : roles) {
                        Optional<Catalog> cat = catalogRepository.findById(rol.getJobRoleId());

                        PersonRole personRole = new PersonRole();
                        PersonRoleId roleId = new PersonRoleId();
                        roleId.setPersonId(savedPerson.getId());
                        if(cat.isPresent()) {
                            roleId.setTypeRolePersonId(cat.get().getId());
                        }
                        else {
                            roleId.setTypeRolePersonId(1);
                        }
                        personRole.setId(roleId);
                        personRole.setPerson(savedPerson);
                        personRole.setCatPersonRole(cat.get());
                        personRole.setActive(true);
                        personRole.setRegistrationUser(regUser);
                        personRole.setRegistrationDate(LocalDateTime.now());

                        personRoleRepository.save(personRole);
                    }
                } catch (Exception e) {
                    log.error("Error parsing persona_rol_multiple JSON", e);
                    throw new RuntimeException("Error parsing roles JSON", e);
                }
            }

            // 6) Insert into CompanyPerson if empresa_id is not null
            if (empresaId != null) {
                CompanyPerson cp = new CompanyPerson();
                CompanyPersonId cpId = new CompanyPersonId();
                cpId.setCompanyId(empresaId);
                cpId.setPersonId(savedPerson.getId());
                cp.setId(cpId);

                // We assume we have a Company entity with that ID
                Company company = new Company();
                company.setId(empresaId);
                cp.setCompany(company);

                cp.setPerson(savedPerson);
                cp.setActive(true);
                cp.setRegistrationUser(regUser);
                cp.setRegistrationDate(LocalDateTime.now());
                companyPersonRepository.save(cp);
            }

            output.setRespEstado(1);
            output.setRespMensaje("Registration completed successfully.");
        } catch (Exception ex) {
            log.error("Error in registerPerson", ex);
            output.setRespEstado(0);
            output.setRespMensaje(ex.getMessage());
        }

        return output;
    }

    // Helper class to handle the JSON structure for persona_rol_multiple
    private static class JobRole {
        @JsonProperty("rol_trabajador_id")
        private Integer jobRoleId;

        public Integer getJobRoleId() {
            return jobRoleId;
        }

        public void setJobRoleId(Integer jobRoleId) {
            this.jobRoleId = jobRoleId;
        }
    }
}
