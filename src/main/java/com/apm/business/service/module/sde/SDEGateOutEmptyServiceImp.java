package com.apm.business.service.module.sde;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sde/SDEGateOutEmptyServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEGateOutEmptyServiceImp extends SDEGateOutEmptyService {

	@RequestMapping(value = "/sdelistarSalidasEmptyPendientes", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdelistarSalidasEmptyPendientes(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdelistarSalidasEmptyPendientes(ppo, request);
	}

}