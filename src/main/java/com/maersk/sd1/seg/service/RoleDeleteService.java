package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.Role;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.seg.controller.dto.RoleDeleteInput;
import com.maersk.sd1.seg.controller.dto.RoleDeleteOutput;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class RoleDeleteService {

    private static final Logger logger = LogManager.getLogger(RoleDeleteService.class);

    @PersistenceContext
    private EntityManager entityManager;

    private final RoleRepository roleRepository;
    private final RoleMenuRepository roleMenuRepository;
    private final UserRoleRepository userRoleRepository;
    private final NotificationRoleRepository notificationRoleRepository;
    private final RoleBusinessUnitRepository roleBusinessUnitRepository;
    private final UserRoleBusinessUnitRepository userRoleBusinessUnitRepository;
    private final ReportRoleRepository reportRoleRepository;
    private final RoleMenuActionRepository reportMenuActionRepository;
    private final EmailTemplateRoleRepository emailTemplateRoleRepository;
    private final LoadTemplateRoleRepository loadTemplateRoleRepository;

    public RoleDeleteService(RoleRepository roleRepository,
                             RoleMenuRepository roleMenuRepository,
                             UserRoleRepository userRoleRepository,
                             NotificationRoleRepository notificationRoleRepository,
                             RoleBusinessUnitRepository roleBusinessUnitRepository,
                             UserRoleBusinessUnitRepository userRoleBusinessUnitRepository,
                             ReportRoleRepository reportRoleRepository,
                             RoleMenuActionRepository reportMenuActionRepository,
                             EmailTemplateRoleRepository emailTemplateRoleRepository,
                             LoadTemplateRoleRepository loadTemplateRoleRepository) {
        this.roleRepository = roleRepository;
        this.roleMenuRepository = roleMenuRepository;
        this.userRoleRepository = userRoleRepository;
        this.notificationRoleRepository = notificationRoleRepository;
        this.roleBusinessUnitRepository = roleBusinessUnitRepository;
        this.userRoleBusinessUnitRepository = userRoleBusinessUnitRepository;
        this.reportRoleRepository = reportRoleRepository;
        this.reportMenuActionRepository = reportMenuActionRepository;
        this.emailTemplateRoleRepository = emailTemplateRoleRepository;
        this.loadTemplateRoleRepository = loadTemplateRoleRepository;
    }

    @Transactional
    public RoleDeleteOutput deleteRole(RoleDeleteInput.Input input) {
        RoleDeleteOutput output = new RoleDeleteOutput();
        try {
            if (input.getRoleId() == null || input.getUserId() == null) {
                throw new IllegalArgumentException("roleId or userId is null");
            }

            Role role = roleRepository.findById(input.getRoleId())
                    .orElseThrow(() -> new IllegalArgumentException("Role not found for ID: " + input.getRoleId()));

            reportMenuActionRepository.deleteByRoleId(input.getRoleId());
            roleMenuRepository.deleteByRoleId(input.getRoleId());
            userRoleRepository.deleteByRole(role);
            userRoleBusinessUnitRepository.deleteByRole(role);
            notificationRoleRepository.deleteByRole(role);
            reportRoleRepository.deleteByRole(role);
            roleBusinessUnitRepository.deleteByRole(role);
            emailTemplateRoleRepository.deleteByRoleId(input.getRoleId());
            loadTemplateRoleRepository.deleteByRole(role);
            deleteDashboardRoleByRoleId(input.getRoleId());
            roleRepository.delete(role);

            output.setRespEstado(1);
            output.setRespMensaje("Rol eliminado correctamente");
        } catch (Exception e) {
            logger.error("Error while deleting role:", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }

    public void deleteDashboardRoleByRoleId(Integer roleId) {
        String sql = "DELETE FROM ges.dashboard_rol WHERE rol_id = :roleId";
        entityManager.createNativeQuery(sql)
                .setParameter("roleId", roleId)
                .executeUpdate();
    }
}