package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.model.IsoCode;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sds.dto.ContainerRegisterInput;
import com.maersk.sd1.sds.dto.ContainerRegisterOutput;
import com.maersk.sd1.common.repository.ContainerRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class ContainerRegisterService {

    private static final Logger logger = LogManager.getLogger(ContainerRegisterService.class);

    private final ContainerRepository containerRepository;

    @Autowired
    public ContainerRegisterService(ContainerRepository containerRepository) {
        this.containerRepository = containerRepository;
    }

    @Transactional
    public ContainerRegisterOutput registerContainer(ContainerRegisterInput.Input input) {
        ContainerRegisterOutput output = new ContainerRegisterOutput();
        output.setRespNewId(0);
        output.setRespEstado(0);
        output.setRespMensaje("");

        try {
            Optional<Container> existingContainer = containerRepository.findByContainerNumberIgnoreCase(input.getContainerNumber());
            if (existingContainer.isPresent()) {
                Integer existingId = existingContainer.get().getId();
                String failMessage = String.format("Container of ID %d exists.", existingId);
                output.setRespEstado(2);
                output.setRespMensaje(failMessage);
                output.setRespNewId(0);
            } else {
                Container newContainer = new Container();

                newContainer.setContainerNumber(input.getContainerNumber());

                Catalog familyCatalog = new Catalog();
                familyCatalog.setId(input.getCatFamilyId());
                newContainer.setCatFamily(familyCatalog);

                Catalog sizeCatalog = new Catalog();
                sizeCatalog.setId(input.getCatSizeId());
                newContainer.setCatSize(sizeCatalog);

                Catalog containerTypeCatalog = new Catalog();
                containerTypeCatalog.setId(input.getCatContainerTypeId());
                newContainer.setCatContainerType(containerTypeCatalog);

                ShippingLine shippingLine = new ShippingLine();
                shippingLine.setId(input.getShippingLineId());
                newContainer.setShippingLine(shippingLine);

                newContainer.setContainerTare(input.getContainerTare());
                newContainer.setMaximunPayload(input.getMaximumPayload());

                if (input.getIsoCodeId() != null) {
                    IsoCode isoCode = new IsoCode();
                    isoCode.setId(input.getIsoCodeId());
                    newContainer.setIsoCode(isoCode);
                }

                if (input.getCatGradeId() != null) {
                    Catalog gradeCatalog = new Catalog();
                    gradeCatalog.setId(input.getCatGradeId());
                    newContainer.setCatGrade(gradeCatalog);
                }

                if (input.getCatReeferTypeId() != null) {
                    Catalog reeferCatalog = new Catalog();
                    reeferCatalog.setId(input.getCatReeferTypeId());
                    newContainer.setCatReeferType(reeferCatalog);
                }

                if (input.getCatEngineBrandId() != null) {
                    Catalog engineBrandCatalog = new Catalog();
                    engineBrandCatalog.setId(input.getCatEngineBrandId());
                    newContainer.setCatEngineBrand(engineBrandCatalog);
                }

                if (input.getManufactureDate() != null) {
                    newContainer.setManufactureDate(input.getManufactureDate());
                }

                newContainer.setShipperOwn(input.getShipperOwn());
                newContainer.setActive(input.getActive());

                User registrationUser = new User();
                registrationUser.setId(input.getUserRegistrationId());
                newContainer.setRegistrationUser(registrationUser);
                newContainer.setRegistrationDate(LocalDateTime.now());
                newContainer.setForSale(input.getForSale());

                Container savedContainer = containerRepository.save(newContainer);

                output.setRespEstado(1);
                output.setRespNewId(savedContainer.getId());
                output.setRespMensaje("Registered Successfully.");
            }
        } catch (Exception ex) {
            logger.error("An error occurred while registering container", ex);
            output.setRespEstado(0);
            output.setRespMensaje("Error: " + ex.getMessage());
            output.setRespNewId(0);
        }
        return output;
    }
}
