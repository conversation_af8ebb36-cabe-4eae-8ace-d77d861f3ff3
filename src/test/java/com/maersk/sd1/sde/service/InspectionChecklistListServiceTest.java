package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.InspectionChecklist;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sde.dto.InspectionChecklistListInput;
import com.maersk.sd1.sde.dto.InspectionChecklistListOutput;
import com.maersk.sd1.sde.repository.InspectionChecklistListRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class InspectionChecklistListServiceTest {

    @Mock
    InspectionChecklistListRepository inspectionChecklistListRepository;

    @InjectMocks
    InspectionChecklistListService inspectionChecklistListService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_ValidInput_When_GetInspectionChecklistList_Then_ReturnValidOutput() {
        // Given
        InspectionChecklistListInput.Input input = new InspectionChecklistListInput.Input();
        input.setPage(1);
        input.setSize(10);
        input.setInspectionChecklistId(null);
        input.setDescripcion("test");

        InspectionChecklist entity = new InspectionChecklist();
        entity.setId(123);
        entity.setDescription("test description");
        entity.setOrderNumber(10);
        entity.setActive(true);
        entity.setRegistrationDate(LocalDateTime.now());
        User regUser = new User();
        regUser.setId(99999);
        regUser.setNames("John");
        regUser.setFirstLastName("Doe");
        regUser.setSecondLastName("Smith");
        entity.setRegistrationUser(regUser);
        BusinessUnit bu = new BusinessUnit();
        bu.setId(777);
        entity.setSubBusinessUnit(bu);
        Catalog cat = new Catalog();
        cat.setId(888);
        entity.setCatInspectionType(cat);

        Page<InspectionChecklist> pageResult = new PageImpl<>(List.of(entity), PageRequest.of(0,10), 1);
        when(inspectionChecklistListRepository.findAll((Specification<InspectionChecklist>) any(), any(Pageable.class))).thenReturn(pageResult);

        // When
        InspectionChecklistListOutput output = inspectionChecklistListService.getInspectionChecklistList(input);

        // Then
        assertEquals(Collections.singletonList(Collections.singletonList(1L)), output.getTotalRegistros());
        assertEquals(1, output.getRecords().size());
        assertEquals(123, output.getRecords().get(0).getInspectionChecklistId());
        assertEquals("test description", output.getRecords().get(0).getDescripcion());
    }

    @Test
    void given_MissingPageAndSize_When_GetInspectionChecklistList_Then_ThrowException() {
        // Given
        InspectionChecklistListInput.Input input = new InspectionChecklistListInput.Input();
        // Not setting page or size

        try {
            // When
            inspectionChecklistListService.getInspectionChecklistList(input);
        } catch (IllegalArgumentException e) {
            // Then
            assertEquals("Page number must be a positive integer.", e.getMessage());
        }
    }

    @Test
    void given_InvalidSize_When_GetInspectionChecklistList_Then_ThrowException() {
        // Given
        InspectionChecklistListInput.Input input = new InspectionChecklistListInput.Input();
        input.setPage(1);
        input.setSize(0); // invalid size

        try {
            // When
            inspectionChecklistListService.getInspectionChecklistList(input);
        } catch (IllegalArgumentException e) {
            // Then
            assertEquals("Size must be a positive integer.", e.getMessage());
        }
    }

    @Test
    void given_EmptyResult_When_GetInspectionChecklistList_Then_ReturnZeroRecords() {
        // Given
        InspectionChecklistListInput.Input input = new InspectionChecklistListInput.Input();
        input.setPage(1);
        input.setSize(10);
        Page<InspectionChecklist> emptyPage = new PageImpl<>(Collections.emptyList(), PageRequest.of(0, 10), 0);
        when(inspectionChecklistListRepository.findAll((Specification<InspectionChecklist>) any(), any(Pageable.class))).thenReturn(emptyPage);

        // When
        InspectionChecklistListOutput output = inspectionChecklistListService.getInspectionChecklistList(input);

        // Then
        assertEquals(Collections.singletonList(Collections.singletonList(0L)), output.getTotalRegistros());
        assertTrue(output.getRecords().isEmpty());
    }
}