package com.maersk.sd1.seg.service;


import com.maersk.sd1.seg.dto.MenuListarDTO;
import com.maersk.sd1.common.repository.MenuRepository;
import com.maersk.sd1.common.model.Menu;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.List;

@Service
public class MenuListarService {

    private final MenuRepository menuRepository;

    @Autowired
    public MenuListarService(MenuRepository menuRepository) {
        this.menuRepository = menuRepository;
    }
    @Transactional(readOnly = true)
    public List<MenuListarDTO.Menu> getAllMenusWithRolesAndActions() {
        List<Object[]> results = menuRepository.findMenusWithRolesAndActions();
        return results.stream().map(result -> {
            Integer menuId = ((BigDecimal) result[0]).intValue();
            Menu menu = menuRepository.findById(menuId).orElseThrow(() -> new IllegalArgumentException("Menu not found for ID " + menuId));
            MenuListarDTO.Menu menuDto = new MenuListarDTO.Menu();
            menuDto.setMenuId(menu.getId());
            menuDto.setMenuPadreId(menu.getParentMenu() != null ? menu.getParentMenu().getId() : null);
            menuDto.setTitulo(menu.getTitle());
            menuDto.setDescripcion(menu.getDescription());
            menuDto.setPlantilla(menu.getTemplate());
            menuDto.setIcono(menu.getIcon());
            menuDto.setOrden(menu.getOrder());
            menuDto.setEstado(menu.getStatus());
            menuDto.setTieneId(menu.getHasId());
            menuDto.setMenuBaseId(menu.getBaseMenu() != null ? menu.getBaseMenu().getId() : null);
            if (menuDto.getRoles() == null) {
                menuDto.setRoles(new MenuListarDTO.Rol());
            }
            if (menuDto.getAcciones() == null) {
                menuDto.setAcciones(new MenuListarDTO.Accion());
            }
            menuDto.getRoles().setNombre(result[10] != null ? (String) result[10] : null);
            menuDto.getAcciones().setDefectoActivo(result[11] != null ? (String) result[11] : null);
            return menuDto;
        }).toList();
    }

}
