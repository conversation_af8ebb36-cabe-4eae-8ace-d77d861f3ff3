package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.common.repository.EstimateEmrDetailRepository;
import com.maersk.sd1.sde.dto.GetEstimateInfoDownloadInputDTO;
import com.maersk.sd1.sde.dto.GetEstimateInfoDownloadOutputDTO;
import com.maersk.sd1.common.repository.EstimateEmrRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.maersk.sd1.sde.dto.GetEstimateInfoDownloadOutputDTO.EstimateHeaderDTO;
import com.maersk.sd1.sde.dto.GetEstimateInfoDownloadOutputDTO.EstimateDetailDTO;

@Service
@RequiredArgsConstructor
public class GetEstimateInfoDownloadService {

    private static final Logger logger = LogManager.getLogger(GetEstimateInfoDownloadService.class);

    private final EstimateEmrRepository estimateEmrRepository;
    private final EstimateEmrDetailRepository estimateEmrDetailRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;

    @Transactional(readOnly = true)
    public GetEstimateInfoDownloadOutputDTO getEstimateInfoDownload(GetEstimateInfoDownloadInputDTO.Input input) {
        // Log the input.
        logger.info("Start getEstimateInfoDownload with input: {}", input);

        // Parse the input
        List<Integer> estimateIds = input.getEstimateIds();
        Integer languageId = input.getLanguageId();
        Boolean isCleaning = "1".equals(input.getIsCleaning());

        // 1) Fetch the list of EstimateEmr by IDs
        List<EstimateEmr> estimateEmrList = estimateEmrRepository.findAllHeadersByIdIn(estimateIds);
        // 2) Map them to Header DTO
        List<EstimateHeaderDTO> headerList = new ArrayList<>();

        for (EstimateEmr e : estimateEmrList) {
            EstimateHeaderDTO dto = new EstimateHeaderDTO();
            dto.setEstimateEmrId(e.getId());
            if(e.getCatEstimateType()!=null)
                dto.setEstimateType(e.getCatEstimateType().getId());
            // In a real scenario, we might retrieve translation from some service:
            dto.setEstimateTypeDesc(getCatalogTranslationDesc(e.getCatEstimateType(), languageId));

            if(e.getEir()!=null) {
                dto.setEir(e.getEir().getId());
            }
            if(e.getContainer()!=null) {
                dto.setContainerId(e.getContainer().getId());
                dto.setContainerNumber(e.getContainer().getContainerNumber());
                // container type & size from e.getContainer() & those catalogs
                dto.setContainerType(catalogLanguageRepository.fnCatalogTranslationDesc(e.getContainer().getCatContainerType().getId(), languageId));
                dto.setContainerSize(catalogLanguageRepository.fnCatalogTranslationDesc(e.getContainer().getCatSize().getId(), languageId));
            }

            // Inspector
            String inspectorName = buildPersonName(e.getInspectorPerson());
            dto.setInspector(inspectorName.isEmpty() ? "-" : inspectorName);

            // Estimator
            String estimatorName = buildUserName(e.getEstimatorUser());
            dto.setEstimator(estimatorName.isEmpty() ? "-" : estimatorName);

            // Damage cause
            if(e.getCatEstimateDamageCause()!=null) {
                dto.setDamageCauseId(e.getCatEstimateDamageCause().getId());
                dto.setDamageCause(getCatalogTranslationDescLong(e.getCatEstimateDamageCause(), languageId));
            }

            // Currency
            if(e.getCurrency()!=null) {
                dto.setCurrencyId(e.getCurrency().getId());
                dto.setCurrencyAbbreviation(e.getCurrency().getAbbreviation());
                dto.setCurrencySymbol(e.getCurrency().getSymbol());
            }

            // inspection Date
            dto.setInspectionDate(e.getEstimateDateInspection());

            // approval date
            dto.setApprovalDate(e.getApproveRejectEstimateDate());

            // total hours
            dto.setTotalHours(e.getEstimateTotalHours());

            // total cost
            dto.setTotalCost(e.getEstimateTotalCost());

            // estimate status
            dto.setEstimateStatus(getCatalogTranslationDesc(e.getCatEstimateStatus(), languageId));

            // shipping line
            if(e.getShippingLine()!=null) {
                dto.setShippingLine(e.getShippingLine().getName());
            }

            // gate in date
            if(e.getEir()!=null) {
                dto.setGateInDate(e.getEir().getTruckArrivalDate());
            }

            // yard location => replicate sdy.fn_get_container_location if needed, set empty if not found
            // we don't have the function, let's set it to an empty or stub
            dto.setYardLocation("");

            // eirActivityZone inspectionObservationComment => we might look it up if needed
            // We'll do a simplified approach unless we want an extra fetch. We'll set "-" or from eir.
            // The stored procedure joined eirActivityZone with cat_actividad_zona_id = "cat_43161_box_inspection". Not done by JPA here.
            // We'll just set e.g. blank:
            dto.setInspectionObservationComment("-");

            headerList.add(dto);
        }

        // 3) Fetch detail list
        List<EstimateEmrDetail> detailEntities = estimateEmrDetailRepository.findAllDetailsByEstimateIds(estimateIds);
        // Filter by isCleaning logic => (@is_cleaning IS NULL OR (CASE WHEN cat_cleaning_type_id IS NOT NULL THEN 1 ELSE 0 END ) = @is_cleaning)
        List<EstimateEmrDetail> filteredDetails = new ArrayList<>();
        for(EstimateEmrDetail d : detailEntities){
            boolean passesClean = true;
            if(isCleaning != null){
                boolean hasCleaningType = (d.getCatCleaningType() != null);
                // If isCleaning = true => must have catCleaningType
                // If isCleaning = false => must not have catCleaningType
                // Actually, in T-SQL it's 1 or 0, we can interpret boolean -> 1 or 0.
                if(isCleaning && !hasCleaningType) {
                    passesClean = false;
                } else if(!isCleaning && hasCleaningType){
                    passesClean = false;
                }
            }
            if(passesClean) {
                filteredDetails.add(d);
            }
        }

        // 4) Map them to dto
        List<EstimateDetailDTO> detailList = filteredDetails.stream().map(d -> {
            EstimateDetailDTO dto = new EstimateDetailDTO();
            dto.setEstimateEmrDetailId(d.getId());
            if(d.getCatDamageLocationEstimate()!=null) {
                dto.setDamageLocationId(d.getCatDamageLocationEstimate().getId());
                dto.setDamageLocation(
                        getCatalogTranslationDesc(d.getCatDamageLocationEstimate(), languageId) + "-" +
                                getCatalogTranslationDescLong(d.getCatDamageLocationEstimate(), languageId)
                );
            }

            if(d.getCatDamageTypeEstimate()!=null) {
                dto.setDamageTypeId(d.getCatDamageTypeEstimate().getId());
                dto.setDamageType(
                        getCatalogTranslationDesc(d.getCatDamageTypeEstimate(), languageId) + "-" +
                                getCatalogTranslationDescLong(d.getCatDamageTypeEstimate(), languageId)
                );
            }

            if(d.getCatComponentEstimate()!=null) {
                dto.setComponentId(d.getCatComponentEstimate().getId());
                dto.setComponent(
                        getCatalogTranslationDesc(d.getCatComponentEstimate(), languageId) + "-" +
                                getCatalogTranslationDescLong(d.getCatComponentEstimate(), languageId)
                );
            }

            if(d.getCatRepairMethodEstimate()!=null) {
                dto.setRepairMethodId(d.getCatRepairMethodEstimate().getId());
                dto.setRepairMethod(
                        getCatalogTranslationDesc(d.getCatRepairMethodEstimate(), languageId) + "-" +
                                getCatalogTranslationDescLong(d.getCatRepairMethodEstimate(), languageId)
                );
            }

            dto.setCedexMercCode(d.getCedexMercCode());
            dto.setCedexMercDesc(d.getCedexMercDescription());

            if(d.getCatDimensionTypeEstimate()!=null) {
                dto.setDimensionId(d.getCatDimensionTypeEstimate().getId());
                dto.setDimension(
                        getCatalogTranslationDesc(d.getCatDimensionTypeEstimate(), languageId) + "-" +
                                getCatalogTranslationDescLong(d.getCatDimensionTypeEstimate(), languageId)
                );
            }

            dto.setLength(d.getDimensionLong());
            dto.setWidth(d.getDimensionWide());
            dto.setPieces(d.getPartsRepair());
            dto.setCostMaterialPiece(d.getCostMaterialXPiece());
            dto.setHoursPiece(d.getHhXPiece());

            if(d.getCatAssumeCostEstimate()!=null) {
                dto.setAssumeCostId(d.getCatAssumeCostEstimate().getId());
                dto.setAssumeCost(
                        getCatalogTranslationDescLong(d.getCatAssumeCostEstimate(), languageId)
                );
            }

            if(d.getCatUnitMeassureEstimate()!=null) {
                dto.setUnitMeassureId(d.getCatUnitMeassureEstimate().getId());
                dto.setUnitMeassure(
                        getCatalogTranslationDesc(d.getCatUnitMeassureEstimate(), languageId)
                );
            }

            dto.setEstimateEmrId(d.getEstimateEmr().getId());
            return dto;
        }).collect(Collectors.toList());

        // 5) Prepare output
        GetEstimateInfoDownloadOutputDTO output = new GetEstimateInfoDownloadOutputDTO();
        output.setHeaderList(headerList);
        output.setDetailList(detailList);

        logger.info("Finished getEstimateInfoDownload.");
        return output;
    }

    // Placeholder methods for the custom T-SQL user-defined functions.
    private String getCatalogTranslationDesc(Catalog catalog, Integer languageId) {
        if(catalog == null) return "";

        return catalogLanguageRepository.fnCatalogTranslationDesc(
                catalog.getId(), languageId
        );
    }

    private String getCatalogTranslationDescLong(Catalog catalog, Integer languageId) {
        if(catalog == null) return "";
        return catalogLanguageRepository.fnCatalogTranslationDescLong(
                catalog.getId(), languageId
        );
    }

    private String buildPersonName(Person person){
        if(person == null) return "";
        String name = (person.getNames() == null ? "" : person.getNames().trim());
        String fln = (person.getFirstLastName() == null ? "" : person.getFirstLastName().trim());
        String sln = (person.getSecondLastName() == null ? "" : person.getSecondLastName().trim());
        return (name + " " + fln + " " + sln).trim();
    }

    private String buildUserName(User user){
        if(user == null) return "";
        String n = (user.getNames() == null ? "" : user.getNames().trim());
        String fln = (user.getFirstLastName() == null ? "" : user.getFirstLastName().trim());
        String sln = (user.getSecondLastName() == null ? "" : user.getSecondLastName().trim());
        return (n + " " + fln + " " + sln).trim();

    }
}