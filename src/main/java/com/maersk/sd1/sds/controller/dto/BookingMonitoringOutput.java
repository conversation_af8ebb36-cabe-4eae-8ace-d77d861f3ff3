package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class BookingMonitoringOutput {

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<Integer> total;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<BookingMonitoringOutputItem> records;

}
