package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Notification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

public interface NotificationRepository extends JpaRepository<Notification, Integer> {

    @Transactional
    @Query(value = "INSERT INTO seg.notificacion " +
            "(titulo, subtitulo, descripcion, icono, nivel, unica_vez, unica_alerta, fecha_caducidad, estado, fecha_registro, usuario_registro_id, business_unit_id) " +
            "VALUES (:title, :subtitle, :description, :icon, :level, :onlyTime, :onlyAlert, :expirationDate, :notificationStatus, CURRENT_TIMESTAMP, :userId, :businessUnitId); " +
            "SELECT SCOPE_IDENTITY()", nativeQuery = true)
    int saveNotification(@Param("title") String title,
                         @Param("subtitle") String subtitle,
                         @Param("description") String description,
                         @Param("icon") String icon,
                         @Param("level") char level,
                         @Param("onlyTime") char onlyTime,
                         @Param("onlyAlert") char onlyAlert,
                         @Param("expirationDate") LocalDateTime expirationDate,
                         @Param("notificationStatus") char notificationStatus,
                         @Param("userId") int userId,
                         @Param("businessUnitId") int businessUnitId);

    @Query("""
                SELECT COUNT(N)
                FROM Notification N
                WHERE (:roles IS NULL OR N.id IN :roleNotificationIds)
            """)
    Integer countByRoles(@Param("roles") String roles,
                         @Param("roleNotificationIds") List<Integer> roleNotificationIds);

    @Query("""
            SELECT n FROM Notification n
            WHERE (:businessUnitId IS NULL OR n.businessUnit.id = :businessUnitId)            
            AND (:roles IS NULL OR n.id IN :notificationIds)
            ORDER BY n.id DESC
            """)
    Page<Notification> findNotifications(
            @Param("businessUnitId") Integer businessUnitId,
            @Param("notificationIds") List<Integer> notificationIds,
            @Param("roles") String roles,
            Pageable pageable
    );
}