package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.seg.controller.dto.UserValidateInput;
import com.maersk.sd1.seg.controller.dto.UserValidateOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@RequiredArgsConstructor
@Service
public class UserValidateService {

    private static final Logger logger =  LogManager.getLogger(UserValidateService.class.getName());

    private final UserRepository userRepository;

    public UserValidateOutput validateUser(UserValidateInput.Root root) {
        logger.info("Starting user validation");

        String id = root.getPrefix().getInput().getId();
        String email = root.getPrefix().getInput().getEmail();
        logger.debug("Received input - ID: {}, Email: {}", id, email);

        User user = userRepository.findUserDetailsByIdAndEmail(email, id);
        if (user == null) {
            logger.warn("User not found for ID: {} and Email: {}", id, email);
            return null;
        }

        logger.info("User found for the ID: {}", id);
        return createUserValidateOutput(user);
    }

    private UserValidateOutput createUserValidateOutput(User user) {
        LocalDateTime currentDateTime = LocalDateTime.now();

        UserValidateOutput output = new UserValidateOutput();
        output.setId(user.getId());
        output.setAlias(user.getAlias());
        output.setMail(user.getMail());

        LocalDateTime dateChangeKey = user.getDateChangeKey();
        if (dateChangeKey != null) {
            long daysUntilKeyChange = ChronoUnit.DAYS.between(currentDateTime.toLocalDate(), dateChangeKey.toLocalDate());
            output.setDaysUntilKeyChange((int) daysUntilKeyChange);
        } else {
            output.setDaysUntilKeyChange(null);
        }

        output.setNames(user.getNames());
        output.setFirstLastName(user.getFirstLastName());
        output.setSecondLastName(user.getSecondLastName());
        output.setCompanyId(user.getCompany().getId());
        output.setStatus(user.getStatus());

        return output;
    }
}