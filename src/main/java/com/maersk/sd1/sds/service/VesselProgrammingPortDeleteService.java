package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.repository.BookingRepository;
import com.maersk.sd1.common.repository.CargoDocumentRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.VesselProgrammingPortRepository;
import com.maersk.sd1.sds.dto.VesselProgrammingPortDeleteOutput;
import com.maersk.sd1.common.model.VesselProgrammingPort;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@RequiredArgsConstructor
@Service
public class VesselProgrammingPortDeleteService {

    private static final Logger logger = LogManager.getLogger(VesselProgrammingPortDeleteService.class);

    private final VesselProgrammingPortRepository vesselProgrammingPortRepository;
    private final BookingRepository bookingRepository;
    private final CargoDocumentRepository cargoDocumentRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public VesselProgrammingPortDeleteOutput deleteVesselProgrammingPort(Integer vesselProgrammingPortId,
                                                                         Long userRegistrationId,
                                                                         Integer languageId) {
        VesselProgrammingPortDeleteOutput output = new VesselProgrammingPortDeleteOutput();
        try {
            logger.info("Attempting to delete VesselProgrammingPort with ID: {} by user: {}", vesselProgrammingPortId, userRegistrationId);
            VesselProgrammingPort vesselProgrammingPort = vesselProgrammingPortRepository.findById(vesselProgrammingPortId)
                    .orElseThrow(() -> new IllegalArgumentException("VesselProgrammingPort not found"));

            Integer programacionNaveId = vesselProgrammingPort.getVesselProgramming().getId();
            Integer portId = vesselProgrammingPort.getPort().getId();

            long bookingCount = bookingRepository.countByVesselProgrammingIdAndPortId(programacionNaveId, portId);
            long cargoDocCount = cargoDocumentRepository.countByVesselProgrammingIdAndPortId(programacionNaveId, portId);

            if (bookingCount > 0 || cargoDocCount > 0) {
                output.setRespEstado(2);
                String message = messageLanguageRepository.fnTranslatedMessage("DEL_PROGRAMACION_NAVE_PUERTO", 1, languageId);
                output.setRespMensaje(message);
            } else {
                vesselProgrammingPortRepository.delete(vesselProgrammingPort);
                output.setRespEstado(1);
                String message = messageLanguageRepository.fnTranslatedMessage("DEL_PROGRAMACION_NAVE_PUERTO", 2, languageId);
                output.setRespMensaje(message);
            }
        } catch (Exception e) {
            logger.error("Error occurred while deleting VesselProgrammingPort:", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}
