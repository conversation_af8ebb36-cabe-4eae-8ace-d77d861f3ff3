package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.controller.dto.SignatureGetActiveInput;
import com.maersk.sd1.ges.controller.dto.SignatureGetActiveOutput;
import com.maersk.sd1.ges.service.SignatureGetActiveService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/Inlandnet/module/ind/INDSignatureServiceImp")
public class SignatureGetActiveController {

    private static final Logger logger = LogManager.getLogger(SignatureGetActiveController.class);

    private final SignatureGetActiveService signatureGetActiveService;

    @PostMapping("/admsignatureGetActive")
    public ResponseEntity<ResponseController<SignatureGetActiveOutput>> signatureGetActive(@RequestBody @Valid SignatureGetActiveInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
            }

            SignatureGetActiveInput.Input input = request.getPrefix().getInput();

            SignatureGetActiveOutput output = signatureGetActiveService.getActiveSignature(
                    input.getUserId(),
                    input.getPersonId()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while retrieving active signature.", e);
            SignatureGetActiveOutput output = new SignatureGetActiveOutput();
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }
}