package com.maersk.sd1.sde.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.controller.dto.*;
import com.maersk.sd1.sde.dto.EstimateEmrReplacementDTO;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ListEstimateEmrByIdService {

    private static final Logger logger = LogManager.getLogger(ListEstimateEmrByIdService.class);

    private EstimateEmrRepository estimateEmrRepository;

    private EstimateEmrDetailRepository estimateEmrDetailRepository;

    private StockEmptyRepository stockEmptyRepository;

    private CatalogRepository catalogRepository;

    private EirActivityZoneRepository eirActivityZoneRepository;

    private EstimateEmrSettingRepository estimateEmrSettingRepository;

    private EirObservationInspectorRepository eirObservationInspectorRepository;

    private EstimateEmrEirPhotoRepository estimateEmrEirPhotoRepository;

    private EstimateEmrDetailPhotoRepository estimateEmrDetailPhotoRepository;

    private EstimateEmrDetailRepairPhotoRepository estimateEmrDetailRepairPhotoRepository;

    private MessageLanguageRepository messageLanguageRepository;

    private EstimateAutoApprovalConfigRepository estimateAutoApprovalConfigRepository;

    private EstimateEmrSpareRepository estimateEmrSpareRepository;

    private BusinessUnitConfigRepository businessUnitConfigRepository;

    @Autowired
    public ListEstimateEmrByIdService(
            EstimateEmrRepository estimateEmrRepository,
            CatalogRepository catalogRepository,
            EstimateEmrDetailRepository estimateEmrDetailRepository,
            MessageLanguageRepository messageLanguageRepository,
            EstimateEmrSettingRepository estimateEmrSettingRepository,
            EstimateAutoApprovalConfigRepository estimateAutoApprovalConfigRepository,
            EirObservationInspectorRepository eirObservationInspectorRepository,
            StockEmptyRepository stockEmptyRepository,
            EirActivityZoneRepository eirActivityZoneRepository,
            EstimateEmrEirPhotoRepository estimateEmrEirPhotoRepository,
            EstimateEmrDetailPhotoRepository estimateEmrDetailPhotoRepository,
            EstimateEmrDetailRepairPhotoRepository estimateEmrDetailRepairPhotoRepository,
            EstimateEmrSpareRepository estimateEmrSpareRepository,
            BusinessUnitConfigRepository businessUnitConfigRepository) {
        this.estimateEmrRepository = estimateEmrRepository;
        this.catalogRepository = catalogRepository;
        this.estimateEmrDetailRepository = estimateEmrDetailRepository;
        this.messageLanguageRepository = messageLanguageRepository;
        this.estimateEmrSettingRepository = estimateEmrSettingRepository;
        this.estimateAutoApprovalConfigRepository = estimateAutoApprovalConfigRepository;
        this.eirObservationInspectorRepository = eirObservationInspectorRepository;
        this.stockEmptyRepository = stockEmptyRepository;
        this.eirActivityZoneRepository = eirActivityZoneRepository;
        this.estimateEmrEirPhotoRepository = estimateEmrEirPhotoRepository;
        this.estimateEmrDetailPhotoRepository = estimateEmrDetailPhotoRepository;
        this.estimateEmrDetailRepairPhotoRepository = estimateEmrDetailRepairPhotoRepository;
        this.estimateEmrSpareRepository = estimateEmrSpareRepository;
        this.businessUnitConfigRepository = businessUnitConfigRepository;

    }

    @Transactional(readOnly = true)
    public ListEstimateEmrByIdOutput getEstimateEmrDataById(Integer estimateEmrId, Integer languageId) {
        ListEstimateEmrByIdOutput output = new ListEstimateEmrByIdOutput();

        EstimateEmr estimateEmr = estimateEmrRepository.findById(estimateEmrId)
                .orElseThrow(() -> new EntityNotFoundException("Estimate EMR not found"));

        if (estimateEmr == null) {
            return output;
        }
        output.setEstimateEmrDTO(List.of(buildEstimateEmrDTO(estimateEmr, languageId)));
        output.setDamages(getEstimateDetails(estimateEmrId));
        output.setObservations(getInspectorObservations(estimateEmr.getEir().getId(), languageId));
        output.setEstimatePhotos(getEstimateHeaderPhotos(estimateEmrEirPhotoRepository.findActiveByEstimateEmrId(estimateEmrId)));
        output.setDamagesPhotos(getEstimateDetailPhotos(estimateEmrDetailPhotoRepository.findActiveByEstimateEmrId(estimateEmrId)));
        output.setRepairsPhotos(getEstimateRepairPhotos(estimateEmrDetailRepairPhotoRepository.findActiveByEstimateEmrId(estimateEmrId)));

        return output;
    }


    private ListEstimateEmrByIdOutput.EstimateEmrDTO buildEstimateEmrDTO(EstimateEmr emr, Integer languageId) {
        ListEstimateEmrByIdOutput.EstimateEmrDTO dto = new ListEstimateEmrByIdOutput.EstimateEmrDTO();

        dto.setEstimateEmrId(emr.getId());
        dto.setEstimateInspectionDate(emr.getEstimateDateInspection() == null ? null : processDate(emr.getSubBusinessUnit().getId(), emr.getEstimateDateInspection()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")));
        dto.setCatEstimateDamageReasonId(emr.getCatEstimateDamageCause().getId());
        dto.setCatEstimateModeId(emr.getCatEstimateEMRMode().getId());

        dto.setCatEstimateWithoutGateInId(emr.getWithoutGateinEstimate());
        dto.setContainerId(emr.getContainer().getId());
        dto.setContainerNumber(emr.getContainer().getContainerNumber());
        dto.setContainerSize(emr.getContainer().getCatSize().getDescription());
        dto.setContainerType(emr.getContainer().getCatContainerType().getDescription() + " - " + emr.getContainer().getCatContainerType().getLongDescription());
        dto.setContainerClass(emr.getContainer().getCatGrade().getDescription());
        dto.setManufacturingDate(emr.getContainer().getManufactureDate() == null ? null : emr.getContainer().getManufactureDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        dto.setReeferType(emr.getContainer().getCatReeferType() == null ? null : emr.getContainer().getCatReeferType().getDescription());
        dto.setMotorBrand(emr.getContainer().getCatEngineBrand() == null ? null : emr.getContainer().getCatEngineBrand().getDescription());
        dto.setShippingLine(emr.getShippingLine().getName());
        dto.setEirGateIn(emr.getEir().getId());
        dto.setGateInDate(processDate(emr.getSubBusinessUnit().getId(), emr.getEir().getTruckArrivalDate()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")));
        // dto.setGateOutDate(processDate(emr.getSubBusinessUnit().getId(), emr.getEir().getTruckDepartureDate()));
        // dto.setStructureCondition(emr. getStructureCondition());
        // dto.setMachineCondition(emr.getMachineCondition());
        dto.setCatContainerSizeId(emr.getContainer().getCatSize().getId());
        dto.setCatContainerTypeId(emr.getContainer().getCatContainerType().getId());
        dto.setCatContainerClassId(emr.getContainer().getCatGrade().getId());
        dto.setCatReeferTypeId(emr.getContainer().getCatReeferType() == null ? null : emr.getContainer().getCatReeferType().getId());
        dto.setCatMotorBrandId(emr.getContainer().getCatEngineBrand() == null ? null : emr.getContainer().getCatEngineBrand().getId());

        dto.setInspectorId(emr.getInspectorPerson().getId());
        dto.setInspectorName(emr.getInspectorPerson().getNames() + " " + emr.getInspectorPerson().getFirstLastName());

        dto.setEstimateIssueDate(processDate(emr.getSubBusinessUnit().getId(), emr.getEstimateDateIssue()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")));
        dto.setEstimateTypeId(emr.getCatEstimateType().getId());
        dto.setEstimateTypeName(catalogRepository.findTranslatedShortDesc(emr.getCatEstimateType().getId(), languageId));
        dto.setCurrencyId(emr.getCurrency().getId());
        dto.setCurrencyName(emr.getCurrency().getName());
        dto.setEstimatorUserId(emr.getEstimatorUser() == null ? null : emr.getEstimatorUser().getId());
        dto.setEstimatorName(emr.getEstimatorUser() == null ? null : emr.getEstimatorUser().getNames() + " " + emr.getEstimatorUser().getFirstLastName() + " " + emr.getEstimatorUser().getSecondLastName());
        dto.setEstimateStatusId(emr.getCatEstimateStatus() == null ? null : emr.getCatEstimateStatus().getId());
        dto.setEstimateStatusName(emr.getCatEstimateStatus() == null ? null : catalogRepository.findTranslatedShortDesc(emr.getCatEstimateStatus().getId(), languageId));
        dto.setLaborCost(emr.getEstimateCostHh());

        dto.setEstimateRemarks(emr.getRemarkEstimate());
        dto.setEstimateObservations(emr.getEstimateObs());
        dto.setBlockControlSending(emr.getBlockSendingCtrl());
        //dto.setBlockControlReason(emr.getBlock);
        dto.setBlockSending(emr.getBlockSending());
        dto.setBlockSendingReason(emr.getBlockSendingReason());
        //dto.setTransmissionStatus(emr.getTransmissionStatus());
        dto.setTotalEstimatedHours(emr.getEstimateTotalHours());
        dto.setTotalEstimatedCost(emr.getEstimateTotalCost());
        dto.setTotalLineCost(estimateEmrDetailRepository.sumTotalLineCost(emr.getId()));
        dto.setTotalClientCost(estimateEmrDetailRepository.sumTotalClientCost(emr.getId()));
        dto.setIsReefer(emr.getIsReefer());
        dto.setShippingLineId(emr.getShippingLine().getId());

        Integer appliesMercSending = estimateEmrSettingRepository.checkIfAppliesMercSending(
                emr.getShippingLine().getId(), emr.getSubBusinessUnit().getId()
        );
        dto.setAppliesMercSending(appliesMercSending == 1 ? 1 : 0);


        dto.setMercPlusEstimateNumber(emr.getMercPlusEstimateNumber());
        //dto.setObservationComments(emr.getObservationComments());//ask about conditions
        dto.setFlagAutoApproval(emr.getFlagAutoApproval());
        //dto.setAutoApprovalAmount(emr.getAutoApprovalAmount());
        dto.setOriginalEstimateId(emr.getOriginalEMREstimate() == null ? null : emr.getOriginalEMREstimate().getId());

        setStockConditions(emr.getEir() == null ? null : emr.getEir().getId(), dto, languageId); // For setStructureCondition,  setMachineCondition and observationComments
        setBlockControlReason(emr, dto, languageId);// For setBlockControlReason
        setTransmissionStatus(emr, dto, languageId);// For setTransmissionStatus
        setAutoApprovalAmount(emr, dto);// For setAutoApprovalAmount
        return dto;
    }

    private LocalDateTime processDate(Integer subBusinessUnitId, LocalDateTime date) {
        return businessUnitConfigRepository.fnDatetimeGet(subBusinessUnitId, date);
    }

    private void setStockConditions(Integer eirId, ListEstimateEmrByIdOutput.EstimateEmrDTO dto, Integer languageId) {
        if (eirId != null) {
            Optional<StockEmpty> stockEmptyResult = stockEmptyRepository.findActiveByGateInEirId(eirId);

            if (stockEmptyResult.isEmpty()) {
                dto.setStructureCondition("");
                dto.setObservationComments("");
                dto.setMachineCondition("");
            } else {
                List<EirActivityZone> eirActivityZones = eirActivityZoneRepository.findByEirIdAndActiveTrue(eirId);
                if (!eirActivityZones.isEmpty()) {

                    String boxCondition = eirActivityZones.stream()
                            .filter(it -> it.getCatZoneActivity().getId() == 43162)
                            .findFirst()
                            .map(eaz -> eaz.getConcluded() ?
                                    (eaz.getEir().getStructureWithDamage() ? "D" : "S") : "P")
                            .orElse("");
                    dto.setStructureCondition(translateConditionMessage(boxCondition, languageId, true));

                    String inspectionObservation = eirActivityZones.stream()
                            .filter(it -> it.getCatZoneActivity().getId() == 43162)
                            .findFirst()
                            .map(EirActivityZone::getInspectionObservationComment)
                            .orElse("");
                    dto.setObservationComments(inspectionObservation);

                    String machineCondition = eirActivityZones.stream()
                            .filter(it -> it.getCatZoneActivity().getId() == 43163)
                            .findFirst()
                            .map(eaz -> eaz.getConcluded() ?
                                    (eaz.getEir().getMachineryWithDamage() ? "D" : "S") : "P")
                            .orElse("");
                    dto.setMachineCondition(translateConditionMessage(machineCondition, languageId, false));
                }

                if (stockEmptyResult.get().getGateOutEir() != null) {
                    LocalDateTime localDateTime = processDate(stockEmptyResult.get().getSubBusinessUnit().getId(), stockEmptyResult.get().getGateOutEir().getTruckDepartureDate());
                    if(localDateTime ==null){
                        dto.setGateOutDate(null);
                    }else{
                        dto.setGateOutDate(localDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")));
                    }

                }

            }
        }
    }

    private String translateConditionMessage(String condition, Integer languageId, boolean isStructure) {
        int code = 0;
        switch (condition) {
            case "P":
                if (isStructure) {
                    code = 1;
                } else {
                    code = 2;
                }
                break;
            case "S":
                code = 3;
                break;
            case "D":
                code = 4;
                break;
        }
        return code == 0 ? "" : messageLanguageRepository.findMensaje("LIS_ESTIMADO_BUSCNT", code, languageId);
    }

    private void setBlockControlReason(EstimateEmr estimateEmr, ListEstimateEmrByIdOutput.EstimateEmrDTO dto, Integer idiomaId) {
        if (dto.getBlockControlSending() != null && dto.getBlockControlSending() == 1) {
            String translatedMessage = "";
            if (estimateEmr.getCatEstimateType().getId() != null && estimateEmr.getCatEstimateType().getId() == 47490) {
                translatedMessage = messageLanguageRepository.findMensaje("LIS_ESTIMADO", 1, idiomaId);
            } else {
                translatedMessage = messageLanguageRepository.findMensaje("LIS_ESTIMADO", 2, idiomaId);
            }
            dto.setBlockControlReason(translatedMessage);
        } else {
            dto.setBlockControlReason("");
        }
    }

    private void setTransmissionStatus(EstimateEmr estimateEmr, ListEstimateEmrByIdOutput.EstimateEmrDTO dto, Integer idiomaId) {
        if (estimateEmr.getCatApprovalSendEstimateStatus() != null && estimateEmr.getCatApprovalSendEstimateStatus().getId() != 47493) {
            String translatedMessage = catalogRepository.findTranslatedShortDesc(estimateEmr.getCatApprovalSendEstimateStatus().getId(), idiomaId);
            dto.setTransmissionStatus(translatedMessage);
        } else {
            String statusFromSeteo = getStatusFromSeteoEstimadoEmr(estimateEmr);

            if ("N".equals(statusFromSeteo)) {
                dto.setTransmissionStatus(messageLanguageRepository.fnTranslatedMessage("GENERAL", 4, 1)); // '<< no aplica envío >>'
            } else {
                dto.setTransmissionStatus(catalogRepository.findTranslatedShortDesc(47493, idiomaId));

            }
        }
    }

    private String getStatusFromSeteoEstimadoEmr(EstimateEmr estimateEmr) {
        Optional<String> status = estimateEmrSettingRepository.findStatusByParams(
                estimateEmr.getShippingLine().getId(),
                estimateEmr.getSubBusinessUnit().getId()
        );

        return status.orElse("N");
    }

    private void setAutoApprovalAmount(EstimateEmr estimateEmr, ListEstimateEmrByIdOutput.EstimateEmrDTO dto) {
        if (estimateEmr.getEir().getLocalSubBusinessUnit().getId() != null &&
                estimateEmr.getContainer().getCatContainerType().getId() != null &&
                estimateEmr.getShippingLine().getId() != null) {

            Optional<BigDecimal> autoApprovalAmount = estimateAutoApprovalConfigRepository.findAutoApprovalAmount(
                    estimateEmr.getEir().getLocalSubBusinessUnit().getId(),
                    estimateEmr.getContainer().getCatContainerType().getId(),
                    estimateEmr.getShippingLine().getId()
            );

            dto.setAutoApprovalAmount(autoApprovalAmount.orElse(BigDecimal.valueOf(0)));
        } else {
            dto.setAutoApprovalAmount(null);
        }
    }

    public List<ListEstimateEmrByIdOutput.EstimatedEmrDetailDTO> getEstimateDetails(Integer id) {
        List<EstimateEmrDetail> details = estimateEmrDetailRepository.findByEstimateEmr_IdAndActiveTrue(id);
        List<ListEstimateEmrByIdOutput.EstimatedEmrDetailDTO> result = new ArrayList<>();
        for (int i = 0; i < details.size(); i++) {
            EstimateEmrDetail detail = details.get(i);
            ListEstimateEmrByIdOutput.EstimatedEmrDetailDTO dto = new ListEstimateEmrByIdOutput.EstimatedEmrDetailDTO();
            dto.setItem(((long) i) + 1);
            dto.setEstimateEmrDetailId(detail.getId());
            dto.setDamageLocationCategoryId(detail.getCatDamageLocationEstimate() == null ? null : detail.getCatDamageLocationEstimate().getId());
            dto.setDamageLocation(detail.getCatDamageLocationEstimate() == null ? null : detail.getCatDamageLocationEstimate().getDescription());
            dto.setDamageTypeCategoryId(detail.getCatDamageTypeEstimate() == null ? null : detail.getCatDamageTypeEstimate().getId());
            dto.setDamageType(detail.getCatDamageTypeEstimate() == null ? null : detail.getCatDamageTypeEstimate().getDescription());
            dto.setComponentCategoryId(detail.getCatComponentEstimate() == null ? null : detail.getCatComponentEstimate().getId());
            dto.setComponent(detail.getCatComponentEstimate() == null ? null : detail.getCatComponentEstimate().getDescription());
            dto.setRepairMethodCategoryId(detail.getCatRepairMethodEstimate() == null ? null : detail.getCatRepairMethodEstimate().getId());
            dto.setRepairMethod(detail.getCatRepairMethodEstimate() == null ? null : detail.getCatRepairMethodEstimate().getDescription());
            dto.setCedexMercCode(detail.getCedexMercCode());
            dto.setCedexMercDescription(detail.getCedexMercDescription());
            dto.setDimensionTypeCategoryId(detail.getCatDimensionTypeEstimate() != null ? detail.getCatDimensionTypeEstimate().getId() : null);
            dto.setDamageDimensionType(detail.getCatDimensionTypeEstimate() != null ? detail.getCatDimensionTypeEstimate().getLongDescription() : null);
            dto.setDimensionLength(detail.getDimensionLong());
            dto.setDimensionWidth(detail.getDimensionWide());
            dto.setDimension(calculateDimension(detail));
            dto.setCostAssumptionCategoryId(detail.getCatAssumeCostEstimate() == null ? null : detail.getCatAssumeCostEstimate().getId());
            dto.setCostAssumption(detail.getCatAssumeCostEstimate() == null ? null : detail.getCatAssumeCostEstimate().getDescription());
            dto.setPartsToRepair(detail.getPartsRepair());
            dto.setHoursPerPart(detail.getHhXPiece());
            dto.setLaborCostPerPart(detail.getHhXPiece() == null ? null : detail.getHhXPiece().multiply(detail.getEstimateEmr().getEstimateCostHh()).setScale(2, BigDecimal.ROUND_HALF_UP));
            dto.setMaterialCostPerPart(detail.getCostMaterialXPiece());
            dto.setSparePart(buildSparePartJson(detail.getId()));
            dto.setTotalSparePartCost(calculateTotalSparePartCost(detail.getId()).setScale(2, BigDecimal.ROUND_HALF_UP));
            dto.setTotalItemCost(detail.getItemTotalCost());
            dto.setObservation(detail.getObservation());
            dto.setIsReefer(detail.getEstimateEmr().getIsReefer());
            dto.setShippingLineId(detail.getEstimateEmr().getShippingLine().getId());
            dto.setUnitMeasureCategoryId(detail.getCatUnitMeassureEstimate() != null ? detail.getCatUnitMeassureEstimate().getId() : null);
            dto.setRepairLocationDescription(detail.getCatDamageLocationEstimate() == null ? null : detail.getCatDamageLocationEstimate().getDescription() + " " + detail.getCatDamageLocationEstimate().getLongDescription());
            dto.setDamageTypeDescription(detail.getCatDamageTypeEstimate() == null ? null : detail.getCatDamageTypeEstimate().getDescription() + " " + detail.getCatDamageTypeEstimate().getLongDescription());
            dto.setComponentDescription(detail.getCatComponentEstimate() == null ? null : detail.getCatComponentEstimate().getDescription() + " " + detail.getCatComponentEstimate().getLongDescription());
            dto.setRepairMethodDescription(detail.getCatRepairMethodEstimate() == null ? null : detail.getCatRepairMethodEstimate().getDescription() + " " + detail.getCatRepairMethodEstimate().getLongDescription());
            dto.setMechanicWorkUserName(detail.getMechanicWorkDoneUser() != null ? (detail.getMechanicWorkDoneUser().getNames() + " " +
                    detail.getMechanicWorkDoneUser().getFirstLastName() + " " + detail.getMechanicWorkDoneUser().getSecondLastName()) : null);
            dto.setMechanicWorkCheckDate(detail.getMechanicWorkDoneDate() != null ? detail.getMechanicWorkDoneDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")) : null);
            dto.setMechanicWorkDoneUserId(detail.getMechanicWorkDoneUser() != null ? detail.getMechanicWorkDoneUser().getId() : null);
            result.add(dto);
        }
        return result;

    }

    private String calculateDimension(EstimateEmrDetail detail) {
        if (detail.getCatDimensionTypeEstimate() != null &&
                (detail.getCatDimensionTypeEstimate().getId() == 47479 || detail.getCatDimensionTypeEstimate().getId() == 47480)) {
            return String.format("%.1f", detail.getDimensionLong());
        } else {
            return String.format("%.1f x %.1f", detail.getDimensionLong(), detail.getDimensionWide());
        }
    }

    private String buildSparePartJson(Integer detailId) {
        List<EstimateEmrReplacementDTO> spareParts = estimateEmrSpareRepository.findReplacementsByDetailId(detailId);
        if (spareParts == null || spareParts.isEmpty()) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(spareParts);
        } catch (JsonProcessingException e) {
            logger.error("Error converting spare parts to JSON", e);
            return null;
        }
    }

    private BigDecimal calculateTotalSparePartCost(Integer detailId) {
        List<EstimateEmrReplacementDTO> spareParts = estimateEmrSpareRepository.findReplacementsByDetailId(detailId);
        if (spareParts == null || spareParts.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return spareParts.stream()
                .map(sparePart -> BigDecimal.valueOf(sparePart.getReplacementPieces())
                        .multiply(sparePart.getReplacementCostPerPiece()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public List<ListEstimateEmrByIdOutput.InspectorObservationDTO> getInspectorObservations(Integer eirId, Integer languageId) {
        List<ListEstimateEmrByIdOutput.InspectorObservationDTO> observations = new ArrayList<>();
        for (EirObservationInspector observation : eirObservationInspectorRepository.findActiveByEirIdOrderByCatalogId(eirId)) {
            ListEstimateEmrByIdOutput.InspectorObservationDTO dto = new ListEstimateEmrByIdOutput.InspectorObservationDTO();
            dto.setObservationId(observation.getCatObservationEIR().getId());
            dto.setObservationDescription(catalogRepository.findTranslatedShortDesc(observation.getCatObservationEIR().getId(), languageId));
            dto.setOtherDescription(observation.getObservationEirOther() != null ? observation.getObservationEirOther().replace("\n", " ") : "");
            observations.add(dto);
        }
        return observations;
    }

    public List<ListEstimateEmrByIdOutput.EstimatedEmrPhotoHeaderDTO> getEstimateHeaderPhotos(List<EstimateEmrEirPhoto> eirPhotos) {
        List<ListEstimateEmrByIdOutput.EstimatedEmrPhotoHeaderDTO> dtoList = new ArrayList<>();
        for (EstimateEmrEirPhoto photo : eirPhotos) {
            ListEstimateEmrByIdOutput.EstimatedEmrPhotoHeaderDTO dto = new ListEstimateEmrByIdOutput.EstimatedEmrPhotoHeaderDTO();
            dto.setEstimatedEmrId(photo.getEstimadoEmr().getId());
            dto.setEstimatedEmrEirPhotoId(photo.getId());
            dto.setAttachmentId(photo.getAttachment().getId());
            dto.setIdentifier(photo.getAttachment().getId1());
            dto.setUrl(photo.getAttachment().getUrl());
            dtoList.add(dto);
        }

        return dtoList;
    }

    public List<ListEstimateEmrByIdOutput.EstimatedEmrDetailPhotoDTO> getEstimateDetailPhotos(List<EstimateEmrDetailPhoto> detailPhotos) {
        List<ListEstimateEmrByIdOutput.EstimatedEmrDetailPhotoDTO> dtoList = new ArrayList<>();
        for (EstimateEmrDetailPhoto photo : detailPhotos) {
            ListEstimateEmrByIdOutput.EstimatedEmrDetailPhotoDTO dto = new ListEstimateEmrByIdOutput.EstimatedEmrDetailPhotoDTO();
            dto.setEstimatedEmrId(photo.getEstimateEmrDetail().getEstimateEmr().getId());
            dto.setEstimatedEmrDetailId(photo.getEstimateEmrDetail().getId());
            dto.setAttachmentId(photo.getAttachment().getId());
            dto.setIdentifier(photo.getAttachment().getId1());
            dto.setUrl(photo.getAttachment().getUrl());
            dtoList.add(dto);
        }
        return dtoList;
    }

    public List<ListEstimateEmrByIdOutput.EstimateEmrDetailRepairPhotoDTO> getEstimateRepairPhotos(List<EstimateEmrDetailRepairPhoto> repairPhotos) {
        List<ListEstimateEmrByIdOutput.EstimateEmrDetailRepairPhotoDTO> dtoList = new ArrayList<>();

        for (EstimateEmrDetailRepairPhoto photo : repairPhotos) {
            ListEstimateEmrByIdOutput.EstimateEmrDetailRepairPhotoDTO dto = new ListEstimateEmrByIdOutput.EstimateEmrDetailRepairPhotoDTO();
            dto.setEstimatedEmrId(photo.getEstimateEmrDetail().getEstimateEmr().getId());
            dto.setEstimatedEmrDetailId(photo.getEstimateEmrDetail().getId());
            dto.setAttachmentId(photo.getAttachment().getId());
            dto.setIdentifier(photo.getAttachment().getId1());
            dto.setUrl(photo.getAttachment().getUrl());
            dtoList.add(dto);
        }
        return dtoList;
    }
}