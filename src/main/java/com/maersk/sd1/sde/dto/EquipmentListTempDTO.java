package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EquipmentListTempDTO implements EquipmentListTempInterface {
    private Long businessUnitId;
    private Long subBusinessUnitId;
    private Integer programacionNaveDetalleId;
    private Integer containerId;
    private Integer chassisId;
    private Long catEmptyFullId;
    private String tipoMov;
    private String local;
    private Integer eirId;
    private LocalDateTime fechaIngresoCamion;
    private LocalDateTime fechaSalidaCamion;
    private String equipmentNumber;
    private String equipmentSizeType;
    private Integer catEquipmentCategory;
    private String isoCode;
    private String ownerPropietario;
    private String plateTruckNumber;
    private Long transportCompanyId;
    private String transportCompanyName;
    private String driverName;
    private Long userModificationId;
    private String userModificationName;
    private String userModificationLastName;
    private LocalDateTime fechaModificacion;
    private Long userRegistrationId;
    private String userRegistrationName;
    private String userRegistrationLastName;
    private LocalDateTime fechaRegistro;
    private String seals;
    private Long catCargoDocumentTypeId;
    private String cargoDocumentNumber;
    private Long operationTypeId;
    private String operationTypeName;
    private Integer isShow;
    private Integer eirChassisId;
    private Long catStructureConditionId;
    private Long catMachineryConditionId;
    private String operationGroupType;
    private Long gradeId;
    private String containerOperationType;
    private String vesselName;
    private String voyageName;
    private String depotOperationType;
    private Boolean estructuaConDano;
    private Boolean maquinariaConDano;
    private String paraVenta;
    private String camionMultipleCarga;
    private Long userDepartureId;
    private String userDepartureName;
    private String userDepartureLastName;
    private String origenCreacionEir;
    private Long catMovimientoId;
    private String catOperacionDescripcion;
    private String tipoGate;
    private String inspectionPhotos;
    private Integer inspectionsQuantity;
    private String gatePhotos;
    private Integer userDeleteId;
    private String userDeleteName;
    private String userDeleteLastName;
    private LocalDateTime deleteDate;
    private Boolean active;
}