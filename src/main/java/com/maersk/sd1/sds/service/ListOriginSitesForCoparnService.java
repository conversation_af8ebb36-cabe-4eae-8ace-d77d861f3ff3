package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.BookingEdiSetting;
import com.maersk.sd1.common.repository.BookingEdiSettingRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class ListOriginSitesForCoparnService {

    private final BookingEdiSettingRepository bookingEdiSettingRepository;

    private static final Integer MAERSK_LINE_ID = 4104;
    private static final boolean ACTIVE = true;

    public List<BookingEdiSetting> listOriginSitesForCoparnService() {

        return bookingEdiSettingRepository.findBookingDetails(MAERSK_LINE_ID, ACTIVE);
    }
}