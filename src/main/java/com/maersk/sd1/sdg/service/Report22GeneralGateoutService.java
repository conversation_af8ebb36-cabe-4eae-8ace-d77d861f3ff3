package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sdg.controller.dto.Report22GeneralGateoutInput;
import com.maersk.sd1.sdg.controller.dto.Report22GeneralGateoutOutput;
import com.maersk.sd1.sdg.dto.Report22GeneralGateoutData;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@RequiredArgsConstructor
public class Report22GeneralGateoutService {


    private static final String CAT_EMPTY_FULL_ID = "catEmptyFullId";
    private static final String RESTRICTION_REMARK = "restrictionRemark";
    private static final String REASONS = "reasons";

    private final GESCatalogService catalogService;
    private final BusinessUnitRepository businessUnitRepository;
    private final ContainerRepository containerRepository;
    private final EirRepository eirRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;
    private final ContainerRestrictionRepository containerRestrictionRepository;
    private final VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    private final ChassisRestrictionRepository chassisRestrictionRepository;
    private final ProductRepository productRepository;

    @Transactional
    public ResponseEntity<ResponseController<Report22GeneralGateoutOutput>> report22GeneralGateoutService(Report22GeneralGateoutInput.Root request) {

        if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null ||
                request.getPrefix().getInput().getTruckInDateDesde() == null ||
                request.getPrefix().getInput().getTruckInDateHasta() == null ||
                request.getPrefix().getInput().getIdiomaId() == null ||
                request.getPrefix().getInput().getPagina() == null ||
                request.getPrefix().getInput().getCantidad() == null ||
                request.getPrefix().getInput().getUserId() == null || request.getPrefix().getInput().getSubUnidadNegocioId() == null || request.getPrefix().getInput().getSubUnidadNegocioId()<0) {
            return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input"));
        }


        Integer subUnidadNegocioId = request.getPrefix().getInput().getSubUnidadNegocioId();
        Integer idiomaId = request.getPrefix().getInput().getIdiomaId();
        LocalDate truckInDateDesde = request.getPrefix().getInput().getTruckInDateDesde();
        LocalDate truckInDateHasta = request.getPrefix().getInput().getTruckInDateHasta();
        Integer equipmentCategory = request.getPrefix().getInput().getEquipmentCategory();
        Integer catEmptyFullId = request.getPrefix().getInput().getCatEmptyFullId();
        String equipmentNumber = request.getPrefix().getInput().getEquipmentNumber();
        Integer eirNumber = request.getPrefix().getInput().getEirNumber();
        String referenceDocumentNumber = request.getPrefix().getInput().getReferenceDocumentNumber();
        Integer shippingLineId = request.getPrefix().getInput().getShippingLineId();
        Integer shipperName = request.getPrefix().getInput().getShipperName();
        Integer consigneeName = request.getPrefix().getInput().getConsigneeName();
        Integer truckingCompanyScac = request.getPrefix().getInput().getTruckingCompanyScac();
        Integer ownerChassisName = request.getPrefix().getInput().getOwnerChassisName();
        Integer pagina = request.getPrefix().getInput().getPagina();
        Integer cantidad = request.getPrefix().getInput().getCantidad();

        HashMap<String, Integer> catalogIds;

        List<Report22GeneralGateoutData> tbData = new ArrayList<>();

        Map<Integer, Map<String, Object>> tbRestriction = new HashMap<>();
        Map<Integer, Map<String, Object>> tbChassisRestriction = new HashMap<>();
        Map<Integer, Map<String, Object>> tbIMOs = new HashMap<>();


        assert businessUnitRepository != null;
        Integer subBusinessUnitId = businessUnitRepository.findParentBusinessUnitId(subUnidadNegocioId);
        Integer businessUnitId = businessUnitRepository.findParentBusinessUnitId(subBusinessUnitId);

        String businessUnitAlias = businessUnitRepository.findAliasByBusinessUnit(businessUnitId);

        String formatToDateTime = businessUnitRepository.getFormatoDateTime(businessUnitId);
        String formatToDate = businessUnitRepository.getFormatoDate(businessUnitId);

        catalogIds=new HashMap<>(catalogService.findIdsByAliases(Arrays.asList(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS, Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS, Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS, Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS, Parameter.IS_CHASSIS, Parameter.IS_CONTAINER, Parameter.IS_GENSET, Parameter.IS_UNDERSLUNG, Parameter.IS_DOCUMENT_TYPE_BK, Parameter.IS_DOCUMENT_TYPE_BL, Parameter.IS_OPERATION_EXPORT, Parameter.IS_OPERATION_IMPORT, Parameter.IS_OPERATION_STORAGE, Parameter.IS_CATALOGO_SCAC)));


        String equipmentNotApplicable = "NOT APPLICA";
        Integer equipmentNotApplicableId = containerRepository.findEquipmentNotApplicableId(equipmentNotApplicable);

        if (equipmentCategory == null && (catEmptyFullId != null || shippingLineId != null)) {
            equipmentCategory = catalogIds.get(Parameter.IS_CONTAINER);
        }

        if (equipmentCategory != null && !equipmentCategory.equals(catalogIds.get(Parameter.IS_CONTAINER)) && (catEmptyFullId != null || shippingLineId != null)) {
            equipmentCategory = -1;
        }


        if (equipmentCategory == null || equipmentCategory.equals(catalogIds.get(Parameter.IS_CONTAINER))) {
            List<Object[]> results = eirRepository.fetchGateoutData(subBusinessUnitId, catalogIds.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS), catEmptyFullId, truckInDateDesde, truckInDateHasta, eirNumber, equipmentNotApplicableId, equipmentNumber, shippingLineId, catalogIds.get(Parameter.IS_CONTAINER),catalogIds.get(Parameter.IS_CATALOGO_SCAC));
            for (Object[] row : results) {
                Report22GeneralGateoutData data = new Report22GeneralGateoutData();
                setReport22GeneralGateoutDataProperties(data, row);
                tbData.add(data);
            }
        }


        int batchSize = 500;

        for (int startIndex = 0; startIndex < tbData.size(); startIndex += batchSize) {
            int endIndex = Math.min(startIndex + batchSize, tbData.size());
            List<Report22GeneralGateoutData> batchData = tbData.subList(startIndex, endIndex);
            processBatch(batchData, referenceDocumentNumber, shipperName, consigneeName);
        }
        tbData.removeIf(data -> !Boolean.TRUE.equals(data.getIsShow()));

        for (Report22GeneralGateoutData data : tbData) {
            if (data.getCatCargoDocumentTypeId() == null) {
                String operationGroupType = data.getOperationGroupType();
                if ("I".equals(operationGroupType)) {
                    data.setCatCargoDocumentTypeId(BigDecimal.valueOf(catalogIds.get(Parameter.IS_DOCUMENT_TYPE_BL)));
                } else if ("E".equals(operationGroupType)) {
                    data.setCatCargoDocumentTypeId(BigDecimal.valueOf(catalogIds.get(Parameter.IS_DOCUMENT_TYPE_BK)));
                } else {
                    data.setCatCargoDocumentTypeId(null);
                }
            }
        }

        for (Report22GeneralGateoutData data : tbData) {
            if (data.getOperationTypeId() == null) {
                String operationGroupType = data.getOperationGroupType();
                if ("I".equals(operationGroupType)) {
                    data.setOperationTypeId(BigDecimal.valueOf(catalogIds.get(Parameter.IS_OPERATION_IMPORT)));
                } else if ("E".equals(operationGroupType)) {
                    data.setOperationTypeId(BigDecimal.valueOf(catalogIds.get(Parameter.IS_OPERATION_EXPORT)));
                } else {
                    data.setOperationTypeId(BigDecimal.valueOf(catalogIds.get(Parameter.IS_OPERATION_STORAGE)));
                }
            }
        }

        for (Report22GeneralGateoutData data : tbData) {
            if (data.getOperationTypeId() != null) {
                String operationTypeName = catalogLanguageRepository.getTranslation(data.getOperationTypeId(), idiomaId);
                data.setOperationTypeName(operationTypeName);
            }
        }

        List<Integer> containerIds = new ArrayList<>();
        List<Integer> subBusinessUnitIds = new ArrayList<>();
        List<Integer> catEmptyFullIds = new ArrayList<>();
        List<Integer> programacionNaveDetalleIds = new ArrayList<>();
        Map<Integer, Integer> containerIdToEirContainerIdMap = new HashMap<>();

        for (Report22GeneralGateoutData data : tbData) {
            containerIds.add(data.getContainerId());
            subBusinessUnitIds.add(data.getSubBusinessUnitId().intValue());
            catEmptyFullIds.add(data.getCatEmptyFullId().intValue());
            programacionNaveDetalleIds.add(data.getProgramacionNaveDetalleId());
            containerIdToEirContainerIdMap.put(data.getContainerId(), data.getEirContainerId());
        }

        List<List<Integer>> containerIdBatches = batchList(containerIds, batchSize);
        List<List<Integer>> subBusinessUnitIdBatches = batchList(subBusinessUnitIds, batchSize);
        List<List<Integer>> catEmptyFullIdBatches = batchList(catEmptyFullIds, batchSize);

        for (int i = 0; i < containerIdBatches.size(); i++) {
            List<Integer> containerIdBatch = containerIdBatches.get(i);
            List<Integer> subBusinessUnitIdBatch = subBusinessUnitIdBatches.get(i);
            List<Integer> catEmptyFullIdBatch = catEmptyFullIdBatches.get(i);

            List<Object[]> fetchedData = containerRestrictionRepository.findRestrictionsByContainerIdsSubBusinessUnitIdsAndCatEmptyFullIds(
                    new HashSet<>(containerIdBatch),
                    new HashSet<>(subBusinessUnitIdBatch),
                    new HashSet<>(catEmptyFullIdBatch));

            if (fetchedData != null && !fetchedData.isEmpty()) {
                for (Object[] row : fetchedData) {
                    Map<String, Object> restriction = new HashMap<>();
                    Integer containerId = row[1] != null ? (Integer) row[1] : null;
                    Integer eirContainerId = containerIdToEirContainerIdMap.getOrDefault(containerId, null);

                    restriction.put(CAT_EMPTY_FULL_ID, row[0]);
                    restriction.put("containerId", containerId);
                    restriction.put(RESTRICTION_REMARK, row[2]);
                    restriction.put(REASONS, row[3]);

                    tbRestriction.put(eirContainerId, restriction);
                }
            }

        }

        List<List<Integer>> containerIdBatches2 = batchList(containerIds, batchSize);
        List<List<Integer>> programacionNaveDetalleIdBatches = batchList(programacionNaveDetalleIds, batchSize);

        for (int i = 0; i < containerIdBatches2.size(); i++) {
            List<Integer> containerIdBatch = containerIdBatches2.get(i);
            List<Integer> programacionNaveDetalleIdBatch = programacionNaveDetalleIdBatches.get(i);

            List<Object[]> fetchedDataImos = vesselProgrammingContainerRepository.findImoByContainerIdsAndVesselProgrammingDetailIds(
                    new HashSet<>(containerIdBatch),
                    new HashSet<>(programacionNaveDetalleIdBatch));

            if (fetchedDataImos != null && !fetchedDataImos.isEmpty()) {
                for (Object[] row : fetchedDataImos) {
                    Map<String, Object> imo = new HashMap<>();

                    Integer containerId = (Integer) row[0];
                    Integer eirContainerId = containerIdToEirContainerIdMap.get(containerId);

                    imo.put("eirContainerId", eirContainerId);
                    imo.put("containerId", row[0]);
                    imo.put("programacionNaveDetalleId", row[1]);
                    imo.put("imo", row[2]);
                    tbIMOs.put(eirContainerId, imo);
                }
            }
        }

        if (equipmentCategory == null || equipmentCategory.equals(catalogIds.get(Parameter.IS_CHASSIS))) {
            List<Object[]> results = eirRepository.fetchChassisGateoutData(subBusinessUnitId, catalogIds.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS), truckInDateDesde, truckInDateHasta, eirNumber, equipmentNumber, shippingLineId, catalogIds.get(Parameter.IS_CHASSIS), catalogIds.get(Parameter.IS_CATALOGO_SCAC), referenceDocumentNumber, shipperName, consigneeName, idiomaId, ownerChassisName);
            for (Object[] row : results) {
                Report22GeneralGateoutData data = new Report22GeneralGateoutData();
                setReport22GeneralGateoutDataProperties(data, row);
                tbData.add(data);
            }
        }


        List<Integer> chassisIds = new ArrayList<>();
        List<Integer> subBusinessUnitIds2 = new ArrayList<>();

        for (Report22GeneralGateoutData data : tbData) {
            chassisIds.add(data.getChassisId());
            subBusinessUnitIds2.add(data.getSubBusinessUnitId().intValue());
        }

        List<List<Integer>> chassisIdBatches = batchList(chassisIds, batchSize);
        List<List<Integer>> subBusinessUnitIdBatches2 = batchList(subBusinessUnitIds2, batchSize);

        for (int i = 0; i < chassisIdBatches.size(); i++) {
            List<Integer> chassisIdBatch = chassisIdBatches.get(i);
            List<Integer> subBusinessUnitIdBatch = subBusinessUnitIdBatches2.get(i);

            List<Object[]> fetchedData = chassisRestrictionRepository.findChassisRestrictionsByChassisIdsAndSubBusinessUnitIds(
                    new HashSet<>(chassisIdBatch),
                    new HashSet<>(subBusinessUnitIdBatch), catalogIds.get(Parameter.IS_CHASSIS));


            if (fetchedData != null && !fetchedData.isEmpty()) {
                for (Object[] row : fetchedData) {
                    Map<String, Object> restriction = new HashMap<>();
                    restriction.put(RESTRICTION_REMARK, row[1]);
                    restriction.put(REASONS, row[2]);
                    tbChassisRestriction.put((Integer) row[0], restriction);
                }
            }
        }

        List<Report22GeneralGateoutData> tbFinal = new ArrayList<>();
        for (Report22GeneralGateoutData data : tbData) {
            Integer transportCompanyId = data.getTransportCompanyId().intValue();
            if (truckingCompanyScac == null || truckingCompanyScac.equals(transportCompanyId)) {
                tbFinal.add(data);
            }
        }

        int total = tbFinal.size();

        List<Report22GeneralGateoutData> sortedData = new ArrayList<>();
        Report22GeneralGateoutOutput result = new Report22GeneralGateoutOutput();

        if (total > 0) {
            sortedData = getSortedData(tbFinal, pagina, cantidad);
        }

        if (businessUnitAlias!=null && businessUnitAlias.equals("CRI")) {
            generateReport22GeneralGateoutOutput(result, sortedData, "CRI", formatToDateTime, formatToDate, idiomaId,catalogIds,tbRestriction,tbChassisRestriction,tbIMOs);
        } else {
            generateReport22GeneralGateoutOutput(result, sortedData, "", formatToDateTime, formatToDate, idiomaId,catalogIds,tbRestriction,tbChassisRestriction,tbIMOs);
        }

        result.setTotalCount(total);

        return ResponseEntity.ok(new ResponseController<>(result));
    }

    public void setReport22GeneralGateoutDataProperties(Report22GeneralGateoutData data, Object[] row) {
        data.setBusinessUnitId(convertToBigDecimal(row[0]));
        data.setSubBusinessUnitId(convertToBigDecimal(row[1]));
        data.setProgramacionNaveDetalleId(convertToInteger(row[2]));
        data.setContainerId(convertToInteger(row[3]));
        data.setChassisId(convertToInteger(row[4]));
        data.setCatEmptyFullId(convertToBigDecimal(row[5]));
        data.setTipoMov(convertToString(row[6]));
        data.setLocal(convertToString(row[7]));
        data.setEirContainerId(convertToInteger(row[8]));
        data.setFechaIngresoCamion(convertToLocalDateTime(row[9]));
        data.setFechaSalidaCamion(convertToLocalDateTime(row[10]));
        data.setEquipmentNumber(convertToString(row[11]));
        data.setEquipmentSizeType(convertToString(row[12]));
        data.setCatEquipmentCategory(convertToBigDecimal(row[13]));
        data.setIsocodeId(convertToInteger(row[14]));
        data.setIsocodeNumber(convertToString(row[15]));
        data.setOwnerPropietario(convertToString(row[16]));
        data.setReefer(convertToString(row[17]));
        data.setVehiculeId(convertToInteger(row[18]));
        data.setPlateTruckNumber(convertToString(row[19]));
        data.setTransportCompanyId(convertToBigDecimal(row[20]));
        data.setTransportCompanyCode(convertToString(row[21]));
        data.setTransportCompanyName(convertToString(row[22]));
        data.setTransportCompanyScac(convertToString(row[23]));
        data.setDriverId(convertToInteger(row[24]));
        data.setDriverDoc(convertToString(row[25]));
        data.setDriverName(convertToString(row[26]));
        data.setUserRegisterId(convertToBigDecimal(row[27]));
        data.setUserRegisterName(convertToString(row[28]));
        data.setFechaRegistro(convertToLocalDateTime(row[29]));
        data.setSeals(convertToString(row[30]));
        data.setObservacion(convertToString(row[31]));
        data.setCatCargoDocumentTypeId(convertToBigDecimal(row[32]));
        data.setCargoDocumentNumber(convertToString(row[33]));
        data.setShipperNro(convertToString(row[34]));
        data.setShipperName(convertToString(row[35]));
        data.setConsigneeNro(convertToString(row[36]));
        data.setConsigneeName(convertToString(row[37]));
        data.setOperationTypeId(convertToBigDecimal(row[38]));
        data.setOperationTypeName(convertToString(row[39]));
        data.setIsShow(convertToBoolean(row[40]));
        data.setUsuarioSalidaCamion(convertToInteger(row[41]));
        data.setProductoId(convertToInteger(row[42]));
        data.setGateInEirNumber(convertToInteger(row[43]));
        data.setGateInDate(convertToLocalDateTime(row[44]));
        data.setEirChassisId(convertToInteger(row[45]));
        data.setEirChassisNumber(convertToString(row[46]));
        data.setCatStructureConditionId(convertToBigDecimal(row[47]));
        data.setCatMachineryConditionId(convertToBigDecimal(row[48]));
        data.setRefChassisNumber(convertToString(row[49]));
        data.setOperationGroupType(convertToString(row[50]));
        data.setGradeId(convertToBigDecimal(row[51]));
        data.setFlagChassisPickup(convertToBoolean(row[52]));
        data.setNumeroTwr(convertToString(row[53]));
    }

    private BigDecimal convertToBigDecimal(Object obj) {
        return switch (obj) {
            case null -> null;
            case BigDecimal bigDecimal -> bigDecimal;
            case Integer i -> BigDecimal.valueOf(i);
            default ->
                    throw new IllegalArgumentException("Unexpected type for BigDecimal conversion: " + obj.getClass());
        };
    }

    private Integer convertToInteger(Object obj) {
        return switch (obj) {
            case null -> null;
            case Integer i -> i;
            case BigDecimal bigDecimal -> bigDecimal.intValue();
            default -> throw new IllegalArgumentException("Unexpected type for Integer conversion: " + obj.getClass());
        };
    }

    private String convertToString(Object obj) {
        return switch (obj) {
            case null -> null;
            case String s -> s;
            case Character c -> String.valueOf(c);
            default -> throw new IllegalArgumentException("Unexpected type for String conversion: " + obj.getClass());
        };
    }

    private LocalDateTime convertToLocalDateTime(Object obj) {
        if (obj == null) return null;
        if (obj instanceof Timestamp timestamp) return timestamp.toLocalDateTime();
        throw new IllegalArgumentException("Unexpected type for LocalDateTime conversion: " + obj.getClass());
    }


    private Boolean convertToBoolean(Object obj) {
        return switch (obj) {
            case null -> null;
            case Boolean b -> b;
            case Integer i -> i == 1;
            default -> throw new IllegalArgumentException("Unexpected type for Boolean conversion: " + obj.getClass());
        };
    }


    public void processBatch(
            List<Report22GeneralGateoutData> batchData,
            String referenceDocumentNumber,
            Integer shipperName,
            Integer consigneeName
    ) {
        List<Integer> eirContainerIds = batchData.stream()
                .map(Report22GeneralGateoutData::getEirContainerId)
                .toList();

        List<Object[]> fetchedData = eirRepository.getCargoDocumentDataInBatch(
                eirContainerIds,
                referenceDocumentNumber,
                shipperName,
                consigneeName
        );

        Map<Integer, List<Object[]>> dataMap = fetchedData.stream()
                .collect(Collectors.groupingBy(row -> (Integer) row[9]));

        for (Report22GeneralGateoutData data : batchData) {
            List<Object[]> rows = dataMap.getOrDefault(data.getEirContainerId(), Collections.emptyList());
            for (Object[] row : rows) {
                BigDecimal cargoDocTypeId = null;
                if (row[0] instanceof BigDecimal bigdecimal) {
                    cargoDocTypeId = bigdecimal;
                } else if (row[0] instanceof Number number) {
                    cargoDocTypeId = new BigDecimal(number.longValue());
                }
                data.setCatCargoDocumentTypeId(cargoDocTypeId);

                data.setCargoDocumentNumber(row[1] != null ? row[1].toString() : null);
                data.setShipperNro(row[2] != null ? row[2].toString() : null);
                data.setShipperName(row[3] != null ? row[3].toString() : null);
                data.setConsigneeNro(row[5] != null ? row[5].toString() : null);
                data.setConsigneeName(row[6] != null ? row[6].toString() : null);

                Integer productoId = null;
                if (row[7] instanceof Integer integer) {
                    productoId = integer;
                } else if (row[7] != null) {
                    productoId = Integer.valueOf(row[7].toString());
                }
                data.setProductoId(productoId);

                boolean isShow = row[8] instanceof Integer integer && integer == 1;
                data.setIsShow(isShow);
            }
        }
    }

    public static <T> List<List<T>> batchList(List<T> list, int batchSize) {
        return IntStream.range(0, (list.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> list.subList(i * batchSize, Math.min((i + 1) * batchSize, list.size())))
                .toList();
    }

    public static List<Report22GeneralGateoutData> getSortedData(List<Report22GeneralGateoutData> tbData, Integer pagina, Integer cantidad) {
        return tbData.stream()
                .sorted(Comparator.comparing(Report22GeneralGateoutData::getFechaIngresoCamion))
                .skip((long) (pagina - 1) * cantidad)
                .limit(cantidad)
                .toList();
    }

    public void generateReport22GeneralGateoutOutput(
            Report22GeneralGateoutOutput result,
            List<Report22GeneralGateoutData> sortedData,
            String businessUnitAlias,
            String formatToDateTime,
            String formatToDate,
            Integer idiomaId,
            Map<String, Integer> catalogIds,
            Map<Integer, Map<String, Object>> tbRestriction,
            Map<Integer, Map<String, Object>> tbChassisRestriction,
            Map<Integer, Map<String, Object>> tbIMOs) {

        List<Report22GeneralGateoutOutput.SingleData> dataList = new ArrayList<>();

        for (Report22GeneralGateoutData item : sortedData) {
            int equipmentCategory = safeBigDecimalToInt(item.getCatEquipmentCategory());
            boolean isContainer = equipmentCategory == catalogIds.get(Parameter.IS_CONTAINER);
            boolean isChassis = equipmentCategory == catalogIds.get(Parameter.IS_CHASSIS);
            boolean isContainerOrChassis = isContainer || isChassis;
            boolean isChassisPickup = Boolean.TRUE.equals(item.getFlagChassisPickup());

            String structureCondition = isContainerOrChassis
                    ? Optional.ofNullable(catalogLanguageRepository.fnCatalogTranslationDescLong(safeBigDecimalToInt(item.getCatStructureConditionId()), idiomaId))
                    .orElse("")
                    : "";

            String machineryCondition = isContainerOrChassis
                    ? Optional.ofNullable(catalogLanguageRepository.fnCatalogTranslationDescLong(safeBigDecimalToInt(item.getCatMachineryConditionId()), idiomaId))
                    .orElse("")
                    : "";

            String attachedChassisNumber = isContainer
                    ? (Optional.ofNullable(item.getEirChassisNumber()).orElse("") + " "
                    + Optional.ofNullable(item.getRefChassisNumber()).orElse("")).trim()
                    : "";

            String chassisPickupValue;
            if (isContainer) {
                if (isChassisPickup) {
                    chassisPickupValue = "Yes";
                } else {
                    chassisPickupValue = "No";
                }
            } else {
                chassisPickupValue = "";
            }

            String imoInformation = isContainer
                    ? (String) Optional.ofNullable(tbIMOs.get(item.getEirContainerId()))
                    .map(imo -> Optional.ofNullable(imo.get("imo")).orElse(""))
                    .orElse("")
                    : "";
            String sealsValue = isContainer ? Optional.ofNullable(item.getSeals()).orElse("") : "";

            int dwellTime = Optional.ofNullable(item.getGateInDate())
                    .map(gateInDate -> {
                        LocalDate startDate = gateInDate.toLocalDate();
                        LocalDate endDate = Optional.ofNullable(item.getFechaSalidaCamion())
                                .map(LocalDateTime::toLocalDate)
                                .orElse(LocalDate.now());
                        return Math.toIntExact(ChronoUnit.DAYS.between(startDate, endDate)) + 1;
                    })
                    .orElse(0);

            Report22GeneralGateoutOutput.SingleData data = Report22GeneralGateoutOutput.SingleData.builder()
                    .moveType(item.getTipoMov())
                    .depot(item.getLocal())
                    .containerEirNumber(item.getEirContainerId())
                    .equipmentNumber(item.getEquipmentNumber())
                    .equipmentCategory(Optional.ofNullable(
                            catalogLanguageRepository.fnCatalogTranslationDescLong(equipmentCategory, idiomaId)
                    ).orElse(""))
                    .equipmentSizeType(item.getEquipmentSizeType())
                    .isoCode(Optional.ofNullable(item.getIsocodeNumber()).orElse(""))
                    .equipmentGrade(Optional.ofNullable(
                            catalogLanguageRepository.fnCatalogTranslationDesc(
                                    safeBigDecimalToInt(item.getGradeId()), idiomaId
                            )
                    ).orElse(""))
                    .accelerateCode(Optional.ofNullable(item.getAccelerateProgramNumber()).orElse(""))
                    .shippingLineOrChassisOwner(Optional.ofNullable(item.getOwnerPropietario()).orElse(""))
                    .shipperName(Optional.ofNullable(item.getShipperName()).orElse(""))
                    .consigneeName(Optional.ofNullable(item.getConsigneeName()).orElse(""))
                    .gateInDepot(Optional.ofNullable(item.getGateInDate())
                            .map(date -> date.format(DateTimeFormatter.ofPattern(formatToDateTime)))
                            .orElse(""))
                    .gateOutTruckArrival(Optional.ofNullable(item.getFechaIngresoCamion())
                            .map(date -> date.format(DateTimeFormatter.ofPattern(formatToDateTime)))
                            .orElse(""))
                    .gateOutTruckDeparture(Optional.ofNullable(item.getFechaSalidaCamion())
                            .map(date -> date.format(DateTimeFormatter.ofPattern(formatToDateTime)))
                            .orElse(""))
                    .operationType(Optional.ofNullable(item.getOperationTypeName()).orElse(""))
                    .documentType(Optional.ofNullable(
                            catalogLanguageRepository.fnCatalogTranslationDescLong(
                                    safeBigDecimalToInt(item.getCatCargoDocumentTypeId()), idiomaId
                            )
                    ).orElse(""))
                    .documentNumber(Optional.ofNullable(item.getCargoDocumentNumber()).orElse(""))
                    .commodity(Optional.ofNullable(
                            productRepository.findProductNameByProductId(item.getProductoId())
                    ).orElse(""))
                    .dwellTime(dwellTime)
                    .structureConditionDelivered(structureCondition)
                    .machineryConditionDelivered(machineryCondition)
                    .truckingCompany(Optional.ofNullable(item.getTransportCompanyName()).orElse(""))
                    .scac(Optional.ofNullable(item.getTransportCompanyScac()).orElse(""))
                    .truckDriver(Optional.ofNullable(item.getDriverName()).orElse(""))
                    .truckIdentifier(Optional.ofNullable(item.getPlateTruckNumber()).orElse(""))
                    .attachedChassisNumber(attachedChassisNumber)
                    .chassisEirNumber(item.getEirChassisId() == null ? "" : String.valueOf(item.getEirChassisId()))
                    .chassisPickup(chassisPickupValue)
                    .imoInformation(imoInformation)
                    .equipmentRestriction(getEquipmentRestriction(
                            item.getEirContainerId(),item.getChassisId(),tbRestriction, tbChassisRestriction,
                            catalogIds))
                    .seals(sealsValue)
                    .remark(Optional.ofNullable(item.getObservacion()).orElse(""))
                    .registerUser(Optional.ofNullable(item.getUserRegisterName()).orElse(""))
                    .registerDate(Optional.ofNullable(item.getFechaRegistro())
                            .map(date -> date.format(DateTimeFormatter.ofPattern(formatToDateTime)))
                            .orElse(""))
                    .twrNumber("CRI".equals(businessUnitAlias) ? item.getNumeroTwr() : null)
                    .build();

            dataList.add(data);
        }

        result.setDataList(dataList);
    }



    public Integer safeBigDecimalToInt(BigDecimal value) {
        return value != null ? value.intValue() : null;
    }


    public String getEquipmentRestriction(Integer eirContainerId,Integer chassisId, Map<Integer, Map<String, Object>> tbRestriction, Map<Integer, Map<String, Object>> tbChassisRestriction, Map<String, Integer> catalogIds) {

        StringBuilder equipmentRestriction = new StringBuilder();

        Map<String, Object> emptyRestriction = tbRestriction.get(eirContainerId);
        if (emptyRestriction != null && emptyRestriction.get(CAT_EMPTY_FULL_ID) != null
                && Objects.equals(emptyRestriction.get(CAT_EMPTY_FULL_ID), catalogIds.get(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))) {
            String restrictionRemark = (String) emptyRestriction.get(RESTRICTION_REMARK);
            String reasons = (String) emptyRestriction.get(REASONS);
            equipmentRestriction.append("[Empty] ").append(restrictionRemark).append(": ").append(reasons).append(" ");
        }


        Map<String, Object> fullRestriction = tbRestriction.get(eirContainerId);
        if (fullRestriction != null && fullRestriction.get(CAT_EMPTY_FULL_ID) != null
                && Objects.equals(fullRestriction.get(CAT_EMPTY_FULL_ID), catalogIds.get(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS))) {
            String restrictionRemark = (String) fullRestriction.get(RESTRICTION_REMARK);
            String reasons = (String) fullRestriction.get(REASONS);
            equipmentRestriction.append("[Full] ").append(restrictionRemark).append(": ").append(reasons).append(" ");
        }


        Map<String, Object> chassisRestriction = tbChassisRestriction.get(chassisId);
        if (chassisRestriction != null) {
            String restrictionRemark = (String) chassisRestriction.get(RESTRICTION_REMARK);
            String reasons = (String) chassisRestriction.get(REASONS);
            equipmentRestriction.append("[Chassis] ").append(restrictionRemark).append(": ").append(reasons).append(" ");
        }

        return equipmentRestriction.toString().trim();
    }
}