package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.System;
import com.maersk.sd1.common.model.SystemProjectId;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "sistema_proyecto", schema = "seg")
public class ProjectSystem {
    @EmbeddedId
    private SystemProjectId id;

    @MapsId("systemId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sistema_id", nullable = false)
    private System system;

    @MapsId("menuId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "menu_id", nullable = false)
    private Menu menu;

}