package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Eir;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "log_repcontver", schema = "sde", indexes = {
        @Index(name = "LOGREPCONTVER_EIR", columnList = "eir_id"),
        @Index(name = "nci_wi_log_repcontver_0AFF0EB4A0DF92A07AF81C6D748F7ECA", columnList = "envio_respuesta, estado")
})
public class RepcontverLog {
    @Id
    @Column(name = "log_repcontver_id", nullable = false)
    private Integer id;

    @Size(max = 1)
    @NotNull
    @Column(name = "envio_respuesta", nullable = false, length = 1)
    private String sentAnswer;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "eir_id", nullable = false)
    private Eir eir;

    @Lob
    @Column(name = "entrada")
    private String entrance;

    @Lob
    @Column(name = "respuesta")
    private String answer;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @Column(name = "estado", nullable = false)
    private Integer status;

}