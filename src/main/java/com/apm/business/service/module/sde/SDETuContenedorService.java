package com.apm.business.service.module.sde;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDETuContenedorService {

	@RequestMapping(value = "/sdetucontenedorCreaBl", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdetucontenedorCreaBl(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.tucontenedor_crea_bl","SDE");
			pResult.input("Datos", Jpo.STRING);
			pResult.output("ExitoFracaso", Jpo.INTEGER);
			pResult.output("MensajeFracaso", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}