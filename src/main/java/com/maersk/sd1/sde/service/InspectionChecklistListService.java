package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.InspectionChecklist;
import com.maersk.sd1.sde.dto.InspectionChecklistListInput;
import com.maersk.sd1.sde.dto.InspectionChecklistListOutput;
import com.maersk.sd1.sde.dto.InspectionChecklistListOutput.InspectionChecklistRecord;
import com.maersk.sd1.sde.repository.InspectionChecklistListRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class InspectionChecklistListService {

    private static final Logger logger = LogManager.getLogger(InspectionChecklistListService.class);
    private final InspectionChecklistListRepository inspectionChecklistListRepository;

    @Transactional(readOnly = true)
    public InspectionChecklistListOutput getInspectionChecklistList(InspectionChecklistListInput.Input input) {
        // Validate non-null fields
        validateInput(input);

        // Build specification
        Specification<InspectionChecklist> spec = buildSpecification(input);

        PageRequest pageable = PageRequest.of(input.getPage() - 1, input.getSize());
        Page<InspectionChecklist> pageResult = inspectionChecklistListRepository.findAll(spec, pageable);

        // Build output object
        InspectionChecklistListOutput output = new InspectionChecklistListOutput();
        output.setTotalRegistros(Collections.singletonList(Collections.singletonList(pageResult.getTotalElements())));
        // Convert entities to output records
        List<InspectionChecklistRecord> records = new ArrayList<>();
        pageResult.forEach(entity -> {
            InspectionChecklistRecord record = mapEntityToRecord(entity);
            records.add(record);
        });
        output.setRecords(records);
        return output;
    }

    private void validateInput(InspectionChecklistListInput.Input input) {
        if (input == null) {
            throw new IllegalArgumentException("Input cannot be null.");
        }
        if (input.getPage() == null || input.getPage() <= 0) {
            throw new IllegalArgumentException("Page number must be a positive integer.");
        }
        if (input.getSize() == null || input.getSize() <= 0) {
            throw new IllegalArgumentException("Size must be a positive integer.");
        }
    }

    public Specification<InspectionChecklist> buildSpecification(InspectionChecklistListInput.Input input) {
        return (root, query, cb) -> {
            List<jakarta.persistence.criteria.Predicate> predicates = new ArrayList<>();

            if (Objects.nonNull(input.getInspectionChecklistId())) {
                predicates.add(cb.equal(root.get("id"), input.getInspectionChecklistId()));
            }
            if (Objects.nonNull(input.getSubBusinessUnitId())) {
                predicates.add(cb.equal(root.get("subBusinessUnit").get("id"), input.getSubBusinessUnitId()));
            }
            if (Objects.nonNull(input.getCatInspectionTypeId())) {
                predicates.add(cb.equal(root.get("catInspectionType").get("id"), input.getCatInspectionTypeId()));
            }
            if (Objects.nonNull(input.getOrderNumber())) {
                predicates.add(cb.equal(root.get("orderNumber"), input.getOrderNumber()));
            }
            if (Objects.nonNull(input.getDescripcion()) && !input.getDescripcion().isEmpty()) {
                predicates.add(cb.like(root.get("description"), "%" + input.getDescripcion() + "%"));
            }
            if (Objects.nonNull(input.getUserRegistrationId())) {
                predicates.add(cb.equal(root.get("registrationUser").get("id"), input.getUserRegistrationId()));
            }
            if (Objects.nonNull(input.getRegistrationDateMin()) && Objects.nonNull(input.getRegistrationDateMax())) {
                predicates.add(
                        cb.between(
                                root.get("registrationDate"),
                                input.getRegistrationDateMin().atStartOfDay(),
                                input.getRegistrationDateMax().plusDays(1).atStartOfDay()
                        )
                );
            }
            if (Objects.nonNull(input.getUserModificationId())) {
                predicates.add(cb.equal(root.get("modificationUser").get("id"), input.getUserModificationId()));
            }
            if (Objects.nonNull(input.getModificationDateMin()) && Objects.nonNull(input.getModificationDateMax())) {
                predicates.add(
                        cb.between(
                                root.get("modificationDate"),
                                input.getModificationDateMin().atStartOfDay(),
                                input.getModificationDateMax().plusDays(1).atStartOfDay()
                        )
                );
            }
            if (Objects.nonNull(input.getActive())) {
                predicates.add(cb.equal(root.get("active"), input.getActive()));
            }

            query.orderBy(cb.desc(root.get("id")));
            return cb.and(predicates.toArray(new jakarta.persistence.criteria.Predicate[0]));
        };
    }

    public InspectionChecklistRecord mapEntityToRecord(InspectionChecklist entity) {
        InspectionChecklistRecord record = new InspectionChecklistRecord();
        record.setInspectionChecklistId(entity.getId());
        record.setSubBusinessUnitId(Long.valueOf(entity.getSubBusinessUnit() != null ? entity.getSubBusinessUnit().getId() : null));
        record.setCatInspectionTypeId(Long.valueOf(entity.getCatInspectionType() != null ? entity.getCatInspectionType().getId() : null));
        record.setOrderNumber(entity.getOrderNumber());
        record.setDescripcion(entity.getDescription());
        record.setUserRegistrationId(entity.getRegistrationUser() != null ? entity.getRegistrationUser().getId() : null);
        record.setUserRegistrationName(
                entity.getRegistrationUser() != null ? entity.getRegistrationUser().getNames() : null
        );
        String userRegLastName = null;
        if (entity.getRegistrationUser() != null) {
            String firstLast = entity.getRegistrationUser().getFirstLastName() != null ? entity.getRegistrationUser().getFirstLastName() : "";
            String secondLast = entity.getRegistrationUser().getSecondLastName() != null ? entity.getRegistrationUser().getSecondLastName() : "";
            userRegLastName = (firstLast + " " + secondLast).trim();
        }
        record.setUserRegistrationLastname(userRegLastName);

        // Format date as string if needed
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        record.setRegistrationDate(entity.getRegistrationDate() != null
                ? entity.getRegistrationDate().format(formatter)
                : null);
        record.setUserModificationId(entity.getModificationUser() != null ? entity.getModificationUser().getId() : null);
        record.setUserModificationName(
                entity.getModificationUser() != null ? entity.getModificationUser().getNames() : null
        );
        String userModLastName = null;
        if (entity.getModificationUser() != null) {
            String firstLast = entity.getModificationUser().getFirstLastName() != null ? entity.getModificationUser().getFirstLastName() : "";
            String secondLast = entity.getModificationUser().getSecondLastName() != null ? entity.getModificationUser().getSecondLastName() : "";
            userModLastName = (firstLast + " " + secondLast).trim();
        }
        record.setUserModificationLastname(userModLastName);

        record.setModificationDate(entity.getModificationDate() != null
                ? entity.getModificationDate().format(formatter)
                : null);
        record.setActive(entity.getActive());
        return record;
    }
}