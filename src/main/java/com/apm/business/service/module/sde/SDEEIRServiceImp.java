package com.apm.business.service.module.sde;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONObject;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.apm.business.bean.PlannigGateInAssignmentAndAprroveInRequest;
import com.apm.business.externalService.integration.ServiceConnection;
import com.apm.business.service.external.Connection;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import kong.unirest.HttpResponse;
import kong.unirest.RequestBodyEntity;
import kong.unirest.Unirest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.JpoUtil;
import ohSolutions.ohRest.util.bean.Response;

@RestController
@RequestMapping("/module/sde/SDEEIRServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEEIRServiceImp extends SDEEIRService {
	
	final static Logger logger = LogManager.getLogger(SDEEIRServiceImp.class);
	
	@RequestMapping(value = "/sdeeirListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeeirListar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdeeirListar(ppo, request);
	}


	@RequestMapping(value = "/sdeeirObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeeirObtener(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdeeirObtener(ppo, request);
	}
	
	@SuppressWarnings({ "unchecked" })
	@RequestMapping(value = "/sdseirEliminar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdseirEliminar(Jpo ppo, HttpServletRequest request) throws Exception {
		
		Response myResponse = new Response();

		Response eirServiceResponse = (Response) super.sdgeirDeleteBeforeyardv(ppo, request);
		
		Map<String, Object> eirServiceResult =  (Map<String, Object>) eirServiceResponse.getResult(); // {resp_result, resp_message, resp_flag_requireplanapprove, resp_gatein_eir_id}
		
		boolean flag_executeDeparture = false;
		boolean flag_requirePlanApprove = false;
		int sub_business_unit_local_id = 0;
		
		if(eirServiceResult.get("resp_result").equals(1)) {
		System.out.println(eirServiceResult.get("resp_flag_requireplanapprove"));
			if(eirServiceResult.get("resp_flag_requireplanapprove").equals("1")) {
				
				flag_requirePlanApprove = true;
				sub_business_unit_local_id = Integer.parseInt(""+eirServiceResult.get("sub_business_unit_local_id"));
				
			}
			
			flag_executeDeparture = true;
			
		} else {
			
			myResponse = mapError(""+eirServiceResult.get("resp_message"), 2);
			
		}

		if(flag_executeDeparture) {
			
			Response respuestaEliminar = null;
			logger.info("Start sdseirEliminar"); 
			try {
				respuestaEliminar = (Response) super.sdseirEliminar(ppo, request);
				List<Object> objRespuestaEliminar = (List<Object>) respuestaEliminar.getResult();
				Boolean eirEliminadoCorrectamente = false;
				Boolean requiereEliminarCita = false;
				Boolean citaElminadaCorrectamente = false;
				if(objRespuestaEliminar.get(0) != null && objRespuestaEliminar.get(0).equals(1)) {
					eirEliminadoCorrectamente = true;
				}
				if(objRespuestaEliminar.get(2) != null && objRespuestaEliminar.get(2) != "" && !objRespuestaEliminar.get(2).equals("")) {
					requiereEliminarCita = true;
				}
				
				if(eirEliminadoCorrectamente) {
					
					boolean flag_requirePlanApprove_executed = false;
					
					if(flag_requirePlanApprove) {
						
						int eir_gatein = Integer.parseInt(""+eirServiceResult.get("resp_gatein_eir_id"));
						
						//MAKE SDY INTEGRATION CALL				
						ServiceConnection sdyConnection = new ServiceConnection(
							JpoUtil.getPropertie("SDE", "msk.api.apiUserLogin.sdy.loginUrl"),
							JpoUtil.getPropertie("SDE", "msk.api.apiUserLogin.sdy.user"),
							JpoUtil.getPropertie("SDE", "msk.api.apiUserLogin.sdy.password"),
							JpoUtil.getPropertie("SDE", "msk.api.apiUserLogin.sdy.system")
						);
						
						try {

							PlannigGateInAssignmentAndAprroveInRequest yardgisi = new PlannigGateInAssignmentAndAprroveInRequest();
							
							yardgisi.setEir_id(eir_gatein);
							yardgisi.setLanguage_id(Integer.parseInt(ppo.getData("SDE", "idioma_id")));
							yardgisi.setSub_business_unit_local_id(sub_business_unit_local_id);
							yardgisi.setUser_modification_id(""+ppo.getData("SDE", "usuario_modificacion_id"));

							Response resp = sdyConnection.post(JpoUtil.getPropertie("SDE", "msk.api.apiUserLogin.sdy.plannigGateInAssignmentAndAprrove"), yardgisi.toJSON("SDY"));					

							if (resp.isCorrect()) {

								if(resp.getMessage().equals("BusinessError")) {
									
									myResponse = mapError(""+resp.getResult());
									
								} else {
									
									flag_requirePlanApprove_executed = true;
									
								}
								
							} else {
								
								myResponse = mapError(""+resp.getResult(), 2);
								
							}
							
						} catch (Exception e) {
							
							e.printStackTrace();
							myResponse = mapError(e.getMessage());
							
						}
						
						
					} else {
						
						flag_requirePlanApprove_executed = true;
						
					}
					
					if(flag_requirePlanApprove_executed) {
						
						if(requiereEliminarCita) {
							
							Connection connection = new Connection(JpoUtil.getPropertie("SDE", "apsint.api.login.user"),JpoUtil.getPropertie("SDE", "apsint.api.login.password"));
							String body_f = actualizarEstadoCitaJSON(objRespuestaEliminar.get(2).toString(), "4", "2", ppo.getData("SDE", "usuario_modificacion_id"));
							RequestBodyEntity resp;
							String auth = "Bearer ";
							auth = auth.concat(connection.getToken());
							logger.info("body_f : " + body_f); 
							resp = Unirest.post(JpoUtil.getPropertie("SDE", "apsint.api.updateAppointmentStatus.url")).header("Authorization", auth).body(body_f);
							logger.info("Unirest result : " + resp); 
							HttpResponse<String> responseAPI = resp.asString();
							logger.info("responseAPI : " + responseAPI); 
							logger.info("responseAPI.getBody() : " + responseAPI.getBody()); 
							JSONObject objAPS = new JSONObject(responseAPI.getBody());
							logger.info("objAPS : " + objAPS); 
							List<Object> resultObjAPS = objAPS.optJSONArray("result").toList();
							logger.info("resultObjAPS : " + resultObjAPS); 
							logger.info("resultObjAPS.get(0) : " + resultObjAPS.get(0)); 
							if(resultObjAPS.get(0) != null && resultObjAPS.get(0).equals(1)) {
								citaElminadaCorrectamente = true;
							}else {
								objRespuestaEliminar.set(0, resultObjAPS.get(0));
								objRespuestaEliminar.set(1, "EIR Number: " + ppo.getData("SDE", "eir_id") + " can't be deleted because of an error in appointment system. Error: " + resultObjAPS.get(1));
								logger.info("objRespuestaEliminar 1 : " + objRespuestaEliminar); 
							}
							logger.info("citaElminadaCorrectamente : " + citaElminadaCorrectamente); 
							
							if(citaElminadaCorrectamente) {
								objRespuestaEliminar.set(0, resultObjAPS.get(0));
								objRespuestaEliminar.set(1, "EIR Number: " + ppo.getData("SDE", "eir_id") + " successfully removed.");
								logger.info("objRespuestaEliminar 2 : " + objRespuestaEliminar); 
							} else {
								objRespuestaEliminar.set(0, 1);
								objRespuestaEliminar.set(1, "Exeuted correctly, witout updating appointment status, check validation");
							}
							
							ppo.commit();
							
						} else {
							
							ppo.commit();
							
						}
						
						myResponse.setCorrect(true);
						myResponse.setResult(objRespuestaEliminar);
						
					} else {
						
						ppo.commit();
						
					}
					
				} else {
					
					ppo.rollback();
					myResponse = mapError(""+objRespuestaEliminar.get(1), 2);
					
				}

			} catch (Exception e) {
				e.printStackTrace();
				logger.error("ERROR: " + e.getMessage());
				ppo.rollback();
				myResponse = mapError(""+e.getMessage());
			}
		
		} else {

			ppo.rollback();
			
		}
		
		return myResponse;
		
	}

	public String actualizarEstadoCitaJSON(String cita_id, String current_status, String new_status, String usuario_modificacion_id) {
		// Armar la llamada al servicio del APS
		Map<String, Object> body_t = new HashMap<String, Object>();
		Map<String, Object> f = new HashMap<String, Object>();
		Map<String, String> data = new HashMap<String, String>();
		data.put("cita_id", cita_id);
		data.put("current_status", current_status);
		data.put("new_status", new_status);
		data.put("usuario_modificacion_id", usuario_modificacion_id);
		f.put("F", data);
		body_t.put("APS", f);
		Gson prettyGson = new GsonBuilder().setPrettyPrinting().create();
		return prettyGson.toJson(body_t);
	}
	
	private Response mapError(String message) {
		return mapError(message, 0);
	}
	
	private Response mapError(String message, int status) {
		
		Response myResponse = new Response();
		
		List<Object> resultChild = new ArrayList<Object>();

		resultChild.add(status);
		resultChild.add(message);

		myResponse.setResult(resultChild);
		myResponse.setCorrect(true);
		
		return myResponse;
		
	}
	
}