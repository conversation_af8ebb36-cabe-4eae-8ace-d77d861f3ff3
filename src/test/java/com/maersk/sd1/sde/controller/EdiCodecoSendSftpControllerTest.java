package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.EdiCodecoSendSftpInput;
import com.maersk.sd1.sde.dto.EdiCodecoSendSftpOutput;
import com.maersk.sd1.sde.service.EdiCodecoSendSftpService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EdiCodecoSendSftpControllerTest {

    @Mock
    private EdiCodecoSendSftpService ediCodecoSendSftpService;

    @InjectMocks
    private EdiCodecoSendSftpController controller;

    private EdiCodecoSendSftpInput.Root validRequest;
    private EdiCodecoSendSftpInput.Root invalidRequest;
    private EdiCodecoSendSftpOutput output;

    @BeforeEach
    void setUp() {
        EdiCodecoSendSftpInput.Input input = new EdiCodecoSendSftpInput.Input();
        input.setGateTransmissionSettingId(1);
        input.setReferenceId(1);
        input.setRepositoryFiles("repositoryFiles");

        validRequest = new EdiCodecoSendSftpInput.Root();
        validRequest.setPrefix(new EdiCodecoSendSftpInput.Prefix());
        validRequest.getPrefix().setInput(input);

        invalidRequest = new EdiCodecoSendSftpInput.Root();

        output = new EdiCodecoSendSftpOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");
    }

    @Test
    void sendCodecoToSftpShouldReturnOkWhenValidRequest() {
        when(ediCodecoSendSftpService.sendCodecoToSftp(anyInt(), anyInt(), anyString())).thenReturn(output);

        ResponseEntity<ResponseController<EdiCodecoSendSftpOutput>> response = controller.sendCodecoToSftp(validRequest);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(output, Objects.requireNonNull(response.getBody()).getResult());
    }

    @Test
    void sendCodecoToSftpShouldReturnBadRequestWhenInvalidRequest() {
        ResponseEntity<ResponseController<EdiCodecoSendSftpOutput>> response = controller.sendCodecoToSftp(invalidRequest);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals("Invalid request", Objects.requireNonNull(response.getBody()).getResult().getRespMensaje());
    }

    @Test
    void sendCodecoToSftpShouldReturnInternalServerErrorWhenServiceThrowsException() {
        when(ediCodecoSendSftpService.sendCodecoToSftp(anyInt(), anyInt(), anyString())).thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<EdiCodecoSendSftpOutput>> response = controller.sendCodecoToSftp(validRequest);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals("Service error", Objects.requireNonNull(response.getBody()).getResult().getRespMensaje());
    }
}