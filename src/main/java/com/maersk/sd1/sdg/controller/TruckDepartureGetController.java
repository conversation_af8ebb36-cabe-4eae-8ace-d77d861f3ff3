package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.service.UtilsService;
import com.maersk.sd1.sdg.controller.dto.ResponseTruckDepartureGetDto;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureGetInput;
import com.maersk.sd1.sdg.service.TruckDepartureGetService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDG/module/sdg/SDGtruckDepartureServiceImp")
public class TruckDepartureGetController {

    private final TruckDepartureGetService service;

    private final UtilsService utilsService;

    @PostMapping("/sdgtruckDepartureGet")
    public ResponseEntity<ResponseController<List<List<Object>>>> truckDepartureBeforeYard(@RequestBody SdgTruckDepartureGetInput.Root ppo) throws Exception {
        List<ResponseTruckDepartureGetDto> responseTruckDepartureGetDto = service.getTruckDeparture(ppo);
        return ResponseEntity.ok(new ResponseController<>(utilsService.convertToResult(responseTruckDepartureGetDto)));
    }

}
