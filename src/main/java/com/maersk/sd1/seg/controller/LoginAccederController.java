package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.LoginAccederInput;
import com.maersk.sd1.seg.controller.dto.LoginAccederOutput;
import com.maersk.sd1.seg.service.LoginAccederService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/Inlandnet/SEG/security/UserServiceImp")
public class LoginAccederController {

    private static final Logger logger = LogManager.getLogger(LoginAccederController.class);

    private final LoginAccederService loginAccederService;

    @PostMapping("/segloginAcceder")
    public ResponseEntity<ResponseController<LoginAccederOutput>> loginAcceder(@RequestBody @Valid LoginAccederInput.Root request) {
        try {
            logger.info("Request received loginAcceder: {}", request);
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure"));
            }
            LoginAccederInput.Input input = request.getPrefix().getInput();
            if(input == null) {
                return ResponseEntity.badRequest().body(new ResponseController<>("Input cannot be null"));
            }

            LoginAccederOutput output = loginAccederService.loginAcceder(
                    input.getUsuario(),
                    input.getClaveMd5(),
                    input.getSistemaId(),
                    input.getOrigenLogin()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing loginAcceder request.", e);
            LoginAccederOutput output = new LoginAccederOutput();
            output.setRespStatus(0);
            output.setRespMessage(e.toString());
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }
}