package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.seg.dto.LoginSyncOutputDTO;
import com.maersk.sd1.common.model.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

@RequiredArgsConstructor
@Log4j2
@Service
public class LoginSyncService {

    private final UserRepository userRepository;
    private final LoginDataObtainService loginDataObtainService; // Inject the service to get login data

    public LoginSyncOutputDTO loginSincronizar(Integer userId, Integer systemId) {
        log.info("Starting loginSincronizar with userId={} and systemId={}.", userId, systemId);

        LoginSyncOutputDTO outputDTO = new LoginSyncOutputDTO();

        // Fetch user with joined company
        Optional<User> optionalUser = userRepository.findById(userId);
        if (optionalUser.isEmpty()) {
            log.warn("User not found for userId={}. Returning empty output.", userId);
            return outputDTO; // Return empty if user not found, or handle accordingly.
        }

        User user = optionalUser.get();

        // Build the equivalent of @Tabla_persona row.
        outputDTO.setUsuarioId(user.getId() == null ? "" : user.getId().toString());
        outputDTO.setCodigo(user.getAlias());
        outputDTO.setNombre(user.getNames());
        outputDTO.setApellidoPaterno(user.getFirstLastName());
        outputDTO.setApellidoMaterno(user.getSecondLastName());

        // Handle company info
        if (user.getCompany() != null) {
            outputDTO.setEmpresa(user.getCompany().getLegalName());
            outputDTO.setDocumento(user.getCompany().getDocument());
        }

        // Calculate days difference for key change
        LocalDateTime now = LocalDateTime.now();
        if (user.getDateChangeKey() != null) {
            long diffDaysFromChangeKey = ChronoUnit.DAYS.between(user.getDateChangeKey().toLocalDate(), now.toLocalDate());
            outputDTO.setClaveCambiar(diffDaysFromChangeKey == 0 ? 1 : 0);
        } else {
            outputDTO.setClaveCambiar(0);
        }

        // Calculate days remaining until key change
        long diffDaysToChangeKey = 0;
        if (user.getDateChangeKey() != null) {
            diffDaysToChangeKey = ChronoUnit.DAYS.between(now.toLocalDate(), user.getDateChangeKey().toLocalDate());
        }
        outputDTO.setClaveDias(diffDaysToChangeKey);

        // Set fixed value for 'origen'
        outputDTO.setOrigen("IND");

        // Fetch additional login data using the new service
        var loginData = loginDataObtainService.getLoginData(userId.longValue(), systemId.longValue());

        // Map the relevant fields from LoginDataObtainOutput to LoginSyncOutputDTO
        outputDTO.setBusinessUnits(loginData.getBusinessUnits()); // Map business units
        outputDTO.setRoles(loginData.getRoles()); // Map roles
        outputDTO.setProjects(loginData.getProjects()); // Map projects
        outputDTO.setSystemValidation(loginData.getSystemValidation()); // Map system validation
        outputDTO.setRules(loginData.getRules()); // Map rules
        outputDTO.setLanguages(loginData.getLanguages()); // Map languages
        outputDTO.setHasQuestions(loginData.getHasQuestions()); // Map has questions

        log.info("Finished loginSincronizar with output: {}", outputDTO);
        return outputDTO;
    }
}
