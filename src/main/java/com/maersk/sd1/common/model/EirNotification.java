package com.maersk.sd1.common.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "EmailProcesarProcedure",
        procedureName = "[ges].[email_procesar]",
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "plantilla_id", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "destinatario", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "copia", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "copia_oculta", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "titulo", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "campos", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "sistema_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "origen", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "referencia", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "indicador_enviar", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "adjuntos", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "resp_estado", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "resp_mensaje", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "resp_correo", type = String.class)
        }
)
@NamedStoredProcedureQuery(
        name = "RuleGeneralGetOutProcedure",
        procedureName = "sds.rule_general_get_out",
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "sub_business_unit_local_alias", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "system_rule_id", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "type_rule", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "r_action", type = String.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "r_actionjson", type = String.class)
        }
)
@Table(name = "eir_notification", schema = "sde")
public class EirNotification {

    @Id
    @Column(name = "eir_notification_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "eir_id", nullable = false)
    private Eir eir;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "status_id", nullable = false)
    private Catalog status;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @Size(max = 300)
    @Column(name = "azure_storage_url", length = 300)
    private String azureStorageUrl;

}