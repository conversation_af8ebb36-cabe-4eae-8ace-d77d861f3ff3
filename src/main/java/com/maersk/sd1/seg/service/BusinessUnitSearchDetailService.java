package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.System;
import com.maersk.sd1.seg.controller.dto.BusinessUnitSearchDetailOutput;
import com.maersk.sd1.seg.repository.SegUnidadNegocioRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Service
public class BusinessUnitSearchDetailService {

    private static final Logger logger = LogManager.getLogger(BusinessUnitSearchDetailService.class);

    private final SegUnidadNegocioRepository segUnidadNegocioRepository;

    public BusinessUnitSearchDetailService(SegUnidadNegocioRepository segUnidadNegocioRepository) {
        this.segUnidadNegocioRepository = segUnidadNegocioRepository;
    }

    @Transactional(readOnly = true)
    public BusinessUnitSearchDetailOutput searchBusinessUnit(Integer systemId) {
        BusinessUnitSearchDetailOutput output = new BusinessUnitSearchDetailOutput();
        try {
            List<BusinessUnit> buList = segUnidadNegocioRepository.findBusinessUnitsBySystemId(systemId);
            List<BusinessUnitSearchDetailOutput.BusinessUnitSearchDetail> resultDetails = new ArrayList<>();

            for (BusinessUnit bu : buList) {
                BusinessUnitSearchDetailOutput.BusinessUnitSearchDetail detail = new BusinessUnitSearchDetailOutput.BusinessUnitSearchDetail();
                detail.setBusinessUnitId(bu.getId());
                String chain = buildParentChain(bu);
                System sys = bu.getSystem();
                if (sys != null && sys.getName() != null) {
                    chain = chain + " (" + sys.getName() + ")";
                }
                detail.setBusinessUnit(chain);
                detail.setSystemId(sys == null ? null : sys.getId());
                detail.setSystemName(sys == null ? null : sys.getName());
                resultDetails.add(detail);
            }

            resultDetails.sort(Comparator.comparing(BusinessUnitSearchDetailOutput.BusinessUnitSearchDetail::getBusinessUnit, String.CASE_INSENSITIVE_ORDER));

            output.setBusinessUnits(resultDetails);
            output.setRespStatus(1);
            output.setRespMessage("Operation completed successfully");

        } catch (Exception e) {
            logger.error("Error in searchBusinessUnit service", e);
            output.setRespStatus(0);
            output.setRespMessage("An error occurred: " + e.getMessage());
        }
        return output;
    }

    private String buildParentChain(BusinessUnit bu) {
        if (bu.getParentBusinessUnit() != null) {
            return buildParentChain(bu.getParentBusinessUnit()) + " - " + bu.getName();
        } else {
            return bu.getName();
        }
    }
}