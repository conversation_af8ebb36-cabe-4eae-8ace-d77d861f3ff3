package com.maersk.sd1.common.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "cancelacion_bloqueo_booking_detalle", schema = "sds")
public class BookingBlockCancellationDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "cancel_bloqueo_booking_detalle_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cancel_bloqueo_booking_id", nullable = false)
    private BookingBlockCancellation bookingBlockCancellation;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "documento_carga_id", nullable = false)
    private CargoDocument cargoDocument;

    @NotNull
    @Column(name = "booking_liberado", nullable = false)
    private Boolean releasedBooking = false;

    @Column(name = "fecha_libera_booking")
    private LocalDateTime bookingReleaseDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_motivo_libera_booking")
    private Catalog catBookingReasonRelease;

    @Size(max = 250)
    @Column(name = "comentario_libera_booking", length = 250)
    private String bookingReleaseComment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_libera_booking_id")
    private User bookingReleaseUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_origen_libera_booking_id")
    private Catalog carOriginBookingRelease;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

}