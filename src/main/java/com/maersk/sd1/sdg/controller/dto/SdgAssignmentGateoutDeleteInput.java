package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class SdgAssignmentGateoutDeleteInput {

    @Data
    public static class Input {
        @JsonProperty("eir_id")
        private int eirId;

        @JsonProperty("sub_business_unit_local_id")
        private int subBusinessUnitLocalId;

        @JsonProperty("cancel_reason")
        private String cancelReason;

        @JsonProperty("language_id")
        private int languageId;

        @JsonProperty("user_modification_id")
        private int userModificationId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix sdg;

        public Input getInput() {
            return sdg != null ? sdg.getInput() : null;
        }
    }

}