package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "log_contecon", schema = "sde", indexes = {
        @Index(name = "LOGCONTECON_EIR", columnList = "eir_id"),
        @Index(name = "nci_wi_log_contecon_F318EE85A91274979BE280B170130B1D", columnList = "activo, eir_id, webservices, fueok, turno"),
        @Index(name = "nci_wi_log_contecon_E5E728BCCB7024086BEF7404FEDF0BDE", columnList = "activo, fueok, webservices, eir_id, idpregaterecepcion"),
        @Index(name = "nci_wi_log_contecon_909D81A34BF7C6FBB74A24134DC4DAC1", columnList = "activo, fueok, webservices")
})
public class ConteconLog {
    @Id
    @Column(name = "log_contecon_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_id")
    private Eir eir;

    @Size(max = 100)
    @NotNull
    @Column(name = "webservices", nullable = false, length = 100)
    private String webservices;

    @NotNull
    @Column(name = "fueok", nullable = false)
    private Boolean ok = false;

    @Size(max = 200)
    @Column(name = "mensaje", length = 200)
    private String message;

    @NotNull
    @Column(name = "idpregaterecepcion", nullable = false)
    private Integer idpregaterecepcion;

    @NotNull
    @Column(name = "idtransaccion", nullable = false)
    private Integer idtransaccion;

    @Size(max = 100)
    @Column(name = "token", length = 100)
    private String token;

    @Size(max = 200)
    @NotNull
    @Column(name = "mensajedetalle", nullable = false, length = 200)
    private String messageDetail;

    @Size(max = 50)
    @Column(name = "booking", length = 50)
    private String booking;

    @Size(max = 50)
    @Column(name = "contenedor", length = 50)
    private String container;

    @Size(max = 50)
    @Column(name = "cedula", length = 50)
    private String license;

    @Size(max = 50)
    @Column(name = "placa", length = 50)
    private String plate;

    @Column(name = "turno")
    private Long shift;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @Size(max = 50)
    @NotNull
    @Column(name = "usuario", nullable = false, length = 50)
    private String user;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

}