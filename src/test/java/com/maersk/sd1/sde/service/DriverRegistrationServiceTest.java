package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.PersonRepository;
import com.maersk.sd1.common.repository.PersonRoleRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.sde.dto.DriverRegistrationInput;
import com.maersk.sd1.sde.dto.DriverRegistrationOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class DriverRegistrationServiceTest {

    @Mock
    private PersonRepository personRepository;

    @Mock
    private PersonRoleRepository personRoleRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private DriverRegistrationService driverRegistrationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void Given_NoDuplicateDriver_When_RegisterDriver_Then_ReturnSuccessResponse() {
        // Given
        DriverRegistrationInput.Input input = new DriverRegistrationInput.Input();
        input.setIdentificationDocument("DEF123");
        input.setDriversLicense("ABC123");
        input.setFirstLastName("Doe");
        input.setSecondLastName("Smith");
        input.setNames("John");
        input.setUserRegistrationId(1);
        input.setMail("<EMAIL>");

        when(personRepository.countByDriversLicenseIgnoreCaseOrIdentificationDocumentIgnoreCase("ABC123", "DEF123"))
                .thenReturn(0L);

        Catalog docType = new Catalog();
        Catalog personStatus = new Catalog();
        Catalog catPersonRole = new Catalog();
        User registrationUser = new User();
        Person savedPerson = new Person();
        savedPerson.setId(1);

        when(catalogRepository.getReferenceById(43160)).thenReturn(docType);
        when(catalogRepository.getReferenceById(41609)).thenReturn(personStatus);
        when(catalogRepository.getReferenceById(41567)).thenReturn(catPersonRole);
        when(userRepository.getReferenceById(1)).thenReturn(registrationUser);
        when(personRepository.save(any(Person.class))).thenReturn(savedPerson);

        // When
        DriverRegistrationOutput output = driverRegistrationService.registerDriver(input);

        // Then
        assertEquals(1, output.getRespEstado());
        assertEquals("Registro completado", output.getRespMensaje());
        verify(personRepository, times(1)).save(any(Person.class));
        verify(personRoleRepository, times(1)).save(any(PersonRole.class));
    }

    @Test
    void Given_DuplicateDriver_When_RegisterDriver_Then_ReturnDuplicateResponse() {
        // Given
        DriverRegistrationInput.Input input = new DriverRegistrationInput.Input();
        input.setIdentificationDocument("DEF123");
        input.setDriversLicense("ABC123");

        when(personRepository.countByDriversLicenseIgnoreCaseOrIdentificationDocumentIgnoreCase("ABC123", "DEF123"))
                .thenReturn(1L);

        // When
        DriverRegistrationOutput output = driverRegistrationService.registerDriver(input);

        // Then
        assertEquals(2, output.getRespEstado());
        assertEquals("Registro duplicado", output.getRespMensaje());
        verify(personRepository, never()).save(any(Person.class));
        verify(personRoleRepository, never()).save(any(PersonRole.class));
    }

    @Test
    void Given_ExceptionOccurs_When_RegisterDriver_Then_ReturnErrorResponse() {
        // Given
        DriverRegistrationInput.Input input = new DriverRegistrationInput.Input();
        input.setIdentificationDocument("DEF123");
        input.setDriversLicense("ABC123");

        when(personRepository.countByDriversLicenseIgnoreCaseOrIdentificationDocumentIgnoreCase("ABC123", "DEF123"))
                .thenThrow(new RuntimeException("Database error"));

        // When
        DriverRegistrationOutput output = driverRegistrationService.registerDriver(input);

        // Then
        assertEquals(0, output.getRespEstado());
        assertEquals("Database error", output.getRespMensaje());
        verify(personRepository, never()).save(any(Person.class));
        verify(personRoleRepository, never()).save(any(PersonRole.class));
    }
}