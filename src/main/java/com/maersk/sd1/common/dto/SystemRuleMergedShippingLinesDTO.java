package com.maersk.sd1.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class SystemRuleMergedShippingLinesDTO {

    @JsonProperty("merged_shipping_lines_id")
    private int mergedShippingLinesId;

    @JsonProperty("merged_shipping_lines_name")
    private String mergedShippingLinesName;

    @JsonProperty("merged_details")
    private List<MergedDetail> mergedDetails;

    @Data
    public static class MergedDetail {

        @JsonProperty("line_id")
        private int lineId;

        @JsonProperty("line_id_name")
        private String lineIdName;

    }

}