package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class BusinessUnitSearchDetailInput {

    @Data
    public static class Input {

        @JsonProperty("system_id")
        private Integer systemId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("ADM")
        private Prefix prefix;
    }

    private BusinessUnitSearchDetailInput() {
        // Private constructor to hide the implicit public one.
    }
}