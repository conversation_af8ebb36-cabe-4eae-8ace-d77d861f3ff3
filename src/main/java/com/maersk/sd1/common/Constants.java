package com.maersk.sd1.common;

public class Constants {
    public static final String NO_DATA_FOUND = "No data found";
    public static final String INVALID_INPUT = "Invalid input";
    public static final String EIR_SIGNATURE = "EIR_SIGNATURE";
    public static final String TRUCK_DEP_REMOVED = "REMOVED_FROM_TRUCK_DEP_EXE_SUCCESS";
    public static final String PRC_FULL_GI_GO = "PRC_FULL_GI_GO";
    public static final String MENU_REG_SUCCESS = "Menu registered successfully";
    public static final String ERROR_MSG = "Error occurred during menu registration";
    public static final String IS_ISO_CODE_DUMMY = "2000";
    public static final String RESULT_STATE = "result_state";
    public static final String GENERAL = "GENERAL";
    public static final String GATEOUT_RESULT = "gateOutResult";
    public static final String EIR_ID = "eir_id";
    public static final String PRC_GATE_OUT_EMPTY = "PRC_GATE_OUT_EMPTY";
    public static final String INS_GO_GENERAL = "ins_go_general";
    public static final String REPLACE_STRING = "{IDX}";
    public static final String PRC_GO_GENERAL = "PRC_GO_GENERAL";
    public static final String BOOKING_NOT_ACTIVE = "The Booking is not Active, it does not proceed the cancellation.";
    public static final String BOOKING_CANCEL = "The Booking has been CANCELED";
    public static final String EDI_BOOKING_CANCEL = "EDI Booking Cancel";
    public static final String BOOKING_NOT_FOUND = "Could not locate the booking to CANCEL.";
    public static final String BOOKING_EDI_INSERT = "BookingEDI Insertion";
    public static final String BOOKING_EDI_UPDATE = "BookingEDI Update";
    public static final String BOOKING_EDI_ADD_SEQUENCE = "Booking EDI Add Seq";
    public static final String BOOKING_EDI_ADDITION = "Booking EDI Addition";
    public static final String RESERVE_ADDED = "Reserve Added";
    public static final String BOOKING_NOT_FOUND_ADD = "Could not locate the booking to ADD.";
    public static final String BOOKING_EDI_CANCEL = "Booking Cancel from EDI";
    public static final String BOOKING_EDI_NOT_FOUND = "Booking EDI not found";
    public static final String CNTX = "{CNTX}";

}
