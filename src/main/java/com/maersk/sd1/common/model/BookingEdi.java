package com.maersk.sd1.common.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "BookingProcessArchive.processArchive1C2A",
        procedureName = "[sds].[servicio_coparn_procesar_archivo_1C_2A]",
        parameters = {
                @StoredProcedureParameter(name = "edi_coparn_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "SituacionReserva", type = String.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "unidad_negocio_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "sub_unidad_negocio_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "programacion_nave_detalle_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "Booking", type = String.class, mode = ParameterMode.IN),
                //--------------------------
                @StoredProcedureParameter(name = "CntDimen_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "CntTipo_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "CantidadReserva", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "CntDimen2_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "CntTipo2_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "CantidadReserva2", type = Integer.class, mode = ParameterMode.IN),
                //--------------------------
                @StoredProcedureParameter(name = "Cliente_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "ClienteRS", type = String.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "GrupoProductoDes", type = String.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "producto_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "PtoEmbarque_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "PtoDestino_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "PtoDescarga_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "Temperatura", type = String.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "imo_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "LineaBK_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "PesoBrutoEDI", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "PesoBrutoEDI2", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "ColdTreatment", type = Boolean.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "AtmosferaControlada", type = Boolean.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "usuario_registro_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "param_sequence_details", type = String.class, mode = ParameterMode.IN)
        }
)
@NamedStoredProcedureQuery(
        name = "BookingProcessArchive.processArchive5M",
        procedureName = "[sds].[servicio_coparn_procesar_archivo_5M]",
        parameters = {
                @StoredProcedureParameter(name = "edi_coparn_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "unidad_negocio_id", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "sub_unidad_negocio_id", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "programacion_nave_detalle_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "Booking", type = String.class, mode = ParameterMode.IN),
                //--------------------------
                @StoredProcedureParameter(name = "CntDimen_id", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "CntTipo_id", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "CantidadReserva", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "CntDimen2_id", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "CntTipo2_id", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "CantidadReserva2", type = Integer.class, mode = ParameterMode.IN),
                //--------------------------
                @StoredProcedureParameter(name = "Cliente_id", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "ClienteRS", type = String.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "GrupoProductoDes", type = String.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "producto_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "PtoEmbarque_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "PtoDestino_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "PtoDescarga_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "Temperatura", type = String.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "imo_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "LineaBK_id", type = Integer.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "PesoBrutoEDI", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "PesoBrutoEDI2", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "ColdTreatment", type = Boolean.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "AtmosferaControlada", type = Boolean.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "usuario_registro_id", type = BigDecimal.class, mode = ParameterMode.IN),
                @StoredProcedureParameter(name = "param_sequence_details", type = String.class, mode = ParameterMode.IN)
        }
)
@Table(name = "edi_coparn", schema = "sds", indexes = {
        @Index(name = "EDI_COPARNX_BK", columnList = "numero_booking"),
        @Index(name = "EDI_COPARNX_FPROS", columnList = "fecha_procesado_coparn"),
        @Index(name = "EDI_COPARNX_FREG", columnList = "fecha_registro"),
        @Index(name = "EDI_BOOKINGX_SETTING_STATE_ID", columnList = "seteo_edi_coparn_id, cat_edi_coparn_estado"),
        @Index(name = "EDI_BOOKINGX_SETTING_ID", columnList = "seteo_edi_coparn_id"),
        @Index(name = "nci_msft_1_edi_coparn_D5B8E519484C95BBBA9E0DD539A6031F", columnList = "edi_coparn_nombre_archivo_original, seteo_edi_coparn_id")
})
public class BookingEdi {
    @Id
    @Column(name = "edi_coparn_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "seteo_edi_coparn_id", nullable = false)
    private BookingEdiSetting bookingEdiSetting;

    @Size(max = 200)
    @Column(name = "edi_coparn_nombre_archivo_original", length = 200)
    private String originalBkEdiFileName;

    @Size(max = 200)
    @Column(name = "edi_coparn_nombre_archivo", length = 200)
    private String bkEdiFileName;

    @Size(max = 2)
    @Column(name = "edi_coparn_tipo_registro", length = 2)
    private String bkEdiRegistrationType;

    @Size(max = 1)
    @Column(name = "edi_coparn_situacion_reserva", length = 1)
    private String bkEdiReservationSituation;

    @Size(max = 14)
    @Column(name = "edi_coparn_fecha_creacion_edi", length = 14)
    private String bkEdiCreationDate;

    @Size(max = 10)
    @Column(name = "edi_coparn_linea_naviera", length = 10)
    private String bkEdiShippingLine;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "linea_naviera_id")
    private ShippingLine shippingLine;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sub_unidad_negocio_id")
    private BusinessUnit subBusinessUnit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "programacion_nave_detalle_id")
    private VesselProgrammingDetail vesselProgrammingDetail;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "puerto_embarque_id")
    private Port loadingPort;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "puerto_destino_id")
    private Port destinationPort;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "puerto_descarga_id")
    private Port dischargePort;

    @Size(max = 25)
    @Column(name = "numero_booking", length = 25)
    private String bookingNumber;

    @Column(name = "edi_coparn_cantidad_contenedor")
    private Integer bkEdiContainerQuantity;

    @Size(max = 10)
    @Column(name = "edi_coparn_codigo_iso", length = 10)
    private String bkEdiIsoCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "codigo_iso_id")
    private IsoCode isoCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tamano_contenedor_id")
    private Catalog catContainerSize;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tipo_contenedor_id")
    private Catalog catContainerType;

    @Column(name = "edi_coparn_cantidad_contenedor_2")
    private Integer bkEdiContainerQuantity2;

    @Size(max = 10)
    @Column(name = "edi_coparn_codigo_iso_2", length = 10)
    private String bkEdiIsoCode2;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "codigo_iso_2_id")
    private IsoCode isoCode2;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tamano_contenedor_2_id")
    private Catalog catContainerSize2;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tipo_contenedor_2_id")
    private Catalog catContainerType2;

    @Size(max = 250)
    @Column(name = "edi_coparn_cliente", length = 250)
    private String bkEdiCustomer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "empresa_cliente_id")
    private Company clientCompany;


    @Size(max = 250)
    @Column(name = "edi_coparn_commodity", length = 250)
    private String bkEdiCommodity;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "producto_id")
    private Product product;

    @Size(max = 10)
    @Column(name = "edi_coparn_codigo_imo", length = 10)
    private String bkEdiImoCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "imo_id")
    private Imo imo;

    @Size(max = 10)
    @Column(name = "edi_coparn_temperatura", length = 10)
    private String bkEdiTemperature;

    @Column(name = "edi_coparn_coldtreatment")
    private Boolean bkEdiColdtreatment;

    @Column(name = "edi_coparn_atmosfera_controlada")
    private Boolean bkEdiAtmosphereControlled;

    @Size(max = 10)
    @Column(name = "edi_coparn_deposito_colombia", length = 10)
    private String bkEdiColombiaDepot;

    @Size(max = 1)
    @Column(name = "edi_coparn_reenviado", length = 1)
    private String bkEdiForwarded;

    @Column(name = "edi_coparn_reenviado_fecha")
    private LocalDateTime bkEdiForwardedDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_edi_coparn_estado")
    private Catalog catBkEdiStatus;

    @Column(name = "fecha_registro")
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_registro_id")
    private User registrationUser;

    @Column(name = "fecha_procesado_coparn")
    private LocalDateTime dateProcessedCoparn;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_procesado_id")
    private User processedUser;

    @Size(max = 250)
    @Column(name = "edi_coparn_obs_procesado", length = 250)
    private String bkEdiProcessedComment;

    @Size(max = 250)
    @Column(name = "edi_coparn_obs_para_proceso", length = 250)
    private String bkEdiCommentForProcess;

    @Size(max = 250)
    @Column(name = "edi_coparn_ult_obs_para_proceso", length = 250)
    private String bkEdiLastCommentForProcess;

    @Column(name = "activo")
    private Boolean active;

    @Size(max = 10)
    @Column(name = "edi_coparn_puerto_embarque", length = 10)
    private String bkEdiLoadingPort;

    @Size(max = 10)
    @Column(name = "edi_coparn_puerto_destino", length = 10)
    private String bkEdiDestinationPort;

    @Size(max = 10)
    @Column(name = "edi_coparn_puerto_descarga", length = 10)
    private String bkEdiDischargePort;

    @Size(max = 10)
    @Column(name = "edi_coparn_viaje", length = 10)
    private String bkEdiVoyage;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "nave_id")
    private Vessel vessel;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "linea_naviera_bk_id")
    private ShippingLine bkShippingLine;

    @Size(max = 100)
    @Column(name = "edi_coparn_nombre_nave", length = 100)
    private String bkEdiVesselName;

    @Size(max = 20)
    @Column(name = "trace_edi_coparn", length = 20)
    private String traceBkEdi;

    @Size(max = 50)
    @Column(name = "edi_coparn_cliente_alias", length = 50)
    private String bkEdiCustomerAlias;

    @Size(max = 20)
    @Column(name = "remark_rules_name", length = 20)
    private String remarkRulesName;

    @Size(max = 50)
    @Column(name = "trace_edi_coparn_2", length = 50)
    private String traceBkEdi2;

    @Column(name = "owner_correlative")
    private Integer ownerCorrelative;

    @Size(max = 250)
    @Column(name = "edi_coparn_obs_procesado_2", length = 250)
    private String bkEdiProcessedComment2;

}