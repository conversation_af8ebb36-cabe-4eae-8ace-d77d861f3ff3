package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class InspectionBoxSummaryDTO {

    private Integer finishedRecordsByInspector;
    private Integer recordsAtTime;
    private Integer recordsOnAlert;
    private Integer registrationsWithDelay;
    private Integer quantityRecords;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime dateSystem;
}
