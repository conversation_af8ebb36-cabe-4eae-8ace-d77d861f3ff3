package com.maersk.sd1.sde.service;


import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sde.dto.CedexMercListInput;
import com.maersk.sd1.sde.dto.CedexMercListItemDTO;
import com.maersk.sd1.sde.dto.CedexMercListOutputDTO;
import com.maersk.sd1.common.repository.CedexMercRepository;
import com.maersk.sd1.sde.dto.ICedexMercListItem;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class CedexMercListService {

    private static final Logger logger = LogManager.getLogger(CedexMercListService.class);
    private final CedexMercRepository cedexMercRepository;
    private final CatalogRepository catalogRepository;

    private static final String SD1_LONGITUDUNIT_CENTIMETRES = "sd1_longitudunit_centimetres";

    @Transactional(readOnly = true)
    public CedexMercListOutputDTO listCedexMerc(CedexMercListInput.Input filter) {
        CedexMercListOutputDTO output = new CedexMercListOutputDTO();
        try {
            logger.info("Starting listCedexMerc with filter: {}", filter);

            int pageNum = (filter.getPage() == null || filter.getPage() < 1) ? 1 : filter.getPage();
            int sizeNum = (filter.getSize() == null || filter.getSize() < 1) ? Integer.MAX_VALUE : filter.getSize();

            Integer active;
            if (filter.getActive() == null) {
                active = null;
            } else {
                active = filter.getActive() ? 1 : 0;
            }

            Integer sd1longitudunitcentimetres = catalogRepository.findCatalogIdByAlias(SD1_LONGITUDUNIT_CENTIMETRES);

            Long total = cedexMercRepository.countCedexMerc(
                    filter.getSubBusinessUnitId(),
                    filter.getCatEstimateTypeId(),
                    active,
                    filter.getComponentCode(),
                    filter.getRepairMethodCode(),
                    filter.getDamageLocation(),
                    filter.getCedexMercCode(),
                    filter.getRepairGroupCode(),
                    filter.getEquipmentType(),
                    filter.getShippingLine(),
                    filter.getCustomer()
            );

            logger.info("Total CedexMerc found: {}", total);

            List<ICedexMercListItem> cedexMercList = cedexMercRepository.getCedexMercList(
                    filter.getLanguageId(),
                    sd1longitudunitcentimetres,
                    filter.getBusinessUnitUserId(),
                    filter.getSubBusinessUnitId(),
                    filter.getCatEstimateTypeId(),
                    active,
                    filter.getComponentCode(),
                    filter.getRepairMethodCode(),
                    filter.getDamageLocation(),
                    filter.getCedexMercCode(),
                    filter.getRepairGroupCode(),
                    filter.getEquipmentType(),
                    filter.getShippingLine(),
                    filter.getCustomer(),
                    pageNum,
                    sizeNum,
                    total
            );

            List<List<Long>> totalRegistros = new ArrayList<>();
            totalRegistros.add(List.of(total));
            output.setTotalRegistros(totalRegistros);
            List<CedexMercListItemDTO> dataList = mapToCedexMercListItemDTO(cedexMercList);
            output.setData(dataList);
        } catch (Exception e) {
            logger.error("Error in listCedexMerc", e);
            List<List<Long>> totalRegistros = new ArrayList<>();
            totalRegistros.add(List.of(0L));
            output.setTotalRegistros(totalRegistros);
            output.setData(null);
        }
        return output;
    }

    private List<CedexMercListItemDTO> mapToCedexMercListItemDTO(List<ICedexMercListItem> cedexMercList) {
        List<CedexMercListItemDTO> dtoList = new ArrayList<>();
        for (ICedexMercListItem item : cedexMercList) {
            CedexMercListItemDTO dto = new CedexMercListItemDTO();
            dto.setId(item.getId());
            dto.setIsReefer(item.getIsReefer());
            dto.setEstimateType(item.getEstimateType());
            dto.setComponentCode(item.getComponentCode());
            dto.setRepairMethodCode(item.getRepairMethodCode());
            dto.setDamageLocation(item.getDamageLocation());
            dto.setMercMinValue(item.getMercMinValue());
            dto.setMercMaxValue(item.getMercMaxValue());
            dto.setMercDimension(item.getMercDimension());
            dto.setEngineBrandCode(item.getEngineBrandCode());
            dto.setMercMaxPieces(item.getMercMaxPieces());
            dto.setMercHh(item.getMercHh());
            dto.setMercMaterialCost(item.getMercMaterialCost());
            dto.setCedexMercCode(item.getCedexMercCode());
            dto.setCedexMercDesc(item.getCedexMercDesc());
            dto.setCurrencyId(item.getCurrencyId());
            dto.setCurrencyAbbreviation(item.getCurrencyAbbreviation());
            dto.setMercObs(item.getMercObs());
            dto.setActive(item.getActive());
            dto.setUserRegistrationId(item.getUserRegistrationId());
            dto.setRegistrationDate(item.getRegistrationDate());
            dto.setUserModificationId(item.getUserModificationId());
            dto.setModificationDate(item.getModificationDate());
            dto.setUserRegistrationName(item.getUserRegistrationName());
            dto.setUserRegistrationLastname(item.getUserRegistrationLastname());
            dto.setUserModificationName(item.getUserModificationName());
            dto.setUserModificationLastname(item.getUserModificationLastname());
            dto.setRepairGroupTranslation(item.getRepairGroupTranslation());
            dto.setTypeCodeGroupTranslation(item.getTypeCodeGroupTranslation());
            dto.setShippingLineId(item.getShippingLineId());
            dto.setShippingLine(item.getShippingLine());
            dto.setCustomerId(item.getCustomerId());
            dto.setCustomerName(item.getCustomerName());
            dto.setCatEquipmentCategoryId(item.getCatEquipmentCategoryId());
            dto.setCatEquipmentCategoryTranslation(item.getCatEquipmentCategoryTranslation());
            dtoList.add(dto);
        }
        return dtoList;
    }
}