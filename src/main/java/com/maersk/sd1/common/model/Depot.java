package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "deposito", schema = "sds", uniqueConstraints = {
        @UniqueConstraint(name = "UniqueDeposito", columnNames = {"codigo_deposito"})
})
public class Depot {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "deposito_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "unidad_negocio_id", nullable = false)
    private BusinessUnit businessUnit;

    @Size(max = 10)
    @NotNull
    @Column(name = "codigo_deposito", nullable = false, length = 10)
    private String codeDeposit;

    @Size(max = 100)
    @NotNull
    @Column(name = "nombre_deposito", nullable = false, length = 100)
    private String nameDeposit;

    @Size(max = 250)
    @Column(name = "direccion_deposito", length = 250)
    private String addressDeposit;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sub_unidad_negocio_id")
    private BusinessUnit subBusinessUnit;

    @NotNull
    @Column(name = "deposito_default", nullable = false)
    private Boolean depositDefault = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_codigo_aduana_id")
    private Catalog catCodigoAduana;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_clase_operador_aduana_id")
    private Catalog catClaseOperadorAduana;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_origen_creacion_depot_id")
    private Catalog catOrigenCreacionDepot;

    public Depot(Integer id) {
        this.id = id;
    }

}