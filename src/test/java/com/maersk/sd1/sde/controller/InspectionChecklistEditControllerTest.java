package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.InspectionChecklistEditInput;
import com.maersk.sd1.sde.dto.InspectionChecklistEditOutput;
import com.maersk.sd1.sde.service.InspectionChecklistEditService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InspectionChecklistEditControllerTest {

    @Mock
    private InspectionChecklistEditService inspectionChecklistEditService;

    @InjectMocks
    private InspectionChecklistEditController controller;

    private InspectionChecklistEditInput.Root request;

    @BeforeEach
    void setUp() {
        request = new InspectionChecklistEditInput.Root();
        InspectionChecklistEditInput.Prefix prefix = new InspectionChecklistEditInput.Prefix();
        InspectionChecklistEditInput.Input input = new InspectionChecklistEditInput.Input();
        input.setInspectionChecklistId(1);
        input.setSubBusinessUnitId(1);
        input.setCatInspectionTypeId(1);
        input.setOrderNumber(1);
        input.setDescription("Test Description");
        input.setUserModificationId(1);
        input.setActive(true);
        input.setLanguageId(1);
        prefix.setInput(input);
        request.setPrefix(prefix);
    }

    @Test
    void Given_ValidRequest_When_InspectionChecklistEdit_Then_ReturnResponseEntity() {
        InspectionChecklistEditOutput output = new InspectionChecklistEditOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");

        when(inspectionChecklistEditService.editInspectionChecklist(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(output);

        ResponseEntity<ResponseController<InspectionChecklistEditOutput>> response = controller.inspectionChecklistEdit(request);

        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(output, response.getBody().getResult());
    }

    @Test
    void Given_ExceptionThrown_When_InspectionChecklistEdit_Then_ReturnErrorResponseEntity() {
        when(inspectionChecklistEditService.editInspectionChecklist(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenThrow(new RuntimeException("Test Exception"));

        ResponseEntity<ResponseController<InspectionChecklistEditOutput>> response = controller.inspectionChecklistEdit(request);

        assertNotNull(response);
        assertEquals(500, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Test Exception", response.getBody().getResult().getRespMensaje());
    }
}