package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.CompanyConfig;
import com.maersk.sd1.common.model.CompanyConfigId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface CompanyConfigRepository extends JpaRepository<CompanyConfig, CompanyConfigId> {

    List<CompanyConfig> findByCompany_Id(Integer empresaId);

    @Modifying
    @Transactional
    @Query("DELETE FROM CompanyConfig cc WHERE cc.company.id = :companyId")
    void deleteAllByCompanyId(@Param("companyId") Integer companyId);

    @Query("DELETE FROM CompanyConfig cc WHERE cc.company.id = :companyId")
    void deleteByCompanyId(@Param("companyId") Integer companyId);

    @Query("SELECT c.value FROM CompanyConfig c WHERE c.company.id = :truckCompanyId AND c.catConfigurationType.id = :configTypeId")
    String findTruckCompanyScac(@Param("truckCompanyId") Integer truckCompanyId, @Param("configTypeId") Integer configTypeId);
}