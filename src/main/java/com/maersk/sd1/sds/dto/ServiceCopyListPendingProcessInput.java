package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ServiceCopyListPendingProcessInput {

    @Data
    public static class Input {
        @JsonProperty("ediCoparnSetupId")
        private Integer ediCoparnSetupId;
    }
    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ServiceCopyListPendingProcessInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private ServiceCopyListPendingProcessInput.Prefix prefix;
    }
}
