package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class BookingEdiSettingDeleteInput {

    @Data
    public static class Input {
        @JsonProperty("seteo_edi_coparn_id")
        @NotNull(message = "seteo_edi_coparn_id cannot be null")
        private Integer settingEdiCoparnId;

        @JsonProperty("usuario_modificacion_id")
        @NotNull(message = "usuario_modificacion_id cannot be null")
        private Integer userModificationId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }

    private BookingEdiSettingDeleteInput() {
        // Private constructor to hide the implicit public one
    }
}