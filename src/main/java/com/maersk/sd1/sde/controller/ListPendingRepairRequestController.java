package com.maersk.sd1.sde.controller;

import com.maersk.sd1.sde.dto.ListPendingRepairRequestInputDTO;
import com.maersk.sd1.sde.dto.ListPendingRepairRequestOutput;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.service.ListPendingRepairRequestService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDE/module/sde/SDERepairCleaningRequestServiceImp")
public class ListPendingRepairRequestController {
    private static final Logger logger = LogManager.getLogger(ListPendingRepairRequestController.class);

    private final ListPendingRepairRequestService listPendingCleaningRequestService;

    @PostMapping("/sdelistPendingRepairRequest")
    public ResponseEntity<ResponseController<ListPendingRepairRequestOutput>> listPendingCleaningRequest(
            @RequestBody @Valid ListPendingRepairRequestInputDTO.Root request) {
        try {
            logger.info("Request received listPendingCleaningRequest: {}", request);
            ListPendingRepairRequestInputDTO.Input inputDto = request.getPrefix().getInput();

            ListPendingRepairRequestOutput output = listPendingCleaningRequestService.getPendingCleaningRequests(inputDto);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the listPendingCleaningRequest.", e);
            ListPendingRepairRequestOutput errorOutput = new ListPendingRepairRequestOutput();
            errorOutput.setPendingCleaningRequests(null);
            errorOutput.setTotal(List.of(List.of(0L)));
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}