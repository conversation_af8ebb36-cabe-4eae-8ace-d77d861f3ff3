package com.maersk.sd1.sdg.service;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sdg.controller.dto.GateoutGeneralAssigmentListDtoOutput;
import com.maersk.sd1.sdg.controller.dto.SdgGateoutGeneralAssignmentListInput;
import com.maersk.sd1.sdg.dto.GateOutGeneralAssignmentList.*;
import com.maersk.sd1.sdg.repository.SdgEirRepository;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
public class GateOutGeneralAssignmentListService {

    private static final String FLAG_TO_FLEX = "FLAG_TO_FLEX";
    private static final String PRC_GATE_OUT_EMPTY_LIGHT = "PRC_GATE_OUT_EMPTY_LIGHT";
    private final CatalogLanguageRepository catLangRepository;
    private final ContainerRepository containerRepository;
    private final CatalogRepository catalogService;
    private final SdgEirRepository eirRepository;
    private final MessageLanguageService messageLanguageService;
    private final BookingDetailRepository bookingDetailRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository;
    private final CompanyRepository companyRepository;
    private final ShippingLineRepository shippingLineRepository;
    private final CatalogRepository catalogRepository;
    private final ChassisDocumentRepository chassisDocumentRepository;
    private final ChassisBookingDocumentRepository chassisBookingDocumentRepository;

    public GateOutGeneralAssignmentListService(CatalogLanguageRepository catLangRepository, ContainerRepository containerRepository, CatalogRepository catalogService, SdgEirRepository eirRepository, MessageLanguageService messageLanguageService, BookingDetailRepository bookingDetailRepository, CargoDocumentDetailRepository cargoDocumentDetailRepository, VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository, CompanyRepository companyRepository, ShippingLineRepository shippingLineRepository, CatalogRepository catalogRepository, ChassisDocumentRepository chassisDocumentRepository, ChassisBookingDocumentRepository chassisBookingDocumentRepository) {
        this.catLangRepository = catLangRepository;
        this.containerRepository = containerRepository;
        this.catalogService = catalogService;
        this.eirRepository = eirRepository;
        this.messageLanguageService = messageLanguageService;
        this.bookingDetailRepository = bookingDetailRepository;
        this.cargoDocumentDetailRepository = cargoDocumentDetailRepository;
        this.vesselProgrammingCutoffRepository = vesselProgrammingCutoffRepository;
        this.companyRepository = companyRepository;
        this.shippingLineRepository = shippingLineRepository;
        this.catalogRepository = catalogRepository;
        this.chassisDocumentRepository = chassisDocumentRepository;
        this.chassisBookingDocumentRepository = chassisBookingDocumentRepository;
    }


    public GateoutGeneralAssigmentListDtoOutput gateoutGeneralAssigmentListv2(SdgGateoutGeneralAssignmentListInput.Root inputRoot) {
        if (inputRoot == null) {
            return null;
        }

        SdgGateoutGeneralAssignmentListInput.Input input = inputRoot.getSdg().getInput();

        Integer eirId = input.getEirId();
        Integer languageId = input.getLanguageId();
        LocalDateTime truckInMin = input.getTruckInMin();
        LocalDateTime truckInMax = input.getTruckInMax();
        Integer subBusinessUnitLocalId = input.getSubBusinessUnitLocalId();
        String documentNumber = input.getDocumentNumber();
        String vehicle = input.getVehicle();


        Integer containerNoCntId = containerRepository.findByContainerNumber(Parameter.CONTAINER_NO_CNT).getId();
        Integer containerNotApplicableId = containerRepository.findByContainerNumber(Parameter.CONTAINER_NOT_APPLICA).getId();
        Integer isGateOut = catalogService.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
        Integer isFull = catalogService.findIdByAlias(Parameter.CONTENT_TYPE_FULL);
        Integer isContainerDry = catalogService.findIdByAlias(Parameter.CATALOG_TYPE_CONTAINER_DRY_ALIAS);
        Integer isCreationGoGeneral = catalogService.findIdByAlias(Parameter.IS_CREATION_GO_GENERAL);
        Integer isCreationGoLight = catalogService.findIdByAlias(Parameter.IS_CREATION_GO_LIGHT);


        Integer isDocTypeBooking = catalogService.findIdByAlias(Parameter.IS_TYPE_BOOKING);
        Integer isDocTypeBL = catalogService.findIdByAlias(Parameter.IS_DOCUMENT_TYPE_BL);

        Integer isMeasureWeightKg = catalogService.findIdByAlias(Parameter.CATALOG_MEASURE_WEIGHT_KG_ALIAS);

        Integer isOperImport = catalogService.findIdByAlias(Parameter.IS_OPER_IMPORT);
        Integer isOperExport = catalogService.findIdByAlias(Parameter.IS_OPER_EXPORT);
        Integer isOperStorage = catalogService.findIdByAlias(Parameter.IS_OPER_STORAGE);
        Integer isTypeContainerHC = catalogService.findIdByAlias(Parameter.CATALOG_TYPE_CONTAINER_HC_ALIAS);

        Integer isEmpty = catalogService.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);

        List<TbBase> tbBases = new ArrayList<>();

        tbBases.addAll(eirRepository.getFilteredResults1(containerNoCntId, containerNotApplicableId, eirId, truckInMin, truckInMax,
                subBusinessUnitLocalId, isGateOut, input.getApsId(), vehicle, documentNumber, isDocTypeBL, isOperImport, isOperExport, isOperStorage, isMeasureWeightKg, isCreationGoGeneral, isCreationGoLight, isDocTypeBooking));

        tbBases.addAll(eirRepository.getFilteredResults2(containerNoCntId, eirId, truckInMin, truckInMax,
                subBusinessUnitLocalId, isGateOut, isFull, vehicle, documentNumber, isDocTypeBL, isOperImport, isOperExport, isOperStorage, isMeasureWeightKg, isCreationGoGeneral));

        tbBases.forEach(tbBase -> {
            if (tbBase.getBookingGoeId() == null && tbBase.getDocumentoCargaGofId() == null && Boolean.TRUE.equals(!tbBase.getWithContainerNumer())) {
                tbBase.setContainerFullId(containerNotApplicableId);
                tbBase.setContainerNumber(Parameter.CONTAINER_NOT_APPLICA);
            }
        });


        tbBases.forEach(tbBase -> {
            if (Boolean.TRUE.equals(tbBase.getWithContainerNumer())) {
                Container container = containerRepository.findById(tbBase.getContainerFullId()).orElse(null);
                if (container != null) {
                    tbBase.setContainerNumber(container.getContainerNumber());
                    tbBase.setContainerType(catLangRepository.fnCatalogTranslationDesc(container.getCatSize().getId(), languageId) + " " +
                            catLangRepository.fnCatalogTranslationDesc(container.getCatContainerType().getId(), languageId));
                    tbBase.setIsoCode(container.getIsoCode().getIsoCode());
                    tbBase.setGrade(catLangRepository.fnCatalogTranslationDesc(container.getCatGrade().getId(), languageId));
                    String tare = String.format("%.1f", (double) container.getContainerTare()) + " " +
                            catLangRepository.fnCatalogTranslationDesc(container.getCatEquipMeasureTare().getId(), languageId);
                    tbBase.setTara(tare);
                    String payload = String.format("%.1f", (double) container.getMaximunPayload()) + " " +
                            catLangRepository.fnCatalogTranslationDesc(container.getCatEquipMeasurePayload().getId(), languageId);
                    tbBase.setPayload(payload);
                    Integer reeferType = Optional.ofNullable(container.getCatReeferType()).map(Catalog::getId).orElse(null);
                    if (reeferType != null) {
                        tbBase.setReeferType(catLangRepository.fnCatalogTranslationDescLong(container.getCatReeferType().getId(), languageId));
                    } else {
                        tbBase.setReeferType(null);
                    }
                }
            }
        });

        List<Tb01> tb01List = new ArrayList<>();
        List<Tb02> tb02List = new ArrayList<>();


        tbBases.forEach(tbBase -> {
            if (tbBase.getBookingGoeId() != null) {
                List<BookingDetail> bookingDetails = bookingDetailRepository.findByBookingId(tbBase.getBookingGoeId());
                tb01List.addAll(generateTb01(tbBases, bookingDetails, isContainerDry));

            }
        });


        tbBases.forEach(tbBase -> {
            if (tbBase.getBookingGoeId() != null) {
                List<Eir> eirList = eirRepository.findByBookingGout(tbBase.getBookingGoeId());
                tb02List.addAll(generateTb02(tbBases, eirList, isContainerDry, isTypeContainerHC, containerNoCntId, isGateOut));
            }
        });

        List<TbDocchaDet> tbDocchaDets = new ArrayList<>(getDocchaDet(tbBases, languageId));


        List<TbDocDet> docDetList = new ArrayList<>();
        docDetList.addAll(generateDocDetList(tb01List, tb02List));
        docDetList.addAll(generateDocDetFromTbBase(tbBases));


        List<Integer> programacionNaveDetalleIds = tbBases.stream()
                .map(TbBase::getProgramacionNaveDetalleId)
                .distinct()
                .toList();

        List<VesselProgrammingCutoff> cutoffList = vesselProgrammingCutoffRepository.findByProgramacionNaveDetalleIdInAndActivo(programacionNaveDetalleIds, true);

        Map<String, VesselProgrammingCutoff> cutoffMap = cutoffList.stream()
                .collect(Collectors.toMap(
                        c -> c.getVesselProgrammingDetail().getId() + "-" + c.getShippingLine().getId(),
                        c -> c
                ));

        for (TbBase tbBase : tbBases) {
            if (Objects.equals(tbBase.getCatEmptyFullId(), isEmpty)) {
                String key = tbBase.getProgramacionNaveDetalleId() + "-" + tbBase.getLineaNavieraId();
                VesselProgrammingCutoff cutoff = cutoffMap.get(key);

                if (cutoff != null) {
                    tbBase.setFechaCutoffRetiroVacioDry(cutoff.getDateCutoffRetreatEmptyDry());
                    tbBase.setFechaCutoffRetiroVacioReefer(cutoff.getDateCutoffRetreatEmptyReefer());
                }
            }
        }


        programacionNaveDetalleIds = tbBases.stream()
                .filter(tb -> tb.getFechaCutoffRetiroVacioDry() == null && tb.getFechaCutoffRetiroVacioReefer() == null)
                .map(TbBase::getProgramacionNaveDetalleId)
                .distinct()
                .toList();

        cutoffList = vesselProgrammingCutoffRepository.findByProgramacionNaveDetalleIdInAndLineaNavieraIdIsNullAndActivo(programacionNaveDetalleIds, true);

        cutoffMap = cutoffList.stream()
                .collect(Collectors.toMap(
                        c -> String.valueOf(c.getVesselProgrammingDetail().getId()),
                        c -> c
                ));

        for (TbBase tbBase : tbBases) {
            if (Objects.equals(tbBase.getCatEmptyFullId(), isEmpty) &&
                    tbBase.getFechaCutoffRetiroVacioDry() == null &&
                    tbBase.getFechaCutoffRetiroVacioReefer() == null) {

                VesselProgrammingCutoff cutoff = cutoffMap.get(String.valueOf(tbBase.getProgramacionNaveDetalleId()));

                if (cutoff != null) {
                    tbBase.setFechaCutoffRetiroVacioDry(cutoff.getDateCutoffRetreatEmptyDry());
                    tbBase.setFechaCutoffRetiroVacioReefer(cutoff.getDateCutoffRetreatEmptyReefer());
                }
            }
        }

        tbBases.forEach(item -> {
            if (item.getBookingGoeId() != null && !item.getWithContainerNumer()) {
                item.setFindContainer(true);
            }
        });

        tbBases.forEach(item -> {
            if (item.getDocumentoCargaGofId() != null && !item.getWithContainerNumer()) {
                item.setFindContainer(true);
            }
        });

        GateoutGeneralAssigmentListDtoOutput output = new GateoutGeneralAssigmentListDtoOutput();
        output.setData(mapToResultList(tbBases, languageId, isEmpty, docDetList, tbDocchaDets, input.getPage() - 1, input.getSize()));
        output.setTotal(List.of(List.of(tbBases.size())));

        return output;

    }

    public List<GateoutGeneralAssigmentListDtoOutput.ResultList> mapToResultList(List<TbBase> tbBaseList, Integer languageId, Integer isEmpty,
                                                                                 List<TbDocDet> docDetList, List<TbDocchaDet> docchaDetList, int page, int size) {

        Integer isDocumentActive = catalogService.findIdByAlias(Parameter.IS_DOCUMENT_ACTIVE);
        return tbBaseList.stream()
                .filter(a -> a.getCatEmptyFullId().equals(isEmpty))
                .sorted(Comparator.comparing(TbBase::getFechaIngresoCamion))
                .skip((long) page * size)
                .limit(size)
                .map(a -> {
                    GateoutGeneralAssigmentListDtoOutput.ResultList result = new GateoutGeneralAssigmentListDtoOutput().new ResultList();

                    result.setEirNumber(a.getEirId());
                    result.setEirChassis(a.getEirChassisId());
                    result.setCatProcedenceId(a.getCatProcedenciaId());
                    result.setTruckInDate(a.getFechaIngresoCamion().toString());
                    result.setPlateNumber(a.getPlaca());
                    result.setFindContainer(Boolean.TRUE.equals(a.getFindContainer()) ? 1 : 0);
                    result.setFindChassis(Boolean.TRUE.equals(a.getFindChassis()) ? 1 : 0);
                    result.setChassisNumber(a.getChassisNumber());
                    result.setOwnerCompany(a.getOwnerCompany());
                    result.setObservation(a.getObservation());
                    result.setPreAllocatedContainer(a.getPreAllocatedContainer());

                    if (a.getBookingGoeId() != null || a.getDocumentoCargaGofId() != null) {
                        if (!Objects.equals(Optional.ofNullable(a.getCatEstadoBooking()).orElse(a.getCatEstadoDocumentoCargaId()), isDocumentActive)) {
                            result.setStatus(messageLanguageService.getMessage("PRC_GO_GENERAL", 3, languageId)); // Document Cancelled
                        } else {
                            switch (a.getControlAsignacionLight()) {
                                case 0 ->
                                        result.setStatus(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 2, languageId)); // To assign
                                case 1 ->
                                        result.setStatus(messageLanguageService.getMessage("PRC_GO_GENERAL", 4, languageId)); // To completed
                                case 2 ->
                                        result.setStatus(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 3, languageId)); // Reassign
                                default ->
                                        result.setStatus("");
                            }
                        }
                    } else if (a.getDocumentChassisGoId() != null) {
                        result.setStatus(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 2, languageId)); // To assign
                    }

                    if (a.getBookingGoeId() != null || a.getDocumentoCargaGofId() != null) {
                        String tipoMovimiento = catLangRepository.fnCatalogTranslationDescLong(a.getCatEmptyFullId(), languageId);
                        if (a.getCatProcedenciaId() != null) {
                            tipoMovimiento += " - " + catLangRepository.fnCatalogTranslationDescLong(a.getCatProcedenciaId(), languageId);
                        }
                        result.setMoveType(tipoMovimiento);
                    } else {
                        result.setMoveType("Gate Out Chassis");
                    }

                    if (a.getBookingGoeId() != null) {
                        result.setDocumentContainer(catLangRepository.fnCatalogTranslationDescLong(a.getCatDocumentTypeGoeId(), languageId) + " " + a.getNumeroBookingGoe());
                    } else if (a.getDocumentoCargaGofId() != null) {
                        result.setDocumentContainer(catLangRepository.fnCatalogTranslationDescLong(a.getCatDocumentTypeGofId(), languageId) + " " + a.getDocumentoCargaGof());
                    } else {
                        result.setDocumentContainer("");
                    }

                    if (a.getDocumentChassisGoId() != null) {
                        result.setDocumentChassis(catLangRepository.fnCatalogTranslationDesc(a.getCatChassisDocumentType(), languageId) + " " + a.getDocumentChassisNumber());
                    } else {
                        result.setDocumentChassis("");
                    }

                    if (a.getLineaNavieraId() != null) {
                        Optional<ShippingLine> optionalShippingLine = shippingLineRepository.findById(a.getLineaNavieraId());
                        if (optionalShippingLine.isPresent()) {
                            ShippingLine shippingLine = optionalShippingLine.get();
                            result.setShippingLine(shippingLine.getName());
                        }
                    }

                    if (a.getBookingGoeId() != null || a.getDocumentoCargaGofId() != null) {
                        result.setVesselPlanDetail(a.getVesselVoyage() + " / " + catLangRepository.fnCatalogTranslationDescLong(a.getCatOperacionId(), languageId));
                    } else {
                        result.setVesselPlanDetail("");
                    }

                    for (TbDocDet docDet : docDetList) {
                        if (docDet.getEirId().equals(a.getEirId())) {
                            if (Boolean.TRUE.equals(docDet.getIsReefer())) {
                                result.setCutoff(String.valueOf(a.getFechaCutoffRetiroVacioReefer()));
                            } else {
                                result.setCutoff(String.valueOf(a.getFechaCutoffRetiroVacioDry()));
                            }
                        }
                    }
                    Eir eir = eirRepository.findById(a.getEirId()).orElse(null);
                    if (eir != null) {
                        result.setDriverName(eir.getDriverPerson().getIdentificationDocument() + " - " + eir.getDriverPerson().getNames() + " " + eir.getDriverPerson().getFirstLastName() + " " + eir.getDriverPerson().getSecondLastName());
                    }

                    result.setOperationType(catLangRepository.fnCatalogTranslationDescLong(a.getOperationTypeId(), languageId));

                    if (a.getEmpresaEmbarcadorId() != null) {
                        Optional<Company> companyOptional = companyRepository.findById(a.getEmpresaEmbarcadorId());
                        if (companyOptional.isPresent()) {
                            Company company = companyOptional.get();
                            result.setShipperName(company.getDocument() + " - " + company.getLegalName());
                        }
                    }

                    if (a.getEmpresaConsignatarioId() != null) {
                        Optional<Company> consigneeOptional = companyRepository.findById(a.getEmpresaConsignatarioId());
                        if (consigneeOptional.isPresent()) {
                            Company consignee = consigneeOptional.get();
                            result.setConsigneeName(consignee.getDocument() + " - " + consignee.getLegalName());
                        }
                    }


                    // Tipo de contenedor disponible
                    result.setContainerAvailabilityType(getContainerAvailabilityType(a.getEirId(), docDetList));

                    // Datos de chasis
                    result.setChassisOperationType(catLangRepository.fnCatalogTranslationDesc(a.getCatChassisOperationTypeId(), languageId));
                    result.setChassisAvailabilityType(getChassisAvailabilityType(a.getEirChassisId(), docchaDetList));


                    // Datos del contenedor
                    result.setContainerId(a.getContainerFullId());
                    result.setContainerNumber(a.getContainerNumber());
                    result.setContainerType(a.getContainerType());
                    result.setIsoCode(a.getIsoCode());
                    result.setGrade(a.getGrade());
                    result.setTara(a.getTara());
                    result.setPayload(a.getPayload());
                    result.setReeferType(a.getReeferType());

                    // Documento de chasis
                    result.setDocumentChassisId(String.valueOf(a.getDocumentChassisGoId()));

                    Double weight = a.getValueReceivedWeight() != null ? a.getValueReceivedWeight().doubleValue() : 0.0;
                    result.setReceivedWeight(String.format("%.2f %s",
                            weight,
                            catLangRepository.fnCatalogTranslationDesc(a.getCatReceivedWeightMeasureId(), languageId)
                    ));
                    result.setSeal1(a.getSeal1());
                    result.setSeal2(a.getSeal2());
                    result.setSeal3(a.getSeal3());
                    result.setSeal4(a.getSeal4());

                    result.setContainerSizeTypeJson(obtenerContainerSizeTypeJson(docDetList));

                    if (a.getBookingGoeId() != null) {
                        result.setDocumentContainerNumber(a.getNumeroBookingGoe());
                    } else if (a.getDocumentoCargaGofId() != null) {
                        result.setDocumentContainerNumber(a.getDocumentoCargaGof());
                    } else {
                        result.setDocumentContainerNumber("");
                    }

                    if (eir != null && eir.getBookingGout() != null) {
                        result.setMercaderia(eir.getBookingGout().getCommodity());
                    }


                    return result;
                })
                .toList();
    }

    public String getChassisAvailabilityType(Integer eirChassisId, List<TbDocchaDet> docchaDetList) {
        return docchaDetList.stream()
                .filter(doc -> doc.getEirChassisId().equals(eirChassisId))
                .sorted(Comparator.comparing(TbDocchaDet::getChaType).reversed())
                .map(doc -> doc.getChaType() + " (" + doc.getQ() + ")")
                .collect(Collectors.joining(", "));
    }


    public List<TbDocchaDet> getDocchaDet(List<TbBase> tbBaseList,
                                          Integer languageId) {

        List<ChassisDocument> documentChassisList = chassisDocumentRepository.findByIds(tbBaseList.stream()
                .map(TbBase::getDocumentChassisGoId)
                .toList());

        List<ChassisBookingDocument> documentChassisBookingList = chassisBookingDocumentRepository.findByChassisDocumentIds(tbBaseList.stream()
                .map(TbBase::getDocumentChassisGoId)
                .toList());

        return tbBaseList.stream()
                .flatMap(tbBase -> documentChassisList.stream()
                        .filter(docChassis -> Objects.equals(tbBase.getDocumentChassisGoId(), docChassis.getId()))
                        .flatMap(docChassis -> documentChassisBookingList.stream()
                                .filter(chassisBooking -> Objects.equals(docChassis.getId(), chassisBooking.getChassisDocument().getId()))
                                .collect(Collectors.groupingBy(
                                        chassisBooking -> Arrays.asList(tbBase.getEirChassisId(), chassisBooking.getCatChassisType().getId()),
                                        Collectors.summingInt(chassisBooking ->
                                                Optional.ofNullable(chassisBooking.getQuanty()).orElse(0) -
                                                        Optional.ofNullable(chassisBooking.getAttendedQuantity()).orElse(0)
                                        )
                                ))
                                .entrySet().stream()
                                .map(entry -> new TbDocchaDet(
                                        entry.getKey().getFirst(), // eir_chassis_id
                                        entry.getKey().get(1), // cat_chassis_type_id
                                        entry.getValue(), // SUM(quanty - quantity_attended)
                                        catLangRepository.fnCatalogTranslationDescLong(entry.getKey().get(1), languageId) // CHATYPE
                                ))
                        )
                )
                .toList();
    }


    public String getContainerAvailabilityType(Integer eirId, List<TbDocDet> docDetList) {
        List<String> availabilityTypes = docDetList.stream()
                .filter(doc -> doc.getEirId().equals(eirId))
                .sorted((d1, d2) -> d2.getTipcnt().compareTo(d1.getTipcnt())) // Orden descendente por TipCnt
                .map(doc -> doc.getTnocnt() + " - " + doc.getTipcnt() + " (" + doc.getQuantity() + ")")
                .toList();

        return String.join(", ", availabilityTypes);
    }

    public String obtenerContainerSizeTypeJson(List<TbDocDet> docDetList) {
        AtomicInteger counter = new AtomicInteger(1);
        List<TbContainerSizeType> containerSizeTypes = docDetList.stream()
                .sorted(Comparator.comparing(TbDocDet::getCatTamanoId))
                .map(doc -> new TbContainerSizeType(
                        counter.getAndIncrement(),
                        doc.getCatTamanoId(),
                        doc.getTnocnt(),
                        doc.getCatTipoContenedorId(),
                        doc.getTipcnt(),
                        doc.getTnocnt() + " - " + doc.getTipcnt()
                ))
                .toList();

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.writeValueAsString(containerSizeTypes);
        } catch (JsonProcessingException e) {
            return "[]";
        }
    }

    public List<Tb01> generateTb01(List<TbBase> tbBaseList, List<BookingDetail> bookingDetalles, Integer isTypeContainerDry) {
        List<Tb01EirBooking> eirBookings = tbBaseList.stream()
                .filter(tb -> tb.getBookingGoeId() != null)
                .map(tb -> new Tb01EirBooking(tb.getEirId(), tb.getBookingGoeId()))
                .distinct()
                .toList();

        Map<Integer, Integer> eirBookingMap = eirBookings.stream()
                .collect(Collectors.toMap(
                        Tb01EirBooking::getBookingId,
                        Tb01EirBooking::getEirId,
                        (existing, newValue) -> existing
                ));

        return bookingDetalles.stream()
                .filter(bk -> bk.getActive() && eirBookingMap.containsKey(bk.getBooking().getId()))
                .collect(Collectors.groupingBy(
                        bk -> Arrays.asList(
                                bk.getBooking().getId(),
                                bk.getCatSize().getId(),
                                isFlagToFlex(bk.getRemarkRulesName()) ? isTypeContainerDry : bk.getCatContainerType().getId(),
                                Optional.ofNullable(bk.getRemarkRulesName()).orElse(""),
                                eirBookingMap.get(bk.getBooking().getId())
                        ),
                        Collectors.summingInt(BookingDetail::getReservationQuantity)
                ))
                .entrySet().stream()
                .map(entry -> new Tb01(
                        (Integer) entry.getKey().getFirst(), // bookingId
                        (Integer) entry.getKey().get(1), // sizeContainerId
                        (Integer) entry.getKey().get(2), // typeContainerId
                        entry.getValue(), // totalQuantity
                        (String) entry.getKey().get(3), // remarkRule
                        (Integer) entry.getKey().get(4)  // eirId
                ))
                .toList();
    }

    private boolean isFlagToFlex(String remarkRulesName) {
        return FLAG_TO_FLEX.equalsIgnoreCase(Optional.ofNullable(remarkRulesName).orElse(""));
    }

    public List<Tb02> generateTb02(List<TbBase> tbBaseList, List<Eir> eirList, Integer isTypeContainerDry, Integer isTypeContainerHC, Integer containerNoCntId, Integer isGateOut) {
        Map<Integer, String> bookingRemarks = tbBaseList.stream()
                .filter(tb -> tb.getBookingGoeId() != null)
                .collect(Collectors.toMap(
                        TbBase::getBookingGoeId,
                        tb -> Optional.ofNullable(tb.getRemarkRule()).orElse(""),
                        (remark1, remark2) -> remark1
                ));

        return eirList.stream()
                .filter(eir -> isGateOut.equals(eir.getCatMovement().getId()) && eir.getActive())
                .filter(eir -> bookingRemarks.containsKey(eir.getBookingGout().getId()))
                .collect(Collectors.groupingBy(
                        eir -> new Tb02BookingStatusKey(
                                eir.getBookingGout().getId(),
                                eir.getCatSizeCnt().getId(),
                                shouldMapToDry(bookingRemarks.get(eir.getBookingGout().getId()), eir.getCatContainerType().getId(), isTypeContainerDry, isTypeContainerHC)
                        ),
                        Collectors.collectingAndThen(
                                Collectors.partitioningBy(
                                        eir -> eir.getContainer().getId().equals(containerNoCntId),
                                        Collectors.counting()
                                ),
                                counts -> new int[]{counts.get(true).intValue(), counts.get(false).intValue()}
                        )
                ))
                .entrySet().stream()
                .map(entry -> {
                    Tb02BookingStatusKey key = entry.getKey();
                    int[] counts = entry.getValue();
                    return new Tb02(
                            key.getBookingId(),
                            key.getSizeContainerId(),
                            key.getTypeContainerId(),
                            counts[0], // eirs_in_progress_quantity
                            counts[1]  // eirs_completed_quantity
                    );
                })
                .toList();
    }

    private Integer shouldMapToDry(String remarkRule, Integer containerTypeId, Integer isTypeContainerDry, Integer isTypeContainerHC) {
        if (FLAG_TO_FLEX.equalsIgnoreCase(remarkRule) && (containerTypeId.equals(isTypeContainerDry) || containerTypeId.equals(isTypeContainerHC))) {
            return isTypeContainerDry;
        }
        return containerTypeId;
    }

    public List<TbDocDet> generateDocDetList(List<Tb01> tb01List, List<Tb02> tb02List) {
        List<TbDocDet> docDetList = new ArrayList<>();

        Map<List<Integer>, Tb02> tb02Map = tb02List.stream()
                .distinct()
                .collect(Collectors.toMap(
                        t -> Arrays.asList(t.getBookingId(), t.getSizeContainerId(), t.getTypeContainerId()),
                        t -> t,
                        (existing, newValue) -> existing
                ));

        for (Tb01 bkt : tb01List) {
            Tb02 bkd = tb02Map.get(Arrays.asList(bkt.getBookingId(), bkt.getSizeContainerId(), bkt.getTypeContainerId()));

            int qAvailability = (bkd == null) ? bkt.getTotalQuantity() :
                    Math.max(0, bkt.getTotalQuantity() - bkd.getEirsCompletedQuantity());


            boolean isReefer = false;
            Optional<Catalog> catalogContaonerSize = catalogRepository.findById(bkt.getSizeContainerId());
            String sizeDesc = "";
            if (catalogContaonerSize.isPresent()) {
                sizeDesc = catalogContaonerSize.get().getDescription();
            }
            Optional<Catalog> catalogContainerType = catalogRepository.findById(bkt.getTypeContainerId());
            String typeDesc = "";
            if (catalogContainerType.isPresent()) {
                typeDesc = FLAG_TO_FLEX.equals(bkt.getRemarkRule()) ? "DC/HC" : catalogContainerType.get().getDescription();
                isReefer = "1".equals(catalogContainerType.get().getCode());
            }


            docDetList.add(new TbDocDet(
                    bkt.getEirId(),
                    bkt.getSizeContainerId(),
                    bkt.getTypeContainerId(),
                    qAvailability,
                    isReefer,
                    sizeDesc,
                    typeDesc
            ));
        }
        return docDetList;
    }

    public List<TbDocDet> generateDocDetFromTbBase(List<TbBase> tbBaseList) {
        List<TbDocDet> docDetList = new ArrayList<>();

        List<Integer> documentoCargaIds = tbBaseList.stream()
                .map(TbBase::getDocumentoCargaGofId)
                .distinct()
                .toList();

        List<CargoDocumentDetail> documentoCargaDetalles = cargoDocumentDetailRepository.findByDocumentoCargaIdIn(documentoCargaIds);

        for (TbBase tbBase : tbBaseList) {
            for (CargoDocumentDetail detalle : documentoCargaDetalles) {
                if (!detalle.getCargoDocument().getId().equals(tbBase.getDocumentoCargaGofId())) {
                    continue;
                }

                Optional<Container> contenedor = containerRepository.findById(detalle.getContainer().getId());
                if (contenedor.isEmpty()) continue;

                if ((detalle.getReceivedQuantity() == null || detalle.getReceivedQuantity().compareTo(BigDecimal.ZERO) <= 0) &&
                        (detalle.getReceivedWeight() == null || detalle.getReceivedWeight().compareTo(BigDecimal.ZERO) <= 0)) {
                    continue;
                }

                if (detalle.getBalanceQuantity() == null || detalle.getBalanceQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }

                String tnocnt = contenedor.get().getCatSize().getDescription();
                String tipcnt = contenedor.get().getCatContainerType().getDescription();

                Optional<TbDocDet> existingDoc = docDetList.stream()
                        .filter(d -> Objects.equals(d.getEirId(), tbBase.getEirId())
                                && Objects.equals(d.getCatTamanoId(), contenedor.get().getCatSize().getId())
                                && Objects.equals(d.getCatTipoContenedorId(), contenedor.get().getCatContainerType().getId()))
                        .findFirst();

                if (existingDoc.isPresent()) {
                    existingDoc.get().setQuantity(existingDoc.get().getQuantity() + 1);
                } else {
                    TbDocDet newDocDet = new TbDocDet(
                            tbBase.getEirId(),
                            contenedor.get().getCatSize().getId(),
                            contenedor.get().getCatContainerType().getId(),
                            1,
                            null,
                            tnocnt,
                            tipcnt
                    );
                    docDetList.add(newDocDet);
                }
            }
        }

        return docDetList;
    }
}
