package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class EstimatedEmrPhotoHeaderDTO {
    private Integer estimatedEmrId;
    private Integer estimatedEmrEirPhotoId;
    private Integer attachmentId;
    private String identifier;
    private String url;
}