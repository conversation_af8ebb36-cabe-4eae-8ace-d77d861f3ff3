package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


public class ListEstimateEmrByIdInput {

    private ListEstimateEmrByIdInput() {}

    @Data
    public static class Input {
        @JsonProperty("estimado_emr_id")
        @NotNull
        private Integer estimateEmrId;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer languageId;

        private Input() {}
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;

        private Prefix() {}
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;

        private Root() {}
    }
}
