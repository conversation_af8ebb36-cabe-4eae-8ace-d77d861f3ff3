package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "azure_storage_config", schema = "ges")
public class AzureStorageConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "azure_storage_config_id", nullable = false)
    private Integer id;

    @Size(max = 100)
    @Column(name = "id", length = 100)
    private String id1;

    @Size(max = 10)
    @Column(name = "azure_container", length = 10)
    private String azureContainer;

    @Size(max = 200)
    @Column(name = "azure_path", length = 200)
    private String azurePath;

    @Size(max = 200)
    @Column(name = "azure_storage_name", length = 200)
    private String azureStorageName;

    @Size(max = 2000)
    @Column(name = "azure_storage_key", length = 2000)
    private String azureStorageKey;

    @Size(max = 100)
    @Column(name = "referencia_01", length = 100)
    private String reference01;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "estado", nullable = false)
    private Boolean status = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

}