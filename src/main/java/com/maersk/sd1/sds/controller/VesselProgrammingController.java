package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ProgrammingVesselRegisterInput;
import com.maersk.sd1.sds.dto.ProgrammingVesselRegisterOutput;
import com.maersk.sd1.sds.service.VesselProgrammingService;
import jakarta.validation.Valid;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProgramacionNaveServiceImp")
@Log4j2
public class VesselProgrammingController {

    private final VesselProgrammingService vesselProgrammingService;

    @Autowired
    public VesselProgrammingController(VesselProgrammingService vesselProgrammingService) {
        this.vesselProgrammingService = vesselProgrammingService;
    }

    @PostMapping("/sdsregistrarProgramacionNaveOperacion")
    public ResponseEntity<ResponseController<ProgrammingVesselRegisterOutput>> register(@RequestBody @Valid ProgrammingVesselRegisterInput.Root request) {
        try {
            ProgrammingVesselRegisterInput.Input input = request.getPrefix().getInput();

            ProgrammingVesselRegisterOutput output = vesselProgrammingService.registerVesselProgramming(input);

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            log.error("An error occurred while processing the vessel programming request.", e);
            ProgrammingVesselRegisterOutput output = new ProgrammingVesselRegisterOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            output.setRespNewId(null);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

