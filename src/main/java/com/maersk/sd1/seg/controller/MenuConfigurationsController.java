package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.MenuConfigurationsOutput;
import com.maersk.sd1.seg.service.MenuConfigurationsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@RestController
@RequestMapping("/ModuleSEG/module/seg/ADMMenuService")
public class MenuConfigurationsController {

    private static final Logger logger = LogManager.getLogger(MenuConfigurationsController.class.getName());

    private final MenuConfigurationsService service;

    @Autowired
    public MenuConfigurationsController(MenuConfigurationsService service) {
        this.service = service;
    }

    @PostMapping("/segmenuConfiguraciones")
    public ResponseEntity<ResponseController<MenuConfigurationsOutput>> menuConfigurationsService() {
        try {
            return service.menuConfigurationsService();
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}
