package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ShippingLineRemoveInput {

    @Data
    public static class Input {
        @JsonProperty("linea_naviera_id")
        @NotNull(message = "linea_naviera_id cannot be null")
        private Integer shippingLineId;

        @JsonProperty("usuario_modificacion_id")
        @NotNull(message = "usuario_modificacion_id cannot be null")
        private Integer userModificationId;

        @JsonProperty("idioma_id")
        @NotNull(message = "idioma_id cannot be null")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }

    private ShippingLineRemoveInput() {
        // This constructor is intentionally empty. Nothing special is needed here.
    }
}