package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.controller.dto.ProductObtainOutput;
import com.maersk.sd1.common.repository.ProductRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ProductObtainService {

    private static final Logger logger = LogManager.getLogger(ProductObtainService.class);

    private final ProductRepository productRepository;

    public ProductObtainService(ProductRepository productRepository) {
        this.productRepository = productRepository;
    }

    @Transactional(readOnly = true)
    public ProductObtainOutput obtainProduct(Integer productId) {
        try {
            ProductObtainOutput productData = productRepository.findProductDataById(productId);
            if (productData == null) {
                ProductObtainOutput output = new ProductObtainOutput();
                output.setRespStatus(0);
                output.setRespMessage("No product found with ID: " + productId);
                return output;
            } else {
                productData.setRespStatus(1);
                productData.setRespMessage("Success");
                return productData;
            }
        } catch (Exception e) {
            logger.error("Error while obtaining product data for productId: {}", productId, e);
            ProductObtainOutput output = new ProductObtainOutput( null,null,null, null, null, null, null, null, null, null, null, null, null, null, null);
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
            return output;
        }
    }
}