package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class RoleDeleteInput {

    @Data
    public static class Input {

        @JsonProperty("rol_id")
        @NotNull(message = "rol_id cannot be null")
        private Integer roleId;

        @JsonProperty("usuario_id")
        @NotNull(message = "usuario_id cannot be null")
        private Integer userId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }

    private RoleDeleteInput() {
        // Private constructor to hide the implicit public one
    }
}