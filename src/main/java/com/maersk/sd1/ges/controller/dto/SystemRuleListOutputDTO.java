package com.maersk.sd1.ges.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SystemRuleListOutputDTO {

    @JsonProperty("total_registros")
    private long totalRegistros;

    @JsonProperty("reglas")
    private List<SystemRuleData> reglas;

    @Data
    public static class SystemRuleData {
        @JsonProperty("regla_sistema_id")
        private Integer systemRuleId;

        @JsonProperty("alias")
        private String alias;

        @JsonProperty("sistema_id")
        private Integer systemId;

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("descripcion")
        private String description;

        @JsonProperty("regla")
        private String rule;

        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("fecha_registro")
        private LocalDateTime registrationDate;

        @JsonProperty("fecha_modificacion")
        private LocalDateTime modificationDate;

        @JsonProperty("usuario_registro_id")
        private Integer registrationUserId;

        @JsonProperty("usuario_registro_nombres")
        private String registrationUserNames;

        @JsonProperty("usuario_registro_apellidos")
        private String registrationUserLastNames;

        @JsonProperty("usuario_modificacion_id")
        private Integer modificationUserId;

        @JsonProperty("usuario_modificacion_nombres")
        private String modificationUserNames;

        @JsonProperty("usuario_modificacion_apellidos")
        private String modificationUserLastNames;
    }
}