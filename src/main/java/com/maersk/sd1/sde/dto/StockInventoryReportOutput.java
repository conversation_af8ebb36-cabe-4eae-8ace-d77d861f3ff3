package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class StockInventoryReportOutput {

    @JsonProperty("total_records")
    private Long totalRecords;

    @JsonProperty("inventory_list")
    private List<StockInventoryDTO> inventoryList;

    @Data
    public static class StockInventoryDTO {

        private String container;

        private String size;

        private String containerType;

        private String containerTypeName;

        private String isoCode;

        private String statusBox;

        private String statusMachinery;

        private String currentZone;

        private String containerHold;

        private Integer payload;

        private Integer tare;

        private String grade;

        private String manufactureDate;

        private String reeferType;

        private String engineBrand;

        private String shippingLine;

        private Integer gateInEir;

        private String moveType;

        private String gateInDate;

        private Integer permanence;

        private String washDate;

        private String restrictions;

        private String vessel;

        private String voyage;

        private String operation;

        private String shippingLineEir;

        private String endPtiDate;

        private String endInspDate;

        private String local;

        private String cargoDocument;

        private String inspectorObservations;

        private String endOperationDate;

        private Integer daysEta;

        private String consignee;

        private String shipper;
    }
}