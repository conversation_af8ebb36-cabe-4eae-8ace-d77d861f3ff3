package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.SystemRule;
import com.maersk.sd1.common.repository.SystemRuleRepository;
import com.maersk.sd1.ges.controller.dto.SystemRuleListInputDTO;
import com.maersk.sd1.ges.controller.dto.SystemRuleListOutputDTO;
import com.maersk.sd1.ges.controller.dto.SystemRuleListOutputDTO.SystemRuleData;
import jakarta.validation.constraints.NotNull;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class SystemRuleListService {

    private static final Logger logger = LogManager.getLogger(SystemRuleListService.class);

    private final SystemRuleRepository systemRuleRepository;

    public SystemRuleListService(SystemRuleRepository systemRuleRepository) {
        this.systemRuleRepository = systemRuleRepository;
    }

    @Transactional(readOnly = true)
    public SystemRuleListOutputDTO listSystemRules(@NotNull SystemRuleListInputDTO.Input input) {
        SystemRuleListOutputDTO outputDTO = new SystemRuleListOutputDTO();
        try {
            int pageIndex = (input.getPage() == null || input.getPage() < 0) ? 0 : input.getPage();
            int pageSize = (input.getSize() == null || input.getSize() < 1) ? 10 : input.getSize();
            PageRequest pageRequest = PageRequest.of(pageIndex, pageSize, Sort.by(Sort.Direction.DESC, "id"));

            Page<SystemRule> pageResult = systemRuleRepository.findByFilters(
                    input.getSystemRuleId() != null ? input.getSystemRuleId().toString() : null,
                    input.getAlias(),
                    input.getSystemId() != null ? input.getSystemId().toString() : null,
                    input.getBusinessUnitId() != null ? input.getBusinessUnitId().toString() : null,
                    input.getSubBusinessUnitId() != null ? input.getSubBusinessUnitId().toString() : null,
                    input.getDescription(),
                    input.getRule(),
                    input.getActive(),
                    input.getRegistrationDateMin() != null ? input.getRegistrationDateMin().atStartOfDay() : null,
                    input.getRegistrationDateMax() != null ? input.getRegistrationDateMax().plusDays(1).atStartOfDay() : null,
                    input.getModificationDateMin() != null ? input.getModificationDateMin().atStartOfDay() : null,
                    input.getModificationDateMax() != null ? input.getModificationDateMax().plusDays(1).atStartOfDay() : null,
                    pageRequest
            );

            outputDTO.setTotalRegistros(pageResult.getTotalElements());

            List<SystemRuleData> dataList = new ArrayList<>();

            for (SystemRule sr : pageResult.getContent()) {

                SystemRuleData data = new SystemRuleData();

                data.setSystemRuleId(sr.getId());
                data.setAlias(sr.getAlias());
                data.setSystemId(sr.getSystem() == null ? null : sr.getSystem().getId());
                data.setBusinessUnitId(sr.getBusinessUnit() == null ? null : sr.getBusinessUnit().getId());
                data.setSubBusinessUnitId(sr.getSubBusinessUnit() == null ? null : sr.getSubBusinessUnit().getId());
                data.setDescription(sr.getDescription());
                data.setRule(sr.getRule());
                data.setActive(sr.getActive());
                data.setRegistrationDate(sr.getRegistrationDate());
                data.setModificationDate(sr.getModificationDate());

                if (sr.getRegistrationUser() != null) {
                    data.setRegistrationUserId(sr.getRegistrationUser().getId());
                    data.setRegistrationUserNames(sr.getRegistrationUser().getNames());
                    String userLast = sr.getRegistrationUser().getFirstLastName() == null ? "" : sr.getRegistrationUser().getFirstLastName();
                    String userLast2 = sr.getRegistrationUser().getSecondLastName() == null ? "" : sr.getRegistrationUser().getSecondLastName();
                    data.setRegistrationUserLastNames((userLast + " " + userLast2).trim());
                }

                if (sr.getModificationUser() != null) {
                    data.setModificationUserId(sr.getModificationUser().getId());
                    data.setModificationUserNames(sr.getModificationUser().getNames());
                    String userLast = sr.getModificationUser().getFirstLastName() == null ? "" : sr.getModificationUser().getFirstLastName();
                    String userLast2 = sr.getModificationUser().getSecondLastName() == null ? "" : sr.getModificationUser().getSecondLastName();
                    data.setModificationUserLastNames((userLast + " " + userLast2).trim());
                }

                dataList.add(data);
            }
            outputDTO.setReglas(dataList);
        } catch (Exception e) {
            logger.error("Error listing system rules", e);
            outputDTO.setTotalRegistros(0);
            outputDTO.setReglas(new ArrayList<>());
        }

        return outputDTO;
    }
}