package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.dto.VehicleDeleteOutput;
import com.maersk.sd1.common.repository.TruckRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
public class VehicleDeleteService {

    private static final Logger logger = LogManager.getLogger(VehicleDeleteService.class);

    private final TruckRepository truckRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    public VehicleDeleteService(TruckRepository truckRepository, MessageLanguageRepository messageLanguageRepository) {
        this.truckRepository = truckRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public VehicleDeleteOutput deleteVehicle(Integer vehicleId, Integer userModificationId, Integer languageId) {
        VehicleDeleteOutput output = new VehicleDeleteOutput();
        try {
            truckRepository.deactivateTruck(vehicleId, userModificationId, LocalDateTime.now());

            String message = messageLanguageRepository.fnTranslatedMessage("GENERAL", 7, languageId);

            output.setResponseStatus(1);
            output.setResponseMessage(message);
        } catch (Exception e) {
            logger.error("Error in deleteVehicle:", e);
            output.setResponseStatus(0);
            output.setResponseMessage(e.getMessage());
        }
        return output;
    }
}