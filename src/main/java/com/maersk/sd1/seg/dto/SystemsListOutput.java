package com.maersk.sd1.seg.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Output DTO that will encapsulate:
 *  - totalRecords (the total of matching rows)
 *  - a list of system details with user data.
 */
@Data
public class SystemsListOutput {

    @JsonProperty("total_records")
    private Long totalRecords;

    @JsonProperty("systems")
    private List<SystemsListDetail> systems;

    @Data
    public static class SystemsListDetail {

        @JsonProperty("system_id")
        private Integer systemId;

        @JsonProperty("name")
        private String name;

        @JsonProperty("description")
        private String description;

        @JsonProperty("status")
        private Character status;

        @JsonProperty("registration_date")
        private LocalDateTime registrationDate;

        @JsonProperty("modification_date")
        private LocalDateTime modificationDate;

        @JsonProperty("registration_user_id")
        private Integer registrationUserId;

        @JsonProperty("modification_user_id")
        private Integer modificationUserId;

        @JsonProperty("registration_user_names")
        private String registrationUserNames;

        @JsonProperty("registration_user_last_names")
        private String registrationUserLastNames;

        @JsonProperty("modification_user_names")
        private String modificationUserNames;

        @JsonProperty("modification_user_last_names")
        private String modificationUserLastNames;

        @JsonProperty("configuration")
        private String configuration;
    }
}

