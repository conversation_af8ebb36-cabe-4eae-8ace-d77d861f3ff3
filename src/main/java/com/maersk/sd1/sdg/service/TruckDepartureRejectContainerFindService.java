package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.repository.StockEmptyRepository;
import com.maersk.sd1.sdg.controller.dto.TruckDepartureRejectContainerFindInput;
import com.maersk.sd1.sdg.controller.dto.TruckDepartureRejectContainerFindOutput;
import com.maersk.sd1.sdg.dto.EirDetailsDTO;
import com.maersk.sd1.sdg.dto.FoundContainerDetailsDTO;
import com.maersk.sd1.sdg.dto.StockEmptyDetailsDTO;
import com.maersk.sd1.sdg.repository.SdgEirRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class TruckDepartureRejectContainerFindService {


    private final SdgEirRepository sdgEirRepository;

    private final StockEmptyRepository stockEmptyRepository;

    @Transactional
    public List<TruckDepartureRejectContainerFindOutput> findTruckDepartureRejectContainerDetail(TruckDepartureRejectContainerFindInput.Root inputRoot) {
        int eirId = inputRoot.getInput().getEirId();
        List<EirDetailsDTO> eirDetailsDTOs = sdgEirRepository.findEirDetailsByEirId(eirId);
        if (CollectionUtils.isEmpty(eirDetailsDTOs)) {
            return Collections.emptyList();
        }
        EirDetailsDTO eirDetailsDTO = eirDetailsDTOs.getFirst();

        List<FoundContainerDetailsDTO> foundContainers = sdgEirRepository.findMostAvailableEmptyContainer(
                eirDetailsDTO.getBookingId(),
                eirDetailsDTO.getCatContainerSizeId(),
                eirDetailsDTO.getCatContainerTypeId(),
                eirDetailsDTO.getLocalSubBusinessUnitId(),
                eirId
        );
        FoundContainerDetailsDTO topFoundContainers = getTopFoundContainers(foundContainers);

        StockEmptyDetailsDTO stockEmptyDetails = getEirGateinDetails(eirId);
        List<TruckDepartureRejectContainerFindOutput> output = new ArrayList<>();
        TruckDepartureRejectContainerFindOutput containerOutput = TruckDepartureRejectContainerFindOutput.builder()
                .localSubBusinessUnitId(eirDetailsDTO.getLocalSubBusinessUnitId())
                .returningContainerNumber(eirDetailsDTO.getContainerNumber())
                .replacementContainerNumber(topFoundContainers!=null ? topFoundContainers.getReplaceContainerNumber() : null)
                .movementInstructionId(null)
                .gateinEirId(stockEmptyDetails !=null ? stockEmptyDetails.getEirGateInId() : null)
                .gateoutEirId(eirId)
                .gateinCatProcedenceCode(stockEmptyDetails !=null ? stockEmptyDetails.getEirGateInTypeOriginId() : null)
                .gateoutCatProcedenceCode(eirDetailsDTO.getEirGateoutTypeOriginId())
                .catEmptyFullDesc("E")
                .build();
            output.add(containerOutput);
        return output;
    }

    private StockEmptyDetailsDTO getEirGateinDetails(Integer eirId) {
        List<StockEmptyDetailsDTO> stockEmptyDetails = stockEmptyRepository.findEirGateinDetails(eirId);
        return (CollectionUtils.isEmpty(stockEmptyDetails)) ? null : stockEmptyDetails.getFirst();
    }

    private FoundContainerDetailsDTO getTopFoundContainers(List<FoundContainerDetailsDTO> foundContainers) {
        if (CollectionUtils.isEmpty(foundContainers)) {
            return null;
        }
        return foundContainers.getFirst();
    }
}

