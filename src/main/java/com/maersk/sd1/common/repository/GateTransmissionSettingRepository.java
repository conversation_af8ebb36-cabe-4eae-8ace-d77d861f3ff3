package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.GateTransmissionSetting;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.sde.dto.EDIConfigurationListOutput;
import com.maersk.sd1.sde.dto.GateTransmissionSettingDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface GateTransmissionSettingRepository extends JpaRepository<GateTransmissionSetting, Integer> {
    @Query("SELECT s FROM GateTransmissionSetting s " +
            "WHERE s.businessUnit.id = :businessUnitId " +
            "  AND s.subBusinessUnit.id = :subBusinessUnitId " +
            "  AND s.shippingLine.id = :shippingLineId " +
            "  AND s.systemDelivery = :systemDelivery")
    Optional<GateTransmissionSetting> findByUniqueKeys(@Param("businessUnitId") Long businessUnitId,
                                                       @Param("subBusinessUnitId") Long subBusinessUnitId,
                                                       @Param("shippingLineId") Integer shippingLineId,
                                                       @Param("systemDelivery") String systemDelivery);

    @Modifying
    @Query("UPDATE GateTransmissionSetting g SET g.active = false, g.modificationUser = :modificationUser, g.modificationDate = :modificationDate "
            + "WHERE g.id = :id")
    int deactivateGateTransmissionSetting(
            @Param("id") Integer id,
            @Param("modificationUser") User modificationUser,
            @Param("modificationDate") LocalDateTime modificationDate);

    @Query("SELECT g FROM GateTransmissionSetting g " +
            "WHERE g.businessUnit.id = :businessUnitId " +
            "AND g.subBusinessUnit.id = :subBusinessUnitId " +
            "AND g.shippingLine.id = :shippingLineId " +
            "AND g.systemDelivery = :systemDelivery " +
            "AND g.id <> :currentId")
    Optional<GateTransmissionSetting> findSameKeyDifferentId(
            @Param("businessUnitId") Integer businessUnitId,
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("shippingLineId") Integer shippingLineId,
            @Param("systemDelivery") String systemDelivery,
            @Param("currentId") Integer currentId
    );
  
    @Query("""
    SELECT COUNT(s.id) 
    FROM GateTransmissionSetting s
    LEFT JOIN s.registrationUser regUser
    LEFT JOIN s.modificationUser modUser
    WHERE ( :unidadNegocioId IS NULL OR s.businessUnit.id = :unidadNegocioId )
      AND ( :subUnidadNegocioId IS NULL OR s.subBusinessUnit.id = :subUnidadNegocioId )
      AND ( :lineaNavieraId IS NULL OR s.shippingLine.id = :lineaNavieraId )
      AND ( :sistemaEntrega IS NULL OR s.systemDelivery LIKE CONCAT('%', :sistemaEntrega, '%') )
      AND ( :activo IS NULL OR s.active = :activo )
      AND ( (:fechaRegistroMin IS NULL AND :fechaRegistroMax IS NULL )
            OR ( s.registrationDate >= :fechaRegistroMin AND s.registrationDate < :fechaRegistroMaxPlusOne ) )
""")
    Long countAllWithFilters(
            @Param("unidadNegocioId") BigDecimal unidadNegocioId,
            @Param("subUnidadNegocioId") BigDecimal subUnidadNegocioId,
            @Param("lineaNavieraId") Integer lineaNavieraId,
            @Param("sistemaEntrega") String sistemaEntrega,
            @Param("activo") Boolean activo,
            @Param("fechaRegistroMin") LocalDateTime fechaRegistroMin,
            @Param("fechaRegistroMax") LocalDateTime fechaRegistroMax,
            @Param("fechaRegistroMaxPlusOne") LocalDateTime fechaRegistroMaxPlusOne
    );

    @Query("""
    SELECT new com.maersk.sd1.sde.dto.EDIConfigurationListOutput$SeteoEdiCodecoItemDto(
        s.id,
        s.businessUnit.id,
        s.subBusinessUnit.id,
        s.shippingLine.id,
        s.systemDelivery,
        s.infoSystemDelivery,
        s.receptorIdentifier,
        s.sendGateInEmpty,
        s.sendGateOutEmpty,
        s.sendGateInFull,
        s.sendGateOutFull,
        s.sendStatusActivity,
        s.catGateOutEmptyFormat.id,
        s.catGateInFullFormat.id,
        s.catGateOutFullFormat.id,
        s.catGateInEmptyFormat.id,
        s.catStatusActivityFormat.id,
        s.catDeliveryChannel.id,
        s.catGenerateFileMode.id,
        s.gateTransmissionDestinationMail,
        s.telexDestinationMail,
        s.parameter1,
        s.parameter2,
        s.parameter3,
        s.parameter4,
        s.isHistorical,
        s.deactivationDate,
        s.deactivationReason,
        s.active,
        s.registrationDate,
        s.modificationDate,
        s.parameter5,
        s.parameter6,
        s.sftpId,
        s.extensionFileSend,
        s.elapsedMinutes,
        s.azureIdGateTransmission,
        s.azureIdTelex,
        regUser.id,
        regUser.names,
        CONCAT(COALESCE(regUser.firstLastName, ''), ' ', COALESCE(regUser.secondLastName, '')),
        modUser.id,
        modUser.names,
        CONCAT(COALESCE(modUser.firstLastName, ''), ' ', COALESCE(modUser.secondLastName, ''))
    )
    FROM GateTransmissionSetting s
    JOIN s.registrationUser regUser
    LEFT JOIN s.modificationUser modUser
    WHERE ( :unidadNegocioId IS NULL OR s.businessUnit.id = :unidadNegocioId )
      AND ( :subUnidadNegocioId IS NULL OR s.subBusinessUnit.id = :subUnidadNegocioId )
      AND ( :lineaNavieraId IS NULL OR s.shippingLine.id = :lineaNavieraId )
      AND ( :sistemaEntrega IS NULL OR s.systemDelivery LIKE CONCAT('%', :sistemaEntrega, '%') )
      AND ( :activo IS NULL OR s.active = :activo )
      AND ( (:fechaRegistroMin IS NULL AND :fechaRegistroMax IS NULL )
            OR ( s.registrationDate >= :fechaRegistroMin AND s.registrationDate < :fechaRegistroMaxPlusOne ) )
    ORDER BY s.id DESC
""")
    List<EDIConfigurationListOutput.SeteoEdiCodecoItemDto> findAllWithFilters(
            @Param("unidadNegocioId") BigDecimal unidadNegocioId,
            @Param("subUnidadNegocioId") BigDecimal subUnidadNegocioId,
            @Param("lineaNavieraId") Integer lineaNavieraId,
            @Param("sistemaEntrega") String sistemaEntrega,
            @Param("activo") Boolean activo,
            @Param("fechaRegistroMin") LocalDateTime fechaRegistroMin,
            @Param("fechaRegistroMax") LocalDateTime fechaRegistroMax,
            @Param("fechaRegistroMaxPlusOne") LocalDateTime fechaRegistroMaxPlusOne,
            Pageable pageable
    );

    @Query("""
        SELECT new com.maersk.sd1.sde.dto.GateTransmissionSettingDTO(
            g.elapsedMinutes,
            g.systemDelivery,
            COALESCE(g.receptorIdentifier, ''),
            COALESCE(g.parameter1, ''),
            COALESCE(g.parameter2, ''),
            COALESCE(g.parameter3, ''),
            COALESCE(g.parameter4, ''),
            COALESCE(g.parameter5, ''),
            COALESCE(g.parameter6, ''),
            g.extensionFileSend,
            CASE
                WHEN g.catDeliveryChannel.id = :isSendForEmail THEN
                    CASE
                        WHEN :typeStructureName = 'TELEX' THEN g.telexDestinationMail
                        ELSE g.gateTransmissionDestinationMail
                    END
                ELSE ''
            END
        )
        FROM GateTransmissionSetting g
        WHERE g.id = :id
    """)
    Optional<GateTransmissionSettingDTO> findGateTransmissionSetting(
            @Param("id") Integer id,
            @Param("isSendForEmail") Integer isSendForEmail,
            @Param("typeStructureName") String typeStructureName
    );

}