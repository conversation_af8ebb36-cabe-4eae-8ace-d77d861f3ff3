package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.ProductSearchInputDTO;
import com.maersk.sd1.sds.controller.dto.ProductSearchOutputDTO;
import com.maersk.sd1.sds.service.ProductSearchService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/")
public class ProductSearchController {

    private static final Logger logger = LogManager.getLogger(ProductSearchController.class);

    private final ProductSearchService productSearchService;

    @PostMapping("SDSProductoServiceImp/sdsproductSearch/")
    public ResponseEntity<ResponseController<List<ProductSearchOutputDTO>>> searchProducts(@RequestBody ProductSearchInputDTO.Root request) {
        try {
            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                return ResponseEntity.status(400).body(new ResponseController<>(Constants.INVALID_INPUT));
            }
            ProductSearchInputDTO.Input input = request.getPrefix().getInput();
            if (input.getProductId() == null ){
                return ResponseEntity.status(400).body(new ResponseController<>(Constants.INVALID_INPUT));
            }
            List<ProductSearchOutputDTO> products = productSearchService.searchProducts(input);
            return ResponseEntity.ok(new ResponseController<>(products));
        } catch (Exception e) {
            logger.error("An error occurred while processing the product search request.", e);
            return ResponseEntity.status(500).body(null);
        }
    }
}