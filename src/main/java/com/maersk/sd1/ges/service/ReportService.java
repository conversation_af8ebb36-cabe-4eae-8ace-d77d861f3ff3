package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.Report;
import com.maersk.sd1.ges.controller.dto.ReportListInput;
import com.maersk.sd1.ges.controller.dto.ReportListOutput;
import com.maersk.sd1.ges.controller.dto.ReportListOutput.ReportData;
import com.maersk.sd1.common.repository.ReportRepository;
import com.maersk.sd1.ges.dto.ReportJsonDTO;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class ReportService {

    private static final Logger logger = LogManager.getLogger(ReportService.class);

    private final ReportRepository reportRepository;

    private Boolean parseStatus(String status) {
        if ("1".equals(status) || "true".equalsIgnoreCase(status)) {
            return Boolean.TRUE;
        } else if ("0".equals(status ) || "false".equalsIgnoreCase(status)) {
            return Boolean.FALSE;
        }
        throw new IllegalArgumentException("Invalid status value: " + status);
    }

    @Transactional(readOnly = true)
    public ReportListOutput listReports(ReportListInput.Input input) {

        ReportListOutput output = new ReportListOutput();
        try {
            Boolean status;
            if(input.getStatus() == null){
                status = null;
            }
            else{
                status = parseStatus(input.getStatus());
            }

            int pageIndex = input.getPage() - 1; // Spring Data is 0-based
            int pageSize = input.getSize();

            PageRequest pageRequest = PageRequest.of(pageIndex, pageSize);

            Page<Report> pageResult = reportRepository.searchReports(
                    status,
                    input.getNames(),
                    input.getNamesStore(),
                    pageRequest
            );

            List<ReportData> dataList = new ArrayList<>();

            List<ReportJsonDTO> reportRoles =  reportRepository.findData(input.getStatus(), input.getNames(), input.getNamesStore());

            for (Report r : pageResult.getContent()) {
                ReportData rd = new ReportData();
                rd.setReportId(r.getId());
                rd.setId1(r.getId1());
                rd.setProject(r.getMenu() != null ? r.getMenu().getTitle() : null);
                rd.setTemplate(r.getMenu() != null ? r.getMenu().getTemplate() : null);
                rd.setName(r.getName());
                rd.setDescription(r.getDescription());
                rd.setNameStore(r.getNameStore());
                rd.setStatus(r.getStatus());
                rd.setParameters(r.getParameters());

                dataList.add(rd);
            }

            dataList.forEach(reportData ->
                    reportRoles.stream()
                            .filter(reportJsonDTO -> reportJsonDTO.getReportId().equals(reportData.getReportId()))
                            .findFirst()
                            .ifPresent(reportJsonDTO -> reportData.setRoles(reportJsonDTO.getRoles()))
            );

            output.setReports(dataList);
            output.setTotalRecords(pageResult.getTotalElements());
            output.setRespStatus(1);
            output.setRespMessage("Success");

        } catch (Exception e) {
            logger.error("Error listing reports.", e);
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
            output.setReports(new ArrayList<>());
            output.setTotalRecords(0L);
        }
        return output;
    }
}