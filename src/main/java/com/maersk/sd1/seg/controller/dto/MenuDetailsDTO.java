package com.maersk.sd1.seg.controller.dto;

import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.MenuAction;
import com.maersk.sd1.common.model.MenuConfig;
import com.maersk.sd1.common.model.RoleMenu;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MenuDetailsDTO {

    private MenuDTO menu;
    private List<MenuConfigDTO> menuConfigList;
    private List<RoleMenuDTO> roleMenuList;
    private List<MenuActionDTO> menuActionList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MenuDTO {
        private Integer menuBaseId;
        private Integer menuParentId;
        private String title;
        private String description;
        private String template;
        private String icon;
        private Integer order;
        private String hasId;
        private String status;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MenuConfigDTO {
        private Integer catConfigurationType;
        private String value;
        private String status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RoleMenuDTO {
        private Integer rolId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MenuActionDTO {
        private Integer catalogId;
        private String defaultActive;
    }

}
