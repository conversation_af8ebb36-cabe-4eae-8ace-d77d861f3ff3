package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.sde.dto.DamageTypeSearchOutputDTO;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class DamageTypeSearchService {

    private static final Logger logger = LogManager.getLogger(DamageTypeSearchService.class);
    private final CatalogRepository catalogRepository;

    public List<DamageTypeSearchOutputDTO> searchDamageTypes(String damageType) {
        List<DamageTypeSearchOutputDTO> resultList = new ArrayList<>();
        try {

            List<Catalog> catalogs = catalogRepository.findDamageTypes(damageType, PageRequest.of(0, 10));


            for (Catalog c : catalogs) {
                DamageTypeSearchOutputDTO dto = new DamageTypeSearchOutputDTO();
                dto.setCatDamageTypeId(c.getId());
                String combinedDesc = c.getDescription();
                if (c.getLongDescription() != null && !c.getLongDescription().isEmpty()) {
                    combinedDesc += " - " + c.getLongDescription();
                }
                dto.setDamageType(combinedDesc);
                dto.setDamageTypeCode(c.getDescription());
                resultList.add(dto);
            }
        } catch (Exception ex) {
            logger.error("Error while searching damage types", ex);
            throw ex;
        }
        return resultList;
    }
}

