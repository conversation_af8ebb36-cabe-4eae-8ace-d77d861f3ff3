package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "api_log", schema = "sds")
public class ApiLog {
    @Id
    @Column(name = "api_log_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_tipo_api", nullable = false)
    private Catalog catApiType;

    @Lob
    @Column(name = "parametro_valores")
    private String parameterValues;

    @Size(max = 11)
    @Column(name = "parametro_contenedor", length = 11)
    private String parameterContainer;

    @Size(max = 50)
    @Column(name = "parametro_unidad_negocio", length = 50)
    private String parameterBusinessUnit;

    @Column(name = "id_generado")
    private Integer idGenerated;

    @NotNull
    @Column(name = "codigo_resultado", nullable = false)
    private Integer resultCode;

    @Size(max = 1000)
    @Column(name = "mensaje_resultado", length = 1000)
    private String resultMessage;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

}