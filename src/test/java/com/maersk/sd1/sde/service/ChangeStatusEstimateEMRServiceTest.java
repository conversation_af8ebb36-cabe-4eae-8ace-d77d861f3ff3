package com.maersk.sd1.sde.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.ChangeStatusEstimateEMRInput;
import com.maersk.sd1.sde.dto.ChangeStatusEstimateEMROutput;
import com.maersk.sd1.sde.dto.TbChangeStatusDTO;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ChangeStatusEstimateEMRServiceTest {

    @InjectMocks
    private ChangeStatusEstimateEMRService service;

    @Mock private EirActivityZoneRepository eirActivityZoneRepository;
    @Mock private MessageLanguageRepository messageLanguageRepository;
    @Mock private CatalogRepository catalogRepository;
    @Mock private EstimateEmrRepository estimateEmrRepository;
    @Mock private EirZoneRepository eirZoneRepository;
    @Mock private EstimateEmrDetailRepository estimateEmrDetailRepository;
    @Mock private ParameterSpLogRepository parameterSpLogRepository;
    @Mock private EirRepository eirRepository;
    @Mock private JdbcTemplate jdbcTemplate;
    @Mock private EntityManager entityManager;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void changeStatusEstimateEMR_shouldApproveEstimate() throws Exception {
        int statusApproved = 6; // Example value for isEstimateStatusApproved
        int estimateId = 101;
        int userId = 1;

        ChangeStatusEstimateEMRInput.Input input = new ChangeStatusEstimateEMRInput.Input();
        input.setEstadoEstimadoEmrId(statusApproved);
        input.setUsuarioModificacionId(userId);
        input.setIsStatusChange(1);
        input.setCambiosEstadosJson("[{\"estimado_emr_id\":" + estimateId + "}]");
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(
                0,
                0,
                0,
                0,
                0,
                statusApproved, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
        EstimateEmr estimateEmr = mock(EstimateEmr.class);
        when(estimateEmr.getId()).thenReturn(estimateId);
        when(estimateEmr.getActive()).thenReturn(true);
        when(estimateEmr.getShippingLine()).thenReturn(new ShippingLine());
        when(estimateEmr.getEir()).thenReturn(new Eir(1));
        when(estimateEmr.getFlagAutoApproval()).thenReturn(false);
        when(estimateEmr.getCatEstimateStatus()).thenReturn(new Catalog(1));
        when(estimateEmr.getCatEstimateType()).thenReturn(new Catalog(1));
        when(estimateEmr.getApproveRejectEstimateDate()).thenReturn(null);

        when(estimateEmrRepository.findAllById(anyList())).thenReturn(List.of(estimateEmr));
        when(estimateEmrRepository.findById(anyInt())).thenReturn(Optional.of(estimateEmr));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("OK");

        ChangeStatusEstimateEMROutput output = service.changeStatusEstimateEMR(input);

        verify(estimateEmrRepository).saveAllAndFlush(anyList());
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
    }

    @Test
    public void changeStatusEstimateEMR_shouldRejectEstimate() throws Exception {
        int statusRejected = 7; // Example value for isEstimateStatusRejected
        int estimateId = 102;
        int userId = 1;

        ChangeStatusEstimateEMRInput.Input input = new ChangeStatusEstimateEMRInput.Input();
        input.setEstadoEstimadoEmrId(statusRejected);
        input.setUsuarioModificacionId(userId);
        input.setIsStatusChange(1);
        input.setCambiosEstadosJson("[{\"estimado_emr_id\":" + estimateId + "}]");
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(
                0,
                0,
                0,
                0,
                0,
                0,
                statusRejected, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
        EstimateEmr estimateEmr = mock(EstimateEmr.class);
        when(estimateEmr.getId()).thenReturn(estimateId);
        when(estimateEmr.getActive()).thenReturn(true);
        when(estimateEmr.getShippingLine()).thenReturn(new ShippingLine());
        when(estimateEmr.getEir()).thenReturn(new Eir(1));
        when(estimateEmr.getFlagAutoApproval()).thenReturn(false);
        when(estimateEmr.getCatEstimateStatus()).thenReturn(new Catalog(1));
        when(estimateEmr.getCatEstimateType()).thenReturn(new Catalog(1));
        when(estimateEmr.getApproveRejectEstimateDate()).thenReturn(null);

        when(estimateEmrRepository.findAllById(anyList())).thenReturn(List.of(estimateEmr));
        when(estimateEmrRepository.findById(anyInt())).thenReturn(Optional.of(estimateEmr));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("OK");

        ChangeStatusEstimateEMROutput output = service.changeStatusEstimateEMR(input);

        verify(estimateEmrRepository).saveAllAndFlush(anyList());
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
    }

    @Test
    public void changeStatusEstimateEMR_shouldCancelEstimate() throws Exception {
        int statusCanceled = 8; // Example value for isEstimateStatusCanceled
        int estimateId = 103;
        int userId = 1;
        int cancelReason = 99;
        String cancelDescription = "Test cancel reason";

        // The input JSON must include the cancel reason and description fields
        String cambiosEstadosJson = String.format(
                "[{\"estimado_emr_id\":%d,\"estimate_cancel_reason\":%d,\"estimate_cancel_description\":\"%s\"}]",
                estimateId, cancelReason, cancelDescription
        );

        ChangeStatusEstimateEMRInput.Input input = new ChangeStatusEstimateEMRInput.Input();
        input.setEstadoEstimadoEmrId(statusCanceled);
        input.setUsuarioModificacionId(userId);
        input.setIsStatusChange(1);
        input.setCambiosEstadosJson(cambiosEstadosJson);
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(
                0,
                0,
                0,
                statusCanceled, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

        EstimateEmr estimateEmr = mock(EstimateEmr.class);
        when(estimateEmr.getId()).thenReturn(estimateId);
        when(estimateEmr.getActive()).thenReturn(true);
        when(estimateEmr.getShippingLine()).thenReturn(new ShippingLine());
        when(estimateEmr.getEir()).thenReturn(new Eir(1));
        when(estimateEmr.getFlagAutoApproval()).thenReturn(false);
        when(estimateEmr.getCatEstimateStatus()).thenReturn(new Catalog(1));
        when(estimateEmr.getCatEstimateType()).thenReturn(new Catalog(1));
        when(estimateEmr.getApproveRejectEstimateDate()).thenReturn(null);

        when(estimateEmrRepository.findAllById(anyList())).thenReturn(List.of(estimateEmr));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("OK");

        ChangeStatusEstimateEMROutput output = service.changeStatusEstimateEMR(input);

        verify(estimateEmrRepository).save(estimateEmr);
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
    }

    // 1. Auto-approval, status is Repair Complete, and submission date is null
    @Test
    public void changeStatusEstimateEMR_shouldSubmitAutoApprovedRepairComplete() throws Exception {
        int statusSubmitted = 5;
        int statusRepairComplete = 3;
        int statusPending = 9;
        int estimateId = 201;
        int userId = 1;

        ChangeStatusEstimateEMRInput.Input input = new ChangeStatusEstimateEMRInput.Input();
        input.setEstadoEstimadoEmrId(statusSubmitted);
        input.setUsuarioModificacionId(userId);
        input.setIsStatusChange(1);
        input.setCambiosEstadosJson("[{\"estimado_emr_id\":" + estimateId + ",\"cat_estimate_status\":" + statusRepairComplete + ",\"flag_auto_approval\":true}]");
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString()))
                .thenReturn(1, 2, statusSubmitted, 4, statusRepairComplete, 6, 7, statusPending);

        EstimateEmr estimateEmr = mock(EstimateEmr.class);
        when(estimateEmr.getId()).thenReturn(estimateId);
        when(estimateEmr.getActive()).thenReturn(true);
        when(estimateEmr.getCatEstimateStatus()).thenReturn(new Catalog(statusRepairComplete));
        when(estimateEmr.getFlagAutoApproval()).thenReturn(true);
        when(estimateEmr.getEstimateSubmissionDate()).thenReturn(null);
        when(estimateEmr.getCatApprovalSendEstimateStatus()).thenReturn(null);
        when(estimateEmr.getCatEstimateType()).thenReturn(new Catalog(1));
        when(estimateEmr.getShippingLine()).thenReturn(new ShippingLine(9999));
        when(estimateEmr.getEir()).thenReturn(new Eir(1));

        when(estimateEmrRepository.findAllById(anyList())).thenReturn(List.of(estimateEmr));
        when(estimateEmrRepository.findById(anyInt())).thenReturn(Optional.of(estimateEmr));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("OK");

        ChangeStatusEstimateEMROutput output = service.changeStatusEstimateEMR(input);

        verify(estimateEmrRepository).save(estimateEmr);
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
    }

    // 2. Maersk Line or HSD, not allowed to submit (pending inspection)
    @Test
    public void changeStatusEstimateEMR_shouldNotSubmitIfPendingInspection() throws Exception {
        int statusSubmitted = 5;
        int estimateId = 202;
        int userId = 1;
        int hsdId = 4102;

        ChangeStatusEstimateEMRInput.Input input = new ChangeStatusEstimateEMRInput.Input();
        input.setEstadoEstimadoEmrId(statusSubmitted);
        input.setUsuarioModificacionId(userId);
        input.setIsStatusChange(1);
        input.setCambiosEstadosJson("[{\"estimado_emr_id\":" + estimateId + ",\"linea_naviera_id\":" + hsdId + ",\"eir_id\":1}]");
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1, 2, statusSubmitted, 4, 3, 6, 7, 9);

        EstimateEmr estimateEmr = mock(EstimateEmr.class);
        when(estimateEmr.getId()).thenReturn(estimateId);
        when(estimateEmr.getActive()).thenReturn(true);
        when(estimateEmr.getCatEstimateStatus()).thenReturn(new Catalog(1));
        when(estimateEmr.getFlagAutoApproval()).thenReturn(false);
        when(estimateEmr.getShippingLine()).thenReturn(new ShippingLine(hsdId));
        when(estimateEmr.getEir()).thenReturn(new Eir(1));
        when(estimateEmr.getCatEstimateType()).thenReturn(new Catalog(1));

        when(estimateEmrRepository.findAllById(anyList())).thenReturn(List.of(estimateEmr));
        when(estimateEmrRepository.findById(anyInt())).thenReturn(Optional.of(estimateEmr));
        when(estimateEmrRepository.fnIsAllowChangeToSubmittedEmrReefer(anyInt(), anyInt())).thenReturn(false);
        when(estimateEmrDetailRepository.findByEstimateIdAndCedexMercCodeNull(anyInt())).thenReturn(Collections.emptyList());
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Pending inspection");

        ChangeStatusEstimateEMROutput output = service.changeStatusEstimateEMR(input);

        verify(estimateEmrRepository, never()).save(any());
        assertNotNull(output);
        assertEquals(2, output.getRespEstado());
        assertTrue(output.getRespMensaje().contains("Pending inspection"));
    }

    //3. Maersk Line or HSD, not allowed to submit (missing STS code)
    @Test
    public void changeStatusEstimateEMR_shouldNotSubmitIfMissingSTSCode() throws Exception {
        int statusSubmitted = 5;
        int estimateId = 203;
        int userId = 1;
        int mslId = 4104;

        ChangeStatusEstimateEMRInput.Input input = new ChangeStatusEstimateEMRInput.Input();
        input.setEstadoEstimadoEmrId(statusSubmitted);
        input.setUsuarioModificacionId(userId);
        input.setIsStatusChange(1);
        input.setCambiosEstadosJson("[{\"estimado_emr_id\":" + estimateId + ",\"linea_naviera_id\":" + mslId + ",\"eir_id\":1}]");
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1, 2, statusSubmitted, 4, 3, 6, 7, 9);

        EstimateEmr estimateEmr = mock(EstimateEmr.class);
        when(estimateEmr.getId()).thenReturn(estimateId);
        when(estimateEmr.getActive()).thenReturn(true);
        when(estimateEmr.getCatEstimateStatus()).thenReturn(new Catalog(1));
        when(estimateEmr.getFlagAutoApproval()).thenReturn(false);
        when(estimateEmr.getShippingLine()).thenReturn(new ShippingLine(mslId));
        when(estimateEmr.getEir()).thenReturn(new Eir(1));
        when(estimateEmr.getCatEstimateType()).thenReturn(new Catalog(1));

        when(estimateEmrRepository.findAllById(anyList())).thenReturn(List.of(estimateEmr));
        when(estimateEmrRepository.findById(anyInt())).thenReturn(Optional.of(estimateEmr));
        when(estimateEmrRepository.fnIsAllowChangeToSubmittedEmrReefer(anyInt(), anyInt())).thenReturn(true);
        // Simulate missing STS code
        when(estimateEmrDetailRepository.findByEstimateIdAndCedexMercCodeNull(anyInt())).thenReturn(List.of(mock(EstimateEmrDetail.class)));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Missing STS code");

        ChangeStatusEstimateEMROutput output = service.changeStatusEstimateEMR(input);

        verify(estimateEmrRepository, never()).save(any());
        assertNotNull(output);
        assertEquals(2, output.getRespEstado());
        assertTrue(output.getRespMensaje().contains("Missing STS code"));
    }

    // 4. Maersk Line or HSD, allowed to submit (all validations pass)
    @Test
    public void changeStatusEstimateEMR_shouldSubmitMaerskOrHsdWhenValid() throws Exception {
        int statusSubmitted = 5;
        int estimateId = 204;
        int userId = 1;
        int mslId = 4101;

        ChangeStatusEstimateEMRInput.Input input = new ChangeStatusEstimateEMRInput.Input();
        input.setEstadoEstimadoEmrId(statusSubmitted);
        input.setUsuarioModificacionId(userId);
        input.setIsStatusChange(1);
        input.setCambiosEstadosJson("[{\"estimado_emr_id\":" + estimateId + ",\"linea_naviera_id\":" + mslId + ",\"eir_id\":1}]");
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1, 2, statusSubmitted, 4, 3, 6, 7, 9);

        EstimateEmr estimateEmr = mock(EstimateEmr.class);
        when(estimateEmr.getId()).thenReturn(estimateId);
        when(estimateEmr.getActive()).thenReturn(true);
        when(estimateEmr.getCatEstimateStatus()).thenReturn(new Catalog(1));
        when(estimateEmr.getFlagAutoApproval()).thenReturn(false);
        when(estimateEmr.getShippingLine()).thenReturn(new ShippingLine(mslId));
        when(estimateEmr.getEir()).thenReturn(new Eir(1));
        when(estimateEmr.getCatEstimateType()).thenReturn(new Catalog(1));

        when(estimateEmrRepository.findAllById(anyList())).thenReturn(List.of(estimateEmr));
        when(estimateEmrRepository.findById(anyInt())).thenReturn(Optional.of(estimateEmr));
        when(estimateEmrRepository.fnIsAllowChangeToSubmittedEmrReefer(anyInt(), anyInt())).thenReturn(true);
        when(estimateEmrDetailRepository.findByEstimateIdAndCedexMercCodeNull(anyInt())).thenReturn(Collections.emptyList());
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("OK");

        ChangeStatusEstimateEMROutput output = service.changeStatusEstimateEMR(input);

        verify(estimateEmrRepository).save(estimateEmr);
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
    }

    // 5. General case: other shipping lines
    @Test
    public void changeStatusEstimateEMR_shouldSubmitGeneralCase() throws Exception {
        int statusSubmitted = 5;
        int estimateId = 205;
        int userId = 1;
        int otherShippingLine = 9999;

        ChangeStatusEstimateEMRInput.Input input = new ChangeStatusEstimateEMRInput.Input();
        input.setEstadoEstimadoEmrId(statusSubmitted);
        input.setUsuarioModificacionId(userId);
        input.setIsStatusChange(1);
        input.setCambiosEstadosJson("[{\"estimado_emr_id\":" + estimateId + ",\"linea_naviera_id\":" + otherShippingLine + "}]");
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1, 2, statusSubmitted, 4, 3, 6, 7, 9);

        EstimateEmr estimateEmr = mock(EstimateEmr.class);
        when(estimateEmr.getId()).thenReturn(estimateId);
        when(estimateEmr.getActive()).thenReturn(true);
        when(estimateEmr.getCatEstimateStatus()).thenReturn(new Catalog(1));
        when(estimateEmr.getFlagAutoApproval()).thenReturn(false);
        when(estimateEmr.getShippingLine()).thenReturn(new ShippingLine(otherShippingLine));
        when(estimateEmr.getEir()).thenReturn(new Eir(1));
        when(estimateEmr.getCatEstimateType()).thenReturn(new Catalog(1));

        when(estimateEmrRepository.findAllById(anyList())).thenReturn(List.of(estimateEmr));
        when(estimateEmrRepository.findById(anyInt())).thenReturn(Optional.of(estimateEmr));
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("OK");

        ChangeStatusEstimateEMROutput output = service.changeStatusEstimateEMR(input);

        verify(estimateEmrRepository).save(estimateEmr);
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
    }
    //6. Maersk Line or HSD, not allowed to submit (missing STS code)
    @Test
    public void changeStatusEstimateEMR_shouldSubmitIfCanChangeFlagsAreTrue() throws Exception {
        int statusSubmitted = 5;
        int estimateId = 203;
        int userId = 1;
        int mslId = 4104;

        ChangeStatusEstimateEMRInput.Input input = new ChangeStatusEstimateEMRInput.Input();
        input.setEstadoEstimadoEmrId(statusSubmitted);
        input.setUsuarioModificacionId(userId);
        input.setIsStatusChange(1);
        input.setCambiosEstadosJson("[{\"estimado_emr_id\":" + estimateId + ",\"linea_naviera_id\":" + mslId + ",\"eir_id\":1}]");
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1, 2, statusSubmitted, 4, 3, 6, 7, 9);

        EstimateEmr estimateEmr = mock(EstimateEmr.class);
        when(estimateEmr.getId()).thenReturn(estimateId);
        when(estimateEmr.getActive()).thenReturn(true);
        when(estimateEmr.getCatEstimateStatus()).thenReturn(new Catalog(1));
        when(estimateEmr.getFlagAutoApproval()).thenReturn(false);
        when(estimateEmr.getShippingLine()).thenReturn(new ShippingLine(mslId));
        when(estimateEmr.getEir()).thenReturn(new Eir(1));
        when(estimateEmr.getCatEstimateType()).thenReturn(new Catalog(1));

        when(estimateEmrRepository.findAllById(anyList())).thenReturn(List.of(estimateEmr));
        when(estimateEmrRepository.findById(anyInt())).thenReturn(Optional.of(estimateEmr));
        when(estimateEmrRepository.fnIsAllowChangeToSubmittedEmrReefer(anyInt(), anyInt())).thenReturn(true);
        // Simulate missing STS code
        when(estimateEmrDetailRepository.findByEstimateIdAndCedexMercCodeNull(anyInt())).thenReturn(Collections.emptyList());
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Missing STS code");

        ChangeStatusEstimateEMROutput output = service.changeStatusEstimateEMR(input);

        verify(estimateEmrRepository).save(any());
        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
        assertTrue(output.getRespMensaje().contains("Missing STS code"));
    }

    @Test
    public void changeStatusEstimateEMR_shouldSetEirZoneToOkAndCreateCntRepaired3P07_whenAllRepairsAndCleaningDone() throws Exception {
        int statusRepairComplete = 3;
        int estimateId = 301;
        int userId = 1;
        int eirId = 1001;
        int estimateTypeStructure = 11;
        int statusCreated = 1;
        int statusCanceled = 8;
        int statusRejected = 9;
        int statusApproved = 4;
        int statusFinalized = 2;
        int statusSubmitted = 5;
        int statusCleaningCleaned = 21;
        int statusRepairRepaired = 31;
        int mtyZoneOk = 41;
        int creationSourceCntRepair3rdPartyRepair = 51;
        int isEstApproveSourceAuto3rd = 61;

        ChangeStatusEstimateEMRInput.Input input = new ChangeStatusEstimateEMRInput.Input();
        input.setEstadoEstimadoEmrId(statusRepairComplete);
        input.setUsuarioModificacionId(userId);
        input.setIsStatusChange(1);
        input.setCambiosEstadosJson("[{\"estimado_emr_id\":" + estimateId + ",\"eir_id\":" + eirId + "}]");
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString()))
                .thenReturn(
                        statusCreated,
                        statusFinalized,
                        statusSubmitted,
                        statusApproved,
                        statusRepairComplete,
                        statusRejected,
                        statusCanceled,
                        0,
                        estimateTypeStructure, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        statusRepairRepaired, statusCleaningCleaned, creationSourceCntRepair3rdPartyRepair, mtyZoneOk, isEstApproveSourceAuto3rd);

        EstimateEmr estimateEmr = mock(EstimateEmr.class);
        when(estimateEmr.getId()).thenReturn(estimateId);
        when(estimateEmr.getActive()).thenReturn(true);
        when(estimateEmr.getCatEstimateStatus()).thenReturn(new Catalog(statusFinalized));
        when(estimateEmr.getCatEstimateType()).thenReturn(new Catalog(estimateTypeStructure));
        when(estimateEmr.getShippingLine()).thenReturn(new ShippingLine(1234));
        when(estimateEmr.getEir()).thenReturn(new Eir(eirId));
        when(estimateEmr.getEstimateSubmissionDate()).thenReturn(LocalDateTime.now());
        when(estimateEmr.getApproveRejectEstimateDate()).thenReturn(LocalDateTime.now());
        when(estimateEmr.getCompletedEstimateDate()).thenReturn(null);

        when(estimateEmrRepository.findAllById(anyList())).thenReturn(new ArrayList<>(List.of(estimateEmr)));
        when(estimateEmrRepository.findById(anyInt())).thenReturn(Optional.of(estimateEmr));
        when(estimateEmrRepository.saveAndFlush(any(EstimateEmr.class))).thenAnswer(invocation -> invocation.getArgument(0));

        Eir eir = mock(Eir.class);
        when(eir.getId()).thenReturn(eirId);
        when(eir.getCatApprovalRepBox()).thenReturn(new Catalog(statusRepairRepaired));
        when(eir.getCatApprovalRepMachine()).thenReturn(new Catalog(statusRepairRepaired));
        when(eir.getCatCleaningStatus()).thenReturn(null);
        when(eirRepository.findAllById(anyList())).thenReturn(new ArrayList<>(List.of(eir)));
        when(eirRepository.saveAndFlush(any(Eir.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(eirRepository.findById(anyInt())).thenReturn(Optional.of(eir));

        EirActivityZone structureInspection = mock(EirActivityZone.class);
        when(structureInspection.getEir()).thenReturn(new Eir(eirId));
        when(structureInspection.getCatZoneActivity()).thenReturn(new Catalog(0));
        when(structureInspection.getConcluded()).thenReturn(true);
        when(structureInspection.getRegistrationDate()).thenReturn(LocalDateTime.now());
        when(structureInspection.getActive()).thenReturn(true);

        EirActivityZone machineryInspection = mock(EirActivityZone.class);
        when(machineryInspection.getEir()).thenReturn(new Eir(eirId));
        when(machineryInspection.getCatZoneActivity()).thenReturn(new Catalog(0));
        when(machineryInspection.getConcluded()).thenReturn(true);
        when(machineryInspection.getRegistrationDate()).thenReturn(LocalDateTime.now());
        when(machineryInspection.getActive()).thenReturn(true);

        when(eirActivityZoneRepository.findAllByEirIdAndActive(anyList(), eq(true)))
                .thenReturn(new ArrayList<>(List.of(structureInspection, machineryInspection)));
        when(eirActivityZoneRepository.saveAndFlush(any(EirActivityZone.class))).thenAnswer(invocation -> {
            EirActivityZone zone = invocation.getArgument(0);
            EirActivityZone saved = new EirActivityZone();
            saved.setId(888);
            saved.setEir(zone.getEir());
            saved.setCatZoneActivity(zone.getCatZoneActivity());
            saved.setRegistrationDate(zone.getRegistrationDate());
            saved.setActive(zone.getActive());
            return saved;
        });
        when(eirActivityZoneRepository.save(any(EirActivityZone.class))).thenAnswer(invocation -> {
            EirActivityZone zone = invocation.getArgument(0);
            EirActivityZone saved = new EirActivityZone();
            saved.setId(889);
            saved.setEir(zone.getEir());
            saved.setCatZoneActivity(zone.getCatZoneActivity());
            saved.setRegistrationDate(zone.getRegistrationDate());
            saved.setActive(zone.getActive());
            saved.setEirZone(zone.getEirZone());
            saved.setCatZoneActivityResult(zone.getCatZoneActivityResult());
            saved.setTracezoneActivity(zone.getTracezoneActivity());
            saved.setIsPartialInspection(zone.getIsPartialInspection());
            saved.setCatCreacionEIROrigin(zone.getCatCreacionEIROrigin());
            saved.setEndDate(zone.getEndDate());
            saved.setConcluded(zone.getConcluded());
            saved.setFinalUser(zone.getFinalUser());
            return saved;
        });

        EirZone eirZone = mock(EirZone.class);
        when(eirZone.getEir()).thenReturn(new Eir(eirId));
        when(eirZone.getCatContainerZone()).thenReturn(new Catalog(mtyZoneOk));
        when(eirZone.getActive()).thenReturn(true);
        when(eirZone.getRegistrationDate()).thenReturn(LocalDateTime.now());

        when(eirZoneRepository.findAllById(anyList())).thenReturn(new ArrayList<>(List.of(eirZone)));
        when(eirZoneRepository.findLastByEirIdAndActive(any(), eq(eirId), eq(true)))
                .thenReturn(new ArrayList<>(List.of(eirZone)));
        when(eirZoneRepository.saveAndFlush(any(EirZone.class))).thenAnswer(invocation -> {
            EirZone zone = invocation.getArgument(0);
            EirZone saved = new EirZone();
            saved.setId(999);
            saved.setEir(zone.getEir());
            saved.setCatContainerZone(zone.getCatContainerZone());
            saved.setRegistrationDate(zone.getRegistrationDate());
            saved.setRegistrationUser(zone.getRegistrationUser());
            saved.setActive(zone.getActive());
            saved.setTraceEirZone(zone.getTraceEirZone());
            saved.setCatOrigenCreacionEirzona(zone.getCatOrigenCreacionEirzona());
            return saved;
        });
        when(eirZoneRepository.save(any(EirZone.class))).thenAnswer(invocation -> {
            EirZone zone = invocation.getArgument(0);
            zone.setTraceEirZone("cnt_repaired_3P_07");
            return zone;
        });

        // Mock updateByEirIdAndContainerZoneIdAndActive
        doNothing().when(eirZoneRepository).updateByEirIdAndContainerZoneIdAndActive(
                eq(eirId), eq(mtyZoneOk), eq(false), eq(userId), eq("cnt_repaired_3P_06")
        );

        EstimateEmrDetail cleaningDetail = mock(EstimateEmrDetail.class);
        when(cleaningDetail.getEstimateEmr()).thenReturn(estimateEmr);
        when(cleaningDetail.getCatCleaningType()).thenReturn(new Catalog(1));
        when(cleaningDetail.getActive()).thenReturn(true);

        EstimateEmrDetail damageDetail = mock(EstimateEmrDetail.class);
        when(damageDetail.getEstimateEmr()).thenReturn(estimateEmr);
        when(damageDetail.getCatCleaningType()).thenReturn(null);
        when(damageDetail.getActive()).thenReturn(true);

        when(estimateEmrDetailRepository.findAllByEirIdAndActiveWithCleaningType(anyList(), eq(true)))
                .thenReturn(new ArrayList<>(List.of(cleaningDetail)));
        when(estimateEmrDetailRepository.findAllByEirIdAndEstimateType(anyInt(), anyList()))
                .thenReturn(new ArrayList<>(List.of(cleaningDetail)));
        when(estimateEmrDetailRepository.findAllByEstimateIdAndActiveWithNoCleaningType(anyList(), eq(true)))
                .thenReturn(new ArrayList<>(List.of(damageDetail)));
        when(estimateEmrDetailRepository.existsByEirIdAndEstimateTypeIdAndStatusIdWithNoCleaningType(anyInt(), anyInt(), anyList()))
                .thenReturn(false);

        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("OK");

        ChangeStatusEstimateEMROutput output = service.changeStatusEstimateEMR(input);

        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
    }

    @Test
    public void changeStatusEstimateEMR_shouldSetEirZoneToOkAndCreateCntRepaired3P07_whenAllRepairsAndCleaningDoneWithNewValues() throws Exception {
        int statusRepairComplete = 3;
        int estimateId = 301;
        int userId = 1;
        int eirId = 1001;
        int estimateTypeStructure = 11;
        int statusCreated = 1;
        int statusCanceled = 8;
        int statusRejected = 9;
        int statusApproved = 4;
        int statusFinalized = 2;
        int statusSubmitted = 5;
        int statusCleaningCleaned = 21;
        int statusRepairRepaired = 31;
        int mtyZoneOk = 41;
        int creationSourceCntRepair3rdPartyRepair = 51;
        int isEstApproveSourceAuto3rd = 61;

        ChangeStatusEstimateEMRInput.Input input = new ChangeStatusEstimateEMRInput.Input();
        input.setEstadoEstimadoEmrId(statusRepairComplete);
        input.setUsuarioModificacionId(userId);
        input.setIsStatusChange(1);
        input.setCambiosEstadosJson("[{\"estimado_emr_id\":" + estimateId + ",\"eir_id\":" + eirId + "}]");
        input.setLanguageId(1);

        when(catalogRepository.findIdByAlias(anyString()))
                .thenReturn(
                        statusCreated,
                        statusFinalized,
                        statusSubmitted,
                        statusApproved,
                        statusRepairComplete,
                        statusRejected,
                        statusCanceled,
                        0,
                        estimateTypeStructure, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                        statusRepairRepaired, statusCleaningCleaned, creationSourceCntRepair3rdPartyRepair, mtyZoneOk, isEstApproveSourceAuto3rd);

        EstimateEmr estimateEmr = mock(EstimateEmr.class);
        when(estimateEmr.getId()).thenReturn(estimateId);
        when(estimateEmr.getActive()).thenReturn(true);
        when(estimateEmr.getCatEstimateStatus()).thenReturn(new Catalog(statusFinalized));
        when(estimateEmr.getCatEstimateType()).thenReturn(new Catalog(estimateTypeStructure));
        when(estimateEmr.getShippingLine()).thenReturn(new ShippingLine(1234));
        when(estimateEmr.getEir()).thenReturn(new Eir(eirId));
        when(estimateEmr.getEstimateSubmissionDate()).thenReturn(LocalDateTime.now());
        when(estimateEmr.getApproveRejectEstimateDate()).thenReturn(LocalDateTime.now());
        when(estimateEmr.getCompletedEstimateDate()).thenReturn(null);

        when(estimateEmrRepository.findAllById(anyList())).thenReturn(new ArrayList<>(List.of(estimateEmr)));
        when(estimateEmrRepository.findById(anyInt())).thenReturn(Optional.of(estimateEmr));
        when(estimateEmrRepository.saveAndFlush(any(EstimateEmr.class))).thenAnswer(invocation -> invocation.getArgument(0));

        Eir eir = mock(Eir.class);
        when(eir.getId()).thenReturn(eirId);
        when(eir.getCatApprovalRepBox()).thenReturn(new Catalog(statusRepairRepaired));
        when(eir.getCatApprovalRepMachine()).thenReturn(new Catalog(statusRepairRepaired));
        when(eir.getCatCleaningStatus()).thenReturn(null);
        when(eirRepository.findAllById(anyList())).thenReturn(new ArrayList<>(List.of(eir)));
        when(eirRepository.saveAndFlush(any(Eir.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(eirRepository.findById(anyInt())).thenReturn(Optional.of(eir));

        EirActivityZone structureInspection = mock(EirActivityZone.class);
        when(structureInspection.getEir()).thenReturn(new Eir(eirId));
        when(structureInspection.getCatZoneActivity()).thenReturn(new Catalog(0));
        when(structureInspection.getConcluded()).thenReturn(true);
        when(structureInspection.getRegistrationDate()).thenReturn(LocalDateTime.now());
        when(structureInspection.getActive()).thenReturn(true);

        EirActivityZone machineryInspection = mock(EirActivityZone.class);
        when(machineryInspection.getEir()).thenReturn(new Eir(eirId));
        when(machineryInspection.getCatZoneActivity()).thenReturn(new Catalog(0));
        when(machineryInspection.getConcluded()).thenReturn(true);
        when(machineryInspection.getRegistrationDate()).thenReturn(LocalDateTime.now());
        when(machineryInspection.getActive()).thenReturn(true);

        when(eirActivityZoneRepository.findAllByEirIdAndActive(anyList(), eq(true)))
                .thenReturn(new ArrayList<>());

        when(eirActivityZoneRepository.saveAndFlush(any(EirActivityZone.class))).thenAnswer(invocation -> {
            EirActivityZone zone = invocation.getArgument(0);
            EirActivityZone saved = new EirActivityZone();
            saved.setId(888);
            saved.setEir(zone.getEir());
            saved.setCatZoneActivity(zone.getCatZoneActivity());
            saved.setRegistrationDate(zone.getRegistrationDate());
            saved.setActive(zone.getActive());
            return saved;
        });
        when(eirActivityZoneRepository.save(any(EirActivityZone.class))).thenAnswer(invocation -> {
            EirActivityZone zone = invocation.getArgument(0);
            EirActivityZone saved = new EirActivityZone();
            saved.setId(889);
            saved.setEir(zone.getEir());
            saved.setCatZoneActivity(zone.getCatZoneActivity());
            saved.setRegistrationDate(zone.getRegistrationDate());
            saved.setActive(zone.getActive());
            saved.setEirZone(zone.getEirZone());
            saved.setCatZoneActivityResult(zone.getCatZoneActivityResult());
            saved.setTracezoneActivity(zone.getTracezoneActivity());
            saved.setIsPartialInspection(zone.getIsPartialInspection());
            saved.setCatCreacionEIROrigin(zone.getCatCreacionEIROrigin());
            saved.setEndDate(zone.getEndDate());
            saved.setConcluded(zone.getConcluded());
            saved.setFinalUser(zone.getFinalUser());
            return saved;
        });

        EirZone eirZone = mock(EirZone.class);
        when(eirZone.getEir()).thenReturn(new Eir(eirId));
        when(eirZone.getCatContainerZone()).thenReturn(new Catalog(mtyZoneOk));
        when(eirZone.getActive()).thenReturn(true);
        when(eirZone.getRegistrationDate()).thenReturn(LocalDateTime.now());

        when(eirZoneRepository.findAllById(anyList())).thenReturn(new ArrayList<>(List.of(eirZone)));
        when(eirZoneRepository.findLastByEirIdAndActive(any(), eq(eirId), eq(true)))
                .thenReturn(new ArrayList<>(List.of(eirZone)));
        when(eirZoneRepository.saveAndFlush(any(EirZone.class))).thenAnswer(invocation -> {
            EirZone zone = invocation.getArgument(0);
            EirZone saved = new EirZone();
            saved.setId(999);
            saved.setEir(zone.getEir());
            saved.setCatContainerZone(zone.getCatContainerZone());
            saved.setRegistrationDate(zone.getRegistrationDate());
            saved.setRegistrationUser(zone.getRegistrationUser());
            saved.setActive(zone.getActive());
            saved.setTraceEirZone(zone.getTraceEirZone());
            saved.setCatOrigenCreacionEirzona(zone.getCatOrigenCreacionEirzona());
            return saved;
        });
        when(eirZoneRepository.save(any(EirZone.class))).thenAnswer(invocation -> {
            EirZone zone = invocation.getArgument(0);
            zone.setTraceEirZone("cnt_repaired_3P_07");
            return zone;
        });

        // Mock updateByEirIdAndContainerZoneIdAndActive
        doNothing().when(eirZoneRepository).updateByEirIdAndContainerZoneIdAndActive(
                eq(eirId), eq(mtyZoneOk), eq(false), eq(userId), eq("cnt_repaired_3P_06")
        );

        EstimateEmrDetail cleaningDetail = mock(EstimateEmrDetail.class);
        when(cleaningDetail.getEstimateEmr()).thenReturn(estimateEmr);
        when(cleaningDetail.getCatCleaningType()).thenReturn(new Catalog(1));
        when(cleaningDetail.getActive()).thenReturn(true);

        EstimateEmrDetail damageDetail = mock(EstimateEmrDetail.class);
        when(damageDetail.getEstimateEmr()).thenReturn(estimateEmr);
        when(damageDetail.getCatCleaningType()).thenReturn(null);
        when(damageDetail.getActive()).thenReturn(true);

        when(eirActivityZoneRepository.findLastByEirIdAndActivityZoneIdAndActive(any(), anyInt(), anyInt(), anyBoolean())).thenReturn(List.of(
                EirActivityZone.builder()
                        .id(1002)
                        .build()
        ));
        when(estimateEmrDetailRepository.findAllByEirIdAndActiveWithCleaningType(anyList(), eq(true)))
                .thenReturn(new ArrayList<>(List.of(cleaningDetail)));
        when(estimateEmrDetailRepository.findAllByEirIdAndEstimateType(anyInt(), anyList()))
                .thenReturn(new ArrayList<>(List.of(cleaningDetail)));
        when(estimateEmrDetailRepository.findAllByEstimateIdAndActiveWithNoCleaningType(anyList(), eq(true)))
                .thenReturn(new ArrayList<>(List.of(damageDetail)));
        when(estimateEmrDetailRepository.existsByEirIdAndEstimateTypeIdAndStatusIdWithNoCleaningType(anyInt(), anyInt(), anyList()))
                .thenReturn(false);

        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("OK");

        ChangeStatusEstimateEMROutput output = service.changeStatusEstimateEMR(input);

        assertNotNull(output);
        assertEquals(1, output.getRespEstado());
    }
}