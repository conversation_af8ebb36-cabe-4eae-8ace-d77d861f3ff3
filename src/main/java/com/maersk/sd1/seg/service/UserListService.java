package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.CompanyUser;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.UserRole;
import com.maersk.sd1.seg.dto.UserListInput;
import com.maersk.sd1.seg.dto.UserListOutput;
import com.maersk.sd1.seg.dto.UserListOutput.UserData;
import com.maersk.sd1.seg.dto.UserListOutput.CompanyData;
import com.maersk.sd1.seg.dto.UserListOutput.RoleData;
import com.maersk.sd1.seg.repository.UserListRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

@Service
public class UserListService {

    private static final Logger logger = LogManager.getLogger(UserListService.class);

    private final UserListRepository userListRepository;

    @Autowired
    public UserListService(UserListRepository userListRepository) {
        this.userListRepository = userListRepository;
    }

    @Transactional(readOnly = true)
    public UserListOutput listUsers(UserListInput.Input inputParams) {
        UserListOutput output = new UserListOutput();

        try {
            if (inputParams == null) {
                output.setResponseStatus(0);
                output.setResponseMessage("Invalid input parameters");
                return output;
            }

            if (inputParams.getUserIds() != null && !inputParams.getUserIds().isEmpty()) {
                inputParams.setPage(1);
                inputParams.setSize(10000);
            }

            Integer page = (inputParams.getPage() == null || inputParams.getPage() < 1)
                    ? 1 : inputParams.getPage();
            Integer size = (inputParams.getSize() == null || inputParams.getSize() < 1)
                    ? 10 : inputParams.getSize();

            Pageable pageable = PageRequest.of(page - 1, size);

            List<String> aliases = null;
            if (inputParams.getUserIds() != null && !inputParams.getUserIds().isEmpty()) {
                String raw = inputParams.getUserIds().replace("'", "");
                aliases = List.of(raw.split(","))
                        .stream().map(String::trim).toList();
            }

            List<Integer> roleIds = null;
            if (inputParams.getRoles() != null && !inputParams.getRoles().isEmpty()) {
                String raw = inputParams.getRoles().replace("'", "");
                roleIds = List.of(raw.split(","))
                        .stream().map(s -> Integer.valueOf(s.trim()))
                        .toList();
            }

            List<Integer> companyIds = null;
            if (inputParams.getCompanies() != null && !inputParams.getCompanies().isEmpty()) {
                String raw = inputParams.getCompanies().replace("'", "");
                companyIds = List.of(raw.split(","))
                        .stream().map(s -> Integer.valueOf(s.trim()))
                        .toList();
            }

            Page<User> userPage = userListRepository.findByAllFilters(
                    inputParams.getUserId(),
                    inputParams.getAlias(),
                    inputParams.getEmail(),
                    inputParams.getNames(),
                    inputParams.getStatus(),
                    aliases,
                    roleIds,
                    companyIds,
                    pageable
            );

            List<UserData> userDataList = new ArrayList<>();

            if (userPage != null && userPage.hasContent()) {
                for (User user : userPage.getContent()) {
                    UserData ud = new UserData();
                    ud.setUserId(user.getId());
                    ud.setAlias(user.getAlias());
                    ud.setEmail(user.getMail());

                    Long diffDays = null;
                    if (user.getDateChangeKey() != null) {
                        diffDays = ChronoUnit.DAYS.between(LocalDateTime.now(), user.getDateChangeKey());
                    }
                    ud.setDaysSincePasswordChange(diffDays);

                    ud.setNames(user.getNames());
                    ud.setFatherLastName(user.getFirstLastName());
                    ud.setMotherLastName(user.getSecondLastName());

                    if (user.getCompany() != null) {
                        ud.setCompanyId(user.getCompany().getId());
                        ud.setCompanyDocument(user.getCompany().getDocument());
                        ud.setCompanyLegalName(user.getCompany().getLegalName());
                    }

                    ud.setStatus(user.getStatus());

                    if (user.getAttachmentPhoto() != null) {
                        ud.setAttachedPhotoId(user.getAttachmentPhoto().getId());
                        ud.setAttachedPhotoAlias(user.getAttachmentPhoto().getId1());
                    }

                    List<RoleData> roleDataList = new ArrayList<>();
                    if (user.getId() != null) {
                        List<UserRole> userRoles = userListRepository.findUserRolesByUserId(user.getId()); // Explicitly fetch roles
                        for (UserRole ur : userRoles) {
                            if (ur.getRole() != null) {
                                RoleData rd = new RoleData();
                                rd.setRoleId(ur.getRole().getId());
                                rd.setName(ur.getRole().getName());
                                roleDataList.add(rd);
                            }
                        }
                    }
                    ud.setRoles(roleDataList);

                    List<CompanyData> companyDataList = new ArrayList<>();
                    List<CompanyUser> companyUsers = userListRepository.findCompanyUsersByUserId(user.getId());
                    for (CompanyUser cu : companyUsers) {
                        CompanyData cd = new CompanyData();
                        if (cu.getCompany() != null) {
                            cd.setCompanyId(cu.getCompany().getId());
                            cd.setDocument(cu.getCompany().getDocument());
                            cd.setLegalName(cu.getCompany().getLegalName());
                            companyDataList.add(cd);
                        }
                    }
                    ud.setCompanies(companyDataList);

                    if (user.getPerson() != null) {
                        ud.setPersonId(user.getPerson().getId());
                    }

                    userDataList.add(ud);
                }
            }

            output.setUsers(userDataList);
            output.setTotalRecords(userPage != null ? userPage.getTotalElements() : 0);
            output.setResponseStatus(1);
            output.setResponseMessage("OK");

        } catch (DataAccessException e) {
            logger.error("Database error in listUsers", e);
            output.setResponseStatus(0);
            output.setResponseMessage("Database error: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error in listUsers", e);
            output.setResponseStatus(0);
            output.setResponseMessage("Unexpected error: " + e.getMessage());
        }
        return output;
    }
}
