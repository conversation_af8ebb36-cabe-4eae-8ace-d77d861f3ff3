package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EstimateEmrEirPhoto;
import com.maersk.sd1.sde.controller.dto.EstimatedEmrPhotoHeaderDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import java.util.Optional;

public interface EstimateEmrEirPhotoRepository extends JpaRepository<EstimateEmrEirPhoto, Integer> {

    Optional<EstimateEmrEirPhoto> findByEir_IdAndAttachment_Id(Integer eirId, Integer attachmentId);

    @Query("""
    SELECT new com.maersk.sd1.sde.controller.dto.EstimatedEmrPhotoHeaderDTO(
        est.id, estf.id, adj.id, adj.id1, adj.url)
    FROM EstimateEmrEirPhoto estf
    INNER JOIN estimadoEmr est
    INNER JOIN attachment adj
    WHERE est.id = :emrNumber AND estf.active = true
    """)
    List<EstimatedEmrPhotoHeaderDTO> findEstimateEmrPhotoHeaders(@Param("emrNumber") Integer emrNumber);

    @Query("SELECT p FROM EstimateEmrEirPhoto p " +
            "WHERE p.estimadoEmr.id = :estimateEmrId AND p.active = true")
    List<EstimateEmrEirPhoto> findActiveByEstimateEmrId(@Param("estimateEmrId") Integer estimateEmrId);

    @Modifying
    @Transactional
    @Query("DELETE FROM EstimateEmrEirPhoto e WHERE e.attachment.id = :attatchmentId")
    void deleteByAttachmentId(@Param("attatchmentId") Integer attatchmentId);
}