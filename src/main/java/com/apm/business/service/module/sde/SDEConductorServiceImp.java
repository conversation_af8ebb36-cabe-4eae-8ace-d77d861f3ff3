package com.apm.business.service.module.sde;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sde/SDEConductorServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEConductorServiceImp extends SDEConductorService {

	@RequestMapping(value = "/sdeconductorObtener", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeconductorObtener(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdeconductorObtener(ppo, request);
	}

	@RequestMapping(value = "/sdeconductorRegistrar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeconductorRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdeconductorRegistrar(ppo, request);
	}

}