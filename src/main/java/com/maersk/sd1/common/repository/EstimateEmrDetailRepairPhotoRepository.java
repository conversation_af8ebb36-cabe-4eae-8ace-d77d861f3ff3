package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EstimateEmrDetailRepairPhoto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface EstimateEmrDetailRepairPhotoRepository extends JpaRepository<EstimateEmrDetailRepairPhoto, Integer> {

    @Query("SELECT p FROM EstimateEmrDetailRepairPhoto p " +
            "WHERE p.estimateEmrDetail.estimateEmr.id = :estimateEmrId " +
            "AND p.estimateEmrDetail.active = true AND p.active = true")
    List<EstimateEmrDetailRepairPhoto> findActiveByEstimateEmrId(@Param("estimateEmrId") Integer estimateEmrId);

    @Modifying
    @Transactional
    @Query("DELETE FROM EstimateEmrDetailRepairPhoto r WHERE r.attachment.id = :attatchmentId")
    void deleteByAttachmentId(@Param("attatchmentId") Integer attatchmentId);



}