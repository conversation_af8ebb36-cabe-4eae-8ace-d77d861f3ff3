package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.BookingEdiSetting;
import com.maersk.sd1.common.model.BookingEdiSettingBU;
import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.BookingEdiSettingBURepository;
import com.maersk.sd1.common.repository.BookingEdiSettingRepository;
import com.maersk.sd1.sds.dto.SetEdiCopyUpdateInput;
import com.maersk.sd1.sds.dto.SetEdiCopyUpdateOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@RequiredArgsConstructor
@Service
public class SetEdiCopyUpdateService {

    private static final Logger logger = LogManager.getLogger(SetEdiCopyUpdateService.class);

    private final BookingEdiSettingRepository bookingEdiSettingRepository;
    private final  BookingEdiSettingBURepository bookingEdiSettingBURepository;

    @Transactional
    public SetEdiCopyUpdateOutput updateSeteoEdiCoparn(SetEdiCopyUpdateInput.Input inputData) {
        SetEdiCopyUpdateOutput output = new SetEdiCopyUpdateOutput();
        try {
            // 1. Find the existing BookingEdiSetting by ID.
            BookingEdiSetting existingSetting = bookingEdiSettingRepository.findById(inputData.getSeteoEdiCoparnId())
                    .orElseThrow(() -> new IllegalArgumentException("No BookingEdiSetting found for ID: " + inputData.getSeteoEdiCoparnId()));

            // 2. Update all necessary fields.
            // shippingLine
            if (inputData.getLineaNavieraId() != null) {
                if (existingSetting.getShippingLine() == null) {
                    existingSetting.setShippingLine(new com.maersk.sd1.common.model.ShippingLine());
                }
                existingSetting.getShippingLine().setId(inputData.getLineaNavieraId());
            } else {
                existingSetting.setShippingLine(null);
            }

            // catCanalRecepcionCoparn
            if (inputData.getCatCanalRecepcionCoparnId() != null) {
                if (existingSetting.getCatCanalRecepcionCoparn() == null) {
                    existingSetting.setCatCanalRecepcionCoparn(new Catalog());
                }
                existingSetting.getCatCanalRecepcionCoparn().setId(inputData.getCatCanalRecepcionCoparnId().intValue());
            } else {
                existingSetting.setCatCanalRecepcionCoparn(null);
            }

            // catModoProcesarCoparn
            if (inputData.getCatModoProcesarCoparnId() != null) {
                if (existingSetting.getCatModoProcesarCoparn() == null) {
                    existingSetting.setCatModoProcesarCoparn(new Catalog());
                }
                existingSetting.getCatModoProcesarCoparn().setId(inputData.getCatModoProcesarCoparnId().intValue());
            } else {
                existingSetting.setCatModoProcesarCoparn(null);
            }

            // bookingEdiDescription
            existingSetting.setBkEdiDescription(inputData.getEdiCoparnDescripcion());
            // azureId
            existingSetting.setAzureId(inputData.getAzureId());
            // bkEdiSftpId
            existingSetting.setBkEdiSftpId(inputData.getSftpCoparnId());
            // bkEdiFtpId
            existingSetting.setBkEdiFtpId(inputData.getFtpCoparnId());
            // bkEdiFolderRoute
            existingSetting.setBkEdiFolderRoute(inputData.getCarpetaCoparnRuta());
            // downloadFileExtension
            existingSetting.setDownloadFileExtension(inputData.getExtensionArchivoDescargar());
            // edi_move_route
            existingSetting.setEdi_move_route(inputData.getRutaMoverEdi());
            // allowCreateAutomaticVesselProgramming
            existingSetting.setAllowCreateAutomaticVesselProgramming(inputData.getPermitirCrearProgNaveAutomatico());
            // allowCreateAutomaticCustomer
            existingSetting.setAllowCreateAutomaticCustomer(inputData.getPermitirCrearClienteAutomatico());
            // isHistorical
            existingSetting.setIsHistorical(inputData.getEsHistorico());
            // deactivationDate
            existingSetting.setDeactivationDate(inputData.getFechaDebaja());
            // deactivationReason
            existingSetting.setDeactivationReason(inputData.getMotivoDebaja());
            // active
            existingSetting.setActive(inputData.getActivo());

            // modificationUser
            if (inputData.getUsuarioModificacionId() != null) {
                User modUser = new User();
                modUser.setId(inputData.getUsuarioModificacionId().intValue());
                existingSetting.setModificationUser(modUser);
            } else {
                existingSetting.setModificationUser(null);
            }

            // modificationDate
            existingSetting.setModificationDate(LocalDateTime.now());

            // businessUnit
            if (inputData.getUnidadNegocioId() != null) {
                if (existingSetting.getBusinessUnit() == null) {
                    existingSetting.setBusinessUnit(new com.maersk.sd1.common.model.BusinessUnit());
                }
                existingSetting.getBusinessUnit().setId(inputData.getUnidadNegocioId().intValue());
            } else {
                existingSetting.setBusinessUnit(null);
            }

            // catBkEdiMessageType
            if (inputData.getCatBkEdiMessageTypeId() != null) {
                if (existingSetting.getCatBkEdiMessageType() == null) {
                    existingSetting.setCatBkEdiMessageType(new Catalog());
                }
                existingSetting.getCatBkEdiMessageType().setId(inputData.getCatBkEdiMessageTypeId().intValue());
            } else {
                existingSetting.setCatBkEdiMessageType(null);
            }

            // catOwnerEdiBooking
            if (inputData.getCatOwnerEdiBookingId() != null) {
                if (existingSetting.getCatOwnerEdiBooking() == null) {
                    existingSetting.setCatOwnerEdiBooking(new Catalog());
                }
                existingSetting.getCatOwnerEdiBooking().setId(inputData.getCatOwnerEdiBookingId().intValue());
            } else {
                existingSetting.setCatOwnerEdiBooking(null);
            }

            // filenameMask
            existingSetting.setFilenameMask(inputData.getFilenameMask());

            // 3. Save the updated entity.
            bookingEdiSettingRepository.save(existingSetting);

            // 4. If detail is not null, do the delete and insert logic.
            if (inputData.getDetalle() != null && !inputData.getDetalle().isEmpty()) {
                // First delete all existing child records.
                bookingEdiSettingBURepository.deleteByBookingEdiSetting(existingSetting);

                for (SetEdiCopyUpdateInput.Detail detailItem : inputData.getDetalle()) {
                    BookingEdiSettingBU newBU = new BookingEdiSettingBU();
                    newBU.setBookingEdiSetting(existingSetting);

                    // subBusinessUnit
                    BusinessUnit subBU = new BusinessUnit();
                    subBU.setId(detailItem.getSubBusinessUnitId().intValue());
                    newBU.setSubBusinessUnit(subBU);

                    newBU.setBkEdiApplyCopySend(detailItem.getBkEdiApplyCopySend());

                    if (detailItem.getCatBkEdiForwardChannelId() != null) {
                        Catalog forwardChannel = new Catalog();
                        forwardChannel.setId(detailItem.getCatBkEdiForwardChannelId().intValue());
                        newBU.setCatBkEdiForwardChannel(forwardChannel);
                    }

                    newBU.setBkEdiForwardFftpId(detailItem.getBkEdiForwardFftpId());
                    newBU.setBkEdiForwardSftpId(detailItem.getBkEdiForwardSftpId());
                    newBU.setBkEdiForwardFolderRoute(detailItem.getBkEdiForwardFolderRoute());

                    // Set default active = true (since stored procedure sets it to 1)
                    newBU.setActive(true);

                    // user_registration_id is not changed in stored procedure if we are updating.
                    // But we do see it uses @usuario_modificacion_id for the insert. We mimic that.
                    // We will treat usuario_modificacion_id as the user who is creating these new records.

                    User regUser = new User();
                    regUser.setId(inputData.getUsuarioModificacionId().intValue());
                    newBU.setRegistrationUser(regUser);

                    newBU.setRegistrationDate(LocalDateTime.now());

                    User modUser = new User();
                    modUser.setId(inputData.getUsuarioModificacionId().intValue());
                    newBU.setModificationUser(modUser);
                    newBU.setModificationDate(null); // The sp sets this to null

                    // Let JPA create the new primary key (if we have a generated strategy) else we might need to set.
                    // Here, for simplicity, we assume the database or sequence sets it.

                    bookingEdiSettingBURepository.save(newBU);
                }
            }

            // 5. Prepare output.
            output.setRespEstado(1);
            output.setRespMensaje("Registro actualizado correctamente");
        } catch (Exception ex) {
            logger.error("Error updating seteo_edi_coparn:", ex);
            output.setRespEstado(0);
            output.setRespMensaje(ex.getMessage());
        }
        return output;
    }
}
