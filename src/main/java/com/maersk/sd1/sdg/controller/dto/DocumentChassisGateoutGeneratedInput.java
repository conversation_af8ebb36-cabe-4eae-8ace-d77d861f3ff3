package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class DocumentChassisGateoutGeneratedInput {

    @Data
    public static class Input {

        @NotNull
        @JsonProperty("sub_business_unit_local_id")
        private Integer subBusinessUnitLocalId;

        @NotBlank
        @JsonProperty("chassis")
        private String chassis;

        @NotNull
        @JsonProperty("language_id")
        private Integer languageId;

        @NotNull
        @JsonProperty("user_registration_id")
        private Integer userRegistrationId;
    }

    @Data
    public static class Prefix {
        @NotNull
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @NotNull
        @JsonProperty("SDG")
        private Prefix sdg;

        public Input getInput() {
            return sdg != null ? sdg.getInput() : null;
        }
    }

}