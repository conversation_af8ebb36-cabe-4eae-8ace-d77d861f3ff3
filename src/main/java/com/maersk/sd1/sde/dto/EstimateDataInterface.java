package com.maersk.sd1.sde.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public interface EstimateDataInterface {

    Integer getEstimadoEmrId();

    Integer getMercPlusEstimateNumber();

    Long getCatTipoEstimadoId();

    Integer getEirId();

    LocalDateTime getGateInDate();

    Integer getLineaNavieraId();

    Integer getContenedorId();

    String getNumeroContenedor();

    Long getCatTamanoId();

    Long getCatTipoContenedorId();

    String getViaje();

    LocalDateTime getApprovalDate();

    Long getUsuarioApruebaRechazaId();

    Long getCatRepairBoxStatusId();

    Long getCatRepairMachineryStatusId();

    Long getCatEstadoEstimadoId();

    Boolean getFlagAutoApproval();

    Long getCatCleaningStatusId();

    BigDecimal getEstimadoTotalHoras();
}