package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EstimateCleaningDetailDTO {

    @JsonProperty("estimate_detail_id")
    private Integer estimateDetailId;

    @JsonProperty("damage_location_id")
    private Integer damageLocationId;

    @JsonProperty("damage_location")
    private String damageLocation;

    @JsonProperty("damage_type_id")
    private Integer damageTypeId;

    @JsonProperty("damage_type")
    private String damageType;

    @JsonProperty("component_id")
    private Integer componentId;

    @JsonProperty("component")
    private String component;

    @JsonProperty("repair_method_id")
    private Integer repairMethodId;

    @JsonProperty("repair_method")
    private String repairMethod;

    @JsonProperty("cedex_merc_code")
    private String cedexMercCode;

    @JsonProperty("cedex_merc_desc")
    private String cedexMercDesc;

    @JsonProperty("dimension_id")
    private Integer dimensionId;

    @JsonProperty("dimension")
    private String dimension;

    @JsonProperty("length")
    private BigDecimal length;

    @JsonProperty("width")
    private BigDecimal width;

    @JsonProperty("pieces")
    private Integer pieces;

    @JsonProperty("cost_material_piece")
    private BigDecimal costMaterialPiece;

    @JsonProperty("hours_piece")
    private BigDecimal hoursPiece;

    @JsonProperty("assume_cost_id")
    private Integer assumeCostId;

    @JsonProperty("assume_cost")
    private String assumeCost;

    @JsonProperty("unit_meassure_id")
    private Integer unitMeassureId;

    @JsonProperty("unit_meassure")
    private String unitMeassure;

    @JsonProperty("estimado_emr_id")
    private Integer estimadoEmrId;

    @JsonProperty("cat_cleaning_type_id")
    private Integer catCleaningTypeId;

    @JsonProperty("cat_cleaning_type_desc")
    private String catCleaningTypeDesc;

    @JsonProperty("selected")
    private Boolean selected;

}