package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Booking;
import com.maersk.sd1.common.model.BookingDetail;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.BookingDetailRepository;
import com.maersk.sd1.common.repository.BookingRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sde.controller.dto.PreAssignmentListInput;
import com.maersk.sd1.sde.controller.dto.PreAssignmentListOutput;
import com.maersk.sd1.sde.dto.PreAssignmentListProcessDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class PreAssignmentListServiceTest {

    @Mock
    private BookingRepository bookingRepository;

    @Mock
    private BookingDetailRepository bookingDetailRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @InjectMocks
    private PreAssignmentListService service;

    private PreAssignmentListInput.Input input;

    @BeforeEach
    void setUp() {
        input = new PreAssignmentListInput.Input();
        input.setSubBusinessUnitId(123);
        input.setPage(1);
        input.setSize(5);
    }

    @Test
    void GivenCatalogRecordsExist_When_PreAssignmentListIsFetched_Then_ReturnsCorrectTotalRegisters() {
        Catalog isDocumentActive = new Catalog();
        isDocumentActive.setId(1);
        Catalog isCreationPreAssig = new Catalog();
        isCreationPreAssig.setId(2);

        Mockito.when(catalogRepository.findByAlias("43061")).thenReturn(isDocumentActive);
        Mockito.when(catalogRepository.findByAlias("sd1_creationsource_pre_assig")).thenReturn(isCreationPreAssig);

        PreAssignmentListProcessDTO dto = new PreAssignmentListProcessDTO();
        dto.setSubBusinessUnitId(input.getSubBusinessUnitId());

        List<Booking> mockList = new ArrayList<>();
        Booking bk = new Booking();
        bk.setId(999);
        bk.setBookingNumber("BKTEST999");
        bk.setApprovedBooking(true);
        bk.setActive(true);
        bk.setBookingIssueDate(LocalDateTime.now());
        mockList.add(bk);

        Page<Booking> page = new PageImpl<>(mockList, PageRequest.of(0, 5), 1);
        Mockito.when(bookingRepository.findBookings(Mockito.any(PreAssignmentListProcessDTO.class), Mockito.any(PageRequest.class)))
                .thenReturn(page);

        Mockito.when(bookingDetailRepository.findByBooking(Mockito.any(Booking.class)))
                .thenReturn(Collections.emptyList());

        PreAssignmentListOutput result = service.preAssignmentList(input);

        assertNotNull(result);
    }

    @Test
    void GivenNullBookingIssueDates_When_PreAssignmentListIsFetched_Then_ReturnsEmptyPage() {

        input.setBookingIssueDateMin(null);
        input.setBookingIssueDateMax(null);

        Catalog isDocumentActive = new Catalog();
        isDocumentActive.setId(1);
        Catalog isCreationPreAssig = new Catalog();
        isCreationPreAssig.setId(2);

        Mockito.when(catalogRepository.findByAlias("43061")).thenReturn(isDocumentActive);
        Mockito.when(catalogRepository.findByAlias("sd1_creationsource_pre_assig")).thenReturn(isCreationPreAssig);

        PreAssignmentListProcessDTO dto = new PreAssignmentListProcessDTO();
        dto.setSubBusinessUnitId(input.getSubBusinessUnitId());

        Mockito.when(bookingRepository.findBookings(Mockito.any(PreAssignmentListProcessDTO.class), Mockito.any(PageRequest.class)))
                .thenReturn(Page.empty());

        PreAssignmentListOutput result = service.preAssignmentList(input);
        assertNotNull(result);
    }

    @Test
    void GivenNoBookingsExist_When_PreAssignmentListIsFetched_Then_ReturnsEmptyTotalRegisters() {
        Catalog isDocumentActive = new Catalog();
        isDocumentActive.setId(1);
        Catalog isCreationPreAssig = new Catalog();
        isCreationPreAssig.setId(2);

        Mockito.when(catalogRepository.findByAlias("43061")).thenReturn(isDocumentActive);
        Mockito.when(catalogRepository.findByAlias("sd1_creationsource_pre_assig")).thenReturn(isCreationPreAssig);
        PreAssignmentListProcessDTO dto = new PreAssignmentListProcessDTO();
        dto.setSubBusinessUnitId(input.getSubBusinessUnitId());

        Page<Booking> emptyPage = Page.empty();
        Mockito.when(bookingRepository.findBookings(Mockito.any(PreAssignmentListProcessDTO.class), Mockito.any(PageRequest.class)))
                .thenReturn(emptyPage);

        PreAssignmentListOutput result = service.preAssignmentList(input);

        assertNotNull(result);
    }

    @Test
    void GivenMultipleBookingsAndDetailsExist_When_PreAssignmentListIsFetched_Then_ReturnsCorrectTotalRegisters() {
        Catalog isDocumentActive = new Catalog();
        isDocumentActive.setId(1);
        Catalog isCreationPreAssig = new Catalog();
        isCreationPreAssig.setId(2);

        Mockito.when(catalogRepository.findByAlias("43061")).thenReturn(isDocumentActive);
        Mockito.when(catalogRepository.findByAlias("sd1_creationsource_pre_assig")).thenReturn(isCreationPreAssig);

        PreAssignmentListProcessDTO dto = new PreAssignmentListProcessDTO();
        dto.setSubBusinessUnitId(input.getSubBusinessUnitId());

        List<Booking> mockList = new ArrayList<>();
        for (int i = 1; i <= 2; i++) {
            Booking bk = new Booking();
            bk.setId(i);
            bk.setBookingNumber("BKTEST" + i);
            bk.setApprovedBooking(true);
            bk.setActive(true);
            bk.setBookingIssueDate(LocalDateTime.now());
            mockList.add(bk);
        }

        Page<Booking> page = new PageImpl<>(mockList, PageRequest.of(0, 5), 2);
        Mockito.when(bookingRepository.findBookings(Mockito.any(PreAssignmentListProcessDTO.class), Mockito.any(PageRequest.class)))
                .thenReturn(page);

        List<BookingDetail> details = new ArrayList<>();
        BookingDetail detail = new BookingDetail();
        detail.setReservationQuantity(10);
        detail.setAttendedQuantity(5);
        details.add(detail);

        Mockito.when(bookingDetailRepository.findByBooking(Mockito.any(Booking.class)))
                .thenReturn(details);

        PreAssignmentListOutput result = service.preAssignmentList(input);

        assertNotNull(result);
    }

    @Test
    void GivenDatabaseErrorOccurs_When_PreAssignmentListIsFetched_Then_ReturnsErrorResponse() {
        Catalog isDocumentActive = new Catalog();
        isDocumentActive.setId(1);
        Catalog isCreationPreAssig = new Catalog();
        isCreationPreAssig.setId(2);

        Mockito.when(catalogRepository.findByAlias("43061")).thenReturn(isDocumentActive);
        Mockito.when(catalogRepository.findByAlias("sd1_creationsource_pre_assig")).thenReturn(isCreationPreAssig);

        Mockito.when(bookingRepository.findBookings(Mockito.any(PreAssignmentListProcessDTO.class), Mockito.any(PageRequest.class)))
                .thenThrow(new RuntimeException("DB error"));

        PreAssignmentListOutput result = service.preAssignmentList(input);
    }
}