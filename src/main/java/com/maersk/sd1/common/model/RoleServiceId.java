package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class RoleServiceId implements Serializable {
    private static final long serialVersionUID = 8639598842039401107L;
    @NotNull
    @Column(name = "rol_id", nullable = false)
    private Integer roleId;

    @NotNull
    @Column(name = "servicio_id", nullable = false)
    private Integer serviceId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        RoleServiceId entity = (RoleServiceId) o;
        return Objects.equals(this.roleId, entity.roleId) &&
                Objects.equals(this.serviceId, entity.serviceId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(roleId, serviceId);
    }

}