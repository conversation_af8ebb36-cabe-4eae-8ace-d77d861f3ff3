package com.maersk.sd1.sde.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;

@UtilityClass
public class EstimateAutoApprovalConfigListInput {

    @Data
    public static class Input {

        @JsonProperty("sub_business_unit_id")
        private Long subBusinessUnitId;

        @JsonProperty("estimate_auto_approval_config_id")
        private Integer estimateAutoApprovalConfigId;

        @JsonProperty("sub_business_local_id")
        @NotNull(message = "sub_business_local_id should not be null")
        private Long subBusinessLocalId;

        @JsonProperty("cat_estimate_type_id")
        private Long catEstimateTypeId;

        @JsonProperty("cat_equipment_category_id")
        private Integer catEquipmentCategoryId;

        @JsonProperty("cat_equipment_type")
        private Integer catEquipmentType;

        @JsonProperty("threshold_value")
        private BigDecimal thresholdValue;

        @JsonProperty("active")
        private Boolean active;

        @JsonProperty("language_id")
        @NotNull(message = "language_id should not be null")
        private Integer languageId;

        @JsonProperty("page")
        @Min(value = 1, message = "page should be at least 1")
        private Integer page;

        @JsonProperty("size")
        @Min(value = 1, message = "size should be at least 1")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}