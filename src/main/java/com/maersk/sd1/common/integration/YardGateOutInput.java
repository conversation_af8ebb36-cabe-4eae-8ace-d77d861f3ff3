package com.maersk.sd1.common.integration;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class YardGateOutInput {

    @SerializedName("user_id")
    private String userId;

    @SerializedName("operation_type")
    private String operationType;

    @SerializedName("business_unit_id")
    private int businessUnitId;

    @SerializedName("containers")
    private List<YardContainer> containers;

    @SerializedName("containers_assignment")
    private Boolean containersAssignment;

    @SerializedName("booking_id")
    private int bookingId;

    @SerializedName("cat_container_size_id")
    private int catContainerSizeId;

    @SerializedName("cat_container_type_id")
    private int catContainerTypeId;

    @SerializedName("cat_container_class_id")
    private Object catContainerClassId;

    @SerializedName("cat_container_content_type_id")
    private int catContainerContentTypeId;

    @SerializedName("pick_up_quantity")
    private int pickUpQuantity;

    @SerializedName("local_sub_business_unit_id")
    private int localSubBusinessUnitId;

    @SerializedName("container_content_type")
    private String containerContentType;

    @SerializedName("documento_carga_id")
    private int cargoDocumentId;

    @SerializedName("containers_related")
    private Boolean containersRelated;

    public String toJSON(){

        Gson gson = new Gson();

        return gson.toJson(this);

    }

    public String toJSON(String reference){

        Map<String, Object> pathField = new HashMap<String, Object>();

        pathField.put("F", this);

        Map<String, Object> pathbase = new HashMap<String, Object>();

        pathbase.put(reference, pathField);

        Gson gson = new Gson();

        return gson.toJson(pathbase);

    }
}
