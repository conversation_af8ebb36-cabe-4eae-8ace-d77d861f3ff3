package com.maersk.sd1.sde.service.dto;

import jakarta.persistence.Tuple;
import jakarta.persistence.TupleElement;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CustomTuple implements Tuple {
    private final Map<String, Object> data = new HashMap<>();

    // Constructor to initialize tuple data
    public CustomTuple(Map<String, Object> values) {
        this.data.putAll(values);
    }

    @Override
    public <X> X get(String alias, Class<X> type) {
        return type.cast(data.get(alias));
    }

    @Override
    public Object get(String alias) {
        return data.get(alias);
    }

    @Override
    public <X> X get(TupleElement<X> element) {
        return get(element.getAlias(), element.getJavaType());
    }

    @Override
    public <X> X get(int index, Class<X> type) {
        throw new UnsupportedOperationException("Index-based retrieval is not supported.");
    }

    @Override
    public Object get(int index) {
        throw new UnsupportedOperationException("Index-based retrieval is not supported.");
    }

    @Override
    public Object[] toArray() {
        return new Object[0];
    }

    @Override
    public List<TupleElement<?>> getElements() {
        throw new UnsupportedOperationException("getElements() is not implemented.");
    }

    // Static method to generate mock tuples
    public static List<Tuple> getMockTuples(boolean isDamaged, String temperature, boolean isRefrigerated) {
        Map<String, Object> values = new HashMap<>();
        values.put("activityDate", "202407291139");
        values.put("ediCodecoId", 919637);
        values.put("lineAdo", "MSL");
        values.put("movement", "S");
        values.put("emptyOrFull", "E");
        values.put("eirId", 898529);
        values.put("containerNumber", "MNBU3429163");
        values.put("vesselName", "LEXA MAERSK");
        values.put("voyageNumber", "431E");
        values.put("containerWeight", BigDecimal.valueOf(4420.000));
        values.put("weightMeasureCategoryId", 43012);
        values.put("isoContainerCode", "45R8");
        values.put("shippingSeal", "ML609114");
        values.put("customerSeal", "SM795459");
        values.put("customsSeal", "1302");
        values.put("otherSeals", "1301");
        values.put("cargoDocument", "242671653");
        values.put("masterCargoDocument", "242671653");
        values.put("containerStatus", '0');
        values.put("remarks", "testremark");
        values.put("vehiclePlate", "GBN8033");
        values.put("vesselCallSign", "ELRQ");
        values.put("vesselImoNumber", "5207354");
        values.put("customerName", "TUCHOK");
        values.put("customerDocument", "31200058300");
        values.put("originCategoryId", 43097);
        values.put("hasBoxDamage", isDamaged);
        values.put("hasMachineDamage", isDamaged);
        values.put("isRefrigeratedContainer", isRefrigerated);
        values.put("requiresInspection", null);
        values.put("carrierOperatorCode", "MSK");
        values.put("vesselScheduleDetailId", 10503);
        values.put("operationCategoryId", 43002);
        values.put("dischargePort", "MAPTM");
        values.put("loadingPort", "ECPSJ");
        values.put("cargoDocumentTypeId", 48736);
        values.put("truckCompanyId", 2);
        values.put("truckCompanyName", "TRUCK VARIOUS THIRD PARTIES");
        values.put("ediUpdateTimestamp", LocalDateTime.of(2024, 3, 10, 12, 30, 0));
        values.put("transactionType", "O");
        values.put("eirComment", "comment");
        values.put("temperature", temperature);
        values.put("temperatureMeasureCategoryId", 48828);
        values.put("twrNumber", "67");

        Tuple tuple = new CustomTuple(values);
        return List.of(tuple);
    }
}
