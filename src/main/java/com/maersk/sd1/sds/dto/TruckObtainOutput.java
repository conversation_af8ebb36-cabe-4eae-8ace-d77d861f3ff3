package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class TruckObtainOutput {

    @JsonProperty("resp_status")
    private Integer respStatus;

    @JsonProperty("resp_message")
    private String respMessage;

    @JsonProperty("resp_vehicle_id")
    private Integer vehicleId;

    @JsonProperty("resp_plate")
    private String plate;

    @JsonProperty("resp_payload")
    private String payload; // Using String for demonstration; can be BigDecimal if needed.

    @JsonProperty("resp_model")
    private String model;

    @JsonProperty("resp_net_weight")
    private String netWeight;

    @JsonProperty("resp_gross_weight")
    private String grossWeight;

    @JsonProperty("resp_transport_company_id")
    private Integer transportCompanyId;

    @JsonProperty("resp_active")
    private Boolean active;

    @JsonProperty("resp_registration_date")
    private String registrationDate;

    @JsonProperty("resp_modification_date")
    private String modificationDate;

    @JsonProperty("resp_legal_name")
    private String legalName;
}