package com.maersk.sd1.sde.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;

@Data
public class TbChangeStatusDTO {

    private Integer rowId;
    private Integer estimadoEmrId;
    private LocalDateTime estimateApprovedDate;
    private LocalDateTime estimateApprovedDateParam;
    private Integer motivoRechazoEstimado;
    private String motivoRechazoDescripcion;
    private LocalDateTime estimadoFechaCompletado;
    private Integer lineaNavieraId;
    private Integer eirId;
    private Boolean flagAutoApproval;
    private Integer catEstimateStatus;
    private LocalDateTime estimateSubmissionDate;
    private Integer estimateCancelReasonJson;
    private String estimateCancelDescriptionJson;
    private Integer catTipoEstimadoId;

    public static <T> Predicate<T> distinctByKey(
            Function<? super T, ?> keyExtractor) {

        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
}
