package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.seg.controller.dto.RoleObtainUserInput;
import com.maersk.sd1.seg.controller.dto.RoleObtainUserOutput;
import com.maersk.sd1.common.model.UserBU;
import com.maersk.sd1.common.model.UserConfigBU;
import com.maersk.sd1.seg.controller.dto.RoleObtainerVariousOutput;
import com.maersk.sd1.seg.dto.RoleListInputDTO;
import com.maersk.sd1.seg.dto.RoleListOutputDTO;
import com.maersk.sd1.seg.repository.SegUnidadNegocioRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class RoleObtainUserService {

    private static final Logger logger = LogManager.getLogger(RoleObtainUserService.class);

    private final UserBURepository userBURepository;

    private final UserConfigBURepository userConfigBURepository;

    private final UserRoleRepository userRoleRepository;

    private final UserRoleBusinessUnitRepository userRoleBusinessUnitRepository;

    private final RoleMenuRepository roleMenuRepository;

    private final SegUnidadNegocioRepository segUnidadNegocioRepository;

    private final RoleObtainerVariousService roleObtainerVariousService;

    private final RoleListService roleListService;

    @Transactional(readOnly = true)
    public RoleObtainUserOutput obtenerRolUsuario(RoleObtainUserInput.Input input) {
        RoleObtainUserOutput output = new RoleObtainUserOutput();
        try {
            if (input.getUsuarioId() == null) {
                throw new IllegalArgumentException("usuario_id is mandatory");
            }

            List<RoleObtainUserOutput.UserRoleDto> userRoles = userRoleRepository.findUserRolesByUserId(input.getUsuarioId());

            List<Integer> roleIds = userRoles.stream().map(RoleObtainUserOutput.UserRoleDto::getRoleId).toList();

            if (!roleIds.isEmpty()) {
                RoleObtainerVariousOutput roleObtainerVariousOutput = roleObtainerVariousService.obtenerVariosRoles(roleIds);

                if(roleObtainerVariousOutput != null) {
                    output.setRolesVarious(roleObtainerVariousOutput.getRoles());
                    output.setRoleMenus(roleObtainerVariousOutput.getRoleMenus());
                    output.setRoleBusinessUnits(roleObtainerVariousOutput.getRoleBusinessUnits());
                }
                else{
                    output.setRolesVarious(null);
                    output.setRoleMenus(null);
                    output.setRoleBusinessUnits(null);
                }

            }

            RoleListInputDTO.Root inputRoot = new RoleListInputDTO.Root();
            RoleListInputDTO.Prefix prefix = new RoleListInputDTO.Prefix();
            RoleListInputDTO.Input inputListar = new RoleListInputDTO.Input();
            prefix.setInput(inputListar);
            inputRoot.setPrefix(prefix);

            RoleListOutputDTO outputList = roleListService.listarRoles(inputRoot);

            output.setTotalRegisters(outputList.getTotalRegistros());
            output.setRolesListar(outputList.getRoles());

            List<RoleObtainUserOutput.UserRolUnidadNegocioDto> usunList = userRoleBusinessUnitRepository.findUsuarioRolUnidadNegocioByUserId(input.getUsuarioId());
            output.setUsuarioRolUnidadNegocio(usunList);

            List<RoleObtainUserOutput.SistemaMenuDto> sistemaMenuList = new ArrayList<>();
            if (!roleIds.isEmpty()) {
                sistemaMenuList = roleMenuRepository.findDistinctSistemaMenuByRoleIds(roleIds);
            }
            output.setTSistemaMenu(sistemaMenuList);


            List<RoleObtainUserOutput.UnidadNegocioSistemaMenuDto> busList = new ArrayList<>();
            for (RoleObtainUserOutput.SistemaMenuDto smItem : sistemaMenuList) {
                Integer systemId = smItem.getSistemaId();
                Integer roleId = smItem.getRolId();
                List<RoleObtainUserOutput.UnidadNegocioSistemaMenuDto> partial = segUnidadNegocioRepository.findBusinessUnitSystemMenu(List.of(systemId), roleId);
                busList.addAll(partial);
            }
            output.setUnidadNegocioSistemaMenu(busList);

            List<UserBU> userBUs = userBURepository.findByUserId(input.getUsuarioId());

            List<RoleObtainUserOutput.UsuarioUnDto> userUnDtoList = userBUs.stream().map(u -> {
                RoleObtainUserOutput.UsuarioUnDto dto = new RoleObtainUserOutput.UsuarioUnDto();
                dto.setUnidadNegocioId(u.getBusinessUnit().getId());
                return dto;
            }).toList();
            output.setUsuarioUn(userUnDtoList);

            List<UserConfigBU> userConfigBUs = userConfigBURepository.findByUserId(input.getUsuarioId());

            List<RoleObtainUserOutput.UsuarioConfigUnDto> configUnList = userConfigBUs.stream().map(c -> {
                RoleObtainUserOutput.UsuarioConfigUnDto dto = new RoleObtainUserOutput.UsuarioConfigUnDto();
                dto.setSistemaId(c.getSystem().getId());
                dto.setUnidadNegocioId(c.getBusinessUnit().getId());
                dto.setUnidadNegocioHijoId(c.getChildBusinessUnit() != null ? c.getChildBusinessUnit().getId() : null);
                return dto;
            }).toList();
            output.setUsuarioConfigUn(configUnList);
        } catch (Exception e) {
            logger.error("Error in obtenerRolUsuario", e);
            throw e;
        }
        return output;
    }
}