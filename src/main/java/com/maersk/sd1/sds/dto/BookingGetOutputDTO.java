package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

@Data
public class BookingGetOutputDTO {
    @JsonProperty("bookings")
    private List<BookingGetOutputRecordsDTO> bookings;

    @JsonProperty("details")
    private List<BookingGetOutputDetailsDTO> details;
}
