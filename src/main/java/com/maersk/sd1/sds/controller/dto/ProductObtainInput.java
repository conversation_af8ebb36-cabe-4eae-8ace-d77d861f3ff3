package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ProductObtainInput {

    @Data
    public static class Input {
        @NotNull
        @JsonProperty("producto_id")
        private Integer productId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }

    private ProductObtainInput() {
        // private constructor to hide the implicit public one
    }
}