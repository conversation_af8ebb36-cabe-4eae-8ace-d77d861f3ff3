package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.ServiceRegisterInput;
import com.maersk.sd1.seg.dto.ServiceRegisterOutput;
import com.maersk.sd1.seg.service.ServiceRegisterService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMServicioServiceImp")
public class ServiceRegisterController {

    private static final Logger logger = LogManager.getLogger(ServiceRegisterController.class);

    private final ServiceRegisterService serviceRegisterService;

    @PostMapping("/segservicioRegister")
    public ResponseEntity<ResponseController<ServiceRegisterOutput>> serviceRegister(@RequestBody @Valid ServiceRegisterInput.Root request) {
        try {

            if(request==null || request.getPrefix()==null || request.getPrefix().getInput()==null){
                logger.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
            }

            logger.info("Request received serviceRegister: {}", request);
            ServiceRegisterInput.Input input = request.getPrefix().getInput();
            ServiceRegisterOutput output = serviceRegisterService.registerService(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing serviceRegister request.", e);
            ServiceRegisterOutput output = new ServiceRegisterOutput();
            output.setRespMessage(e.toString());
            output.setRespResult(0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}
