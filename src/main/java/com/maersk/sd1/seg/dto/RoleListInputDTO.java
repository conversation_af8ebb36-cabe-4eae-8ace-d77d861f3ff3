package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * Mirrors the input parameters for listing Roles (rol_listar) but wrapped in
 * a nested structure similar to the sample code.
 */
public class RoleListInputDTO {

    @Data
    public static class Input {

        @JsonProperty("nombre")
        @Size(max = 100)
        private String nombre;

        @JsonProperty("id")
        @Size(max = 20)
        private String id;

        /**
         * Comma-separated list of unidad_negocio_id (e.g. "1,2,3").
         */
        @JsonProperty("unidades")
        private String unidades;

        /**
         * JSON Array string for menu IDs (e.g. "[1,2,3]").
         */
        @JsonProperty("menus_defecto")
        private String menusDefecto;

        @JsonProperty("page")
        @Positive
        private Integer page;

        @JsonProperty("size")
        @Positive
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull(message = "Input prefix cannot be null")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("ROL")
        @NotNull(message = "Root object cannot be null")
        private Prefix prefix;
    }
}