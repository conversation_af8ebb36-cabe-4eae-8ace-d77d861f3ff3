package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.ReportEditInput;
import com.maersk.sd1.ges.dto.ReportEditOutput;
import com.maersk.sd1.ges.service.ReportEditService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMReporteServiceImp")
public class ReportEditController {

    private static final Logger logger = LogManager.getLogger(ReportEditController.class);

    private final ReportEditService reportEditService;

    @Autowired
    public ReportEditController(ReportEditService reportEditService) {
        this.reportEditService = reportEditService;
    }

    @PostMapping("/gesreporteEditar")
    public ResponseEntity<ResponseController<ReportEditOutput>> editReport(@RequestBody @Valid ReportEditInput.Root request) {
        try {
            ReportEditInput.Input input = request.getPrefix().getInput();
            ReportEditOutput output = reportEditService.updateReport(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            ReportEditOutput output = new ReportEditOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}