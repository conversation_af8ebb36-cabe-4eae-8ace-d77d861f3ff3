package com.maersk.sd1.sds.dto;

import lombok.Data;

@Data
public class EIRFindStock {

    private Integer eirId;
    private String businessUnitName;
    private String catMovementTypeVar2;
    private String catEmptyFullDesc;

    public EIRFindStock(Integer eirId, String businessUnitName, String catMovementTypeVar2, String catEmptyFullDesc) {
        this.eirId = eirId;
        this.businessUnitName = businessUnitName;
        this.catMovementTypeVar2 = catMovementTypeVar2;
        this.catEmptyFullDesc = catEmptyFullDesc;
    }

}
