package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class DeleteEstimatedPhotoInput {

    private DeleteEstimatedPhotoInput() {}

    @Data
    public static class Input {

        @JsonProperty("adjunto_id")
        @NotNull
        private Integer adjuntoId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}

