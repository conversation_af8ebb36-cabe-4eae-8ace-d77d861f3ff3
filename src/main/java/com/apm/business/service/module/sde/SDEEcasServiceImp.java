package com.apm.business.service.module.sde;

import java.util.ArrayList;
import java.util.HashMap;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohRest.util.bean.Response;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sde/SDEEcasServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEEcasServiceImp extends SDEEcasService {

	// Llamadas de API's Externas al Sistema
	@RequestMapping(value = "/sdeecasOpacifCreaBl", method = { RequestMethod.POST })
	@JpoClass(oauth2Enable = false)
	public Object sdeecasOpacifCreaBl(Jpo ppo, HttpServletRequest request) throws Exception {
		Response response = (Response) super.sdeecasOpacifCreaBl(ppo, request); 
		ArrayList<Object> resultArray = (ArrayList<Object>) response.getResult();
		HashMap<String, Object> resultJson = new HashMap<>();
		resultJson.put("ExitoFracaso", resultArray.get(0));
		resultJson.put("MensajeFracaso", resultArray.get(1));
		response.setResult(resultJson);			
		return response;
	}

}