package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.PastOrPresent;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.time.LocalDateTime;

@UtilityClass
public class AutoReportRegisterInput {

    @Data
    public static class Input {
        @JsonProperty("sub_business_unit_id")
        @NotNull
        private Integer subBusinessUnitId;

        @JsonProperty("report_start_date")
        @PastOrPresent
        private LocalDateTime reportStartDate=LocalDateTime.now();

        @JsonProperty("report_end_date")
        private LocalDateTime reportEndDate;

        @JsonProperty("recurrency_cat_id")
        @NotNull
        private Integer recurrencyCatId;

        @JsonProperty("columns")
        @Size(max = 10000)
        private String columns;

        @JsonProperty("filter_params")
        private String filterParams;

        @JsonProperty("alias")
        @Size(max = 50)
        @NotNull
        private String alias;

        @JsonProperty("report_id")
        @NotNull
        private Integer reportId;

        @JsonProperty("email_template_id")
        @NotNull
        private Integer emailTemplateId;

        @JsonProperty("depot_id")
        @NotNull
        private Integer depotId;

        @JsonProperty("active")
        @NotNull
        private String active;

        @JsonProperty("user_registration_id")
        @NotNull
        private Integer userRegistrationId;

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;

        @JsonProperty("host_local")
        @Size(max = 100)
        private String hostLocal;

        @JsonProperty("email_subject")
        @Size(max = 200)
        private String emailSubject;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}
