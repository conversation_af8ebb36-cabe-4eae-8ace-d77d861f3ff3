package com.maersk.sd1.sdg.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdg.controller.dto.DocumentChassisGateoutGeneratedInput;
import com.maersk.sd1.sdg.controller.dto.DocumentChassisGateoutGeneratedOutput;
import com.maersk.sd1.sdg.dto.ChassisDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.maersk.sd1.common.Parameter.*;

@Service
public class DocumentChassisGateoutGeneratedService {

    private final BusinessUnitRepository businessUnitRepository;
    private final CatalogRepository catalogRepository;
    private final ChassisDocumentDetailRepository chassisDocumentDetailRepository;
    private final ChassisRepository chassisRepository;
    private final ChassisDocumentRepository chassisDocumentRepository;
    private final ChassisBookingDocumentRepository chassisBookingDocumentRepository;

    @Autowired
    public DocumentChassisGateoutGeneratedService(BusinessUnitRepository businessUnitRepository, CatalogRepository catalogRepository,
                                                  ChassisDocumentDetailRepository chassisDocumentDetailRepository,
                                                  @Qualifier("chassisRepository") ChassisRepository chassisRepository,
                                                  ChassisDocumentRepository chassisDocumentRepository, ChassisBookingDocumentRepository chassisBookingDocumentRepository) {
        this.businessUnitRepository = businessUnitRepository;
        this.catalogRepository = catalogRepository;
        this.chassisDocumentDetailRepository = chassisDocumentDetailRepository;
        this.chassisRepository = chassisRepository;
        this.chassisDocumentRepository = chassisDocumentRepository;
        this.chassisBookingDocumentRepository = chassisBookingDocumentRepository;
    }


    @Transactional
    public DocumentChassisGateoutGeneratedOutput generateChassisGateoutDocument(DocumentChassisGateoutGeneratedInput.Root inputRoot) {
        Integer localSubBusinessUnitId = inputRoot.getInput().getSubBusinessUnitLocalId();
        Integer userRegistrationId = inputRoot.getInput().getUserRegistrationId();
        Integer languageId = inputRoot.getInput().getLanguageId();
        Integer subBusinessUnitId = businessUnitRepository.findParentBusinessUnitId(localSubBusinessUnitId);
        Integer businessUnitId = businessUnitRepository.findParentBusinessUnitId(subBusinessUnitId);
        Integer catMovementTypeId = catalogRepository.findCatalogIdByAliasAndStatus(CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS, Boolean.TRUE);

        List<ChassisDTO> chassisList = parseChassisJson(inputRoot.getInput().getChassis());
        Integer docChassisGateInId = chassisDocumentDetailRepository.findDocumentChassisIdByDetailId(
                chassisList.getFirst().getDocumentChassisDetailId());
        Integer chassisGateInId = chassisList.getFirst().getChassisId();
        Integer chassisGateInTypeId = chassisRepository.findChassisTypeById(chassisGateInId);
        String chassisGateInTypeName = catalogRepository.findDescriptionById(chassisGateInTypeId);
        List<Object[]> chassisDetails = chassisDocumentRepository.findDocChassisDetailsById(docChassisGateInId);

        String docChassisNumber = chassisDetails.getFirst()[0] + "-OUT";
        Integer customerCompanyId = (Integer) chassisDetails.getFirst()[1];
        Integer catOperationTypeId = catalogRepository.findCatalogIdByAliasAndStatus(SD1_CHASSIS_OPETYPE_CUSDELIVERY, Boolean.TRUE);
        Integer catReferenceTypeId = catalogRepository.findCatalogIdByAliasAndStatus(SD1_CHASSISDOC_REFTYPE_BK, Boolean.TRUE);

        String jsonGateOutChassisList = String.format(
                "[{\"cat_chassis_type_id\":%d,\"cat_chassis_type_name\":\"%s\",\"quanty\":1,\"index\":1}]",
                chassisGateInTypeId, chassisGateInTypeName
        );

        try {
            Integer documentChassisId = chassisDocumentRepository.findActiveDocumentChassis(docChassisNumber, subBusinessUnitId);
            if (documentChassisId == null) {

                Map<String, Object> result = chassisDocumentRepository.callDocumentChassisRegisterGateout(businessUnitId, subBusinessUnitId,
                        jsonGateOutChassisList, "sd1_creationsource_chassis_gate", "gateout for gatein chassis",
                        userRegistrationId, catReferenceTypeId, docChassisNumber, customerCompanyId,
                        catOperationTypeId, languageId);
                Pageable pageable = PageRequest.of(0, 1, Sort.by(Sort.Order.desc("id")));

                List<Integer> chassisDocumentIds = chassisDocumentRepository.findLatestDocumentChassisId(userRegistrationId,
                        subBusinessUnitId, catMovementTypeId, pageable);
                Integer chassisDocumentId = null;
                if(!CollectionUtils.isEmpty(chassisDocumentIds)) {
                    chassisDocumentId = chassisDocumentIds.getFirst();
                }
                Integer respResult = (Integer) result.get("resp_result");
                String respMessage = (String) result.get("resp_message");

                return DocumentChassisGateoutGeneratedOutput.builder()
                        .docGateoutId(chassisDocumentId)
                        .chassisId(chassisGateInId)
                        .state(respResult)
                        .message(respMessage)
                        .build();
            } else {
                return processExistingGateOut(chassisList, documentChassisId, userRegistrationId);
            }

        } catch (Exception e) {
            return DocumentChassisGateoutGeneratedOutput.builder()
                    .state(0)
                    .message("Error: " + e.getMessage())
                    .build();
        }
    }

    private List<ChassisDTO> parseChassisJson(String chassisJson) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode node = objectMapper.readTree(chassisJson);
            if (node.isArray()) {
                return objectMapper.readValue(chassisJson, objectMapper.getTypeFactory().constructCollectionType(List.class, ChassisDTO.class));
            } else if (node.isObject()) {
                ChassisDTO singleItem = objectMapper.readValue(chassisJson, ChassisDTO.class);
                return Collections.singletonList(singleItem);
            } else {
                throw new IllegalArgumentException("Invalid Chassis JSON input: expected array or object.");
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid Chassis JSON input.", e);
        }
    }

    private DocumentChassisGateoutGeneratedOutput processExistingGateOut(
            List<ChassisDTO> chassisDataList, Integer documentChassisId,
            Integer userRegistrationId) {

        Integer catSourceCreationId = catalogRepository.findIdByAlias(SD1_CREATIONSOURCE_CHASSISDOC);
        Integer catStatusChassisId = catalogRepository.findIdByAlias(CATALOG_CHASSIS_DOCUMENT_PENDING);

        for (ChassisDTO chassisData : chassisDataList) {
            List<Integer> bookingIds = chassisBookingDocumentRepository.findBookingIds(documentChassisId, chassisData.getChassisTypeId());
            Integer bookingId = (CollectionUtils.isEmpty(bookingIds)) ? null : bookingIds.getLast();

            if (bookingId == null) {
                ChassisBookingDocument chassisBookingDocument = ChassisBookingDocument.builder()
                        .chassisDocument(ChassisDocument.builder().id(documentChassisId).build())
                        .catChassisType(Catalog.builder().id(chassisData.getChassisTypeId()).build())
                        .registrationUser(User.builder().id(userRegistrationId).build())
                        .quanty(1)
                        .active(true)
                        .registrationDate(LocalDateTime.now())
                        .build();
                ChassisBookingDocument savedBooking = chassisBookingDocumentRepository.save(chassisBookingDocument);
                bookingId = savedBooking.getId();
            } else {
                chassisBookingDocumentRepository.updateBookingQuantity(bookingId);
            }

            ChassisDocumentDetail chassisDocumentDetail = ChassisDocumentDetail.builder()
                    .chassisDocument(ChassisDocument.builder().id(documentChassisId).build())
                    .catSourceCreation(Catalog.builder().id(catSourceCreationId).build())
                    .catStatusChassis(Catalog.builder().id(catStatusChassisId).build())
                    .chassisBookingDocument(ChassisBookingDocument.builder().id(bookingId).build())
                    .active(Boolean.TRUE)
                    .registrationUser(User.builder().id(userRegistrationId).build())
                    .registrationDate(LocalDateTime.now())
                    .traceChassisDocDetail("sdg.doc_cha_go_ge")
                    .build();
            chassisDocumentDetailRepository.save(chassisDocumentDetail);
        }
        return DocumentChassisGateoutGeneratedOutput.builder()
                .docGateoutId(documentChassisId)
                .chassisId(chassisDataList.getFirst().getChassisId())
                .state(1)
                .message("Updated correctly")
                .build();
    }
}
