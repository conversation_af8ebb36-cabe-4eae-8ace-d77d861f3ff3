package com.maersk.sd1.ges.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class CatalogListInput {

    @Data
    public static class Input {

        @JsonProperty("catalogo_id")
        private Integer catalogId;

        @JsonProperty("alias")
        @Size(max = 50)
        private String alias;

        @JsonProperty("codigo")
        @Size(max = 50)
        private String code;

        @JsonProperty("descripcion")
        @Size(max = 100)
        private String description;

        @JsonProperty("descricion_larga")
        @Size(max = 200)
        private String longDescription;

        @JsonProperty("estado")
        @Size(max = 1)
        private String status;

        @JsonProperty("variable_1")
        @Size(max = 3)
        private String variable1;

        @JsonProperty("variable_2")
        private String variable2;

        @JsonProperty("variable_3")
        private Integer variable3;

        @JsonProperty("page")
        @NotNull
        @Min(1)
        private Integer page;

        @JsonProperty("size")
        @NotNull
        @Min(1)
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}
