package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

public class DemurrageDateRegisterInput {

    @Data
    public static class ContainerInfo {

        @JsonProperty("contenedor_id")
        @NotNull(message = "Container ID cannot be null")
        private Integer containerId;

        @JsonProperty("fecha_sobrestadia")
        @NotEmpty(message = "Demurrage date cannot be empty")
        private String demurrageDate;
    }

    @Data
    public static class Input {

        @JsonProperty("sub_unidad_negocio_id")
        @NotNull(message = "sub_unidad_negocio_id is required")
        private Long subUnidadNegocioId;

        @JsonProperty("documento_carga_id")
        @NotNull(message = "documento_carga_id is required")
        private Integer documentoCargaId;

        @JsonProperty("contenedores")
        @NotNull(message = "contenedores list is required")
        @NotEmpty(message = "contenedores list must not be empty")
        private List<ContainerInfo> contenedores;

        @JsonProperty("usuario_registro_id")
        @NotNull(message = "usuario_registro_id is required")
        private Long usuarioRegistroId;

        @JsonProperty("motivo_id")
        @NotNull(message = "motivo_id is required")
        private Long motivoId;

        @JsonProperty("comentario_usuario")
        private String comentarioUsuario;

        @JsonProperty("idioma_id")
        @NotNull(message = "idioma_id is required")
        private Integer idiomaId;
    }

    @Data
    public static class Prefix {

        @JsonProperty("F")
        @Valid
        private Input input;
    }

    @Data
    public static class Root {

        @JsonProperty("SDE")
        @Valid
        private Prefix prefix;
    }
}
