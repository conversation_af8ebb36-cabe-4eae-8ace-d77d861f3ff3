package com.maersk.sd1.sdg.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.repository.GesReglaSistemaRepository;
import com.maersk.sd1.sdg.controller.dto.GateGeneralAppointmentValidateInput;
import com.maersk.sd1.sdg.controller.dto.GateGeneralAppointmentValidateOutput;
import com.maersk.sd1.seg.repository.SegUnidadNegocioRepository;
import com.maersk.sd1.sdg.exception.GateGeneralAppointmentValidateException;  // Import the custom exception
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class GateGeneralAppointmentValidateService {
    private final SegUnidadNegocioRepository segUnidadNegocioRepository;
    private final GesReglaSistemaRepository gesReglaSistemaRepository;

    @Autowired
    public GateGeneralAppointmentValidateService(SegUnidadNegocioRepository segUnidadNegocioRepository,
                                                 GesReglaSistemaRepository gesReglaSistemaRepository) {
        this.segUnidadNegocioRepository = segUnidadNegocioRepository;
        this.gesReglaSistemaRepository = gesReglaSistemaRepository;
    }

    public ResponseEntity<ResponseController<GateGeneralAppointmentValidateOutput>>
    gateGeneralAppointmentValidateService(GateGeneralAppointmentValidateInput.Root input) {
        Integer subBusinessUnitLocalId = input.getPrefix().getInput().getSubBusinessUnitLocalId();
        String typeProcess = input.getPrefix().getInput().getTypeProcess();
        String excludeShippingLine = input.getPrefix().getInput().getExcludeShippingLine();

        String subBusinessUnitLocalAlias = segUnidadNegocioRepository.findAliasByUnidadNegocioId(subBusinessUnitLocalId);
        String reglaJson = gesReglaSistemaRepository.findReglaById(Parameter.SD1_RULE_INETRATION_APS);

        ObjectMapper objectMapper = new ObjectMapper();
        List<Map<String, String>> reglaList;
        try {
            reglaList = objectMapper.readValue(reglaJson, new TypeReference<List<Map<String, String>>>() {});
        } catch (JsonProcessingException e) {
            throw new GateGeneralAppointmentValidateException("Error parsing the regla JSON", e);
        }

        for (Map<String, String> dict : reglaList) {
            if (subBusinessUnitLocalAlias != null && dict != null && typeProcess != null &&
                    (dict.get(Parameter.EXCLUDE_SHIPPING_LINES) == null ||
                            (excludeShippingLine == null || !dict.get(Parameter.EXCLUDE_SHIPPING_LINES).contains(excludeShippingLine))) &&
                    subBusinessUnitLocalAlias.equals(dict.get(Parameter.SUB_BUSINESS_UNIT_LOCAL_ALIAS)) &&
                    typeProcess.equals(dict.get(Parameter.TYPE_PROCESS))) {

                GateGeneralAppointmentValidateOutput output = new GateGeneralAppointmentValidateOutput();
                output.setSubBusinessUnitLocalAlias(subBusinessUnitLocalAlias);
                output.setTypeProductIntegration(dict.get("type_product_integration"));
                return ResponseEntity.ok(new ResponseController<>(output));
            }
        }

        return ResponseEntity.ok(new ResponseController<>(Constants.NO_DATA_FOUND));
    }
}
