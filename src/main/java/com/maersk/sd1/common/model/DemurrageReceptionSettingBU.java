package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.DemurrageReceptionSetting;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "seteo_recepcion_sobrestadia_un", schema = "sde")
public class DemurrageReceptionSettingBU {
    @Id
    @Column(name = "seteo_recepcion_sobrestadia_un_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "seteo_recepcion_sobreestadia_id", nullable = false)
    private DemurrageReceptionSetting seteoRecepcionSobreestadia;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sub_unidad_negocio_id", nullable = false)
    private BusinessUnit subBusinessUnit;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "linea_naviera_id")
    private ShippingLine shippingLine;

    @NotNull
    @Column(name = "es_restrictivo_ope", nullable = false)
    private Boolean isRestrictiveOpe = false;

    @NotNull
    @Column(name = "es_restrictivo_billing", nullable = false)
    private Boolean isRestrictiveBilling = false;

}