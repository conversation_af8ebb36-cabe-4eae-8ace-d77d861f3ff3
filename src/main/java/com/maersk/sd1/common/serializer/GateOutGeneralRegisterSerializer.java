package com.maersk.sd1.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.maersk.sd1.common.utils.Utils;
import com.maersk.sd1.sdg.controller.dto.GateOutGeneralRegisterOutput;

import java.io.IOException;

public class GateOutGeneralRegisterSerializer extends StdSerializer<GateOutGeneralRegisterOutput> {

    public GateOutGeneralRegisterSerializer() {this(null);}

    public GateOutGeneralRegisterSerializer(Class<GateOutGeneralRegisterOutput> t) {super(t);}

    @Override
    public void serialize(GateOutGeneralRegisterOutput value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        gen.writeObject(Utils.toObjectArray(value));
    }
}
