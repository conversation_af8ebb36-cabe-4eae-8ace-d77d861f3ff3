package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class BuscarEstimadosAsociadosOutput {

    @JsonProperty("details")
    private List<BuscarEstimadosAsociadosOutputDetail> details = new ArrayList<>();

    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class BuscarEstimadosAsociadosOutputDetail {

        @JsonProperty("fecha_inspeccion")
        private String fechaInspeccion;

        @JsonProperty("tipo_estimado")
        private String tipoEstimado;

        @JsonProperty("estimado_id")
        private String estimadoId;

        @JsonProperty("estado_estimado")
        private String estadoEstimado;
    }
}