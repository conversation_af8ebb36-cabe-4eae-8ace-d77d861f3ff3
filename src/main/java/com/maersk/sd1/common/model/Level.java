package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Cell;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "nivel", schema = "sdy", indexes = {
        @Index(name = "nci_msft_1_nivel_437A64734E8C3443F1AF7C96EA4EE379", columnList = "activo, celda_id")
})
public class Level {
    @Id
    @Column(name = "nivel_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "celda_id", nullable = false)
    private Cell cell;

    @NotNull
    @Column(name = "indice", nullable = false)
    private Integer index;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    public Level(Integer levelId) {
        this.id = levelId;
    }
}