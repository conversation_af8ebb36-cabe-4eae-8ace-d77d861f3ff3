name: <PERSON>elop - B<PERSON> and Release

# When this action will be executed
on:
  # Automatically trigger it when detected changes in repo
  push:
    branches: [Develop]
    paths:
      - "**"

  # Allow mannually trigger
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      folderprop: property_files
      BUILD_VERSION: ${{ github.sha }}

    steps:
      - name: Checkout to the branch
        uses: actions/checkout@v2

      - name: Create folder before download
        run: |
          mkdir docker maven properties properties/${{ env.folderprop }}

      - name: Set up and Run Azure CLI
        uses: azure/CLI@v1
        with:
          inlineScript: |
            az storage blob download \
              --connection-string "${{ secrets.AZ_STORAGE_CONFIG_CS_DEV }}" \
              --container-name "configs" \
              --name "docker/Dockerfile" \
              --file Dockerfile

            az storage blob download \
              --connection-string "${{ secrets.AZ_STORAGE_CONFIG_CS_DEV }}" \
              --container-name "configs" \
              --name "${{ env.folderprop }}/application.properties" \
              --file application.properties

            az storage blob download \
              --connection-string "${{ secrets.AZ_STORAGE_CONFIG_CS_DEV }}" \
              --container-name "configs" \
              --name "${{ env.folderprop }}/applicationSDE.properties" \
              --file applicationSDE.properties

      - name: Copy files and replace Path
        run: |
          cp ${{ github.workspace }}/application.properties ${{ github.workspace }}/src/main/resources
          cp ${{ github.workspace }}/applicationSDE.properties ${{ github.workspace }}/src/main/resources
          sed -ir "s/^[#]*\s*server.servlet.contextPath=.*/server.servlet.contextPath=\/${{ env.contextPath }}/" ${{ github.workspace }}/src/main/resources/application.properties

      - name: maven-settings-xml-action
        uses: whelk-io/maven-settings-xml-action@v20
        with:
          repositories: '[{ "id": "mvn-repo", "url": "https://maven.pkg.github.com/Maersk-Global/ohRest" }]'
          servers: '[{ "id": "mvn-repo", "username": "${{ secrets.GIT_USER }}", "password": "${{ secrets.GIT_PASSWORD }}" }]'
          output_file: maven/settings.xml

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Log in to container registry
        uses: docker/login-action@v1
        with:
          registry: acrsd1dev.azurecr.io
          username: acrsd1dev
          password: ${{ secrets.ACR_PASSWORD_DEV }}

      - name: Build and push container image to registry
        uses: docker/build-push-action@v2
        with:
          push: true
          tags: acrsd1dev.azurecr.io/sd1-modulesde:${{ env.BUILD_VERSION }}
          file: ./Dockerfile
          context: ./

      - name: Login to k8s cluster
        uses: Maersk-Global/github-actions-commons/kubectl-login@main
        with:
          k8s-cluster: ane-dev-shared-eu2-01
          vault-name: sd1-kv
          vault-role-id: 87d65b80-5aba-9e21-e4f7-08e0d1949ffd
          vault-role-secret: ${{ secrets.VAULT_ROLE_SECRET }}

      - name: Update deployment resource and deploy
        run: |
          sed -i "s|BUILD_VERSION_PLACEHOLDER|${{ env.BUILD_VERSION }}|g" ./k8s/dev/deploy-modulesde.yaml
          kubectl apply -k ./k8s/dev/


      - name: Wait for rollout to complete
        run: |
          echo "Waiting for deployment rollout to complete..."
          kubectl rollout status deployment/modulesde -n sd1-dev --timeout=420s || {
            echo "❌ Rollout failed. Dumping recent events for debugging:"
            kubectl get events -n sd1-dev --sort-by='.lastTimestamp' | tail -n 20
            exit 1
          }
