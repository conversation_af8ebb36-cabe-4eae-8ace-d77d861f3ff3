package com.apm.business.service.module.sde;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sde/SDEActividadZonaServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEActividadZonaServiceImp extends SDEActividadZonaService {

	@RequestMapping(value = "/sdelistarActividadZonaCnt", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdelistarActividadZonaCnt(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdelistarActividadZonaCnt(ppo, request);
	}

	@RequestMapping(value = "/sderegistrarActividadZonaCnt", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sderegistrarActividadZonaCnt(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sderegistrarActividadZonaCnt(ppo, request);
	}

}