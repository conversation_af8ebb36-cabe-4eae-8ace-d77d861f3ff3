package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BookingEditFileInputDTO {

    @Data
    public static class Input {

        @JsonProperty("booking_id")
        private Integer bookingId;

        @JsonProperty("business_unit_id")
        private Integer businessUnitId;

        @JsonProperty("booking_number")
        private String bookingNumber;

        @JsonProperty("booking_issue_date")
        private String bookingIssueDate;

        @JsonProperty("vessel_programming_detail_id")
        private Integer vesselProgrammingDetailId;

        @JsonProperty("loading_port_id")
        private Integer loadingPortId;

        @JsonProperty("discharge_port_id")
        private Integer dischargePortId;

        @JsonProperty("destination_port_id")
        private Integer destinationPortId;

        @JsonProperty("shipping_line_id")
        private Integer shippingLineId;

        @JsonProperty("client_company_id")
        private Integer clientCompanyId;

        @JsonProperty("shipper_company_id")
        private Integer shipperCompanyId;

        @JsonProperty("empty_depot_id")
        private Integer emptyDepotId;

        @JsonProperty("full_depot_id")
        private Integer fullDepotId;

        @JsonProperty("product_id")
        private Integer productId;

        @JsonProperty("temperature_c")
        private String temperatureC;

        @JsonProperty("imo_id")
        private Integer imoId;

        @JsonProperty("commodity")
        private String commodity;

        @JsonProperty("modification_user_id")
        private Integer modificationUserId;

        @JsonProperty("sub_business_unit_id")
        private Integer subBusinessUnitId;

        @JsonProperty("language_id")
        private Integer languageId;

        @JsonProperty("movement_type_id")
        private Integer movementTypeId;

        @JsonProperty("maersk_depot_with_sd1")
        private Boolean maerskDepotWithSd1;

        @JsonProperty("origin_destination_depot_id")
        private Integer originDestinationDepotId;
    }

    @Data
    public static class Prefix {

        @JsonProperty("F")
        private BookingEditFileInputDTO.Input input;
    }

    @Data
    public static class Root {

        @JsonProperty("SDS")
        private BookingEditFileInputDTO.Prefix prefix;

        public Root() {
            this.prefix = new BookingEditFileInputDTO.Prefix();
            this.prefix.setInput(new BookingEditFileInputDTO.Input());
        }
    }
}
