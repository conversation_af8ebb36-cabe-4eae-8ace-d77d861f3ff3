package com.maersk.sd1.seg.controller;

import com.maersk.sd1.seg.dto.LoginSyncInputDTO;
import com.maersk.sd1.seg.dto.LoginSyncOutputDTO;
import com.maersk.sd1.seg.service.LoginSyncService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSEG/module/seg/UserService")
public class LoginSyncController {

    private static final Logger logger = LogManager.getLogger(LoginSyncController.class.getName());

    private final LoginSyncService loginSyncService;

    @PostMapping("/segloginSincronizar")
    public ResponseEntity<ResponseController<LoginSyncOutputDTO>> loginSincronizar(
            @RequestBody @Valid LoginSyncInputDTO.Root request) {
        try {
            logger.info("Request received loginSincronizar: {}", request);
            Integer userId = request.getPrefix().getInput().getUserId();
            Integer systemId = request.getPrefix().getInput().getSystemId();

            LoginSyncOutputDTO output = loginSyncService.loginSincronizar(userId, systemId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing loginSincronizar.", e);
            LoginSyncOutputDTO output = new LoginSyncOutputDTO();
            // Indicate something is wrong in output.
            output.setUsuarioId(null);
            output.setCodigo(null);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

