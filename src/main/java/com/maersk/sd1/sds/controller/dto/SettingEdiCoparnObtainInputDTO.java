package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class SettingEdiCoparnObtainInputDTO {

    @Data
    public static class Input {
        @JsonProperty("seteo_edi_coparn_id")
        @NotNull
        private Integer settingEdiCoparnId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }


    private SettingEdiCoparnObtainInputDTO() {
        // Private constructor to hide the implicit public one
    }
}