package com.maersk.sd1.sde.controller;

import com.maersk.sd1.sde.dto.ComponentSearchInput;
import com.maersk.sd1.sde.dto.ComponentSearchOutput;
import com.maersk.sd1.sde.service.ComponentSearchService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEMercPlusServiceImp")
public class ComponentSearchController {

    private static final Logger logger = LogManager.getLogger(ComponentSearchController.class);

    private final ComponentSearchService componentSearchService;

    public ComponentSearchController(ComponentSearchService componentSearchService) {
        this.componentSearchService = componentSearchService;
    }

    @PostMapping("/sdemercplusComponentSearch")
    public ResponseEntity<ResponseController<List<ComponentSearchOutput>>> searchComponent(
            @RequestBody @Valid ComponentSearchInput.Root request) {
        try {
            logger.info("Received search request.");

            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null ||
                    request.getPrefix().getInput().getComponent() == null) {
                throw new IllegalArgumentException("Invalid request data");
            }

            String component = null;
            component = request.getPrefix().getInput().getComponent();

            List<ComponentSearchOutput> output = componentSearchService.searchComponents(component);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            ComponentSearchOutput output = new ComponentSearchOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(List.of(output)));
        }
    }
}