package com.maersk.sd1.sde.controller;


import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.StockInventoryReportInput;
import com.maersk.sd1.sde.dto.StockInventoryReportOutput;
import com.maersk.sd1.sde.service.StockInventoryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;


class StockInventoryControllerTest {

    @Mock
    private StockInventoryService stockInventoryService;

    @InjectMocks
    private StockInventoryController stockInventoryController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void GivenValidInputs_WhenGetStockInventoryReport_ThenReturnSuccessResponse() {
        // Given
        StockInventoryReportInput.Root request = new StockInventoryReportInput.Root();
        StockInventoryReportInput.Input input = new StockInventoryReportInput.Input();
        StockInventoryReportInput.Prefix prefix = new StockInventoryReportInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);

        StockInventoryReportOutput reportOutput = new StockInventoryReportOutput();
        when(stockInventoryService.getStockInventoryReport(input)).thenReturn(reportOutput);

        // When
        ResponseEntity<ResponseController<StockInventoryReportOutput>> response = stockInventoryController.report06StockInventory(request);

        // Then
        assertEquals(ResponseEntity.ok(new ResponseController<>(reportOutput)), response);
        verify(stockInventoryService, times(1)).getStockInventoryReport(input);
    }

    @Test
    void GivenInvalidInputs_WhenGetStockInventoryReport_ThenReturnException() {
        // Given
        StockInventoryReportInput.Root request = new StockInventoryReportInput.Root();
        StockInventoryReportInput.Input input = new StockInventoryReportInput.Input();
        StockInventoryReportInput.Prefix prefix = new StockInventoryReportInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(stockInventoryService.getStockInventoryReport(input)).thenThrow(new RuntimeException("Error"));

        // When
        ResponseEntity<ResponseController<StockInventoryReportOutput>> response = stockInventoryController.report06StockInventory(request);

        // Then
        assertEquals(500, response.getStatusCode().value());
        StockInventoryReportOutput errorOutput = new StockInventoryReportOutput();
        errorOutput.setTotalRecords(0L);
        errorOutput.setInventoryList(null);
        assertEquals(new ResponseController<>(errorOutput), response.getBody());
        verify(stockInventoryService, times(1)).getStockInventoryReport(input);
    }
}
