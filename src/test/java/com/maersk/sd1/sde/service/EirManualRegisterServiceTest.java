package com.maersk.sd1.sde.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sde.dto.RegisterEirManualInput;
import com.maersk.sd1.sde.dto.RegisterEirManualOutput;
import com.maersk.sd1.sde.repository.SdeEirRepository;
import com.maersk.sd1.sde.repository.SdeStockEmptyRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class EirManualRegisterServiceTest {

    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private ParameterSpLogRepository parameterSpLogRepository;
    @Mock
    private MessageLanguageService messageLanguageService;
    @Mock
    private SdeEirRepository eirRepository;
    @Mock
    private ContainerRepository containerRepository;
    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;
    @Mock
    private SystemRuleRepository systemRuleRepository;
    @Mock
    private SdeStockEmptyRepository sdeStockEmptyRepository;
    @Mock
    private IsoCodeRepository isoCodeRepository;
    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    @InjectMocks
    EirManualRegisterService eirManualRegisterService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void registerEirManual_invalidInput() throws JsonProcessingException {
        RegisterEirManualInput.Input input = new RegisterEirManualInput.Input();
        input.setEirTypeId(1);
        input.setMoveTypeId(1);
        input.setIdiomaId(1);

        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        Catalog mockCatalog = Catalog.builder().id(1).build();

        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);
        RegisterEirManualOutput output = eirManualRegisterService.registerEirManual(input);

        assertEquals(2, output.getResultState());
    }

    @Test
    void registerEirManual_invalidInput_noBU() throws JsonProcessingException {
        RegisterEirManualInput.Input input = new RegisterEirManualInput.Input();
        input.setEirTypeId(1);
        input.setMoveTypeId(1);
        input.setIdiomaId(1);
        input.setTruckId(1);
        input.setTruckerCompanyId(1);
        input.setDriverId(1);
        input.setContainerId(1);
        input.setDocumentoCargaDetalleId(1);
        input.setIsoCodeId(1);
        input.setGradeId(1);
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        Catalog mockCatalog = Catalog.builder().id(1).build();

        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);
        RegisterEirManualOutput output = eirManualRegisterService.registerEirManual(input);

        assertEquals(2, output.getResultState());
    }


    @Test
    void registerEirManual_validInput_lastEirTruckInDate_isAfter_ruckInDate() throws JsonProcessingException {
        RegisterEirManualInput.Input input = new RegisterEirManualInput.Input();
        input.setEirTypeId(1);
        input.setMoveTypeId(1);
        input.setIdiomaId(1);
        input.setTruckId(1);
        input.setTruckerCompanyId(1);
        input.setDriverId(1);
        input.setContainerId(1);
        input.setDocumentoCargaDetalleId(1);
        input.setIsoCodeId(1);
        input.setGradeId(1);
        input.setBusinessUnitId(1);
        input.setSubBusinessUnitId(2);
        input.setSubBusinessUnitLocalId(1);
        input.setTruckInDate(LocalDateTime.now());
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        Catalog mockCatalog = Catalog.builder().id(1).build();

        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);

        BusinessUnit mockBusinessUnit = BusinessUnit.builder().id(1).build();
        Eir mockEir = new Eir();
        mockEir.setId(1);
        mockEir.setBusinessUnit(mockBusinessUnit);
        mockEir.setCatMovement(mockCatalog);
        mockEir.setCatEmptyFull(mockCatalog);
        mockEir.setTruckDepartureDate(LocalDateTime.now().plusDays(1));
        when(eirRepository.findLastEirByContainerAndBusinessUnit(anyInt(), anyInt())).thenReturn(Optional.of(mockEir));

        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn("message test");
        RegisterEirManualOutput output = eirManualRegisterService.registerEirManual(input);

        assertEquals(2, output.getResultState());
    }

    @Test
    void registerEirManual_validInput_noShippingLine() throws JsonProcessingException {
        RegisterEirManualInput.Input input = new RegisterEirManualInput.Input();
        input.setEirTypeId(43080);
        input.setMoveTypeId(1);
        input.setIdiomaId(1);
        input.setTruckId(1);
        input.setTruckerCompanyId(1);
        input.setDriverId(1);
        input.setContainerId(1);
        input.setDocumentoCargaDetalleId(1);
        input.setIsoCodeId(1);
        input.setGradeId(1);
        input.setBusinessUnitId(1);
        input.setSubBusinessUnitId(2);
        input.setSubBusinessUnitLocalId(1);
        input.setTruckInDate(LocalDateTime.now());
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        Catalog mockCatalog = Catalog.builder().id(1).build();

        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);

        BusinessUnit mockBusinessUnit = BusinessUnit.builder().id(1).build();
        Eir mockEir = new Eir();
        mockEir.setId(1);
        mockEir.setBusinessUnit(mockBusinessUnit);
        Catalog mockCatalogMovement = new Catalog();
        mockCatalogMovement.setId(43081);
        mockCatalogMovement.setVariable2("43081");
        mockEir.setCatMovement(mockCatalogMovement);
        mockEir.setCatEmptyFull(mockCatalog);
        mockEir.setTruckDepartureDate(LocalDateTime.now().minusDays(1));
        when(eirRepository.findLastEirByContainerAndBusinessUnit(anyInt(), anyInt())).thenReturn(Optional.of(mockEir));

        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn("message test");

        Container mockContainer = new Container();
        mockContainer.setCatContainerType(mockCatalog);
        mockContainer.setCatSize(mockCatalog);
        mockContainer.setCatEngineBrand(mockCatalog);
        mockContainer.setCatReeferType(mockCatalog);
        mockContainer.setValueTare("1");
        ShippingLine mockShippingLine = new ShippingLine();
        mockShippingLine.setId(1);

        IsoCode mockIsoCode = new IsoCode();
        mockIsoCode.setId(1);
        mockContainer.setIsoCode(mockIsoCode);
        mockContainer.setCatGrade(mockCatalog);
        when(containerRepository.findById(anyInt())).thenReturn(Optional.of(mockContainer));

        CargoDocumentDetail mockCargoDocumentDetail = new CargoDocumentDetail();
        mockCargoDocumentDetail.setId(1);
        CargoDocument mockCargoDocument = new CargoDocument();
        mockCargoDocument.setId(1);

        Company mockCompany = new Company();
        mockCompany.setId(1);
        mockCargoDocument.setConsigneeCompany(mockCompany);
        mockCargoDocumentDetail.setCargoDocument(mockCargoDocument);
        when(cargoDocumentDetailRepository.findById(anyInt())).thenReturn(Optional.of(mockCargoDocumentDetail));

        when(systemRuleRepository.findRuleByIdAndActiveTrue(anyString())).thenReturn("[{\"sub_line_id\":4106,\"sub_line_name\":\"Sealand\",\"line_main_id\":4104,\"line_main_name\":\"Maersk Line\"},{\"sub_line_id\":4107,\"sub_line_name\":\"Alianca\",\"line_main_id\":4102,\"line_main_name\":\"Hamburg Sud\"},{\"sub_line_id\":4108,\"sub_line_name\":\"Compañía Chilena de Navegación Interoceánica\",\"line_main_id\":4102,\"line_main_name\":\"Hamburg Sud\"}]");
        when(sdeStockEmptyRepository.findGateInNotInStock(anyInt(),anyInt())).thenReturn(Optional.empty());

        when(isoCodeRepository.findById(anyInt())).thenReturn(Optional.of(mockIsoCode));

        when(eirRepository.save(any())).thenReturn(mockEir);
        RegisterEirManualOutput output = eirManualRegisterService.registerEirManual(input);

        assertEquals(1, output.getResultState());
    }

    @Test
    void registerEirManual_validInput_noShippingLine_EirType_43081() throws JsonProcessingException {
        RegisterEirManualInput.Input input = new RegisterEirManualInput.Input();
        input.setEirTypeId(43081);
        input.setMoveTypeId(1);
        input.setIdiomaId(1);
        input.setTruckId(1);
        input.setTruckerCompanyId(1);
        input.setDriverId(1);
        input.setContainerId(1);
        input.setDocumentoCargaDetalleId(1);
        input.setIsoCodeId(1);
        input.setGradeId(1);
        input.setBusinessUnitId(1);
        input.setSubBusinessUnitId(2);
        input.setSubBusinessUnitLocalId(1);
        input.setTruckInDate(LocalDateTime.now());
        when(catalogRepository.findIdByAlias(anyString())).thenReturn(1);
        Catalog mockCatalog = Catalog.builder().id(1).build();

        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);

        BusinessUnit mockBusinessUnit = BusinessUnit.builder().id(1).build();
        Eir mockEir = new Eir();
        mockEir.setId(1);
        mockEir.setBusinessUnit(mockBusinessUnit);
        Catalog mockCatalogMovement = new Catalog();
        mockCatalogMovement.setId(43081);
        mockCatalogMovement.setVariable2("43081");
        mockEir.setCatMovement(mockCatalogMovement);
        mockEir.setCatEmptyFull(mockCatalog);
        mockEir.setTruckDepartureDate(LocalDateTime.now().minusDays(1));
        when(eirRepository.findLastEirByContainerAndBusinessUnit(anyInt(), anyInt())).thenReturn(Optional.of(mockEir));

        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn("message test");

        Container mockContainer = new Container();
        mockContainer.setCatContainerType(mockCatalog);
        mockContainer.setCatSize(mockCatalog);
        mockContainer.setCatEngineBrand(mockCatalog);
        mockContainer.setCatReeferType(mockCatalog);
        mockContainer.setValueTare("1");
        ShippingLine mockShippingLine = new ShippingLine();
        mockShippingLine.setId(1);

        IsoCode mockIsoCode = new IsoCode();
        mockIsoCode.setId(1);
        mockContainer.setIsoCode(mockIsoCode);
        mockContainer.setCatGrade(mockCatalog);
        when(containerRepository.findById(anyInt())).thenReturn(Optional.of(mockContainer));

        CargoDocumentDetail mockCargoDocumentDetail = new CargoDocumentDetail();
        mockCargoDocumentDetail.setId(1);
        CargoDocument mockCargoDocument = new CargoDocument();
        mockCargoDocument.setId(1);

        Company mockCompany = new Company();
        mockCompany.setId(1);
        mockCargoDocument.setConsigneeCompany(mockCompany);
        mockCargoDocumentDetail.setCargoDocument(mockCargoDocument);
        when(cargoDocumentDetailRepository.findById(anyInt())).thenReturn(Optional.of(mockCargoDocumentDetail));

        when(systemRuleRepository.findRuleByIdAndActiveTrue(anyString())).thenReturn("[{\"sub_line_id\":4106,\"sub_line_name\":\"Sealand\",\"line_main_id\":4104,\"line_main_name\":\"Maersk Line\"},{\"sub_line_id\":4107,\"sub_line_name\":\"Alianca\",\"line_main_id\":4102,\"line_main_name\":\"Hamburg Sud\"},{\"sub_line_id\":4108,\"sub_line_name\":\"Compañía Chilena de Navegación Interoceánica\",\"line_main_id\":4102,\"line_main_name\":\"Hamburg Sud\"}]");
        when(sdeStockEmptyRepository.findGateInNotInStock(anyInt(),anyInt())).thenReturn(Optional.empty());

        when(isoCodeRepository.findById(anyInt())).thenReturn(Optional.of(mockIsoCode));

        when(eirRepository.save(any())).thenReturn(mockEir);
        RegisterEirManualOutput output = eirManualRegisterService.registerEirManual(input);

        assertEquals(2, output.getResultState());
    }
}