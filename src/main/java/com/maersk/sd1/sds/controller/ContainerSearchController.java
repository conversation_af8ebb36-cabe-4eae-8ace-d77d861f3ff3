package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.ContainerSearchInput;
import com.maersk.sd1.sds.controller.dto.ContainerSearchOutput;
import com.maersk.sd1.sds.service.ContainerSearchService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSContenedorServiceImp")
public class ContainerSearchController {

    private static final Logger logger = LogManager.getLogger(ContainerSearchController.class);

    private final ContainerSearchService containerSearchService;

    public ContainerSearchController(ContainerSearchService containerSearchService) {
        this.containerSearchService = containerSearchService;
    }

    @PostMapping("/sdscontenedorListar")
    public ResponseEntity<ResponseController<ContainerSearchOutput>> contenedorListar(
            @RequestBody @Valid ContainerSearchInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
            }
            ContainerSearchInput.Input input = request.getPrefix().getInput();
            ContainerSearchOutput output = containerSearchService.searchContainers(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the container listing.", e);
            ContainerSearchOutput output = new ContainerSearchOutput();
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }
}