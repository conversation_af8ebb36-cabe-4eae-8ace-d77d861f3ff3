package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.YardBySubBusinessUnitInput;
import com.maersk.sd1.sde.dto.YardBySubBusinessUnitOutput;
import com.maersk.sd1.sde.service.YardBySubBusinessUnitService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@Log4j2
@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDE/module/sde/SDEYardSchemaServiceImp")
public class YardBySubBusinessUnitController {

    private final YardBySubBusinessUnitService yardBySubBusinessUnitService;

    @PostMapping("/sdyobtenerPatioBySubUnidadNegocio")
    public ResponseEntity<ResponseController<List<List<Object>>>> obtenerPatioBySubUnidadNegocio(@RequestBody @Valid YardBySubBusinessUnitInput.Root request) {
        try {
            Integer subUnidadNegocioLocalId = request.getPrefix().getInput().getSubUnidadNegocioLocalId();
            YardBySubBusinessUnitOutput output = yardBySubBusinessUnitService.getYardsBySubBusinessUnit(subUnidadNegocioLocalId);

            List<List<Object>> transformedResult = new ArrayList<>();
            if (output.getYards() != null) {
                for (YardBySubBusinessUnitOutput.YardDto yard : output.getYards()) {
                    List<Object> yardData = new ArrayList<>();
                    yardData.add(yard.getYardId());
                    yardData.add(yard.getYardCode());
                    yardData.add(yard.getYardName());
                    transformedResult.add(yardData);
                }
            }

            return ResponseEntity.ok(new ResponseController<>(transformedResult));
        } catch (Exception e) {
            log.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(500).body(new ResponseController<>("Error: " + e.getMessage()));
        }
    }
}