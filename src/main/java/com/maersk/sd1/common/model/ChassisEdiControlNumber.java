package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.ChassisEdiSetting;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "chassis_edi_control_number", schema = "sdh")
public class ChassisEdiControlNumber {
    @Id
    @Column(name = "chassis_edi_control_number_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "chassis_edi_setting_id", nullable = false)
    private ChassisEdiSetting chassisEdiSetting;

    @Size(max = 15)
    @NotNull
    @Column(name = "chassis_edi_sender_id_value", nullable = false, length = 15)
    private String chassisEdiSenderIdValue;

    @NotNull
    @Column(name = "chassis_edi_correlative", nullable = false)
    private Integer chassisEdiCorrelative;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

}