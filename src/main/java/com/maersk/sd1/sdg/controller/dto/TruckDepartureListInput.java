package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class TruckDepartureListInput {

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Input {
        @JsonProperty("sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("sub_unidad_negocio_local_id")
        private Integer localSubBusinessUnitId;

        @JsonProperty("Page")
        private Integer page;

        @JsonProperty("Size")
        private Integer size;

        @JsonProperty("idioma_id")
        private Integer languageId;

        @JsonProperty("show_list_asignment_gate_out")
        private Boolean showListAsignmentGateOut;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Prefix {
        @JsonProperty("F")
        private TruckDepartureListInput.Input input;
    }

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Root {
        @JsonProperty("SDG")
        private TruckDepartureListInput.Prefix sdg;
    }
}
