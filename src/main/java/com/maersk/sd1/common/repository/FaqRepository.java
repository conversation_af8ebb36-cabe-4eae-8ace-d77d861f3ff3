package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Faq;
import org.springframework.data.jpa.repository.JpaRepository;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface FaqRepository extends JpaRepository<Faq, Integer> {

    @Query("select concat(m.title, ' v', f.version) from Faq f join f.menu m join f.businessUnit bu where bu.id = :businessUnitId")
    List<String> findFaqInfoByBusinessUnitId(@Param("businessUnitId") Integer businessUnitId);

    @Modifying
    @Query("DELETE FROM Faq f WHERE f.businessUnit.id = :unidadNegocioId")
    void deleteFaqByBusinessUnit(@Param("unidadNegocioId") Integer unidadNegocioId);

    @Query("SELECT DISTINCT f.businessUnit.id FROM Faq f " +
            "JOIN Menu m ON m.id = f.menu.id " +
            "JOIN RoleMenu rm ON rm.id.menuId = m.id " +
            "JOIN Role ro ON ro.id = rm.id.roleId " +
            "JOIN UserRole ur ON ur.id.roleId = ro.id " +
            "JOIN User uu ON uu.id = ur.id.userId " +
            "WHERE m.parentMenu IS NULL AND f.status = '1' AND m.status = true AND ro.status = true AND uu.id = :userId")
    List<Integer> findBusinessUnitsForFaq(Long userId);
}