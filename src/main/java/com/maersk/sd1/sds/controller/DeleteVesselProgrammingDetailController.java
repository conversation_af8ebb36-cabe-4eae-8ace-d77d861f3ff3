package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.DeleteVesselProgrammingDetailInput;
import com.maersk.sd1.sds.dto.DeleteVesselProgrammingDetailOutput;
import com.maersk.sd1.sds.service.DeleteVesselProgrammingDetailService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProgramacionNaveServiceImp")
public class DeleteVesselProgrammingDetailController {

    private static final Logger logger = LogManager.getLogger(DeleteVesselProgrammingDetailController.class);

    private final DeleteVesselProgrammingDetailService deleteService;

    @PostMapping("/sdseliminarProgramacionNaveOperacion")
    public ResponseEntity<ResponseController<DeleteVesselProgrammingDetailOutput>> sdgdeleteVesselProgrammingDetail(@RequestBody @Valid DeleteVesselProgrammingDetailInput.Root request) {
        try {

            if(request==null || request.getPrefix()==null || request.getPrefix().getInput()==null){
                logger.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
            }

            logger.info("Request received sdgdeleteVesselProgrammingDetail: {}", request);
            DeleteVesselProgrammingDetailInput.Input input = request.getPrefix().getInput();

            DeleteVesselProgrammingDetailOutput output = deleteService.deleteVesselProgrammingDetail(
                    input.getVesselProgrammingDetailId(),
                    input.getUserId(),
                    input.getLanguageId()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            DeleteVesselProgrammingDetailOutput output = new DeleteVesselProgrammingDetailOutput();
            output.setRespMensaje(e.toString());
            output.setRespEstado(0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}
