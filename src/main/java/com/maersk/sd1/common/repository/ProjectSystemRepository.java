package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.ProjectSystem;
import com.maersk.sd1.common.model.SystemProjectId;
import com.maersk.sd1.seg.dto.RoleObtainOutputDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ProjectSystemRepository extends JpaRepository<ProjectSystem, SystemProjectId> {

    @Query("SELECT DISTINCT new com.maersk.sd1.seg.dto.RoleObtainOutputDTO$SystemMenuDto(" +
            "ps.system.id, ps.system.description, ps.system.icon) " +
            "FROM ProjectSystem ps " +
            "JOIN ps.menu m " +
            "JOIN RoleMenu rm ON rm.menu = m " +
            "WHERE rm.role.id = :roleId")
    List<RoleObtainOutputDTO.SystemMenuDto> findDistinctSystemsByRoleId(@Param("roleId") Integer roleId);
}