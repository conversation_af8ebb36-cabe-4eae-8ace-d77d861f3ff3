package com.maersk.sd1.seg.controller;

import com.maersk.sd1.seg.dto.RuleListFullInput;
import com.maersk.sd1.seg.dto.RuleListFullOutput;
import com.maersk.sd1.seg.service.RuleFullListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;

@RestController
@RequestMapping("/ModuleSEG/module/seg/RuleController")
public class RuleController {

    private final RuleFullListService ruleFullListService;

    @Autowired
    public RuleController(RuleFullListService ruleFullListService) {
        this.ruleFullListService = ruleFullListService;
    }

    /**
     * Endpoint to fetch the list of rules based on filters and pagination.
     *
     * @param request The request payload containing all filter parameters.
     * @return ResponseEntity containing the list of rules and total count.
     */
    @PostMapping("/list")
    public ResponseEntity<RuleListFullOutput.Output> getRuleList(
            @RequestBody RuleListFullInput.Root request) {

        RuleListFullInput.Input input = request.getPrefix().getInput();

        LocalDateTime registrationDateMinLocal = parseDate(input.getFechaRegistroMin());
        LocalDateTime registrationDateMaxLocal = parseDate(input.getFechaRegistroMax());
        LocalDateTime modificationDateMinLocal = parseDate(input.getFechaModificacionMin());
        LocalDateTime modificationDateMaxLocal = parseDate(input.getFechaModificacionMax());

        RuleListFullOutput.Output output = ruleFullListService.getRuleList(
                input.getReglaId(), input.getSistemaId(), input.getParametros(), input.getEstado(),
                registrationDateMinLocal, registrationDateMaxLocal,
                modificationDateMinLocal, modificationDateMaxLocal,
                input.getCatReglaId(), input.getPage(), input.getSize()
        );

        return ResponseEntity.ok(output);
    }

    /**
     * Helper method to parse date strings to LocalDateTime.
     *
     * @param date the date to parse
     * @return LocalDateTime or null if parsing fails
     */
    private LocalDateTime parseDate(LocalDate date) {
        try {
            return date != null ? date.atStartOfDay() : null;
        } catch (DateTimeParseException e) {
            return null;
        }
    }
}
