package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.controller.dto.SftpConfigRemoveAttachmentInput;
import com.maersk.sd1.ges.controller.dto.SftpConfigRemoveAttachmentOutput;
import com.maersk.sd1.ges.service.SftpConfigRemoveAttachmentService;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMSftpConfigServiceImp")
public class SftpConfigRemoveAttachmentController {

    private static final Logger logger = LogManager.getLogger(SftpConfigRemoveAttachmentController.class);

    private final SftpConfigRemoveAttachmentService sftpConfigRemoveAttachmentService;

    public SftpConfigRemoveAttachmentController(SftpConfigRemoveAttachmentService sftpConfigRemoveAttachmentService) {
        this.sftpConfigRemoveAttachmentService = sftpConfigRemoveAttachmentService;
    }

    @PostMapping("/gessftpConfigEliminarAdjunto")
    public ResponseEntity<ResponseController<SftpConfigRemoveAttachmentOutput>> removeAttachment(@RequestBody @Valid SftpConfigRemoveAttachmentInput.Root request) {
        SftpConfigRemoveAttachmentOutput output = new SftpConfigRemoveAttachmentOutput();
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure"));
            }
            SftpConfigRemoveAttachmentInput.Input input = request.getPrefix().getInput();
            if(input.getSftpConfigId() == null || input.getUserId() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("configId and userId should not be null"));
            }
            output = sftpConfigRemoveAttachmentService.removeAttachment(
                    input.getSftpConfigId(),
                    input.getKeyType(),
                    input.getUserId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing removeAttachment request.", e);
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}