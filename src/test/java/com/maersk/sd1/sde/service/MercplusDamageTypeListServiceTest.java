package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sde.dto.MercplusDamageTypeListOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

class MercplusDamageTypeListServiceTest {

    @Mock
    private CatalogRepository catalogRepository;

    @InjectMocks
    private MercplusDamageTypeListService mercplusDamageTypeListService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGetDamageTypes() {
        // Arrange
        Catalog parentCatalog = new Catalog();
        parentCatalog.setId(1);
        when(catalogRepository.findByAlias("sd1_mercplus_damage_type")).thenReturn(parentCatalog);

        Catalog childCatalog = new Catalog();
        childCatalog.setId(2);
        childCatalog.setDescription("Description");
        childCatalog.setLongDescription("Long Description");
        List<Catalog> childCatalogs = Collections.singletonList(childCatalog);
        when(catalogRepository.findByStatusTrueAndParentCatalogIdOrderByDescriptionAscLongDescriptionAsc(1)).thenReturn(childCatalogs);

        // Act
        List<MercplusDamageTypeListOutput> result = mercplusDamageTypeListService.getDamageTypes();

        // Assert
        assertEquals(1, result.size());
        assertEquals("Description - Long Description", result.get(0).getDamageType());
    }

    @Test
    void testGetDamageTypes_Empty() {
        // Arrange
        Catalog parentCatalog = new Catalog();
        parentCatalog.setId(1);
        when(catalogRepository.findByAlias("sd1_mercplus_damage_type")).thenReturn(parentCatalog);
        when(catalogRepository.findByStatusTrueAndParentCatalogIdOrderByDescriptionAscLongDescriptionAsc(1)).thenReturn(Collections.emptyList());

        // Act
        List<MercplusDamageTypeListOutput> result = mercplusDamageTypeListService.getDamageTypes();

        // Assert
        assertEquals(0, result.size());
    }

    @Test
    void testGetDamageTypes_Exception() {
        // Arrange
        when(catalogRepository.findByAlias("sd1_mercplus_damage_type")).thenThrow(new RuntimeException("Repository error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            mercplusDamageTypeListService.getDamageTypes();
        });
        assertEquals("Unexpected error while retrieving damage types", exception.getMessage());
    }
}