package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.repository.RoleServiceRepository;
import com.maersk.sd1.seg.dto.ServiceListInput;
import com.maersk.sd1.seg.dto.ServiceListOutput;
import com.maersk.sd1.seg.dto.ServiceListOutput.ServicioListData;
import com.maersk.sd1.seg.dto.ServiceListOutput.RoleData;
import com.maersk.sd1.common.repository.ServiceRepository;
import com.maersk.sd1.seg.dto.ServicioListDTO;
import com.maersk.sd1.seg.dto.ServicioRoleDTO;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class ServiceListService {

    private static final Logger logger = LogManager.getLogger(ServiceListService.class);

    private final ServiceRepository serviceRepository;
    private final RoleServiceRepository roleServiceRepository;

    @Transactional(readOnly = true)
    public ServiceListOutput getServicios(ServiceListInput.Input input) {
        try {
            if (input.getPfPage() == null || input.getPfSize() == null) {
                throw new IllegalArgumentException("Page and size must not be null");
            }

            // Convert servicio_indicador_protegido and servicio_estado to Character
            Character servicioIndicadorProtegido = (input.getServicioIndicadorProtegido() != null)
                    ? input.getServicioIndicadorProtegido()
                    : null;


            // 1) Count total records
            long totalRecords = serviceRepository.countByFilters(
                    input.getServicioId(),
                    input.getServicioNombre(),
                    servicioIndicadorProtegido,
                    input.getServicioEstado(),
                    input.getUsuarioRegistro()
            );

            // 2) Retrieve all (unpaginated) data matching filters
            List<ServicioListDTO> allFilteredData = serviceRepository.findServicioList(
                    input.getServicioId(),
                    input.getServicioNombre(),
                    servicioIndicadorProtegido,
                    input.getServicioEstado(),
                    input.getUsuarioRegistro()
            );

            // Manual pagination
            int fromIndex = (input.getPfPage() - 1) * input.getPfSize();
            int toIndex = Math.min(fromIndex + input.getPfSize(), allFilteredData.size());
            if (fromIndex < 0 || fromIndex >= allFilteredData.size()) {
                fromIndex = 0;
                toIndex = 0;
            }

            List<ServicioListDTO> paginatedData = new ArrayList<>();
            if (fromIndex < toIndex) {
                paginatedData = allFilteredData.subList(fromIndex, toIndex);
            }

            // Build output
            ServiceListOutput output = new ServiceListOutput();
            output.setTotalRegistros(totalRecords);

            List<ServicioListData> resultData = new ArrayList<>();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            for (ServicioListDTO dto : paginatedData) {
                ServicioListData data = new ServicioListData();
                data.setServicioId(dto.getServicioId());
                data.setServicioNombre(dto.getServicioNombre());
                data.setIndicadorProtegido(dto.getIndicadorProtegido());
                data.setEstado(dto.getEstado());
                data.setUsuarioRegistroId(dto.getUsuarioRegistroId());
                data.setFechaRegistro(dto.getFechaRegistro() != null ? dto.getFechaRegistro().format(formatter) : null);
                data.setUsuarioModificacionId(dto.getUsuarioModificacionId());
                data.setFechaModificacion(dto.getFechaModificacion() != null ? dto.getFechaModificacion().format(formatter) : null);
                data.setUsuarioNombres(dto.getUsuarioNombres());
                data.setUsuarioApellidos(dto.getUsuarioApellidos());

                // fetch roles by service
                List<ServicioRoleDTO> rolesDto = roleServiceRepository.findRolesByServiceIdName(dto.getServicioId());
                List<RoleData> roleDataList = new ArrayList<>();
                for (ServicioRoleDTO rDto : rolesDto) {
                    RoleData rd = new RoleData();
                    rd.setRolId(rDto.getRolId());
                    rd.setRolNombre(rDto.getRolNombre());
                    roleDataList.add(rd);
                }
                data.setServiceRoles(roleDataList);

                resultData.add(data);
            }

            output.setListaServicios(resultData);

            return output;
        } catch (Exception e) {
            logger.error("Error occurred while fetching servicio list:", e);
            throw new RuntimeException("Error in ServiceListService: " + e.getMessage(), e);
        }
    }
}

