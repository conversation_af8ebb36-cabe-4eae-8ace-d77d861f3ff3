package com.maersk.sd1.sdf.service;

import com.maersk.sd1.common.model.CargoDocument;
import com.maersk.sd1.common.model.CargoDocumentDetail;
import com.maersk.sd1.common.model.TransportPlanning;
import com.maersk.sd1.common.model.TransportPlanningDetail;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.TransportPlanningDetailRepository;
import com.maersk.sd1.sdf.controller.dto.DocumentationFullListInput;
import com.maersk.sd1.sdf.controller.dto.DocumentationFullListOutput;
import com.maersk.sd1.sdf.controller.dto.DocumentationFullListOutput.DocumentationFullListItemDTO;
import com.maersk.sd1.sdf.dto.DocumentationFullListProcessDTO;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.toList;

@RequiredArgsConstructor
@Service
public class DocumentationFullListService {
    private static final Logger logger = LogManager.getLogger(DocumentationFullListService.class);

    private static final String STATUS_PROGRAMMED_EQUIPMENT = "cat_48746_done"; // cat_48746_programm
    private static final String STATUS_INPROCESS_EQUIPMENT  = "cat_48746_inprocess"; // cat_48746_inprocess
    private static final String STATUS_COMPLETED_EQUIPMENT  = "cat_48746_programm"; // cat_48746_done

    private static final String STATUS_COMPLETED = "sd1_status_completed";
    public static final String STATUS_IN_PROGRESS = "sd1_status_in_progress";
    private static final String STATUS_PENDING = "sd1_status_pending";

    private final TransportPlanningDetailRepository transportPlanningDetailRepository;
    private final CatalogRepository catalogRepository;
    private final MessageLanguageRepository messageLanguageRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;

    @Transactional(readOnly = true)
    public DocumentationFullListOutput getDocumentationFullList(DocumentationFullListInput.Input input) {
        logger.info("Executing getDocumentationFullList with input: {}", input);

        if(input.getPage() == null || input.getSize() == null){
            input.setPage(1);
            input.setSize(10);
        }

        PageRequest pageRequest = PageRequest.of(input.getPage() - 1, input.getSize());

        Page<DocumentationFullListProcessDTO> pageTransportPlanning = transportPlanningDetailRepository.findAllByFilters(
                input.getSubBusinessUnitId(),
                input.getMovementType(),
                input.getShippingLineId(),
                (input.getShipperName() == null ? null : input.getShipperName().trim()),
                (input.getConsigneeName() == null ? null : input.getConsigneeName().trim()),
                (input.getDocumentNumber() == null ? null : input.getDocumentNumber().trim()),
                input.getEtaMin(),
                input.getEtaMax(),
                (input.getContainers() == null || input.getContainers().isEmpty()) ? null : input.getContainers(),
                pageRequest
        );

        DocumentationFullListOutput outputDTO = new DocumentationFullListOutput();
        if(pageTransportPlanning == null || pageTransportPlanning.isEmpty()){
            outputDTO.setTotalRecords(0L);
            outputDTO.setData(new ArrayList<>());
            return outputDTO;
        }
        outputDTO.setTotalRecords(pageTransportPlanning.getTotalElements());

        Long startRowId = pageTransportPlanning.getTotalElements() - (long) (input.getPage() - 1) * input.getSize();

        List<DocumentationFullListItemDTO> listItems = new ArrayList<>();

        Integer optionalStatusId = input.getStatusId();

        DateTimeFormatter dtFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        List<DocumentationFullListProcessDTO> processDTO = pageTransportPlanning.getContent();

        List<TransportPlanning> transportPlanning = processDTO.stream()
                .map(DocumentationFullListProcessDTO::getTransportPlanningDTO)
                .toList();

        Integer statusInprogressId = getStateId(STATUS_INPROCESS_EQUIPMENT);
        Integer statusCompletedId = getStateId(STATUS_COMPLETED_EQUIPMENT);
        Integer statusPendingId = getStateId(STATUS_PROGRAMMED_EQUIPMENT);

        String messageType = getMessage(30, input.getLanguageId());
        String messageRequested = getMessage(31, input.getLanguageId());
        String messagePending = getMessage(33, input.getLanguageId());
        String messageReceived = getMessage(34, input.getLanguageId());

        for (TransportPlanning tp : transportPlanning) {
            DocumentationFullListItemDTO item = new DocumentationFullListItemDTO();
            item.setTransportPlanningId(tp.getId());
            item.setComments(tp.getComments());

            if (tp.getRegistrationUser() != null) {
                item.setUserRegistrationId(tp.getRegistrationUser().getId());
                item.setUserRegistrationName(tp.getRegistrationUser().getNames());
                item.setUserRegistrationLastname(tp.getRegistrationUser().getFirstLastName() + " " +
                        (tp.getRegistrationUser().getSecondLastName() == null ? "" : tp.getRegistrationUser().getSecondLastName()));
                LocalDateTime regDate = tp.getRegistrationDate();
                if (regDate != null) {
                    item.setUserRegistrationDate(regDate.format(dtFormatter));
                }
            }

            if (tp.getCatTrkMoveType() != null) {
                item.setCatTypeId(tp.getCatTrkMoveType().getId());
            }

            if (tp.getModificationUser() != null) {
                item.setUserModificationId(tp.getModificationUser().getId());
                item.setUserModificationName(tp.getModificationUser().getNames());
                item.setUserModificationLastname(tp.getModificationUser().getFirstLastName() + " " +
                        (tp.getModificationUser().getSecondLastName() == null ? "" : tp.getModificationUser().getSecondLastName()));
                LocalDateTime modDate = tp.getModificationDate();
                if (modDate != null) {
                    item.setUserModificationDate(modDate.format(dtFormatter));
                }
            }

            if (tp.getCatTrkCreationSource() != null) {
                item.setCreationSource(tp.getCatTrkCreationSource().getDescription());
            }

            List<TransportPlanningDetail> tpDetails = tp.getTransportPlanningDetails(); // If we have a helper or direct collection
            if (tpDetails == null || tpDetails.isEmpty()) {
                item.setDocumentType("");
                item.setDocumentNumber("");
                item.setMovementType("");
                item.setShipper("");
                item.setConsignee("");
                item.setStatus("");
                item.setCommodity("");
                item.setProduct("");
                item.setOperationType("");
                item.setShippingLine("");
                item.setDetail("[]");
                listItems.add(item);
                continue;
            }

            Integer totalCount = 0;
            Integer pendingCount = 0;
            Integer inProcessCount = 0;
            Integer completedCount = 0;

            Map<String, ContainerAggreg> containerGroupMap = new LinkedHashMap<>();

            CargoDocument doc = null;

            for (TransportPlanningDetail tpd : tpDetails) {
                Catalog state = tpd.getCatTrkPlanningState();
                if (state != null) {
                    Integer stId = state.getId();
                    if(stId!=null) {
                        if (stId.equals(statusPendingId)) {
                            pendingCount++;
                        } else if (stId.equals(statusInprogressId)) {
                            inProcessCount++;
                        } else if (stId.equals(statusCompletedId)) {
                            completedCount++;
                        }
                    }
                }

                totalCount++;

                if (tpd.getTransportDate() != null) {
                    item.setTransportDate(null);
                }

                if (tpd.getCargoDocumentDetail() != null) {
                    CargoDocumentDetail cdd = tpd.getCargoDocumentDetail();
                    doc = cdd.getCargoDocument();

                    String contSizeDesc;
                    String contTypeDesc;
                    if (cdd.getContainer() != null && cdd.getContainer().getCatSize() != null) {
                        contSizeDesc = cdd.getContainer().getCatSize().getDescription();
                    } else {
                        contSizeDesc = "N/A";
                    }
                    if (cdd.getContainer() != null && cdd.getContainer().getCatContainerType() != null) {
                        contTypeDesc = cdd.getContainer().getCatContainerType().getDescription();
                    } else {
                        contTypeDesc = "N/A";
                    }
                    String groupKey = contSizeDesc + "|" + contTypeDesc;

                    ContainerAggreg agg = containerGroupMap.computeIfAbsent(groupKey, k -> new ContainerAggreg(contSizeDesc, contTypeDesc));

                    agg.total++;
                    if (state != null) {
                        if (state.getId().longValue() == statusPendingId) {
                            agg.pending++;
                        } else if (state.getId().longValue() == statusInprogressId) {
                            agg.inProgress++;
                        } else if (state.getId().longValue() == statusCompletedId) {
                            agg.completed++;
                        }
                    }
                }
            }

            if (doc != null) {
                if (doc.getCatCargoDocumentType() != null) {
                    item.setDocumentType(doc.getCatCargoDocumentType().getDescription());
                }
                item.setDocumentNumber(doc.getCargoDocument());

                if (tp.getCatTrkMoveType() != null) {
                    item.setMovementType(tp.getCatTrkMoveType().getLongDescription() != null
                            ? tp.getCatTrkMoveType().getLongDescription()
                            : tp.getCatTrkMoveType().getDescription());
                }

                if (doc.getShipperCompany() != null) {
                    item.setShipper(doc.getShipperCompany().getLegalName());
                }

                if (doc.getConsigneeCompany() != null) {
                    item.setConsignee(doc.getConsigneeCompany().getLegalName());
                }

                if (doc.getShippingLine() != null) {
                    item.setShippingLine(doc.getShippingLine().getShippingLineCompany());
                }

                if (doc.getId() != null) {
                    CargoDocumentDetail cddExample = tpDetails.get(0).getCargoDocumentDetail();
                    if (cddExample != null) {
                        item.setCommodity(cddExample.getCommodity());
                        if (cddExample.getProduct() != null) {
                            item.setProduct(cddExample.getProduct().getProductName());
                        }
                        if (cddExample.getCatFullReceiptReason() != null) {
                            item.setOperationType(cddExample.getCatFullReceiptReason().getLongDescription());
                        }
                    }
                }
            } else {
                // fallback
                item.setDocumentType("");
                item.setDocumentNumber("");
                item.setMovementType("");
                item.setShipper("");
                item.setConsignee("");
                item.setShippingLine("");
                item.setCommodity("");
                item.setProduct("");
                item.setOperationType("");
            }

            String docStatus = decideDocStatus(totalCount, pendingCount, inProcessCount, completedCount);

            Integer statusId = getStatusId(docStatus);

            item.setStatus(catalogLanguageRepository.fnCatalogTranslationDesc(statusId, input.getLanguageId()));
            List<Map<String, String>> detailObjs = new ArrayList<>();
            for (ContainerAggreg cagg : containerGroupMap.values()) {
                Map<String, String> detailEntry = new LinkedHashMap<>();
                detailEntry.put("item", messageType + " " + cagg.sizeDesc + " " + cagg.typeDesc + " | " + messageRequested + cagg.total + " | " + messageReceived + (cagg.inProgress + cagg.pending) +  " | " + messagePending + cagg.total);
                detailObjs.add(detailEntry);
            }
            if (detailObjs.isEmpty()) {
                item.setDetail("[]");
            } else {
                String detailJson = detailObjs.stream()
                        .map(obj -> "{\"equipment_characteristics\":\"" + obj.get("item") + "\"}")
                        .collect(Collectors.joining(","));
                item.setDetail("[" + detailJson + "]");
            }

            if (statusId != null) {
                item.setStatusId(statusId);
            }

            listItems.add(item);
        }

        setRowId(listItems, startRowId.intValue());

        if (optionalStatusId != null) {
            listItems = listItems.stream()
                    .filter(item -> item.getStatusId().equals(optionalStatusId))
                    .collect(toList());
            outputDTO.setTotalRecords((long) listItems.size());
        }

        outputDTO.setData(listItems);
        return outputDTO;
    }

    String getMessage(Integer messageKey, Integer languageId){
        return messageLanguageRepository.fnTranslatedMessage("GENERAL", messageKey, languageId);
    }

    void setRowId(List<DocumentationFullListItemDTO> listItems, Integer startRowId) {
        IntStream.range(0, listItems.size())
                .forEach(i -> {
                    listItems.get(i).setRowId(startRowId - i);
                    listItems.get(i).setRowIdAlternative(startRowId - i);
                });
    }

    Integer getStatusId(String status){
        if ("In Progress".equalsIgnoreCase(status)) {
            return catalogRepository.findIdByAlias(STATUS_IN_PROGRESS);
        } else if ("Completed".equalsIgnoreCase(status)) {
            return catalogRepository.findIdByAlias(STATUS_COMPLETED);
        } else if("Pending".equalsIgnoreCase(status)){
            return catalogRepository.findIdByAlias(STATUS_PENDING);
        }
            return null;
    }

    Integer getStateId(String state){
        return catalogRepository.findIdByAlias(state);
    }

    static String decideDocStatus(int total, int pending, int inProgress, int completed) {

        if (total > 0 && completed > 0 && (inProgress + pending) > 0) {
            return "In Progress";
        } else if (total > 0 && completed > 0 && (inProgress + pending) == 0) {
            return "Pending";
        } else {
            return "Completed";
        }
    }

    private static class ContainerAggreg {
        String sizeDesc;
        String typeDesc;
        int total;
        int pending;
        int inProgress;
        int completed;

        public ContainerAggreg(String sizeDesc, String typeDesc) {
            this.sizeDesc = sizeDesc;
            this.typeDesc = typeDesc;
        }
    }
}