package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.ProvidenceMonitoringRepository;
import com.maersk.sd1.seg.dto.ProvidenceMonitorRegisterInput;
import com.maersk.sd1.seg.dto.ProvidenceMonitorRegisterOutput;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.*;
import javax.xml.parsers.*;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
@RequiredArgsConstructor
public class ProvidenceMonitorRegisterService {

    private static final Logger logger = LoggerFactory.getLogger(ProvidenceMonitorRegisterService.class);

    private final ProvidenceMonitoringRepository providenceMonitoringRepository;

    @Transactional
    public ResponseEntity<ResponseController<ProvidenceMonitorRegisterOutput>> providenceMonitorRegisterService(ProvidenceMonitorRegisterInput.Root request) {
        ProvidenceMonitorRegisterOutput output = new ProvidenceMonitorRegisterOutput();
        String manifestNumber = "";
        String dinNumber = "";
        String partialWithdrawal = "";
        LocalDateTime withdrawalDate = null;
        String lastWithdrawal = "";
        String deliveryTicketNumber = "";
        LocalDateTime deliveryTicketDate = null;
        String statusCode = "";
        String statusDescription = "";
        String errorCode = "";
        String errorDescription = "";

        ProvidenceMonitorRegisterInput.Input input = request.getPrefix().getInput();
        String consultXml = input.getConsultXml().toUpperCase();
        String responseXml = input.getResponseXml().toUpperCase();
        Long userRegistrationId = input.getRegisteredByUserId();

        try {
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            factory.setXIncludeAware(false);
            factory.setExpandEntityReferences(false);
            DocumentBuilder builder = factory.newDocumentBuilder();

            InputStream consultaInputStream = new ByteArrayInputStream(consultXml.getBytes());
            Document consultaDoc = builder.parse(consultaInputStream);
            NodeList withdrawalNodes = consultaDoc.getElementsByTagName("RETIRO");

            if (withdrawalNodes.getLength() > 0) {
                Node withdrawalNode = withdrawalNodes.item(0);
                NodeList withdrawalDetails = withdrawalNode.getChildNodes();
                for (int i = 0; i < withdrawalDetails.getLength(); i++) {
                    Node detail = withdrawalDetails.item(i);
                    switch (detail.getNodeName()) {
                        case "NUMEROMANIFIESTO" -> manifestNumber = detail.getTextContent();
                        case "NUMERODIN" -> dinNumber = detail.getTextContent();
                        case "RETIROPARCIAL" -> partialWithdrawal = detail.getTextContent();
                        case "FECHARETIRO" -> withdrawalDate = parseDate(detail.getTextContent(), "FECHARETIRO");
                        case "ULTIMORETIRO" -> lastWithdrawal = detail.getTextContent();
                        case "NUMEROPAPELETAENTREGA" -> deliveryTicketNumber = detail.getTextContent();
                        case "FECHAPAPELETAENTREGA" -> deliveryTicketDate = parseDate(detail.getTextContent(), "FECHAPAPELETAENTREGA");
                        default -> logger.warn("Unexpected node: {}", detail.getNodeName());
                    }
                }
            }

            InputStream respuestaInputStream = new ByteArrayInputStream(responseXml.getBytes());
            Document respuestaDoc = builder.parse(respuestaInputStream);

            NodeList statusNodes = respuestaDoc.getElementsByTagName("ESTADO");
            if (statusNodes.getLength() > 0) {
                Node statusNode = statusNodes.item(0);
                NodeList statusDetails = statusNode.getChildNodes();
                for (int i = 0; i < statusDetails.getLength(); i++) {
                    Node detail = statusDetails.item(i);
                    if ("CODIGO".equals(detail.getNodeName())) {
                        statusCode = detail.getTextContent();
                    } else if ("GLOSA".equals(detail.getNodeName())) {
                        statusDescription = detail.getTextContent();
                    }
                }
            }

            NodeList errorNodes = respuestaDoc.getElementsByTagName("ERROR");
            if (errorNodes.getLength() > 0) {
                Node errorNode = errorNodes.item(0);
                NodeList errorDetails = errorNode.getChildNodes();
                for (int i = 0; i < errorDetails.getLength(); i++) {
                    Node detail = errorDetails.item(i);
                    if ("CODIGO".equals(detail.getNodeName())) {
                        errorCode = detail.getTextContent();
                    } else if ("GLOSA".equals(detail.getNodeName())) {
                        errorDescription = detail.getTextContent();
                    }
                }
            }

            providenceMonitoringRepository.insertProvidenceMonitoring(
                    manifestNumber, dinNumber, partialWithdrawal, withdrawalDate, lastWithdrawal,
                    deliveryTicketNumber, deliveryTicketDate, statusCode, statusDescription,
                    errorCode, errorDescription, consultXml, responseXml, userRegistrationId
            );

            output.setResultStatus(1);
            output.setResultMessage("Record processed successfully");

        } catch (Exception e) {
            logger.error("Error processing providence monitor registration", e);
            output.setResultStatus(0);
            output.setResultMessage("Error occurred: " + e.getMessage());
        }

        return ResponseEntity.ok(new ResponseController<>(output));
    }

    private LocalDateTime parseDate(String dateStr, String fieldName) {
        try {
            return LocalDateTime.parse(dateStr, DateTimeFormatter.ISO_DATE_TIME);
        } catch (Exception e) {
            logger.error("Error parsing {}: {}", fieldName, dateStr, e);
            return null;
        }
    }
}