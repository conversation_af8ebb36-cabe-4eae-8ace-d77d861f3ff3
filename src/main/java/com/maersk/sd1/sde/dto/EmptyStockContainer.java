package com.maersk.sd1.sde.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
public class EmptyStockContainer {
    private Integer containerId;
    private Integer gateInEirId;
    private LocalDateTime truckEntryDate;
    private Integer gateOutEirId;
    private LocalDateTime truckExitDate;
    private Integer subBusinessUnitId;
    private Boolean structureWithDamage;
    private Boolean machineryWithDamage;
    private String catContainerTypeCode;
    private String catContainerTypeDescription;
}