package com.maersk.sd1.seg.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public  class EmailTemplateOutputDTO {
    @JsonProperty("descripcion")
    private String descripcion;

    @JsonProperty("estado")
    private Boolean estado;

    public EmailTemplateOutputDTO() {
    }
    public EmailTemplateOutputDTO(String descripcion, Boolean estado) {
        this.descripcion = descripcion;
        this.estado = estado;
    }
}
