package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.CompanyDataGetInput;
import com.maersk.sd1.ges.dto.CompanyDataGetOutput;
import com.maersk.sd1.ges.service.CompanyDataGetService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("ModuleADM/module/adm/companyDataGet")
public class CompanyDataGetController {

    private static final Logger logger = LogManager.getLogger(CompanyDataGetController.class);

    private CompanyDataGetService companyDataGetService;

    @Autowired
    public CompanyDataGetController(CompanyDataGetService companyDataGetService) {
        this.companyDataGetService = companyDataGetService;
    }

    @PostMapping("/getCompanyData")
    public ResponseEntity<ResponseController<CompanyDataGetOutput>> getCompanyData(@RequestBody @Valid CompanyDataGetInput.Root request) {
        try {
            Integer empresaId = request.getGes().getInput().getCompanyId();
            CompanyDataGetOutput output = companyDataGetService.getCompanyData(empresaId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            CompanyDataGetOutput output = new CompanyDataGetOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

