package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.MercplusDamageLocationListInput;
import com.maersk.sd1.sde.dto.MercplusDamageLocationListOutputDto;
import com.maersk.sd1.sde.service.MercplusDamageLocationListService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

class MercplusDamageLocationListControllerTest {

    @Mock
    private MercplusDamageLocationListService mercplusDamageLocationListService;

    @InjectMocks
    private MercplusDamageLocationListController mercplusDamageLocationListController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testMercplusDamageLocationList_Success() {
        // Arrange
        MercplusDamageLocationListInput.Root request = new MercplusDamageLocationListInput.Root();
        MercplusDamageLocationListInput.Prefix prefix = new MercplusDamageLocationListInput.Prefix();
        MercplusDamageLocationListInput.Input input = new MercplusDamageLocationListInput.Input();
        input.setTypeContainer("someType");
        prefix.setInput(input);
        request.setPrefix(prefix);

        MercplusDamageLocationListOutputDto mockOutput = new MercplusDamageLocationListOutputDto();
        mockOutput.setRespEstado(1);
        mockOutput.setRespMensaje("Success");
        when(mercplusDamageLocationListService.getDamageLocations("someType")).thenReturn(mockOutput);

        // Act
        ResponseEntity<ResponseController<MercplusDamageLocationListOutputDto>> response = mercplusDamageLocationListController.mercplusDamageLocationList(request);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(mockOutput, response.getBody().getResult());
    }

    @Test
    void testMercplusDamageLocationList_Exception() {
        // Arrange
        MercplusDamageLocationListInput.Root request = new MercplusDamageLocationListInput.Root();
        MercplusDamageLocationListInput.Prefix prefix = new MercplusDamageLocationListInput.Prefix();
        MercplusDamageLocationListInput.Input input = new MercplusDamageLocationListInput.Input();
        input.setTypeContainer("someType");
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(mercplusDamageLocationListService.getDamageLocations("someType")).thenThrow(new RuntimeException("Service error"));

        // Act
        ResponseEntity<ResponseController<MercplusDamageLocationListOutputDto>> response = mercplusDamageLocationListController.mercplusDamageLocationList(request);

        // Assert
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("java.lang.RuntimeException: Service error", response.getBody().getResult().getRespMensaje());
    }
}
