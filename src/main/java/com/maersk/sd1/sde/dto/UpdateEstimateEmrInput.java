package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class UpdateEstimateEmrInput {

    @Data
    public static class Input {


        @JsonProperty("estimado_emr_id")
        @NotNull
        private Integer estimadoEmrId;

        @JsonProperty("persona_inspector_id")
        @NotNull
        private Integer personaInspectorId;

        @JsonProperty("estimado_remark")
        @Size(max = 200)
        private String estimadoRemark;

        @JsonProperty("estimado_obs")
        @Size(max = 200)
        private String estimadoObs;

        @JsonProperty("bloquear_envio")
        private Boolean bloquearEnvio;

        @JsonProperty("bloquear_envio_motivo")
        @Size(max = 200)
        private String bloquearEnvioMotivo;

        @JsonProperty("usuario_id")
        @NotNull
        private Integer usuarioId;

        @JsonProperty("cat_estimado_causa_dano_id")
        @NotNull
        private Integer catEstimadoCausaDanoId;

        @JsonProperty("detalle_eliminadosID")
        @Size(max = 100)
        private String detalleEliminadosID;

        @JsonProperty("DetalleEstimado")
        private String detalleEstimado;

        @JsonProperty("ObservacionesInspector")
        @Size(max = 240)
        private String observacionesInspector;

        @JsonProperty("ObservacionInspOtros")
        @Size(max = 100)
        private String observacionInspOtros;

        @JsonProperty("Fotos")
        private String fotos;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer idiomaId;

        @JsonProperty("merc_plus_estimate_number")
        private Integer mercPlusEstimateNumber;

        @JsonProperty("obs_comment")
        @Size(max = 250)
        private String obsComment;

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}