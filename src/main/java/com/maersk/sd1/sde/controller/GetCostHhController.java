package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.GetCostHhOutput;
import com.maersk.sd1.sde.dto.GetCostHhInput;
import com.maersk.sd1.sde.service.GetCostHhService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDE/module/sde/SDEEMRServiceImp")
public class GetCostHhController {

    private static final Logger logger = LogManager.getLogger(GetCostHhController.class);

    private final GetCostHhService obtenerCostoHhService;

    @PostMapping("/sdeobtenerCostoHh")
    public ResponseEntity<ResponseController<GetCostHhOutput>> sdgObtenerCostoHh(
            @RequestBody @Valid GetCostHhInput.Root request) {
        try {
            GetCostHhInput.Input input = request.getPrefix().getInput();

            GetCostHhOutput output = obtenerCostoHhService.obtenerCostoHh(
                    input.getSubUnidadNegocioId(),
                    input.getLineaNavieraId(),
                    input.getCatTipoEstimadoId(),
                    input.getMonedaId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing obtenerCostoHh", e);
            GetCostHhOutput errorOutput = new GetCostHhOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}