package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.dto.EstimateAutoApprovalConfigGetOutput;
import com.maersk.sd1.common.repository.EstimateAutoApprovalConfigRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Collections;

@RequiredArgsConstructor
@Service
public class EstimateAutoApprovalConfigGetService {

    private static final Logger logger = LogManager.getLogger(EstimateAutoApprovalConfigGetService.class);

    private final EstimateAutoApprovalConfigRepository estimateAutoApprovalConfigRepository;

    public List<EstimateAutoApprovalConfigGetOutput> getAutoApprovalConfig(Integer autoApprovalId) {
        logger.info("Fetching auto approval config for ID: {}", autoApprovalId);
        try {
            Optional<EstimateAutoApprovalConfigGetOutput> optional = estimateAutoApprovalConfigRepository.findOutputById(autoApprovalId);
            if (optional.isEmpty()) {
                logger.warn("No record found for ID: {}", autoApprovalId);
                return Collections.emptyList();
            }
            return List.of(optional.get());
        } catch (Exception ex) {
            logger.error("Error occurred while fetching config for ID: {}", autoApprovalId, ex);
            throw new RuntimeException("Error fetching EstimateAutoApprovalConfigGetOutput", ex);
        }
    }
}