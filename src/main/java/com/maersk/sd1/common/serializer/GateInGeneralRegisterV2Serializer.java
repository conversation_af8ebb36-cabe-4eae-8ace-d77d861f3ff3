package com.maersk.sd1.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.maersk.sd1.common.utils.Utils;

import java.io.IOException;

import static com.maersk.sd1.sdg.controller.dto.GateInGeneralRegisterV2Output.*;

public class GateInGeneralRegisterV2Serializer extends StdSerializer<Output> {

    public GateInGeneralRegisterV2Serializer() {this(null);}

    public GateInGeneralRegisterV2Serializer(Class<Output> t) {super(t);}

    @Override
    public void serialize(Output value, JsonGenerator gen, SerializerProvider provider) throws IOException {
        Object[] serializedArray = new Object[]{
                new Object[][]{Utils.toObjectArray(value.getResult())},
                value.getResponse() != null ? value.getResponse().stream().map(Utils::toObjectArray).toArray() : new Object[]{},
                value.getIntegration() != null ? new Object[][]{Utils.toObjectArray(value.getIntegration())} : new Object[]{},
                value.getApsIntegration() != null ? new Object[][]{Utils.toObjectArray(value.getApsIntegration())} : new Object[]{}
        };
        gen.writeObject(serializedArray);
    }
}
