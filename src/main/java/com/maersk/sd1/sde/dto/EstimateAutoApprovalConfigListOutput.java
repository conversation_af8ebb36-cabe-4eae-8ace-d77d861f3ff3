package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class EstimateAutoApprovalConfigListOutput {

    @JsonProperty("resp_data")
    private List<EstimateAutoApprovalConfigItemOutput> respData;

    @JsonProperty("resp_total")
    private List<List<Long>> respTotal;


    @Data
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    public static class EstimateAutoApprovalConfigItemOutput {

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("approval_id")
        private String approvalId;

        @JsonProperty("sub_business_unit_local_id")
        private Long subBusinessUnitLocalId;

        @JsonProperty("structure_estimate_flag")
        private Boolean structureEstimateFlag;

        @JsonProperty("machinery_estimate_flag")
        private Boolean machineryEstimateFlag;

        @JsonProperty("cat_equipment_category_id")
        private Integer catEquipmentCategoryId;

        @JsonProperty("cat_equipment_category_desc")
        private String catEquipmentCategoryDesc;

        @JsonProperty("cat_equipment_type_id")
        private Integer catEquipmentTypeId;

        @JsonProperty("cat_equipment_type_desc")
        private String catEquipmentTypeDesc;

        @JsonProperty("threshold_value")
        private BigDecimal thresholdValue;

        @JsonProperty("active")
        private Boolean active;

        @JsonProperty("shipping_line_id")
        private Integer shippingLineId;

        @JsonProperty("shipping_line")
        private String shippingLine;

        @JsonProperty("customer_id")
        private Integer customerId;

        @JsonProperty("customer")
        private String customer;

        @JsonProperty("currency_id")
        private Integer currencyId;

        @JsonProperty("currency")
        private String currency;

        @JsonProperty("user_registration_id")
        private Integer userRegistrationId;

        @JsonProperty("user_registration_name")
        private String userRegistrationName;

        @JsonProperty("user_registration_lastname")
        private String userRegistrationLastname;

        @JsonProperty("registration_date")
        private LocalDateTime registrationDate;

        @JsonProperty("user_modification_id")
        private Integer userModificationId;

        @JsonProperty("user_modification_name")
        private String userModificationName;

        @JsonProperty("user_modification_lastname")
        private String userModificationLastname;

        @JsonProperty("modification_date")
        private LocalDateTime modificationDate;
    }
}