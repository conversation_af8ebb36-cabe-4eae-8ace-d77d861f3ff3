package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "programacion_nave", schema = "sds", indexes = {
        @Index(name = "PRONAVX_NAVE", columnList = "nave_id")
})
public class VesselProgramming {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "programacion_nave_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "unidad_negocio_id", nullable = false)
    private BusinessUnit businessUnit;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sub_unidad_negocio_id", nullable = false)
    private BusinessUnit subBusinessUnit;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "nave_id", nullable = false)
    private Vessel vessel;

    @Size(max = 10)
    @NotNull
    @Column(name = "viaje", nullable = false, length = 10)
    private String voyage;

    @NotNull
    @Column(name = "fecha_eta", nullable = false)
    private LocalDateTime etaDate;

    @Column(name = "fecha_etd")
    private LocalDateTime etdDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "empresa_agencia_maritima_id")
    private Company shippingAgencyCompany;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "empresa_ope_portuario_id")
    private Company opePortCompany;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_origen_creacion_id", nullable = false)
    private Catalog catCreationOrigin;

}