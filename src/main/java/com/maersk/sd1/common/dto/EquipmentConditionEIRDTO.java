package com.maersk.sd1.common.dto;

import lombok.Data;

@Data
public class EquipmentConditionEIRDTO {

    private Boolean structureWithDamage;
    private Boolean machineryWithDamage;
    private Integer catApprovalRepBoxId;
    private Integer catApprovalRepMachineId;
    private Integer catCleaningStatusId;

    public EquipmentConditionEIRDTO(Boolean structureWithDamage, Boolean machineryWithDamage, Integer catApprovalRepBoxId, Integer catApprovalRepMachineId, int catCleaningStatusId) {
        this.structureWithDamage = structureWithDamage;
        this.machineryWithDamage = machineryWithDamage;
        this.catApprovalRepBoxId = catApprovalRepBoxId;
        this.catApprovalRepMachineId = catApprovalRepMachineId;
        this.catCleaningStatusId = catCleaningStatusId;
    }

}