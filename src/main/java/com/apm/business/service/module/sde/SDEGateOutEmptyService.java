package com.apm.business.service.module.sde;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEGateOutEmptyService {

	@RequestMapping(value = "/sdeguardarSalidaCamion", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeguardarSalidaCamion(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.guardar_salida_camion","SDE");
			pResult.input("eir_id", Jpo.INTEGER);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			pResult.input("idioma_id", Jpo.INTEGER);
			pResult.input("truck_out_date", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdelistarSalidasEmptyPendientes", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdelistarSalidasEmptyPendientes(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.listar_salidas_empty_pendientes","SDE");
			pResult.input("sub_unidad_negocio_id", Jpo.DECIMAL);
			pResult.input("sub_unidad_negocio_local_id", Jpo.DECIMAL);
			pResult.input("Page", Jpo.INTEGER);
			pResult.input("Size", Jpo.INTEGER);
			pResult.input("idioma_id", Jpo.INTEGER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdeimpresionTicketEmptySalidaCamion", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeimpresionTicketEmptySalidaCamion(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.impresion_ticket_empty_salida_camion","SDE");
			pResult.input("eir_id", Jpo.INTEGER);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_impresion", Jpo.STRING);
			pResult.input("visualizarMontoDanos", Jpo.CHARACTER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sderegistrarFirmasEir", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sderegistrarFirmasEir(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.registrar_firmas_eir","SDE");
			pResult.input("eir_id", Jpo.INTEGER);
			pResult.input("fecha_firma_conductor", Jpo.DATETIME);
			pResult.input("url_firma_conductor", Jpo.STRING);
			pResult.input("url_firma_inspector", Jpo.STRING);
			pResult.input("usuario_registro_id", Jpo.DECIMAL);
			pResult.output("resp_estado", Jpo.INTEGER);
			pResult.output("resp_mensaje", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}