package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.InspectionBoxGetInputDTO;
import com.maersk.sd1.sde.controller.dto.InspectionBoxGetOutputDTO;
import com.maersk.sd1.sde.service.InspectionBoxGetService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;

@RestController
@RequiredArgsConstructor
@RequestMapping("ModuleSDE/module/sde/SDEInspeccionServiceImp")
public class InspectionBoxGetController {

    private static final Logger logger = LogManager.getLogger(InspectionBoxGetController.class);

    private final InspectionBoxGetService inspectionBoxGetService;

    @PostMapping("/sdeinspectionBoxGet")
    public ResponseEntity<ResponseController<InspectionBoxGetOutputDTO>> getInspectionBox(@RequestBody @Valid InspectionBoxGetInputDTO.Root request) {
        try {
            InspectionBoxGetInputDTO.Input input = request.getPrefix().getInput();
            InspectionBoxGetOutputDTO output = inspectionBoxGetService.getInspectionBox(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("Error fetching inspection box data", e);
            return ResponseEntity.status(500).body(new ResponseController<>(e.toString()));
        }
    }
}
