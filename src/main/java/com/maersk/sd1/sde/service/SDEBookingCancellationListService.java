package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.dto.BookingBlockCancellationListProcess;
import com.maersk.sd1.common.dto.CancellationFilterDTO;
import com.maersk.sd1.common.repository.BookingBlockCancellationRepository;
import com.maersk.sd1.sde.controller.dto.SDEBookingCancellationListInput;
import com.maersk.sd1.sde.controller.dto.SDEBookingCancellationListOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class SDEBookingCancellationListService {
    private static final Logger logger = LogManager.getLogger(SDEBookingCancellationListService.class);

    private final BookingBlockCancellationRepository blockCancellationRepository;

    @Transactional(readOnly = true)
    public SDEBookingCancellationListOutput listSDEBookingCancellations(SDEBookingCancellationListInput.Input input) {

        SDEBookingCancellationListOutput sdeBookingCancellationListOutput = new SDEBookingCancellationListOutput();
        try {
            CancellationFilterDTO cancellationFilterDTO = new CancellationFilterDTO();
            if (input.getBusinessUnitId() != null) {
                cancellationFilterDTO.setBusinessUnitId(input.getBusinessUnitId());
            } else {
                throw new IllegalArgumentException("Business Unit ID is required");
            }

            if (input.getCancellationType() != null) {
                cancellationFilterDTO.setCancellationType(input.getCancellationType());
            }

            if (input.getVesselName() != null) {
                cancellationFilterDTO.setVesselName(input.getVesselName());
            }

            if (input.getVoyage() != null) {
                cancellationFilterDTO.setVoyage(input.getVoyage());
            }

            if (input.getId() != null) {
                cancellationFilterDTO.setId(input.getId());
            }

            if (input.getStartDate() != null) {
                cancellationFilterDTO.setStartDate(input.getStartDate());
            }

            if (input.getEndDate() != null) {
                cancellationFilterDTO.setEndDate(input.getEndDate());
            }

            if (input.getBookingNumber() != null) {
                cancellationFilterDTO.setBookingNumber(input.getBookingNumber());
            }

            cancellationFilterDTO.setLanguageId(input.getLanguageId());
            PageRequest pageRequest = PageRequest.of(input.getPage() - 1, input.getSize());


            Page<BookingBlockCancellationListProcess> list = blockCancellationRepository.findCancellationDetails(cancellationFilterDTO, pageRequest);


            sdeBookingCancellationListOutput = generateResponseDTO(list);
        } catch (Exception e) {
            logger.error("Error listing booking block cancellations.", e);
        }
        return sdeBookingCancellationListOutput;
    }

    public SDEBookingCancellationListOutput generateResponseDTO(Page<BookingBlockCancellationListProcess> dtoList) {
        List<SDEBookingCancellationListOutput.BookingBlockCancellationData> dataList = dtoList.stream()
                .map(this::mapToResponseDTO)
                .toList();

        SDEBookingCancellationListOutput output = new SDEBookingCancellationListOutput();
        List<List<Long>> recordCount = CollectionUtils.isEmpty(dataList) ? Collections.emptyList() :List.of(List.of(dtoList.getTotalElements()));
        output.setDataList(dataList);
        output.setTotalRegisters(recordCount);

        return output;
    }

    private SDEBookingCancellationListOutput.BookingBlockCancellationData mapToResponseDTO(BookingBlockCancellationListProcess dto) {
        SDEBookingCancellationListOutput.BookingBlockCancellationData bookingBlockCancellationData = new SDEBookingCancellationListOutput.BookingBlockCancellationData();

        bookingBlockCancellationData.setIdCB(dto.getCancellationBlockBookingId() != null ? dto.getCancellationBlockBookingId().intValue() : null);
        bookingBlockCancellationData.setType(dto.getCategoryType() != null ? dto.getCategoryType().intValue() : null);
        bookingBlockCancellationData.setTypeDescription(dto.getCategoryTypeDescription());
        bookingBlockCancellationData.setBookingReleased(dto.getBookingReleased() != null && dto.getBookingReleased().equalsIgnoreCase("true")); // Convert String to Boolean
        bookingBlockCancellationData.setCargoDocumentId(dto.getDocumentLoadId() != null ? dto.getDocumentLoadId().intValue() : null);
        bookingBlockCancellationData.setBooking(dto.getBookingNumber());
        bookingBlockCancellationData.setVesselVoyage(dto.getVesselVoyage());
        bookingBlockCancellationData.setOperation(dto.getOperationCategoryId() != null ? dto.getOperationCategoryId().intValue() : null);
        bookingBlockCancellationData.setOperationDescription(dto.getOperationDescription());
        bookingBlockCancellationData.setCancellationReason(dto.getCancellationReason() != null ? dto.getCancellationReason().intValue() : null);
        bookingBlockCancellationData.setCancellationReasonDescription(dto.getCancellationReasonDescription());
        bookingBlockCancellationData.setCancellationDate(dto.getCancellationDate());
        bookingBlockCancellationData.setRegisteredUserId(dto.getRegisteredUserId() != null ? dto.getRegisteredUserId().intValue() : null);
        bookingBlockCancellationData.setRegisteredUserFirstName(dto.getRegisteredUserFirstName());
        bookingBlockCancellationData.setRegisteredUserLastName(dto.getRegisteredUserLastName());
        bookingBlockCancellationData.setReleaseReason(dto.getBookingReleaseReason() != null ? dto.getBookingReleaseReason().intValue() : null);
        bookingBlockCancellationData.setReleaseReasonDescription(dto.getBookingReleaseReasonDescription());
        bookingBlockCancellationData.setReleaseDate(dto.getBookingReleaseDate());
        bookingBlockCancellationData.setReleaseUserId(dto.getBookingReleaseUserId() != null ? dto.getBookingReleaseUserId().intValue() : null);
        bookingBlockCancellationData.setReleaseUserFirstName(dto.getBookingReleaseUserFirstName());
        bookingBlockCancellationData.setReleaseUserLastName(dto.getBookingReleaseUserLastName());
        bookingBlockCancellationData.setCancellationOrigin(dto.getCancellationOrigin() != null ? dto.getCancellationOrigin().intValue() : null);
        bookingBlockCancellationData.setCancellationOriginDescription(dto.getCancellationOriginDescription());
        bookingBlockCancellationData.setBookingReleaseOriginId(dto.getBookingReleaseOriginId() != null ? dto.getBookingReleaseOriginId().intValue() : null);
        bookingBlockCancellationData.setReleaseOriginDescription(dto.getBookingReleaseOriginDescription());

        return bookingBlockCancellationData;
    }
}