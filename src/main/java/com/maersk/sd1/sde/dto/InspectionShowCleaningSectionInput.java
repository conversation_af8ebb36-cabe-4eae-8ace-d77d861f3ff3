package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class InspectionShowCleaningSectionInput {

    @Data
    public static class Input {

        @JsonProperty("cat_cleaning_type_id")
        @NotNull
        private Integer catCleaningTypeId;

        @JsonProperty("eir_id")
        @NotNull
        private Integer eirId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
