package com.maersk.sd1.ges.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class AzureStorageConfigEditInput {

    @Data
    public static class Input {
        @JsonProperty("azure_storage_config_id")
        @NotNull
        private Integer azureStorageConfigId;

        @JsonProperty("id")
        @Size(max = 100)
        private String id1;

        @JsonProperty("azure_container")
        @Size(max = 10)
        private String azureContainer;

        @JsonProperty("azure_path")
        @Size(max = 200)
        private String azurePath;

        @JsonProperty("azure_storage_name")
        @Size(max = 200)
        private String azureStorageName;

        @JsonProperty("azure_storage_key")
        @Size(max = 2000)
        private String azureStorageKey;

        @JsonProperty("referencia_01")
        @Size(max = 100)
        private String reference01;

        @JsonProperty("estado")
        @NotNull
        private Boolean status;

        @JsonProperty("usuario_modificacion_id")
        @NotNull
        private Integer modificationUserId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }

    private AzureStorageConfigEditInput() {
        // Private constructor to hide the implicit public one
    }
}