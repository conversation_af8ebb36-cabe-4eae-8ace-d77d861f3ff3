package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.CatalogTableRequestInput;
import com.maersk.sd1.sde.dto.CatalogTableResponseOutput;
import com.maersk.sd1.sde.dto.ChangeStatusEstimateEMRInput;
import com.maersk.sd1.sde.dto.ChangeStatusEstimateEMROutput;
import com.maersk.sd1.sde.service.CatalogTableInfoService;
import com.maersk.sd1.sde.service.ChangeStatusEstimateEMRService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEEMRServiceImp")
public class ChangeStatusEstimateEMRController {

    private static final Logger logger = LogManager.getLogger(ChangeStatusEstimateEMRController.class);

    private final ChangeStatusEstimateEMRService changeStatusEstimateEMRService;

    public ChangeStatusEstimateEMRController(ChangeStatusEstimateEMRService changeStatusEstimateEMRService){
        this.changeStatusEstimateEMRService = changeStatusEstimateEMRService;
    }

    @PostMapping("/sdecambiarEstadoEstimadosEmr")
    public ResponseEntity<ResponseController<ChangeStatusEstimateEMROutput>> changeStatusEstimateEMR(@RequestBody @Valid ChangeStatusEstimateEMRInput.Root request) {
        try {
            ChangeStatusEstimateEMROutput responseOutput = changeStatusEstimateEMRService.changeStatusEstimateEMR(request.getPrefix().getInput());
            return ResponseEntity.ok(new ResponseController<>(responseOutput));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            ChangeStatusEstimateEMROutput errorOutput = new ChangeStatusEstimateEMROutput();
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}