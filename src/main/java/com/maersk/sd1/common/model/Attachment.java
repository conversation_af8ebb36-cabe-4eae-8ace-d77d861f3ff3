package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "Attachment.loadFiles",
        procedureName = "ges.adjunto_cargar_archivos",
        parameters = {
                @StoredProcedureParameter(name = "usuario_id", mode = ParameterMode.IN, type = Integer.class),
                @StoredProcedureParameter(name = "adjuntos", mode = ParameterMode.IN, type = String.class),
                @StoredProcedureParameter(name = "respuesta", mode = ParameterMode.OUT, type = String.class),
                @StoredProcedureParameter(name = "estado", mode = ParameterMode.OUT, type = Integer.class),
                @StoredProcedureParameter(name = "mensaje", mode = ParameterMode.OUT, type = String.class)
        }
)
@Table(name = "adjunto", schema = "ges")
public class Attachment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "adjunto_id", nullable = false)
    private Integer id;

    @Size(max = 150)
    @NotNull
    @Column(name = "nombre", nullable = false, length = 150)
    private String name;

    @Size(max = 5)
    @NotNull
    @Column(name = "formato", nullable = false, length = 5)
    private String format;

    @NotNull
    @Column(name = "peso", nullable = false)
    private Integer weight;

    @Size(max = 250)
    @NotNull
    @Column(name = "ubicacion", nullable = false, length = 250)
    private String location;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "estado", nullable = false)
    private Boolean status = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @Size(max = 50)
    @Column(name = "id", length = 50)
    private String id1;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tipo_adjunto")
    private Catalog catAttachmentType;

    @Size(max = 500)
    @Column(name = "url", length = 500)
    private String url;

    @Size(max = 50)
    @Column(name = "thumbnail_id", length = 50)
    private String thumbnailId;

    @Size(max = 500)
    @Column(name = "thumbnail_url", length = 500)
    private String thumbnailUrl;

}