package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CargoDocumentObtainDetailOutput {

    @JsonProperty("documento_carga_detalle_id")
    private Integer documentoCargaDetalleId;

    @JsonProperty("producto_id")
    private Integer productoId;

    @JsonProperty("nombre_producto")
    private String nombreProducto;

    @JsonProperty("numero_contenedor")
    private String numeroContenedor;

    @JsonProperty("cat_tipo_contenedor_id")
    private Integer catTipoContenedorId;

    @JsonProperty("tipo_contenedor_descripcion")
    private String tipoContenedorDescripcion;

    @JsonProperty("cat_tamano_id")
    private Integer catTamanoId;

    @JsonProperty("tamano_contenedor_descripcion")
    private String tamanoContenedorDescripcion;

    @JsonProperty("precinto_manifestado")
    private String precintoManifestado;

    @JsonProperty("cantidad_manifestada")
    private BigDecimal cantidadManifestada;

    @JsonProperty("peso_manifestado")
    private BigDecimal pesoManifestado;

    @JsonProperty("volumen_manifestado")
    private BigDecimal volumenManifestado;

    @JsonProperty("cat_unidad_medida_peso_id")
    private Integer catUnidadMedidaPesoId;

    @JsonProperty("cat_unidad_medida_peso_descripcion")
    private String catUnidadMedidaPesoDescripcion;

    @JsonProperty("es_carga_peligrosa")
    private Boolean esCargaPeligrosa;

    @JsonProperty("es_carga_refrigerada")
    private Boolean esCargaRefrigerada;

    @JsonProperty("marcas")
    private String marcas;

    @JsonProperty("mercaderia")
    private String mercaderia;

    @JsonProperty("cat_condicion_carga_id")
    private Integer catCondicionCargaId;

    @JsonProperty("cat_condicion_carga_descripcion")
    private String catCondicionCargaDescripcion;

    @JsonProperty("cat_tipo_contenedor_manifestado_id")
    private Integer catTipoContenedorManifestadoId;

    @JsonProperty("cat_tipo_contenedor_manifestado_descripcion")
    private String catTipoContenedorManifestadoDescripcion;

    @JsonProperty("cat_origen_creacion_id")
    private Integer catOrigenCreacionId;

    @JsonProperty("cat_origen_creacion_descripcion")
    private String catOrigenCreacionDescripcion;

    @JsonProperty("cat_tamano_manifestado_id")
    private Integer catTamanoManifestadoId;

    @JsonProperty("cat_tamano_manifestado_descripcion")
    private String catTamanoManifestadoDescripcion;

    @JsonProperty("cat_unidad_medida_cantidad_id")
    private Integer catUnidadMedidaCantidadId;

    @JsonProperty("cat_unidad_medida_cantidad_descripcion")
    private String catUnidadMedidaCantidadDescripcion;

    @JsonProperty("unidad_negocio_recojo_vacio_id")
    private Integer unidadNegocioRecojoVacioId;

    @JsonProperty("unidad_negocio_recojo_vacio")
    private String unidadNegocioRecojoVacio;

    @JsonProperty("usuario_registro_id")
    private Integer usuarioRegistroId;

    @JsonProperty("usuario_registro_nombres")
    private String usuarioRegistroNombres;

    @JsonProperty("usuario_registro_apellidos")
    private String usuarioRegistroApellidos;

    @JsonProperty("fecha_registro")
    private LocalDateTime fechaRegistro;

    @JsonProperty("usuario_modificacion_id")
    private Integer usuarioModificacionId;

    @JsonProperty("usuario_modificacion_nombres")
    private String usuarioModificacionNombres;

    @JsonProperty("usuario_modificacion_apellidos")
    private String usuarioModificacionApellidos;

    @JsonProperty("fecha_modificacion")
    private LocalDateTime fechaModificacion;
}