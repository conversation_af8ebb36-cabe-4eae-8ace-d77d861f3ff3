package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ContainerSearchInput {

    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("contenedor_id")
        private Integer containerId;

        @JsonProperty("numero_contenedor")
        private String containerNumber;

        @JsonProperty("cat_tamano_id")
        private Integer catSizeId;

        @JsonProperty("cat_tipo_contenedor_id")
        private Integer catContainerTypeId;

        @JsonProperty("linea_naviera_nombre")
        private String shippingLineName;

        @JsonProperty("activo")
        private Boolean active;

        @NotNull
        @JsonProperty("page")
        private Integer page;

        @NotNull
        @JsonProperty("size")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private Prefix prefix;
    }

    private ContainerSearchInput() {
        // Private constructor to hide the implicit public one
    }
}