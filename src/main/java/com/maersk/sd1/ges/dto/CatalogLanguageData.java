package com.maersk.sd1.ges.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class CatalogLanguageData {

    @JsonProperty("catalogo_idioma_id")
    private Integer catalogLanguageId;

    @JsonProperty("catalogo_id")
    private Integer catalogId;

    @JsonProperty("idioma_id")
    private Integer languageId;

    @JsonProperty("descripcion")
    private String description;

    @JsonProperty("descricion_larga")
    private String longDescription;

    public CatalogLanguageData(Integer catalogLanguageId,
                               Integer catalogId,
                               Integer languageId,
                               String description,
                               String longDescription) {
        this.catalogLanguageId = catalogLanguageId;
        this.catalogId = catalogId;
        this.languageId = languageId;
        this.description = description;
        this.longDescription = longDescription;
    }

    public CatalogLanguageData() {
    }
}
