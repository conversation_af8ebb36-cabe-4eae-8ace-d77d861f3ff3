package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class UserPasswordChangeInput {
    @Data
    public static class Input {

        @JsonProperty("usuario_id")
        @NotNull(message = "El campo usuario_id no puede ser nulo.")
        private Integer userId;

        @JsonProperty("clave")
        @NotBlank(message = "El campo clave no puede estar vacío.")
        private String key;

        @JsonProperty("origen")
        @NotBlank(message = "El campo origen no puede estar vacío.")
        private String origin;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull(message = "El objeto F no puede ser nulo.")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        @NotNull(message = "El objeto SDG no puede ser nulo.")
        private Prefix prefix;
    }

    private UserPasswordChangeInput() {
        // private constructor to hide the implicit public one
    }
}
