package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.AutoreportListInput;
import com.maersk.sd1.ges.dto.AutoreportListOutput;
import com.maersk.sd1.ges.service.AutoreportListService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMAutoreportServiceImp")
public class AutoreportListController {

    private static final Logger logger = LogManager.getLogger(AutoreportListController.class);

    private final AutoreportListService autoreportListService;

    @PostMapping("/gesautoreportList")
    public ResponseEntity<ResponseController<AutoreportListOutput>> listAutoreports(@RequestBody @Valid AutoreportListInput.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                logger.error("Request received listAutoreports: {}", request);
                AutoreportListOutput output = new AutoreportListOutput();
                output.setRespMensaje("Invalid request");
                output.setRespEstado(0);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ResponseController<>(output));
            }

            logger.info("Request received listAutoreports: {}", request);
            AutoreportListInput.Input input = request.getPrefix().getInput();
            AutoreportListOutput output = autoreportListService.listAutoreports(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while listing reports.", e);
            AutoreportListOutput output = new AutoreportListOutput();
            output.setRespMensaje(e.toString());
            output.setRespEstado(0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}