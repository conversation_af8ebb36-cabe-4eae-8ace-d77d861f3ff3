package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PersonObtainOutput {

    @JsonProperty("persona_id")
    private Integer personaId;

    @JsonProperty("tipo_documento_identidad_id")
    private Integer typeDocumentIdentificationId;

    @JsonProperty("documento_identidad")
    private String identificationDocument;

    @JsonProperty("apellido_paterno")
    private String firstLastName;

    @JsonProperty("apellido_materno")
    private String secondLastName;

    @JsonProperty("nombres")
    private String names;

    @JsonProperty("fecha_nacimiento")
    private LocalDateTime birthDate;

    @JsonProperty("correo")
    private String mail;

    @JsonProperty("telefono")
    private String phone;

    @JsonProperty("tipo_estado_persona_id")
    private Integer personStatusId;

    @JsonProperty("tipo_area_trabajo_id")
    private Integer workAreaId;

    @JsonProperty("empresa_id")
    private Integer companyId;

    @JsonProperty("empresa_activo")
    private Boolean companyActive;

    @JsonProperty("tipo_rol_persona_id")
    private Integer personRoleId;

    @JsonProperty("licencia_conducir")
    private String driversLicense;

    @JsonProperty("fecha_vigencia_licencia_conducir")
    private LocalDateTime driversLicenseValidityDate;

    @JsonProperty("carnet")
    private String card;

    @JsonProperty("fecha_vigencia_carnet")
    private LocalDateTime cardValidityDate;

    @JsonProperty("genero")
    private Character gender;

    @JsonProperty("situacion_laboral")
    private Integer employmentStatus;

    @JsonProperty("rol_persona_multiple")
    private List<Integer> rolPersonaMultiple;
}
