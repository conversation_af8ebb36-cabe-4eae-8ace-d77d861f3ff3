package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class InspectionChecklistRegisterOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("resp_new_id")
    private Integer respNewId;
}