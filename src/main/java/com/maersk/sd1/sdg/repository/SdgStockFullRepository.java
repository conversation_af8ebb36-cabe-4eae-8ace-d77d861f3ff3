package com.maersk.sd1.sdg.repository;

import com.maersk.sd1.common.repository.StockFullRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SdgStockFullRepository extends StockFullRepository {

    @Query(value = "SELECT SF.gateInEir.id FROM StockFull SF " +
            "WHERE SF.gateOutEir.id = :eirId " +
            "AND SF.inStock = FALSE " +
            "AND SF.active = TRUE " +
            "AND SF.gateInEir.active = TRUE " +
            "AND SF.gateInEir.truckDepartureDate IS NULL")
    Integer getEirAuxByEirId(@Param("eirId") Integer eirId);
}
