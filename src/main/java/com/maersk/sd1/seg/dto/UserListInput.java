package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import lombok.Data;

@Data
public class UserListInput {

    @Data
    public static class Input {

        @JsonProperty("user_ids")
        private String userIds;

        @JsonProperty("user_id")
        private Integer userId;

        @JsonProperty("id")
        private String alias;

        @JsonProperty("email")
        private String email;

        @JsonProperty("names")
        private String names;

        @JsonProperty("status")
        private Character status;

        @JsonProperty("roles")
        private String roles;

        @JsonProperty("companies")
        private String companies;

        @JsonProperty("page")
        @Min(1)
        private Integer page;

        @JsonProperty("size")
        @Min(1)
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }
}
