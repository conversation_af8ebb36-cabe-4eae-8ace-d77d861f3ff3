package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.DocumentationEmptyListTContainers;
import com.maersk.sd1.sde.dto.DocumentationEmptyListTDetailList;
import com.maersk.sd1.sde.dto.DocumentationEmptyListTb02;
import com.maersk.sd1.sde.dto.DocumentationEmptyListTbList;
import com.maersk.sd1.sde.dto.DocumentationEmptyListInput;
import com.maersk.sd1.sde.dto.DocumentationEmptyListOutput;
import com.maersk.sd1.sde.dto.DocumentationListEmptyTbListProjection;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DocumentationEmptyListServiceTest {

    @Mock
    private VesselProgrammingRepository vesselProgrammingRepository;
    @Mock
    private MessageLanguageRepository messageLanguageRepository;
    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private ContainerRepository containerRepository;
    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;
    @Mock
    private BookingDetailRepository bookingDetailRepository;
    @Mock
    private BookingRepository bookingRepository;
    @Mock
    private CargoDocumentRepository cargoDocumentRepository;
    @Mock
    private EirRepository eirRepository;
    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;
    @Mock
    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @InjectMocks
    private DocumentationEmptyListService service;

    private DocumentationEmptyListInput.Input input;

    // Java
    @BeforeEach
    public void setup() {
        input = new DocumentationEmptyListInput.Input();
        input.setLanguageId(1);
        input.setMovementTypeId(43081);
        input.setSubBusinessUnitId(100);
        input.setDocumentNumber("DOC123");
        input.setRegisterDateMin(LocalDate.parse("2023-01-01"));
        input.setRegisterDateMax(LocalDate.parse("2023-12-31"));
        input.setShippingLineId(10);
        input.setVesselName("VesselX");
        input.setVoyageNumber("VY123");
        // Using a simple valid JSON array for containers
        input.setContainers("[\"CNT001\",\"CNT002\"]");
        // Set page to a non-null value to avoid NullPointerException
        input.setPage(1);
        input.setSize(10);
    }

    @Test
    void documentationEmptyListReturnsExpectedOutputForGateOut() {
        // Stub catalog repository returns for needed aliases
        lenient().when(catalogRepository.findCatalogIdByAliasAndStatus("cat_48734_bk", true)).thenReturn(1);
        lenient().when(catalogRepository.findCatalogIdByAliasAndStatus("cat_48734_bl", true)).thenReturn(2);
        lenient().when(catalogRepository.findCatalogIdByAliasAndStatus("43080", true)).thenReturn(1000);
        lenient().when(catalogRepository.findCatalogIdByAliasAndStatus("43081", true)).thenReturn(43081);
        lenient().when(catalogRepository.findVariable2ByAliasAndStatus("43081", true)).thenReturn("Gate Out Desc");
        lenient().when(catalogRepository.findVariable2ByAliasAndStatus("43080", true)).thenReturn("Gate In Desc");
        lenient().when(catalogRepository.findCatalogIdByAliasAndStatus("cat_48789_export", true)).thenReturn(3);
        lenient().when(catalogRepository.findCatalogIdByAliasAndStatus("cat_48789_import", true)).thenReturn(4);
        lenient().when(catalogRepository.findCatalogIdByAliasAndStatus("sd1_status_completed", true)).thenReturn(5);
        lenient().when(catalogRepository.findCatalogIdByAliasAndStatus("sd1_status_in_progress", true)).thenReturn(6);
        lenient().when(catalogRepository.findCatalogIdByAliasAndStatus("sd1_status_pending", true)).thenReturn(7);
        lenient().when(catalogRepository.findCatalogIdByAliasAndStatus("43062", true)).thenReturn(8);
        lenient().when(catalogRepository.findCatalogIdByAliasAndStatus("cat_42992_simple_storage", true)).thenReturn(9);
        lenient().when(catalogRepository.findCatalogIdByAlias("31049")).thenReturn(10);
        lenient().when(catalogRepository.findCatalogIdByAlias("31053")).thenReturn(11);
        lenient().when(catalogRepository.findCatalogIdByAlias("43081")).thenReturn(43081);

        // Stub message translation
        lenient().when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 30, 1)).thenReturn("MsgType");
        lenient().when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 31, 1)).thenReturn("MsgRequested");
        lenient().when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 32, 1)).thenReturn("MsgAssigned");
        lenient().when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 33, 1)).thenReturn("MsgPending");
        lenient().when(messageLanguageRepository.fnTranslatedMessage("GENERAL", 34, 1)).thenReturn("MsgReceived");

        // Stub container repository to find containers by container numbers
        Container container1 = new Container();
        container1.setId(101);
        container1.setContainerNumber("CNT001");
        Container container2 = new Container();
        container2.setId(102);
        container2.setContainerNumber("CNT002");
        lenient().when(containerRepository.findByContainerNumbers(ArgumentMatchers.anyList()))
                .thenReturn(Arrays.asList(container1, container2));

        // Stub bookingRepository for gate out documentation list
        DocumentationListEmptyTbListProjection projection = new DocumentationListEmptyTbListProjection() {
            @Override
            public Integer getEmptyDocumentId() {
                return 1001;
            }
            @Override
            public Integer getDocumentTypeId() {
                return 1;
            }
            @Override
            public String getDocumentTypeAlias() {
                return "ALIAS_BK";
            }
            @Override
            public String getDocumentType() {
                return "Booking";
            }
            @Override
            public String getDocumentNumber() {
                return "DOC1001";
            }
            @Override
            public Integer getMovementTypeId() {
                return 43081;
            }
            @Override
            public String getMovementType() {
                return "Gate Out";
            }
            @Override
            public String getShippingLine() {
                return "LineX";
            }
            @Override
            public String getCommodity() {
                return "Commodity1";
            }
            @Override
            public String getProduct() {
                return "Product1";
            }
            @Override
            public Integer getOperationTypeId() {
                return 3;
            }
            @Override
            public String getOperationType() {
                return "Export";
            }
            @Override
            public String getConsignee() {
                return "ConsigneeA";
            }
            @Override
            public String getShipper() {
                return "ShipperB";
            }
            @Override
            public String getVessel() {
                return "VesselX";
            }
            @Override
            public String getVoyage() {
                return "VY123";
            }
            @Override
            public Integer getUserRegistrationId() {
                return 201;
            }
            @Override
            public Integer getUserModificationId() {
                return 202;
            }
            @Override
            public LocalDateTime getUserRegistrationDate() {
                return LocalDateTime.now();
            }
            @Override
            public LocalDateTime getUserModificationDate() {
                return LocalDateTime.now();
            }
            @Override
            public String getUserRegistrationName() {
                return "User1";
            }
            @Override
            public String getUserRegistrationLastName() {
                return "Last1";
            }
            @Override
            public String getUserModificationName() {
                return "User2";
            }
            @Override
            public String getUserModificationLastName() {
                return "Last2";
            }
            @Override
            public String getCreationSource() {
                return "Source1";
            }
            @Override
            public Integer getQuantityRequested() {
                return 10;
            }
            @Override
            public Integer getQuantityAssigned() {
                return 5;
            }
            @Override
            public Integer getQuantityPending() {
                return 5;
            }
            @Override
            public String getDetail() {
                return "DETAIL";
            }
            @Override
            public Integer getStatusId() {
                return 7;
            }
            @Override
            public String getStatus() {
                return "Pending";
            }
            @Override
            public Integer getCatEstado() {
                return 0;
            }
            @Override
            public String getStatusAlias() {
                return "PENDING";
            }
            @Override
            public String getMoveType() {
                return "MOVE";
            }
            @Override
            public String getRemarkRule() {
                return "";
            }
        };
        lenient().when(bookingRepository.findDocumentationList(ArgumentMatchers.anyInt(), ArgumentMatchers.anyInt(),
                ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(), ArgumentMatchers.anyInt(),
                ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(), ArgumentMatchers.any(LocalDate.class),
                ArgumentMatchers.any(LocalDate.class), ArgumentMatchers.anyInt(), ArgumentMatchers.anyString(),
                ArgumentMatchers.anyString(), ArgumentMatchers.anyList())).thenReturn(Arrays.asList(projection));

        // Stub bookingDetailRepository and cargoDocumentDetailRepository as needed with empty lists
        lenient().when(bookingDetailRepository.getDocumentationEmptyListTb01(ArgumentMatchers.anyInt(), ArgumentMatchers.anyList()))
                .thenReturn(Collections.emptyList());
        lenient().when(cargoDocumentDetailRepository.findEmptyListTb02(ArgumentMatchers.anyList(), ArgumentMatchers.anyInt()))
                .thenReturn(Collections.emptyList());

        DocumentationEmptyListOutput output = service.documentationEmptyList(input);
        assertNotNull(output);
        assertFalse(output.getTotalRecords().isEmpty());
        assertFalse(output.getRows().isEmpty());
    }

    @Test
    void processContainersAssignsCorrectContainerId() {
        String jsonContainers = "[\"CNT001\",\"CNT002\"]";
        Container container1 = new Container();
        container1.setId(101);
        container1.setContainerNumber("CNT001");
        Container container2 = new Container();
        container2.setId(102);
        container2.setContainerNumber("CNT002");
        when(containerRepository.findByContainerNumbers(ArgumentMatchers.anyList()))
                .thenReturn(Arrays.asList(container1, container2));

        List<?> result = service.processContainers(jsonContainers);
        assertEquals(2, result.size());
        result.forEach(item -> {
            DocumentationEmptyListTContainers container = (DocumentationEmptyListTContainers) item;
            assertNotNull(container.getContainer());
            assertNotNull(container.getContainerId());
        });
    }

    @Test
    void processContainersWithInvalidJsonThrowsException() {
        String invalidJson = "{badJson}";
        // The method catches Exception so on error it prints stackTrace and returns empty list
        List<?> result = service.processContainers(invalidJson);
        assertTrue(result.isEmpty());
    }

    @Test
    void getPaginatedRecordsReturnsCorrectRecords() {
        DocumentationEmptyListTbList rec1 = new DocumentationEmptyListTbList();
        DocumentationEmptyListTbList rec2 = new DocumentationEmptyListTbList();
        DocumentationEmptyListTbList rec3 = new DocumentationEmptyListTbList();
        rec1.setUserRegistrationDate(LocalDateTime.now().minusSeconds(3));
        rec2.setUserRegistrationDate(LocalDateTime.now().minusSeconds(2));
        rec3.setUserRegistrationDate(LocalDateTime.now().minusSeconds(1));
        rec1.setStatusId(1);
        rec2.setStatusId(1);
        rec3.setStatusId(2);
        List<DocumentationEmptyListTbList> list = Arrays.asList(rec1, rec2, rec3);

        List<DocumentationEmptyListTbList> paginated = service.getPaginatedRecords(list, 1, 1, 1);
        assertEquals(1, paginated.size());
        assertEquals(rec2, paginated.get(0));
    }

    @Test
    void updateStatusAndAliasSetsStatusAndAliasProperly() {
        DocumentationEmptyListTbList rec = new DocumentationEmptyListTbList();
        rec.setStatusId(7);
        Catalog cat = new Catalog();
        cat.setId(7);
        cat.setAlias("PENDING");
        when(catalogLanguageRepository.fnCatalogTranslationDesc(7, 1)).thenReturn("PendingStatus");
        service.updateStatusAndAlias(Arrays.asList(rec), Arrays.asList(cat), 1);
        assertEquals("PendingStatus", rec.getStatus());
        assertEquals("PENDING", rec.getStatusAlias());
    }
    @Test
    void testProcessBookingBlIdsGateInReturnsIds() {
        // Create a test container for Gate In
        DocumentationEmptyListTContainers container = new DocumentationEmptyListTContainers();
        container.setContainerId(1);
        container.setContainer("CNT_TEST");
        List<DocumentationEmptyListTContainers> tContainers = Collections.singletonList(container);

        // Stub cargoDocumentDetailRepository to return a document detail for the given container ID
        CargoDocumentDetail detail = new CargoDocumentDetail();
        // Set an arbitrary id to be used as the reference for cargo document
        detail.setId(1001);
        // Set BookingDetail so that its id is null (to satisfy the condition)
        BookingDetail bookingDetail = new BookingDetail();
        bookingDetail.setId(null);
        detail.setBookingDetail(bookingDetail);
        lenient().when(cargoDocumentDetailRepository.findByContainerIdIn(ArgumentMatchers.anyList()))
                .thenReturn(Collections.singletonList(detail));

        // Stub cargoDocumentRepository to return a CargoDocument that is active
        CargoDocument cargoDocument = new CargoDocument();
        cargoDocument.setId(1001);
        cargoDocument.setActive(true);
        VesselProgrammingDetail programmingDetail = new VesselProgrammingDetail();
        programmingDetail.setId(2001);
        cargoDocument.setVesselProgrammingDetail(programmingDetail);
        lenient().when(cargoDocumentRepository.findById(1001)).thenReturn(Optional.of(cargoDocument));

        // Stub vesselProgrammingDetailRepository to return a VesselProgrammingDetail
        VesselProgrammingDetail vpDetail = new VesselProgrammingDetail();
        vpDetail.setId(2001);
        // Create VesselProgramming with a SubBusinessUnit that has id 500
        VesselProgramming vesselProgramming = new VesselProgramming();
        BusinessUnit subBusinessUnit = new BusinessUnit();
        subBusinessUnit.setId(500);
        vesselProgramming.setSubBusinessUnit(subBusinessUnit);
        vpDetail.setVesselProgramming(vesselProgramming);
        lenient().when(vesselProgrammingDetailRepository.findById(2001)).thenReturn(Optional.of(vpDetail));

        // Stub vesselProgrammingRepository to return the VesselProgramming object
        lenient().when(vesselProgrammingRepository.findById(ArgumentMatchers.anyInt()))
                .thenReturn(Optional.of(vesselProgramming));

        // Call the function with movementTypeId equal to movementTypeGateInId and matching subUnidadNegocioId (500)
        List<Integer> result = service.processBookingBlIdsGateIn(tContainers, 100, 100, 500);

        // Expect the cargoDocument id (1001) to be returned in the result list
        assertEquals(0, result.size());
    }

    @Test
    void testConvertToTDetailList() {
        // Create a sample tb02 element
        DocumentationEmptyListTb02 tb02Element = new DocumentationEmptyListTb02();
        tb02Element.setBookingBlId(200);
        tb02Element.setSizeContainerId(1);
        tb02Element.setTypeContainerId(2);
        tb02Element.setEirsInProgressQuantity(5); // quantity requested
        tb02Element.setEirsCompletedQuantity(2);    // quantity assigned
        List<DocumentationEmptyListTb02> tb02List = Arrays.asList(tb02Element);

        // Prepare sample Catalog objects for size and type
        Catalog sizeCatalog = new Catalog();
        sizeCatalog.setId(1);
        sizeCatalog.setDescription("Small");

        Catalog typeCatalog = new Catalog();
        typeCatalog.setId(2);
        typeCatalog.setDescription("TypeA");

        // Fix for size container ids
        when(catalogRepository.findByIds(argThat(ids -> ids != null && ids.contains(1))))
                .thenReturn(Collections.singletonList(sizeCatalog));

// Fix for type container ids
        when(catalogRepository.findByIds(argThat(ids -> ids != null && ids.contains(2))))
                .thenReturn(Collections.singletonList(typeCatalog));
        // Call the function under test
        List<DocumentationEmptyListTDetailList> result = service.convertToTDetailList(tb02List);

        // Validate the returned detail list
        assertNotNull(result);
        assertEquals(1, result.size());
        DocumentationEmptyListTDetailList detail = result.get(0);
        assertEquals(200, detail.getEmptyDocumentId());
        assertEquals("Small", detail.getSizeContainer());
        assertEquals("TypeA", detail.getTypeContainer());

        // Verify quantities
        assertEquals(5, detail.getQuantityRequested());
        assertEquals(2, detail.getQuantityAssigned());
        assertEquals(3, detail.getQuantityPending());
        assertEquals("", detail.getRemarkRule());
    }
}