package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.EirActivityZoneRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EirActivityZoneServiceTest {

    @Mock
    private EirActivityZoneRepository eirActivityZoneRepository;

    @Mock
    private com.maersk.sd1.common.repository.EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    @InjectMocks
    private EirActivityZoneService eirActivityZoneService;

    @Test
    void testIsActivityConcluded_eirIdIsNull() {
        // Act
        boolean result = eirActivityZoneService.isActivityConcluded(null, "INSP");

        // Assert
        assertFalse(result);
    }

    @Test
    void testIsActivityConcluded_activityExists() {
        // Arrange
        Integer eirId = 1;
        String zoneCode = "INSP";
        when(eirActivityZoneRepository.existsByEirIdAndCatZoneActivity_DescriptionAndConcluded(eirId, zoneCode, true))
                .thenReturn(true);

        // Act
        boolean result = eirActivityZoneService.isActivityConcluded(eirId, zoneCode);

        // Assert
        assertTrue(result);
    }

    @Test
    void testFindLastActivityEndDate_eirIdIsNull() {
        // Act
        LocalDateTime result = eirActivityZoneService.findLastActivityEndDate(null, "PTI");

        // Assert
        assertNull(result);
    }

    @Test
    void testFindLastActivityEndDate_activityExists() {
        // Arrange
        Integer eirId = 1;
        String zoneCode = "PTI";
        LocalDateTime endDate = LocalDateTime.now();
        when(eirActivityZoneRepository.findLastEndDateForActivity(eirId, zoneCode)).thenReturn(endDate);

        // Act
        LocalDateTime result = eirActivityZoneService.findLastActivityEndDate(eirId, zoneCode);

        // Assert
        assertEquals(endDate, result);
    }

    @Test
    void testIsContainerBlocked_eirIdIsNull() {
        // Act
        boolean result = eirActivityZoneService.isContainerBlocked(null);

        // Assert
        assertFalse(result);
    }

    @Test
    void testIsContainerBlocked_containerBlocked() {
        // Arrange
        Integer eirId = 1;
        when(eirActivityZoneRepository.existsByEirIdAndCatZoneActivity_DescriptionAndBlockContainer(eirId, "INSP", true))
                .thenReturn(true);

        // Act
        boolean result = eirActivityZoneService.isContainerBlocked(eirId);

        // Assert
        assertTrue(result);
    }

    @Test
    void testFindSensorInPTI_sensorExists() {
        // Arrange
        Integer eirId = 1;
        when(eirActivityZoneRepository.findSensorInPTI(eirId)).thenReturn(true);

        // Act
        Boolean result = eirActivityZoneService.findSensorInPTI(eirId);

        // Assert
        assertTrue(result);
    }

    @Test
    void testFindSensorInPTI_noSensorExists() {
        // Arrange
        Integer eirId = 1;
        when(eirActivityZoneRepository.findSensorInPTI(eirId)).thenReturn(null);

        // Act
        Boolean result = eirActivityZoneService.findSensorInPTI(eirId);

        // Assert
        assertNull(result);
    }

    @Test
    void testFindCargoDocuments_noDocumentsFound() {
        // Arrange
        Integer eirId = 1;
        when(eirDocumentCargoDetailRepository.findCargoDocuments(eirId)).thenReturn(List.of());

        // Act
        String result = eirActivityZoneService.findCargoDocuments(eirId);

        // Assert
        assertEquals("", result);
    }

    @Test
    void testFindCargoDocuments_documentsFound() {
        // Arrange
        Integer eirId = 1;
        when(eirDocumentCargoDetailRepository.findCargoDocuments(eirId)).thenReturn(List.of("Doc1", "Doc2"));

        // Act
        String result = eirActivityZoneService.findCargoDocuments(eirId);

        // Assert
        assertEquals("Doc1, Doc2", result);
    }

    @Test
    void testFindConsignee_consigneeExists() {
        // Arrange
        Integer eirId = 1;
        when(eirDocumentCargoDetailRepository.findTopConsignee(eirId)).thenReturn("TopConsignee");

        // Act
        String result = eirActivityZoneService.findConsignee(eirId);

        // Assert
        assertEquals("TopConsignee", result);
    }

    @Test
    void testFindConsignee_noConsigneeExists() {
        // Arrange
        Integer eirId = 1;
        when(eirDocumentCargoDetailRepository.findTopConsignee(eirId)).thenReturn(null);

        // Act
        String result = eirActivityZoneService.findConsignee(eirId);

        // Assert
        assertNull(result);
    }

    @Test
    void testFindShipper_shipperExists() {
        // Arrange
        Integer eirId = 1;
        when(eirDocumentCargoDetailRepository.findTopShipper(eirId)).thenReturn("TopShipper");

        // Act
        String result = eirActivityZoneService.findShipper(eirId);

        // Assert
        assertEquals("TopShipper", result);
    }

    @Test
    void testFindShipper_noShipperExists() {
        // Arrange
        Integer eirId = 1;
        when(eirDocumentCargoDetailRepository.findTopShipper(eirId)).thenReturn(null);

        // Act
        String result = eirActivityZoneService.findShipper(eirId);

        // Assert
        assertNull(result);
    }
}

