package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class SftpConfigEditInput {

    @Data
    public static class Input {

        @JsonProperty("sftp_config_id")
        @NotNull
        private Integer sftpConfigId;

        @JsonProperty("id")
        @Size(max = 200)
        private String alias;

        @JsonProperty("sftp_host")
        @Size(max = 500)
        private String sftpHost;

        @JsonProperty("sftp_name")
        @Size(max = 200)
        private String sftpName;

        @JsonProperty("sftp_pass")
        @Size(max = 100)
        private String sftpPass;

        @JsonProperty("sftp_port")
        @Size(max = 10)
        private String sftpPort;

        @JsonProperty("sftp_path")
        @Size(max = 500)
        private String sftpPath;

        @JsonProperty("evento_despues_subir")
        @Size(max = 200)
        private String eventAfterUpload;

        @JsonProperty("es_ftp")
        private Boolean isFtp;

        @JsonProperty("requiere_private_key")
        private Boolean requiresPrivateKey;

        @JsonProperty("adjuntos_public_key")
        private String adjuntosPublicKey;

        @JsonProperty("adjuntos_private_key")
        private String adjuntosPrivateKey;

        @JsonProperty("requiere_passphrase")
        private Boolean requiresPassphrase;

        @JsonProperty("passphrase")
        @Size(max = 50)
        private String passphrase;

        @JsonProperty("estado")
        @NotNull
        private Boolean status;

        @JsonProperty("usuario_modificacion_id")
        @NotNull
        private Integer usuarioModificacionId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}