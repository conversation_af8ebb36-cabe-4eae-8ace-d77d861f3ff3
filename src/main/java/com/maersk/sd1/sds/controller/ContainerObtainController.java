package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.controller.dto.ContainerObtainInput;
import com.maersk.sd1.sds.controller.dto.ContainerObtainOutput;
import com.maersk.sd1.sds.service.ContainerObtainService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSContenedorServiceImp")
public class ContainerObtainController {

    private static final Logger logger = LogManager.getLogger(ContainerObtainController.class);

    private final ContainerObtainService containerObtainService;

    @PostMapping("/sdscontenedorObtener")
    public ResponseEntity<ResponseController<ContainerObtainOutput>> containerObtain(@RequestBody @Valid ContainerObtainInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
            }
            Integer containerId = request.getPrefix().getInput().getContainerId();
            if(containerId == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("containerId cannot be null."));
            }
            ContainerObtainOutput output = containerObtainService.obtainContainer(containerId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            ContainerObtainOutput output = new ContainerObtainOutput();
            output.setResponseStatus(0);
            output.setResponseMessage(e.getMessage());
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }
}