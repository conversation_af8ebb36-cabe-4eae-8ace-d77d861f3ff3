package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "tipo_cambio", schema = "ges")
public class TypeChange {
    @Id
    @Column(name = "tipo_cambio_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "moneda_primera_id", nullable = false)
    private Currency primaryCurrency;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "moneda_segunda_id", nullable = false)
    private Currency secondaryCurrency;

    @Column(name = "cambio_venta", precision = 18, scale = 7)
    private BigDecimal changeSale;

    @NotNull
    @Column(name = "fecha", nullable = false)
    private LocalDateTime date;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @ColumnDefault("getdate()")
    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "unidad_negocio_id", nullable = false)
    private BusinessUnit businessUnit;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "estado", nullable = false)
    private Catalog catStatus;

}