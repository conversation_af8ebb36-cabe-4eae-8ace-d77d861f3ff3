package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductObtainOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("producto_id")
    private Integer productId;

    @JsonProperty("unidad_negocio_id")
    private Integer businessUnitId;

    @JsonProperty("codigo_producto")
    private String productCode;

    @JsonProperty("nombre_producto")
    private String productName;

    @JsonProperty("cat_grupo_producto_id")
    private Integer catProductGroupId;

    @JsonProperty("cat_unidad_medida_peso_id")
    private Integer catWeightMeasureUnitId;

    @JsonProperty("cat_envase_id")
    private Integer catPackagingId;

    @JsonProperty("activo")
    private Boolean active;

    @JsonProperty("fecha_registro")
    private LocalDateTime registrationDate;

    @JsonProperty("fecha_modificacion")
    private LocalDateTime modificationDate;

    @JsonProperty("coparn_commodity1")
    private String bookingEdiCommodity1;

    @JsonProperty("coparn_commodity2")
    private String bookingEdiCommodity2;

    @JsonProperty("cat_tipo_refrigeracion_id")
    private Integer catRefrigerationTypeId;
}