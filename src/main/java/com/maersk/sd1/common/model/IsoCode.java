package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@NamedStoredProcedureQuery(
        name = "IsoCode.defaultIsoCodeContainer",
        procedureName = "sde.default_isocode_container",
        parameters = {
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "size_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.IN, name = "type_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "iso_code_id", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "tare", type = Integer.class),
                @StoredProcedureParameter(mode = ParameterMode.OUT, name = "payload", type = Integer.class)
        }
)
@Table(name = "codigo_iso", schema = "sds", uniqueConstraints = {
        @UniqueConstraint(name = "UniqueISOCode", columnNames = {"codigo_iso"})
})
public class IsoCode {
    @Id
    @Column(name = "codigo_iso_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "unidad_negocio_id")
    private BusinessUnit businessUnit;

    @Size(max = 4)
    @NotNull
    @Column(name = "codigo_iso", nullable = false, length = 4)
    private String isoCode;

    @Size(max = 100)
    @NotNull
    @Column(name = "descripcion", nullable = false, length = 100)
    private String description;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_tipo_contenedor_id", nullable = false)
    private Catalog catContainerType;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_tamano_id", nullable = false)
    private Catalog catSize;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @Column(name = "tara")
    private Integer tare;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_creationsource_isocode_id")
    private Catalog catCreationsourceIsocode;

    public IsoCode(Integer isoId) {
        this.id = isoId;
    }
}