package com.maersk.sd1.ges.controller;


import com.maersk.sd1.common.controller.dto.ResponseController;


import com.maersk.sd1.ges.dto.PersonListInputDTO;
import com.maersk.sd1.ges.dto.PersonListOutputDTO;
import com.maersk.sd1.ges.service.PersonListService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("ModuleADM/ADM/module/adm/ADMPersonaServiceImp")
public class PersonListController {

    private static final Logger logger = LogManager.getLogger(PersonListController.class);

    private final PersonListService personaListService;

    @Autowired
    public PersonListController(PersonListService personaListService)
    {
        this.personaListService = personaListService;
    }

    @PostMapping("/gespersonaListar")
    public ResponseEntity<ResponseController<PersonListOutputDTO>> personList(@RequestBody @Valid PersonListInputDTO.Root request) {
        try {
            PersonListInputDTO.Input input = request.getPrefix().getInput();
            PersonListOutputDTO response = personaListService.personList(input);
            return ResponseEntity.ok(new ResponseController<>(response));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            PersonListOutputDTO output = new PersonListOutputDTO();
            output.setTotalRegistros(0L);
            output.setPersonas(null);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
