package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Parametrization;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.controller.dto.InspectionMachineryInput;
import com.maersk.sd1.sde.controller.dto.InspectionMachineryOutput;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.EirActivityZone;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class InspectionMachineryListServiceTest {

    @InjectMocks
    private InspectionMachineryListService inspectionMachineryService;

    @Mock
    private UserRepository userRepository;

    @Mock
    private EirRepository eirRepository;

    @Mock
    private EirActivityZoneRepository eirActivityZoneRepository;

    @Mock
    private EirZoneRepository eirZoneRepository;

    @Mock
    private ParametrizationRepository parametrizationRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenValidInput_WhenListInspectionMachinery_ThenReturnValidList() {
        // arrange
        InspectionMachineryInput.Input input = new InspectionMachineryInput.Input();
        input.setBusinessUnitId(1L);
        input.setSubBusinessUnitId(2L);
        input.setSubBusinessUnitLocalId(3L);
        input.setUserId(999);
        input.setFilterBox(1);
        input.setIdiomaId(1);
        input.setPage(1);
        input.setSize(10);

        List<Parametrization> ptiTimes = new ArrayList<>();
        when(parametrizationRepository.findPtiTimesByBusinessUnit(anyLong())).thenReturn(ptiTimes);

        when(userRepository.findPersonIdByUserId(999)).thenReturn(10);

        when(eirRepository.countFinishedEirByUser(any(), any(), anyLong(), anyInt())).thenReturn(4);

        Eir eirMock = new Eir();
        eirMock.setId(123);
        eirMock.setTruckArrivalDate(LocalDateTime.now().minusMinutes(15));
        EirActivityZone eazMock = new EirActivityZone();
        eazMock.setId(1001);
        eazMock.setEir(eirMock);
        eazMock.setActive(true);
        eazMock.setConcluded(false);

        List<EirActivityZone> eazList = Collections.singletonList(eazMock);
        when(eirActivityZoneRepository.findEirActivityZoneNotEnStock(anyLong(), anyLong(), any(), any(), any())).thenReturn(eazList);

        when(eirZoneRepository.findLastEirZoneByEirIds(anyList())).thenReturn(new ArrayList<>());

        when(eirActivityZoneRepository.findInspectionActivity(anyBoolean(), anyInt(), anyList())).thenReturn(new ArrayList<>());

        when(eirZoneRepository.findLavZone(anyList())).thenReturn(new ArrayList<>());

        InspectionMachineryOutput result = inspectionMachineryService.listInspectionMachinery(input);

        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertEquals(1, result.getRecords().size());
        assertEquals(4, result.getSummary().get(0).getCompletedRecords());
    }

    @Test
    void givenExceptionOccurs_WhenListInspectionMachinery_ThenHandleExceptionAndProduceEmptyList() {
        InspectionMachineryInput.Input input = new InspectionMachineryInput.Input();
        input.setBusinessUnitId(1L);
        input.setSubBusinessUnitId(2L);
        input.setSubBusinessUnitLocalId(3L);
        input.setUserId(999);
        input.setIdiomaId(1);
        input.setPage(1);
        input.setSize(10);

        when(parametrizationRepository.findPtiTimesByBusinessUnit(anyLong())).thenThrow(new RuntimeException("DB error"));
        InspectionMachineryOutput result = inspectionMachineryService.listInspectionMachinery(input);
        assertNotNull(result);
        assertEquals(0, result.getRecords().size());
        assertEquals(0, result.getSummary().get(0).getTotalRecords());
    }

    @Test
    void givenTotalFinished_WhenGenerateNullOutput_ThenReturnOutputWithZeroRecordsAndSummaryValues() {
        InspectionMachineryOutput output = new InspectionMachineryOutput();
        InspectionMachineryOutput.InspectionMachinerySummaryDTO summary = new InspectionMachineryOutput.InspectionMachinerySummaryDTO();
        Integer totalFinished = 5;

        InspectionMachineryOutput result = inspectionMachineryService.generateNullOuput(output, summary, totalFinished);

        assertNotNull(result);
        assertNotNull(result.getRecords());
        assertEquals(0, result.getRecords().size());
        assertEquals(totalFinished, result.getSummary().get(0).getCompletedRecords());
        assertEquals(0, result.getSummary().get(0).getOnTimeRecords());
        assertEquals(0, result.getSummary().get(0).getAlertRecords());
        assertEquals(0, result.getSummary().get(0).getDelayedRecords());
        assertEquals(0, result.getSummary().get(0).getTotalRecords());
        assertNotNull(result.getSummary().get(0).getSystemDate());
    }

    @Test
    void givenNullPersonIdAndTotalFinished_WhenFindPersonIdAndCountFinishedEir_ThenReturnZero() {

        InspectionMachineryInput.Input input = new InspectionMachineryInput.Input();
        input.setUserId(999);
        input.setSubBusinessUnitLocalId(3L);

        when(userRepository.findPersonIdByUserId(999)).thenReturn(null);
        when(eirRepository.countFinishedEirByUser(any(), any(), anyLong(), anyInt())).thenReturn(null);

        Integer result = inspectionMachineryService.findPersonIdAndCountFinishedEir(input);

        assertNotNull(result);
        assertEquals(0, result);
    }
}