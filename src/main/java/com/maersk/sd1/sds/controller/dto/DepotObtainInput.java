package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class DepotObtainInput {

    @Data
    public static class Input {
        @JsonProperty("deposito_id")
        @NotNull(message = "deposito_id must not be null")
        private Integer depotId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }

    private DepotObtainInput() {
        // private constructor to hide the implicit public one
    }
}
