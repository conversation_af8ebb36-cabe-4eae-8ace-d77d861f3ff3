package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.Nationalized;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "ubigeo_onpe", schema = "ges")
public class UbigeoOnpe {
    @Id
    @Size(max = 6)
    @Column(name = "ubige_codigo", nullable = false, length = 6)
    private String ubigeCode;

    @Size(max = 100)
    @Nationalized
    @Column(name = "ubige_departamento", length = 100)
    private String ubigeDepartment;

    @Size(max = 100)
    @Nationalized
    @Column(name = "ubige_provincia", length = 100)
    private String ubigeProvince;

    @Size(max = 100)
    @Nationalized
    @Column(name = "ubige_distrito", length = 100)
    private String ubigeDistrict;

    @Size(max = 100)
    @Nationalized
    @Column(name = "ubige_tipo", length = 100)
    private String ubigeType;

}