package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.BookingBlockCancellationReleaseEditInput;
import com.maersk.sd1.sde.dto.BookingBlockCancellationReleaseEditOutput;
import com.maersk.sd1.sde.service.BookingBlockCancellationReleaseEditService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("modulesde/ModuleSDE/module/sde/SDECancelacionBloqueoBookingService")
public class BookingBlockCancellationReleaseEditController {

    private static final Logger logger = LogManager.getLogger(BookingBlockCancellationReleaseEditController.class);


    private final BookingBlockCancellationReleaseEditService service;

    @Autowired
    public BookingBlockCancellationReleaseEditController(BookingBlockCancellationReleaseEditService service) {
        this.service = service;
    }

    @PostMapping("/sdecancelacionBloqueoBookingLiberacionEditar")
    public ResponseEntity<ResponseController<BookingBlockCancellationReleaseEditOutput>> releaseEdit(
            @RequestBody @Valid BookingBlockCancellationReleaseEditInput.Root request) {
        try {
            BookingBlockCancellationReleaseEditInput.Input input = request.getPrefix().getInput();
            BookingBlockCancellationReleaseEditOutput output = service.releaseEdit(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing releaseEdit.", e);
            BookingBlockCancellationReleaseEditOutput output = new BookingBlockCancellationReleaseEditOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

