package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.seg.dto.RoleObtainOutputDTO;
import com.maersk.sd1.seg.dto.BusinessUnitProjection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface BusinessUnitRepository extends JpaRepository<BusinessUnit, Integer> {

    @Query(value = """
            SELECT bu.parentBusinessUnit.id FROM BusinessUnit bu WHERE bu.id = :businessUnitId
            """)
    Integer findParentBusinessUnitId(@Param("businessUnitId") Integer businessUnitId);

    BusinessUnit findByBusinesUnitAlias(String businessUnitAlias);


    Optional<BusinessUnit> findOneById(Integer id);

    @Query("SELECT u.name FROM BusinessUnit u WHERE u.id = :businessUnitId")
    String findNameByBusinessUnit(@Param("businessUnitId") Integer businessUnitId);

    @Query("SELECT u.businesUnitAlias FROM BusinessUnit u WHERE u.id = :businessUnitId AND u.status = true")
    String findAliasByBusinessUnit(@Param("businessUnitId") Integer businessUnitId);

    @Query(value = "SELECT [ges].[fn_FormatoDateTime](:businessUnitId)", nativeQuery = true)
    String getFormatoDateTime(@Param("businessUnitId") Integer businessUnitId);

    @Query(value = "SELECT [ges].[fn_FormatoDate](:businessUnitId)", nativeQuery = true)
    String getFormatoDate(@Param("businessUnitId") Integer businessUnitId);

    @Query("SELECT b.businesUnitAlias FROM BusinessUnit b WHERE b.id = :businessUnitId")
    String findAliasByBusinessUnitId(@Param("businessUnitId") Integer businessUnitId);

    @Query("SELECT COALESCE(d.utcNumber, bc.value) FROM BusinessUnit b " +
            "INNER JOIN BusinessUnitConfig bc ON bc.businessUnit.id = b.id " +
            "LEFT JOIN  DaylightSavingTime d ON d.id = b.daylightSaving.id " +
            "WHERE b.id = :businessUnitId " +
            "AND bc.catConfigurationType.id = :configTypeId")
    Integer getUTCNumberByBusinessUnitAndConfigType(@Param("businessUnitId") Integer businessUnitId, @Param("configTypeId") Integer configTypeId);

    @Query("SELECT b FROM BusinessUnit b WHERE b.parentBusinessUnit.id = :parentId AND b.status = true")
    List<BusinessUnit> findActiveSubUnitsByParent(@Param("parentId") Integer parentId);

    List<BusinessUnit> findByStatusTrueAndParentBusinessUnitIsNullOrderByNameAsc();

    @Query("SELECT b.id FROM BusinessUnit b WHERE b.businesUnitAlias = :alias")
    Integer findIdByAlias(@Param("alias") String alias);

    @Query("SELECT bu.parentBusinessUnit.id FROM BusinessUnit bu WHERE bu.id = :subUnId")
    Long findParentBusinessUnitIdLong(@Param("subUnId") Integer subBusinessUnitId);

    @Query("SELECT bu.parentBusinessUnit.id FROM BusinessUnit bu WHERE bu.id = :localId")
    Optional<Integer> findParentBusinessUnitIdOrNull(@Param("localId") Integer localId);
           
    Optional<BusinessUnit> findFirstByBusinesUnitAliasAndIdNot(String businesUnitAlias, Integer id);

    long countByBusinesUnitAliasAndSystem_Id(String businesUnitAlias, Integer id);

    @Query("SELECT new com.maersk.sd1.seg.dto.RoleObtainOutputDTO$BusinessUnitDto(" +
            "bu.id, bu.name, " +
            "(CASE WHEN bu.parentBusinessUnit IS NULL THEN null ELSE bu.parentBusinessUnit.id END), " +
            "(CASE WHEN bu.parentBusinessUnit IS NULL THEN null ELSE bu.parentBusinessUnit.name END), " +
            "(CASE WHEN bu.parentBusinessUnit IS NULL OR bu.parentBusinessUnit.parentBusinessUnit IS NULL THEN null ELSE bu.parentBusinessUnit.parentBusinessUnit.id END), " +
            "(CASE WHEN bu.system IS NULL THEN null ELSE bu.system.id END)) " +
            "FROM BusinessUnit bu " +
            "WHERE bu.system.id IN :systemIds " +
            "ORDER BY bu.id ASC")
    List<RoleObtainOutputDTO.BusinessUnitDto> findBusinessUnitsBySystemIds(@Param("systemIds") List<Integer> systemIds);

    @Modifying
    @Query("DELETE FROM BusinessUnit bu WHERE bu.id = :unidadNegocioId")
    void deleteBusinessUnit(@Param("unidadNegocioId") Integer unidadNegocioId);


    @Query(value = """
        SELECT DISTINCT
            UNI.unidad_negocio_id AS unidadNegocioId,
            UNI.nombre AS unidadNegocio,
            UNI.alias_unidad_negocio AS aliasUnidadNegocio
        FROM
            seg.unidad_negocio UNI (NOLOCK)
            INNER JOIN seg.rol_unidad_negocio RUN (NOLOCK) ON UNI.unidad_negocio_id = RUN.unidad_negocio_id
            INNER JOIN seg.rol ROL (NOLOCK) ON ROL.rol_id = RUN.rol_id
            INNER JOIN seg.usuario_rol USR (NOLOCK) ON USR.rol_id = ROL.rol_id
            INNER JOIN seg.usuario_rol_unidad_negocio URUN (NOLOCK) ON URUN.rol_id = RUN.rol_id AND URUN.unidad_negocio_id = RUN.unidad_negocio_id AND URUN.usuario_id = USR.usuario_id
            LEFT JOIN seg.usuario_config_un UCN (NOLOCK) ON UCN.unidad_negocio_id = UNI.unidad_negocio_id AND UCN.usuario_id = URUN.usuario_id
        WHERE
            UNI.estado = 1 AND
            USR.usuario_id = 1 AND
            ROL.estado = 1
        """, nativeQuery = true)
    List<BusinessUnitProjection> findUserBusinessUnits(Long userId, Long systemId);

    @Query("SELECT bu FROM BusinessUnit bu " +
            "WHERE bu.parentBusinessUnit.id = :parentId AND bu.status = true " +
            "ORDER BY bu.name ASC")
    List<BusinessUnit> findBusinessUnitChildren(Integer parentId);

    @Query("SELECT DISTINCT b FROM BusinessUnit b " +
            "JOIN UserBU ubu ON ubu.id.unitBusinessId = b.parentBusinessUnit.id " +
            "WHERE ubu.id.userId = :userId AND b.parentBusinessUnit IS NOT NULL " +
            "ORDER BY b.id")
    List<BusinessUnit> findSubUnitsFunction(Long userId);

    @Query("SELECT b FROM BusinessUnit b " +
            "WHERE b.system.id = :systemId AND b.status = true AND b.parentBusinessUnit IS NOT NULL " +
            "ORDER BY b.id")
    List<BusinessUnit> findSystemChildrenBusUnits(Long systemId);

    @Query("SELECT bu.businesUnitAlias FROM BusinessUnit bu WHERE bu.id = :businessUnitId")
    Optional<String> findAliasByBusinessUnitIdOrNull(@Param("businessUnitId") Integer businessUnitId);

    @Query("""
       SELECT bu
       FROM BusinessUnit bu
       INNER JOIN BusinessUnit pbu ON bu.parentBusinessUnit.id = pbu.id
       WHERE bu.id = :id
       """)
    Optional<BusinessUnit> findBusinessUnitById(@Param("id") Integer id);

    @Query("SELECT bu FROM BusinessUnit bu WHERE bu.businesUnitAlias IN :aliases")
    List<BusinessUnit> getBusinessUnitsByAliases(@Param("aliases") List<String> aliases);
    @Query("SELECT u.name FROM BusinessUnit u WHERE u.parentBusinessUnit.id = :businessUnitId")
    String findNameByParentBusinessUnit(@Param("businessUnitId") Integer businessUnitId);

}