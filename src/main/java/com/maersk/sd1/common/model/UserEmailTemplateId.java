package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class UserEmailTemplateId implements Serializable {
    private static final long serialVersionUID = 2992219725100867633L;
    @NotNull
    @Column(name = "usuario_id", nullable = false)
    private Integer userId;

    @NotNull
    @Column(name = "email_plantilla_id", nullable = false)
    private Integer emailTemplateId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        UserEmailTemplateId entity = (UserEmailTemplateId) o;
        return Objects.equals(this.emailTemplateId, entity.emailTemplateId) &&
                Objects.equals(this.userId, entity.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(emailTemplateId, userId);
    }

}