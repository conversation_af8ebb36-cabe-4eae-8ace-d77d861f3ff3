package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Input DTO mirroring the stored procedure parameters.
 * Includes prefix/root structure, as requested.
 */
public class UpdateVesselProgrammingOperationInput {

    private UpdateVesselProgrammingOperationInput() {}

    @Data
    public static class Input {
        @JsonProperty("programacion_nave_detalle_id")
        @NotNull
        private Integer detailId;

        @JsonProperty("manifiesto_ano")
        @Size(max = 4)
        private String manifestYear;

        @JsonProperty("manifiesto_numero")
        @Size(max = 10)
        private String manifestNumber;

        @JsonProperty("inicio_operacion")
        private LocalDateTime beginningOperation;

        @JsonProperty("fin_operacion")
        private LocalDateTime endingOperation;

        @JsonProperty("usuario_id")
        @NotNull
        private Integer userId;

        @JsonProperty("fecha_manifiesto_aduana")
        private LocalDateTime manifestCustomsDate;

        @JsonProperty("inicio_citas_devolucion")
        private LocalDateTime beginningReturnAppointment;

        @JsonProperty("fecha_cutoff_puerto_dry")
        private LocalDateTime dryPortCutOffDate;

        @JsonProperty("fecha_cutoff_puerto_reefer")
        private LocalDateTime reeferPortCutOffDate;

        @JsonProperty("fecha_cutoff_deposito_dry")
        private LocalDateTime dryDepositCutOffDate;

        @JsonProperty("fecha_cutoff_deposito_reefer")
        private LocalDateTime reeferDepositCutOffDate;

        @JsonProperty("fecha_inicio_citas_expo_ecu")
        private LocalDateTime expoEcuAppointmentsBegginingDate;

        /**
         * The JSON string representing cutoff retiro vacios.
         * Example: [{"linea_naviera_id":4102, "retiro_dry":"20210401 17:00", "retiro_reefer":"20210401 18:00"}]
         */
        @JsonProperty("cutoff_retiro_vacios")
        private String cutoffRetiroVacios;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}

