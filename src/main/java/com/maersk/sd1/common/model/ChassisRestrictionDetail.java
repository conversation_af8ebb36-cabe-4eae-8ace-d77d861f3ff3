package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.ChassisRestriction;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "chassis_restriction_detail", schema = "sdh")
public class ChassisRestrictionDetail {
    @Id
    @Column(name = "chassis_restriction_detail_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "chassis_restriction_id", nullable = false)
    private ChassisRestriction chassisRestriction;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_restriction_reason_id", nullable = false)
    private Catalog catRestrictionReason;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

}