package com.apm.business.service.module.sde;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEMercPlusService {

	@RequestMapping(value = "/sdemercplusComponentSearch", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdemercplusComponentSearch(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.mercplus_component_search","SDE");
			pResult.input("component", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdemercplusDamageLocationSearch", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdemercplusDamageLocationSearch(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.mercplus_damage_location_search","SDE");
			pResult.input("damage_location", Jpo.STRING);
			pResult.input("type_container", Jpo.CHARACTER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdemercplusDamageTypeSearch", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdemercplusDamageTypeSearch(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.mercplus_damage_type_search","SDE");
			pResult.input("damage_type", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdemercplusRepairMethodSearch", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdemercplusRepairMethodSearch(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.mercplus_repair_method_search","SDE");
			pResult.input("repair_method", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdemercplusComponentList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdemercplusComponentList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.mercplus_component_list","SDE");
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdemercplusDamageLocationList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdemercplusDamageLocationList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.mercplus_damage_location_list","SDE");
			pResult.input("type_container", Jpo.CHARACTER);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdemercplusDamageTypeList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdemercplusDamageTypeList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.mercplus_damage_type_list","SDE");
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

	@RequestMapping(value = "/sdemercplusRepairMethodList", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdemercplusRepairMethodList(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.mercplus_repair_method_list","SDE");
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}