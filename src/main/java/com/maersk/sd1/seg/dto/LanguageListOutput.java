package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Output DTO for listing Languages, containing total records and the list of records.
 */
@Data
public class LanguageListOutput {

    @JsonProperty("total_records")
    private Long totalRecords;

    @JsonProperty("records")
    private List<LanguageRecord> records;

    /**
     * Nested DTO for each Language record.
     */
    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class LanguageRecord {
        @JsonProperty("language_id")
        private Integer languageId;

        @JsonProperty("code")
        private String code;

        @JsonProperty("name")
        private String name;

        @JsonProperty("active")
        private Character active;

        @JsonProperty("registration_date")
        private LocalDateTime registrationDate;

        @JsonProperty("modification_date")
        private LocalDateTime modificationDate;

        @JsonProperty("registration_user_id")
        private Integer registrationUserId;

        @JsonProperty("registration_user_names")
        private String registrationUserNames;

        @JsonProperty("registration_user_lastnames")
        private String registrationUserLastNames;

        @JsonProperty("modification_user_id")
        private Integer modificationUserId;

        @JsonProperty("modification_user_names")
        private String modificationUserNames;

        @JsonProperty("modification_user_lastnames")
        private String modificationUserLastNames;
    }
}

