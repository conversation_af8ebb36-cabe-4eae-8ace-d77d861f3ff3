package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EstimateEmrSpare;
import com.maersk.sd1.sde.dto.EstimateEmrReplacementDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface EstimateEmrSpareRepository extends JpaRepository<EstimateEmrSpare, Integer> {

    @Query("""
        SELECT new com.maersk.sd1.sde.dto.EstimateEmrReplacementDTO(
            ees.id,
            ees.estimadoEmrDetalle.id,
            ees.partsSpare,
            ees.partSpare,
            ees.previousSerial,
            ees.serialNew,
            ees.costSpareXpiece
        )
        FROM EstimateEmrSpare ees
        WHERE ees.estimadoEmrDetalle.id = :detailId
        AND ees.active = true
    """)
    List<EstimateEmrReplacementDTO> findReplacementsByDetailId(@Param("detailId") Integer detailId);

    @Query("select e from EstimateEmrSpare e where e.estimadoEmrDetalle.id = :detailId and e.active = true")
    List<EstimateEmrSpare> findByEstimadoEmrDetalleIdAndActiveTrue(@Param("detailId") Integer detailId);
}