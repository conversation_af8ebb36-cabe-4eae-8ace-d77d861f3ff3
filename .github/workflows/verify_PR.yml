name: Verify the PR
run-name: ${{ github.actor }} is verify the PR.

on:
  pull_request:
    branches:
      - Develop

jobs:
  build:

    runs-on: ${{matrix.os}}
    strategy:
      matrix:
        os: [ubuntu-latest]

    steps:

      - name: check out branch
        uses: actions/checkout@v3

      - name: Set up JDK 23
        uses: actions/setup-java@v3
        with:
          distribution: 'adopt'
          java-version: '23'

      - name: maven-settings-xml-action
        uses: whelk-io/maven-settings-xml-action@v20
        with:
          repositories: '[{ "id": "mvn-repo", "url": "https://maven.pkg.github.com/Maersk-Global/ohRest" }]'
          servers: '[{ "id": "mvn-repo", "username": "${{ secrets.GIT_USER }}", "password": "${{ secrets.GIT_PASSWORD }}" }]'
          output_file: maven/settings.xml

      - name: Create local Maven repository
        run: mkdir -p $HOME/.m2/repository

      - name: Copy Maven settings
        run: cp maven/settings.xml $HOME/.m2

      # - name: compile the project
      #   # go to app directory
      #   run: mvn -B compile --file pom.xml

      # - name: Run unit tests
      #   # go to app directory
      #   run: mvn -B test --file pom.xml
      
      - name: Run tests, generate coverage, and run SonarQube analysis
        run: mvn clean verify --file pom.xml sonar:sonar
          -Dsonar.host.url=${{ secrets.MDN_SONARQUBE_HOST }} 
          -Dsonar.login=${{ secrets.MDN_SONARQUBE_TOKEN }}
          -Dsonar.pullrequest.key=${{ github.event.pull_request.number }}
          -Dsonar.pullrequest.branch=${{ github.head_ref }}
          -Dsonar.pullrequest.base=${{ github.base_ref }}
          -Dsonar.scm.provider=git
          -Dsonar.newCode.referenceBranch=${{ github.base_ref }}
          -Dsonar.coverage.exclusions=**/*Test*.java
          -Dsonar.scm.revision=${{ github.event.pull_request.head.sha }}