package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;

import com.maersk.sd1.sds.controller.dto.ActivityLogUpdateInput;
import com.maersk.sd1.sds.controller.dto.ActivityLogUpdateOutput;
import com.maersk.sd1.sds.service.ActivityLogUpdateService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSActivityLogServiceImp")
public class ActivityLogUpdateController {

    private static final Logger logger = LogManager.getLogger(ActivityLogUpdateController.class.getName());

    private final ActivityLogUpdateService activityLogUpdateService;

    @Autowired
    public ActivityLogUpdateController(ActivityLogUpdateService activityLogUpdateService)
    {
        this.activityLogUpdateService = activityLogUpdateService;
    }

    @PostMapping("/sdsactivityLogUpdate")
    public ResponseEntity<ResponseController<ActivityLogUpdateOutput>> updateActivityLog(@RequestBody @Valid ActivityLogUpdateInput.Root request) {
        try {
            ActivityLogUpdateInput.Input input = request.getPrefix().getInput();
            ActivityLogUpdateOutput output = activityLogUpdateService.updateActivityLog(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            ActivityLogUpdateOutput output = new ActivityLogUpdateOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            output.setRespIntegration(null);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
