package com.maersk.sd1.sdg.repository;

import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.TransportPlanningDetail;
import com.maersk.sd1.common.repository.CargoDocumentDetailRepository;
import com.maersk.sd1.sdg.dto.GateInGeneralEquipmentFindV3EquipmentData;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface GateInGeneralEquipmentFindV3Repository extends CargoDocumentDetailRepository {

    @Query(value = """
            SELECT DISTINCT 
            new com.maersk.sd1.sdg.dto.GateInGeneralEquipmentFindV3EquipmentData(
            NULL,
            vpr.subBusinessUnit.id,
            cdd.container.id,
            cdd.id,
            vpd.catOperation.id,
            COALESCE(cdd.receivedQuantity, CAST (0 AS java.math.BigDecimal)),
            COALESCE(cdd.receivedWeight, CAST (0 AS java.math.BigDecimal)),
            cdd.catCargoCondition.id,
            cdd.registrationDate,
            vpd.id,
            vpr.vessel.id,
            vpr.voyage,
            cdc.cargoDocument,
            cdc.shippingLine.id,
            cdc.catCargoDocumentType.id,
            cdc.shipperCompany.id,
            cdc.consigneeCompany.id,
            cdd.catFullReceiptReason.id,
            CAST(cdd.manifestedWeight AS java.math.BigDecimal),
            COALESCE(cdd.catWeightMeasureUnit.id, :isMeassureWeightKG),
            cnt.isoCode.id,
            isc.isoCode,
            cnt.containerNumber,
            cnt.catSize.id,
            cnt.catContainerType.id,
            NULL,
            NULL,
            NULL,
            NULL,
            NULL,
            COALESCE(CAST(rfr.code AS java.lang.Integer), 0),
            CASE WHEN (isc.catContainerType.id = :flat OR isc.catContainerType.id = :flatRack) THEN 1 ELSE 0 END,
            catOpt.variable1,
            cdc.catMoveType.id,
            CASE WHEN vpd.catOperation.id IN (:isOperSimpleStorage) THEN :isFull WHEN vpd.catOperation.id IN (:isOperDischargeFull, :isOperDischargeEmpty, :isOperLoadFull, :isOperDischargeCabotaje) THEN :isEmpty END,
            NULL
            )
            FROM
            CargoDocumentDetail cdd
            INNER JOIN cdd.cargoDocument cdc
            INNER JOIN cdc.vesselProgrammingDetail vpd
            INNER JOIN vpd.catOperation catOpt
            INNER JOIN vpd.vesselProgramming vpr
            INNER JOIN VesselProgrammingContainer vpc ON (vpc.container.id = cdd.container.id AND vpd.id = cdc.vesselProgrammingDetail.id)
            INNER JOIN cdd.container cnt
            LEFT JOIN cnt.isoCode isc
            LEFT JOIN isc.catContainerType rfr
            WHERE
            cdd.id IN :documentCargoDetailIds
            AND vpr.subBusinessUnit.id = :subBusinessUnitId
            AND vpd.catOperation.id NOT IN (:isOperLoadEmpty, :isOperLoadCabotaje)
            AND cdd.active = true
            AND cdc.active = true
            AND vpr.active = true
            AND vpc.active = true
            """)
    List<GateInGeneralEquipmentFindV3EquipmentData> getContainerDocumentation(@Param("documentCargoDetailIds") List<Integer> documentCargoDetailIds, @Param("subBusinessUnitId")Integer subBusinessUnitId, @Param("isOperLoadEmpty") Integer isOperLoadEmpty, @Param("isOperLoadCabotaje") Integer isOperLoadCabotaje, @Param("isMeassureWeightKG") Integer isMeassureWeightKG, @Param("flat") Integer flat, @Param("flatRack") Integer flatRack, @Param("isOperSimpleStorage") Integer isOperSimpleStorage, @Param("isOperDischargeFull") Integer isOperDischargeFull, @Param("isOperDischargeEmpty") Integer isOperDischargeEmpty, @Param("isOperLoadFull") Integer isOperLoadFull, @Param("isOperDischargeCabotaje") Integer isOperDischargeCabotaje, @Param("isFull") Integer isFull, @Param("isEmpty") Integer isEmpty);

    @Query(value = "SELECT eir.subBusinessUnit.id FROM Eir eir WHERE eir.container.id = :containerId AND eir.active = true ORDER BY eir.truckArrivalDate DESC")
    Integer getLastSubBusinessUnitId(@Param("containerId") Integer containerId);

    @Query(value = """
            SELECT tpd FROM TransportPlanningDetail tpd INNER JOIN tpd.transportPlanning tpl WHERE tpd.cargoDocumentDetail.id = :cargoDocumentDetailId AND tpl.catTrkMoveType.id = :isGateIn AND tpd.active = true AND tpl.active = true
            """)
    TransportPlanningDetail getFullTransportPlanningDetail(@Param("cargoDocumentDetailId") Integer cargoDocumentDetail,  @Param("isGateIn") Integer isGateIn);

    @Procedure(name = "CargoDocumentDetail.validateContainerStock")
    String validateContainerStock(
            @Param("business_unit_id") Integer businessUnitId,
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("cat_movimiento_id") Integer catMovementId,
            @Param("cat_empty_full_id") Integer catEmptyFullId,
            @Param("container_id") Integer containerId,
            @Param("container_number") String containerNumber,
            @Param("user_id") Integer userId,
            @Param("creation_origen_id") Integer creationOriginId,
            @Param("message_trace") String messageTrace,
            @Param("languaje_id") Integer languageId
    );

    @Procedure(name = "CargoDocumentDetail.validateDemurrageDate")
    Map<String, Object> validateDemurrageDate(
            @Param("business_unit_id") Integer businessUnitId,
            @Param("sub_business_unit_id") Integer subBusinessUnitId,
            @Param("shipping_line_id") Integer shippingLineId,
            @Param("container_id") Integer containerId,
            @Param("programacion_nave_detalle_id") Integer vesselProgrammingDetailId,
            @Param("languaje_id") Integer languageId
    );

    @Query(value = """
            SELECT 1 eir
            FROM Eir eir 
            INNER JOIN eir.subBusinessUnit sbu 
            INNER JOIN eir.catMovement catMvm
            WHERE sbu.id = :subBusinessUnitId
            AND eir.externalDocumentNumber = :apsId
            AND eir.active = true
            ORDER BY eir.truckArrivalDate DESC
            """)
    Eir checkEcuadorColombiaEir(@Param("subBusinessUnitId") Integer subBusinessUnitId, @Param("apsId") Integer apsId);
}
