package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.BusinessUnitCurrency;
import com.maersk.sd1.common.model.UnitBusinessCoinId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface BusinessUnitCurrencyRepository extends JpaRepository<BusinessUnitCurrency, UnitBusinessCoinId> {

        @Query(value = "SELECT " +
                "MON.moneda_id AS currencyId, " +
                "MON.nombre AS name, " +
                "MON.abreviatura AS abbreviation, " +
                "MON.simbolo AS symbol, " +
                "MON.separador_miles AS separatorMiles, " +
                "MON.separador_decimales AS separatorDecimals, " +
                "MON.precision, " +
                "MON.ICU " +
                "FROM seg.unidad_negocio_moneda UNM " +
                "INNER JOIN ges.moneda MON ON MON.moneda_id = UNM.moneda_id " +
                "WHERE UNM.unidad_negocio_id = :businessUnitId", nativeQuery = true)
        List<Object[]> getCurrencyDetailByBusinessUnitId(@Param("businessUnitId") Integer businessUnitId);

        void deleteByBusinessUnit(BusinessUnit businessUnit);

        @Modifying
        @Query("DELETE FROM BusinessUnitCurrency buc WHERE buc.businessUnit.id = :unidadNegocioId")
        void deleteBusinessUnitCurrencyByBusinessUnit(@Param("unidadNegocioId") Integer unidadNegocioId);

}