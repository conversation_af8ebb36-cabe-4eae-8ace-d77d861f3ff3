package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.SftpConfigEditInput;
import com.maersk.sd1.ges.dto.SftpConfigEditOutput;
import com.maersk.sd1.ges.service.SftpConfigEditService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMSftpConfigServiceImp")
public class SftpConfigEditController {

    private static final Logger logger = LogManager.getLogger(SftpConfigEditController.class);

    private final SftpConfigEditService sftpConfigEditService;

    @PostMapping("/gessftpConfigEditar")
    public ResponseEntity<ResponseController<SftpConfigEditOutput>> sdgSftpConfigEdit(@RequestBody @Valid SftpConfigEditInput.Root request) {
        logger.info("Request received sdgSftpConfigEdit: {}", request);
        SftpConfigEditOutput output = new SftpConfigEditOutput();
        try {

            if(request==null || request.getPrefix()==null || request.getPrefix().getInput()==null){
                output.setRespMensaje("Invalid request");
                output.setRespEstado(0);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ResponseController<>(output));
            }

            SftpConfigEditInput.Input input = request.getPrefix().getInput();
            logger.info("Extracted input: {}", input);

            output = sftpConfigEditService.editSftpConfig(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            output.setRespMensaje(e.getMessage());
            output.setRespEstado(0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}