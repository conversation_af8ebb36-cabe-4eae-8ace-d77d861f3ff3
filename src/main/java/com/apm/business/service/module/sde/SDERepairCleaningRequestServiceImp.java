package com.apm.business.service.module.sde;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sde/SDERepairCleaningRequestServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDERepairCleaningRequestServiceImp extends SDERepairCleaningRequestService {

	@RequestMapping(value = "/sdelistPendingRepairCleanningRequest", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdelistPendingRepairCleanningRequest(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdelistPendingRepairCleaningRequest(ppo, request);
	}

}