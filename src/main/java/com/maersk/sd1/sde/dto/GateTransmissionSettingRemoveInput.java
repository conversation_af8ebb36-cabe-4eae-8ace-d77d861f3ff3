package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class GateTransmissionSettingRemoveInput {

    @Data
    public static class Input {

        @JsonProperty("seteo_edi_codeco_id")
        @NotNull
        private Integer seteoEdiCodecoId;

        @JsonProperty("usuario_modificacion_id")
        @NotNull
        private Integer usuarioModificacionId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
