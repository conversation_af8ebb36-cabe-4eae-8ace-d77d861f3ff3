package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class VesselObtainInput {

    @Data
    public static class Input {

        @JsonProperty("nave_id")
        @NotNull
        private Integer naveId;
    }

    @Data
    public static class Prefix {

        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {

        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
