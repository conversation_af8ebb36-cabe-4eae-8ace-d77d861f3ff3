package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.IsoCode;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.MockitoAnnotations.openMocks;

class InspectionBoxServiceTest {

    @InjectMocks
    private InspectionBoxService inspectionBoxService;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private BusinessUnitConfigRepository businessUnitConfigRepository;

    @Mock
    private ParametrizationRepository parametrizationRepository;

    @Mock
    private EirActivityZoneRepository eirActivityZoneRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private EirRepository eirRepository;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private ShippingLineRepository shippingLineRepository;

    @Mock
    private IsoCodeRepository isoCodeRepository;

    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;

    @Mock
    private EstimateEmrRepository estimateEmrRepository;

    @BeforeEach
    void setUp() {
        openMocks(this);
        when(catalogRepository.findIdsByAliases(anyList())).thenReturn(getMockCatalogData());
        when(catalogRepository.findAliasByCatalogId(1002)).thenReturn("alias2");
        when(catalogRepository.findAliasByCatalogId(1001)).thenReturn("alias1");
        when(catalogRepository.findAllById(anyList())).thenReturn(getCatalogs());
        when(businessUnitConfigRepository.findTimeZoneOffset(anyInt(), anyInt())).thenReturn("3");
        when(parametrizationRepository.findInspectionTimes(anyInt())).thenReturn(getInspectionTimeConfigDto());
        when(catalogLanguageRepository.fnCatalogoTraducidoDesLarga(anyInt(), anyInt())).thenReturn("long description");
        when(catalogLanguageRepository.getDescriptionByCatalogIdAndLanguageId(anyInt(), anyInt())).thenReturn("description");
        when(userRepository.findPersonIdByUserId(anyInt())).thenReturn(1);
        when(eirRepository.countEirRecords(any(LocalDateTime.class), any(LocalDateTime.class), anyInt(), anyInt())).thenReturn(2);
        when(businessUnitRepository.findAllById(anyList())).thenReturn(getBusinessUnits());
        when(shippingLineRepository.findAllById(anyList())).thenReturn(getShippingLines());
        when(isoCodeRepository.findAllById(anyList())).thenReturn(getIsoCodes());
        when(estimateEmrRepository.findEstimateEmrByEirIds(anyList(), anyInt())).thenReturn(getEstimateEmrDTOs());
        when(eirActivityZoneRepository.getPTIActivities(anyList(), anyInt())).thenReturn(getMockPTIActivityDTOs());
        when(eirActivityZoneRepository.findEirActivitiesWhenInStock(anyInt(), anyInt(), eq(null), eq(null),
                anyInt(), anyBoolean())).thenReturn(getEirDTOList());
        when(eirActivityZoneRepository.findEirActivitiesWhenNotInStock(anyInt(), anyInt(), eq(null), eq(null),
                anyInt(), anyBoolean())).thenReturn(getEirDTOList());
    }


    @Test
    void given_EirActivitiesAreInStock_when_GetInspectionBoxList_then_DisplayDTOsAreReturnedCorrectly() {
        //Given
        InspectionBoxInput.Root inputRoot = getInputRoot("1", null);

        // When
        InspectionBoxOutput output = inspectionBoxService.getInspectionBoxList(inputRoot);

        // Then
        assertNotNull(output);
        assertThat(output)
                .usingRecursiveComparison()
                .ignoringFields("displayDTOS.elapsedMinutes")
                .ignoringFieldsOfTypes(LocalDateTime.class)
                .isEqualTo(getInspectionBoxOutput());
        verify(catalogRepository, atLeastOnce()).findIdsByAliases(anyList());
        verify(catalogRepository, atLeastOnce()).findAliasByCatalogId(1002);
        verify(catalogRepository, atLeastOnce()).findAliasByCatalogId(1001);
        verify(businessUnitConfigRepository, atLeastOnce()).findTimeZoneOffset(anyInt(), anyInt());
        verify(parametrizationRepository, atLeastOnce()).findInspectionTimes(anyInt());
        verify(catalogLanguageRepository, atLeastOnce()).fnCatalogoTraducidoDesLarga(anyInt(), anyInt());
        verify(catalogLanguageRepository, atLeastOnce()).getDescriptionByCatalogIdAndLanguageId(anyInt(), anyInt());
        verify(userRepository, atLeastOnce()).findPersonIdByUserId(anyInt());
        verify(eirRepository, atLeastOnce()).countEirRecords(any(LocalDateTime.class), any(LocalDateTime.class), anyInt(), anyInt());
        verify(estimateEmrRepository, atLeastOnce()).findEstimateEmrByEirIds(anyList(), anyInt());
        verify(eirActivityZoneRepository, atLeastOnce()).getPTIActivities(anyList(), anyInt());
        verify(eirActivityZoneRepository, atLeastOnce()).findEirActivitiesWhenInStock(anyInt(), anyInt(), eq(null), eq(null),
                anyInt(), anyBoolean());
    }

    @Test
    void given_EirActivitiesAreNotInStock_when_GetInspectionBoxList_then_DisplayDTOsAreReturnedCorrectly() {
        //Given
        InspectionBoxInput.Root inputRoot = getInputRoot(null, 4);

        // When
        InspectionBoxOutput output = inspectionBoxService.getInspectionBoxList(inputRoot);

        // Then
        assertNotNull(output);
        assertThat(output)
                .usingRecursiveComparison()
                .ignoringFields("displayDTOS.elapsedMinutes")
                .ignoringFieldsOfTypes(LocalDateTime.class)
                .isEqualTo(getInspectionBoxOutput());
        verify(catalogRepository, atLeastOnce()).findIdsByAliases(anyList());
        verify(catalogRepository, atLeastOnce()).findAliasByCatalogId(1002);
        verify(catalogRepository, atLeastOnce()).findAliasByCatalogId(1001);
        verify(businessUnitConfigRepository, atLeastOnce()).findTimeZoneOffset(anyInt(), anyInt());
        verify(parametrizationRepository, atLeastOnce()).findInspectionTimes(anyInt());
        verify(catalogLanguageRepository, atLeastOnce()).fnCatalogoTraducidoDesLarga(anyInt(), anyInt());
        verify(catalogLanguageRepository, atLeastOnce()).getDescriptionByCatalogIdAndLanguageId(anyInt(), anyInt());
        verify(userRepository, atLeastOnce()).findPersonIdByUserId(anyInt());
        verify(eirRepository, atLeastOnce()).countEirRecords(any(LocalDateTime.class), any(LocalDateTime.class), anyInt(), anyInt());
        verify(estimateEmrRepository, atLeastOnce()).findEstimateEmrByEirIds(anyList(), anyInt());
        verify(eirActivityZoneRepository, atLeastOnce()).getPTIActivities(anyList(), anyInt());
        verify(eirActivityZoneRepository, atLeastOnce()).findEirActivitiesWhenNotInStock(anyInt(), anyInt(), eq(null), eq(null),
                anyInt(), anyBoolean());
    }

    @Test
    void given_InvalidPaginationParameters_When_GetInspectionBoxList_then_EmptyPaginatedResultsAreReturned() {
        //Given
        InspectionBoxInput.Root inputRoot = getInputRoot(null, null);
        when(eirActivityZoneRepository.findEirActivitiesWhenNotInStock(anyInt(), anyInt(), eq(null), eq(null),
                anyInt(), anyBoolean())).thenReturn(Collections.emptyList());

        // When
        InspectionBoxOutput output = inspectionBoxService.getInspectionBoxList(inputRoot);

        // Then
        assertNotNull(output);
        assertTrue(output.getDisplayDTOS().isEmpty());
        assertTrue(output.getInspectionBoxSummaryDTOS().isEmpty());
    }

    private InspectionBoxInput.Root getInputRoot(String inStock, Integer filterBox) {
        InspectionBoxInput.Input input = new InspectionBoxInput.Input();
        input.setSubBusinessUnitId(86);
        input.setSubBusinessUnitLocalId(87);
        input.setBusinessUnitId(58);
        input.setUserId(46812);
        input.setEirId(null);
        input.setContainer(null);
        input.setInStock(inStock);
        input.setPendingInspection(null);
        input.setFilterBox(filterBox);
        input.setPage(1);
        input.setSize(10);
        input.setLanguageId(1);

        InspectionBoxInput.Prefix prefix = new InspectionBoxInput.Prefix();
        prefix.setInput(input);

        InspectionBoxInput.Root root = new InspectionBoxInput.Root();
        root.setSde(prefix);

        return root;
    }

    private List<EirDTO> getEirDTOList() {
        List<EirDTO> eirDTOList = new ArrayList<>();

        EirDTO eirDTO1 = EirDTO.builder()
                .eirId(1)
                .container("CNTR1")
                .truckEntryDate(LocalDateTime.of(2023, 11, 1, 9, 0, 0, 0))
                .businessUnitId(101)
                .shippingLineId(202)
                .containerId(303)
                .containerTypeId(1)
                .isBoxRepairApproved(0)
                .isMachineRepairApproved(1)
                .activityZoneId(5)
                .startDate(LocalDateTime.of(2023, 11, 1, 10, 0, 0, 0))
                .endDate(LocalDateTime.of(2023, 11, 1, 12, 0, 0, 0))
                .eirActivityZoneId(7)
                .activityZoneRegisterDate(LocalDateTime.of(2023, 10, 30, 10, 0, 0, 0))
                .isStructureDamaged(false)
                .isMachineryDamagedResult(true)
                .hasSensor(true)
                .isSensorDamaged(false)
                .localBusinessUnitId(404)
                .containerSizeId(40)
                .isoCodeId(505)
                .reeferTypeId(3)
                .engineBrandId(1)
                .isMachineryDamagedFlag(true)
                .maxContainerLoad(1500)
                .containerTareWeight(200)
                .containerClassId(1)
                .manufacturingDate(LocalDateTime.of(2021, 5, 15, 14, 30, 0, 0))
                .movementTypeId(3)
                .originTypeId(2)
                .isPartialInspection(false)
                .withSensor(true)
                .isCompleted(true)
                .finishDate(LocalDateTime.of(2023, 11, 1, 12, 0, 0, 0))
                .build();

        EirDTO eirDTO2 = EirDTO.builder()
                .eirId(2)
                .container("CNTR2")
                .truckEntryDate(LocalDateTime.of(2023, 10, 1, 10, 30, 0, 0))
                .businessUnitId(102)
                .shippingLineId(203)
                .containerId(304)
                .containerTypeId(2)
                .isBoxRepairApproved(1)
                .isMachineRepairApproved(0)
                .activityZoneId(6)
                .startDate(LocalDateTime.of(2023, 10, 1, 11, 30, 0, 0))
                .endDate(LocalDateTime.of(2023, 10, 1, 13, 30, 0, 0))
                .eirActivityZoneId(8)
                .activityZoneRegisterDate(LocalDateTime.of(2023, 9, 30, 9, 30, 0, 0))
                .isStructureDamaged(true)
                .isMachineryDamagedResult(false)
                .hasSensor(false)
                .isSensorDamaged(true)
                .localBusinessUnitId(405)
                .containerSizeId(45)
                .isoCodeId(506)
                .reeferTypeId(4)
                .engineBrandId(2)
                .isMachineryDamagedFlag(false)
                .maxContainerLoad(1600)
                .containerTareWeight(220)
                .containerClassId(2)
                .manufacturingDate(LocalDateTime.of(2020, 3, 10, 8, 0, 0, 0))
                .movementTypeId(4)
                .originTypeId(3)
                .isPartialInspection(true)
                .withSensor(false)
                .isCompleted(false)
                .finishDate(LocalDateTime.of(2023, 10, 1, 13, 30, 0, 0))
                .build();

        eirDTOList.add(eirDTO1);
        eirDTOList.add(eirDTO2);

        return eirDTOList;
    }

    private List<Catalog> getCatalogs() {
        return List.of(Catalog.builder().id(1).description("description1").code("1001").build(),
                Catalog.builder().id(2).description("description2").code("1002").build(),
                Catalog.builder().id(40).description("description3").code("1003").build(),
                Catalog.builder().id(45).description("description4").code("1004").build());

    }

    private List<Object[]> getMockCatalogData() {
        return Arrays.asList(
                new Object[]{"sd1_statusinspectiongral_pending", 1001},
                new Object[]{"sd1_statusinspectiongral_started", 1002},
                new Object[]{"20251", 1003},
                new Object[]{"cat_43161_box_inspection", 1004},
                new Object[]{"cat_43161_pti", 1005},
                new Object[]{"47490", 1006}
        );
    }

    private InspectionTimeConfigDto getInspectionTimeConfigDto() {
        return InspectionTimeConfigDto.builder()
                .minutesInspection(15)
                .minutesInspectionWithDelay(30)
                .minutesInspectionATime(45)
                .build();
    }

    private List<EstimateEmrDTO> getEstimateEmrDTOs() {
        return List.of(
                EstimateEmrDTO.builder().eirId(1).estimateEmrId(456).build(),
                EstimateEmrDTO.builder().eirId(2).estimateEmrId(567).build()
        );
    }

    private List<BusinessUnit> getBusinessUnits() {
        return List.of(BusinessUnit.builder().id(404).name("businessUnit1").build(),
                BusinessUnit.builder().id(405).name("businessUnit2").build());
    }

    private List<IsoCode> getIsoCodes() {
        return List.of(IsoCode.builder().id(505).isoCode("isoCode1").build(),
                IsoCode.builder().id(506).isoCode("isoCode2").build());
    }

    private List<ShippingLine> getShippingLines() {
        return List.of(ShippingLine.builder().id(202).name("shippingName1").build(),
                ShippingLine.builder().id(203).name("shippingName2").build());
    }

    private List<PTIActivityDTO> getMockPTIActivityDTOs() {
        PTIActivityDTO record1 = PTIActivityDTO.builder()
                .eirId(1)
                .hasSensor(true)
                .isCompleted(false)
                .endDate(LocalDateTime.of(2025, 2, 10, 15, 30, 0, 0))
                .build();

        PTIActivityDTO record2 = PTIActivityDTO.builder()
                .eirId(2)
                .hasSensor(false)
                .isCompleted(true)
                .endDate(LocalDateTime.of(2025, 2, 10, 15, 30, 0, 0))
                .build();

        return Arrays.asList(record1, record2);
    }

    private InspectionBoxOutput getInspectionBoxOutput() {
        return InspectionBoxOutput.builder()
                .displayDTOS(getDisplayDTOs())
                .inspectionBoxSummaryDTOS(getInspectionBoxSummaryDtos())
                .build();
    }

    private List<InspectionBoxSummaryDTO> getInspectionBoxSummaryDtos() {
        return List.of(
                InspectionBoxSummaryDTO.builder()
                        .finishedRecordsByInspector(2)
                        .recordsAtTime(0)
                        .recordsOnAlert(0)
                        .registrationsWithDelay(0)
                        .quantityRecords(2)
                        .dateSystem(LocalDateTime.of(2025, 2, 10, 16, 48, 19))
                        .build()
        );
    }

    private List<DisplayDTO> getDisplayDTOs() {
        return List.of(
                DisplayDTO.builder()
                        .gateInDate(LocalDateTime.of(2023, 10, 1, 13, 30))
                        .eir("2")
                        .containerId("CNTR2")
                        .containerTare(220)
                        .machineCondition("OK")
                        .movement("long description")
                        .activityZoneId(8)
                        .timeControl(3)
                        .typeReefer(4)
                        .brandMotor(2)
                        .reeferType("long description")
                        .engineBrand("long description")
                        .emrId(567)
                        .status("description")
                        .isPartialInspection(true)
                        .statusAlias("alias2")
                        .build(),
                DisplayDTO.builder()
                        .gateInDate(LocalDateTime.of(2023, 11, 1, 12, 0))
                        .eir("1")
                        .containerId("CNTR1")
                        .containerTare(200)
                        .machineCondition("SC")
                        .movement("long description")
                        .activityZoneId(7)
                        .timeControl(3)
                        .typeReefer(3)
                        .brandMotor(1)
                        .reeferType("long description")
                        .engineBrand("long description")
                        .emrId(456)
                        .status("description")
                        .isPartialInspection(false)
                        .statusAlias("alias1")
                        .build()
        );
    }

}