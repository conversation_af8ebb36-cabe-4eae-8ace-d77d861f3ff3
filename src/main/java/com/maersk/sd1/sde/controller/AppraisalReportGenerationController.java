package com.maersk.sd1.sde.controller;


import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.AppraisalReportGenerationInput;
import com.maersk.sd1.sde.dto.AppraisalReportGenerationOutput;
import com.maersk.sd1.sde.service.AppraisalReportGenerationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDE/module/sde/SDEEMRServiceImp")
public class AppraisalReportGenerationController {

    private static final Logger logger = LogManager.getLogger(AppraisalReportGenerationController.class);

    private final AppraisalReportGenerationService appraisalReportGenerationService;

    @PostMapping("/sdeestimadoGenerarReporte")
    public ResponseEntity<ResponseController<AppraisalReportGenerationOutput>> generateReport(@Valid @RequestBody AppraisalReportGenerationInput.Root request) {
        try {
            AppraisalReportGenerationInput.Input input = request.getPrefix().getInput();
            AppraisalReportGenerationOutput output = appraisalReportGenerationService.generateEstimadoReport(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while generating the report.", e);
            AppraisalReportGenerationOutput output = new AppraisalReportGenerationOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

