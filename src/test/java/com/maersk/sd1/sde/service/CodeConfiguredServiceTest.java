package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.GateTransmissionLocalSetting;
import com.maersk.sd1.common.model.GateTransmissionSetting;
import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.GateTransmissionLocalSettingRepository;
import com.maersk.sd1.sde.dto.CodeConfiguredOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

import java.util.*;

class CodeConfiguredServiceTest {

    @Mock
    private GateTransmissionLocalSettingRepository gateTransmissionLocalSettingRepository;

    @Mock
    private CatalogRepository catalogRepository;

    private CodeConfiguredService codeConfiguredService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        codeConfiguredService = new CodeConfiguredService(gateTransmissionLocalSettingRepository, catalogRepository);
    }

    @Test
    void testFindAllCodecoConfigured() {
        // Mocks for catalogs
        Catalog gateInCatalog = new Catalog();
        gateInCatalog.setAlias("43080");
        Catalog gateOutCatalog = new Catalog();
        gateOutCatalog.setAlias("43081");
        Catalog emptyCatalog = new Catalog();
        emptyCatalog.setAlias("43083");
        Catalog fullCatalog = new Catalog();
        fullCatalog.setAlias("43084");

        when(catalogRepository.findByAliasOptional("43080")).thenReturn(Optional.of(gateInCatalog));
        when(catalogRepository.findByAliasOptional("43081")).thenReturn(Optional.of(gateOutCatalog));
        when(catalogRepository.findByAliasOptional("43083")).thenReturn(Optional.of(emptyCatalog));
        when(catalogRepository.findByAliasOptional("43084")).thenReturn(Optional.of(fullCatalog));

        // Mock data for GateTransmissionLocalSetting repository
        List<GateTransmissionLocalSetting> gateInEmpty = new ArrayList<>();
        List<GateTransmissionLocalSetting> statusActivity = new ArrayList<>();
        List<GateTransmissionLocalSetting> gateOutEmpty = new ArrayList<>();
        List<GateTransmissionLocalSetting> gateInFull = new ArrayList<>();
        List<GateTransmissionLocalSetting> gateOutFull = new ArrayList<>();

        when(gateTransmissionLocalSettingRepository.findGateInEmpty()).thenReturn(gateInEmpty);
        when(gateTransmissionLocalSettingRepository.findStatusActivity()).thenReturn(statusActivity);
        when(gateTransmissionLocalSettingRepository.findGateOutEmpty()).thenReturn(gateOutEmpty);
        when(gateTransmissionLocalSettingRepository.findGateInFull()).thenReturn(gateInFull);
        when(gateTransmissionLocalSettingRepository.findGateOutFull()).thenReturn(gateOutFull);

        CodeConfiguredOutput result = codeConfiguredService.findAllCodecoConfigured();

        assertNotNull(result);
        assertEquals(1, result.getRespEstado());
        assertEquals("Success", result.getRespMensaje());
    }

    @Test
    void testGetCatalogByAlias() {
        Catalog catalog = new Catalog();
        catalog.setAlias("43080");
        when(catalogRepository.findByAliasOptional("43080")).thenReturn(Optional.of(catalog));

        Catalog result = codeConfiguredService.getCatalogByAlias("43080");

        assertNotNull(result);
        assertEquals("43080", result.getAlias());
    }

    @Test
    void testGetCatalogByAlias_nullAlias() {
        Catalog result = codeConfiguredService.getCatalogByAlias(null);
        assertNull(result);
    }

    @Test
    void testMapCommonFields() {
        // Mock GateTransmissionLocalSetting and its fields
        GateTransmissionLocalSetting localSetting = mock(GateTransmissionLocalSetting.class);
        GateTransmissionSetting gateTransmissionSetting = mock(GateTransmissionSetting.class);
        ShippingLine shippingLine = mock(ShippingLine.class);
        BusinessUnit businessUnit = mock(BusinessUnit.class);

        // Mock the behavior for GateTransmissionSetting and ShippingLine
        when(localSetting.getGateTransmissionSetting()).thenReturn(gateTransmissionSetting);
        when(gateTransmissionSetting.getShippingLine()).thenReturn(shippingLine);
        when(shippingLine.getShippingLineCompany()).thenReturn("Shipping Company");

        // Mock the LocalSubBusinessUnit and its behavior
        when(localSetting.getLocalSubBusinessUnit()).thenReturn(businessUnit);
        when(businessUnit.getId()).thenReturn(123);

        // Mock the behavior for the Catalogs
        Catalog movementCatalog = new Catalog();
        movementCatalog.setDescription("Movement");

        Catalog emptyFullCatalog = new Catalog();
        emptyFullCatalog.setDescription("Full");

        // Call the method
        CodeConfiguredOutput.CodeConfiguredItemDto result = codeConfiguredService.mapCommonFields(
                localSetting, movementCatalog, emptyFullCatalog, 1, "Event");

        // Validate the result
        assertNotNull(result);
        assertEquals("Event", result.getEventName());
        assertEquals("Movement", result.getMovementDescription());
        assertEquals("Shipping Company", result.getShippingLineCompany());
        assertEquals(123, result.getLocalSubBusinessUnitId());
    }


    @Test
    void testGetSubBusinessUnitId() {
        GateTransmissionLocalSetting localSetting = mock(GateTransmissionLocalSetting.class);
        GateTransmissionSetting gateTransmissionSetting = mock(GateTransmissionSetting.class);
        BusinessUnit businessUnit = mock(BusinessUnit.class);

        when(localSetting.getGateTransmissionSetting()).thenReturn(gateTransmissionSetting);
        when(gateTransmissionSetting.getSubBusinessUnit()).thenReturn(businessUnit);
        when(businessUnit.getId()).thenReturn(123);

        Long result = codeConfiguredService.getSubBusinessUnitId(localSetting);

        assertEquals(123, result);
    }

    @Test
    void testGetCatalogId() {
        Catalog catalog = new Catalog();
        catalog.setId(789);
        Integer result = codeConfiguredService.getCatalogId(catalog);

        assertEquals(789, result);
    }

    @Test
    void testGetCatalogDescription() {
        Catalog catalog = new Catalog();
        catalog.setDescription("Test Description");
        String result = codeConfiguredService.getCatalogDescription(catalog);

        assertEquals("Test Description", result);
    }

    @Test
    void testDetermineEventId() {
        GateTransmissionLocalSetting localSetting = mock(GateTransmissionLocalSetting.class);
        GateTransmissionSetting gateTransmissionSetting = mock(GateTransmissionSetting.class);
        Catalog formatCatalog = mock(Catalog.class);

        when(localSetting.getGateTransmissionSetting()).thenReturn(gateTransmissionSetting);
        when(gateTransmissionSetting.getCatStatusActivityFormat()).thenReturn(formatCatalog);
        when(formatCatalog.getAlias()).thenReturn("sd1_messagetype_gatetrans_telex");

        int result = codeConfiguredService.determineEventId(localSetting);

        assertEquals(2, result);
    }

    @Test
    void testSortCodeConfiguredList() {
        List<CodeConfiguredOutput.CodeConfiguredItemDto> list = new ArrayList<>();
        CodeConfiguredOutput.CodeConfiguredItemDto item = new CodeConfiguredOutput.CodeConfiguredItemDto();
        item.setShippingLineCompany("Company A");
        list.add(item);

        codeConfiguredService.sortCodeConfiguredList(list);

        assertNotNull(list);
        assertEquals("Company A", list.get(0).getShippingLineCompany());
    }

    // Test collectGateInEmptyData method with Mockito
    @Test
    void testCollectGateInEmptyData() {
        Catalog gateInCatalog = mock(Catalog.class);
        Catalog emptyCatalog = mock(Catalog.class);
        GateTransmissionLocalSetting localSetting = mock(GateTransmissionLocalSetting.class);
        GateTransmissionSetting gateTransmissionSetting = mock(GateTransmissionSetting.class);
        ShippingLine shippingLine = mock(ShippingLine.class);
        BusinessUnit businessUnit = mock(BusinessUnit.class);

        when(localSetting.getGateTransmissionSetting()).thenReturn(gateTransmissionSetting);
        when(gateTransmissionSetting.getShippingLine()).thenReturn(shippingLine);
        when(localSetting.getLocalSubBusinessUnit()).thenReturn(businessUnit);
        when(businessUnit.getId()).thenReturn(123);

        when(gateTransmissionLocalSettingRepository.findGateInEmpty()).thenReturn(Collections.singletonList(localSetting));

        List<CodeConfiguredOutput.CodeConfiguredItemDto> result = codeConfiguredService.collectGateInEmptyData(gateInCatalog, emptyCatalog);

        assertNotNull(result);
        assertEquals(1, result.size());
    }

    // Test collectStatusActivityData method with Mockito
    @Test
    void testCollectStatusActivityData() {
        Catalog gateInCatalog = mock(Catalog.class);
        Catalog emptyCatalog = mock(Catalog.class);
        GateTransmissionLocalSetting localSetting = mock(GateTransmissionLocalSetting.class);
        GateTransmissionSetting gateTransmissionSetting = mock(GateTransmissionSetting.class);
        Catalog statusActivityFormat = mock(Catalog.class);
        BusinessUnit businessUnit = mock(BusinessUnit.class);
        ShippingLine shippingLine = mock(ShippingLine.class);

        when(localSetting.getGateTransmissionSetting()).thenReturn(gateTransmissionSetting);
        when(gateTransmissionSetting.getCatStatusActivityFormat()).thenReturn(statusActivityFormat);
        when(localSetting.getLocalSubBusinessUnit()).thenReturn(businessUnit);
        when(businessUnit.getId()).thenReturn(123);
        when(gateTransmissionSetting.getShippingLine()).thenReturn(shippingLine);
        when(shippingLine.getId()).thenReturn(456);

        List<GateTransmissionLocalSetting> statusActivity = Collections.singletonList(localSetting);
        when(gateTransmissionLocalSettingRepository.findStatusActivity()).thenReturn(statusActivity);

        List<CodeConfiguredOutput.CodeConfiguredItemDto> result = codeConfiguredService.collectStatusActivityData(gateInCatalog, emptyCatalog);

        assertNotNull(result);
        assertEquals(1, result.size());
    }

    // Similar tests can be created for other collect methods
    @Test
    void testCollectGateOutEmptyData() {
        Catalog gateOutCatalog = mock(Catalog.class);
        Catalog emptyCatalog = mock(Catalog.class);
        GateTransmissionLocalSetting localSetting = mock(GateTransmissionLocalSetting.class);
        GateTransmissionSetting gateTransmissionSetting = mock(GateTransmissionSetting.class);
        ShippingLine shippingLine = mock(ShippingLine.class);
        BusinessUnit businessUnit = mock(BusinessUnit.class);

        when(localSetting.getGateTransmissionSetting()).thenReturn(gateTransmissionSetting);
        when(gateTransmissionSetting.getShippingLine()).thenReturn(shippingLine);
        when(localSetting.getLocalSubBusinessUnit()).thenReturn(businessUnit);
        when(businessUnit.getId()).thenReturn(123);

        when(gateTransmissionLocalSettingRepository.findGateOutEmpty()).thenReturn(Collections.singletonList(localSetting));

        List<CodeConfiguredOutput.CodeConfiguredItemDto> result = codeConfiguredService.collectGateOutEmptyData(gateOutCatalog, emptyCatalog);

        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void testCollectGateInFullData() {
        Catalog gateInCatalog = mock(Catalog.class);
        Catalog fullCatalog = mock(Catalog.class);
        GateTransmissionLocalSetting localSetting = mock(GateTransmissionLocalSetting.class);
        GateTransmissionSetting gateTransmissionSetting = mock(GateTransmissionSetting.class);
        ShippingLine shippingLine = mock(ShippingLine.class);
        BusinessUnit businessUnit = mock(BusinessUnit.class);

        when(localSetting.getGateTransmissionSetting()).thenReturn(gateTransmissionSetting);
        when(gateTransmissionSetting.getShippingLine()).thenReturn(shippingLine);
        when(localSetting.getLocalSubBusinessUnit()).thenReturn(businessUnit);
        when(businessUnit.getId()).thenReturn(123);

        when(gateTransmissionLocalSettingRepository.findGateInFull()).thenReturn(Collections.singletonList(localSetting));

        List<CodeConfiguredOutput.CodeConfiguredItemDto> result = codeConfiguredService.collectGateInFullData(gateInCatalog, fullCatalog);

        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void testCollectGateOutFullData() {
        Catalog gateOutCatalog = mock(Catalog.class);
        Catalog fullCatalog = mock(Catalog.class);
        GateTransmissionLocalSetting localSetting = mock(GateTransmissionLocalSetting.class);
        GateTransmissionSetting gateTransmissionSetting = mock(GateTransmissionSetting.class);
        ShippingLine shippingLine = mock(ShippingLine.class);
        BusinessUnit businessUnit = mock(BusinessUnit.class);

        when(localSetting.getGateTransmissionSetting()).thenReturn(gateTransmissionSetting);
        when(gateTransmissionSetting.getShippingLine()).thenReturn(shippingLine);
        when(localSetting.getLocalSubBusinessUnit()).thenReturn(businessUnit);
        when(businessUnit.getId()).thenReturn(123);

        when(gateTransmissionLocalSettingRepository.findGateOutFull()).thenReturn(Collections.singletonList(localSetting));

        List<CodeConfiguredOutput.CodeConfiguredItemDto> result = codeConfiguredService.collectGateOutFullData(gateOutCatalog, fullCatalog);

        assertNotNull(result);
        assertEquals(1, result.size());
    }

}