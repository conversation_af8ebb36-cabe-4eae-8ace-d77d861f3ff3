package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Menu;
import com.maersk.sd1.common.model.NotificationTemplate;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "notificacion_job", schema = "seg")
public class NotificationJob {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "notificacion_job_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "menu_proyecto_id", nullable = false)
    private Menu projectMenu;

    @Size(max = 250)
    @NotNull
    @Column(name = "descripcion", nullable = false, length = 250)
    private String description;

    @NotNull
    @Column(name = "periodo", nullable = false)
    private Integer period;

    @Column(name = "vigencia_desde")
    private LocalDateTime validitySince;

    @Column(name = "vigencia_hasta")
    private LocalDateTime validityUntil;

    @Column(name = "fecha_ultima_ejecucion")
    private LocalDateTime lastExecutionDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "email_plantilla_id")
    private EmailTemplate emailTemplate;

    @Size(max = 200)
    @Column(name = "email_procedimiento", length = 200)
    private String emailProcedure;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "notificacion_web_plantilla_id")
    private NotificationTemplate webNotificationTemplate;

    @Size(max = 200)
    @Column(name = "notificacion_web_procedimiento", length = 200)
    private String webNotificationProcedure;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "notificacion_push_plantilla_id")
    private NotificationTemplate pushNotificationTemplate;

    @Size(max = 10)
    @Column(name = "notificacion_push_procedimiento", length = 10)
    private String pushNotificationProcedure;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "estado", nullable = false)
    private Catalog catStatus;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

}