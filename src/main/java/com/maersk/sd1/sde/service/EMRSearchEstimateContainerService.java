package com.maersk.sd1.sde.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.dto.ContainerDTO;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.service.*;
import com.maersk.sd1.ges.service.BusinessUnitService;
import com.maersk.sd1.ges.service.CompanyService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sde.controller.dto.EMRSearchEstimateContainerInput;
import com.maersk.sd1.sde.controller.dto.EMRSearchEstimateContainerOutput;
import com.maersk.sd1.sde.dto.EmptyStockContainer;
import com.maersk.sd1.sds.service.ShippingLineService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
@RequiredArgsConstructor
public class EMRSearchEstimateContainerService {

    private final String SHIPPING_LINE_SEA = "SEA";
    private final String SHIPPING_LINE_ALI = "ALI";
    private final String SHIPPING_LINE_CCNI = "CCNI";
    private final String SHIPPING_LINE_HSD = "HSD";
    private final String SHIPPING_LINE_MSL = "MSL";

    private final GESCatalogService gesCatalogService;

    private final BusinessUnitService businessUnitService;

    private final ContainerService containerService;

    private final StockEmptyService stockEmptyService;

    private final ShippingLineService shippingLineService;

    private final EirDocumentCargoDetailService eirDocumentCargoDetailService;

    private final EirActivityZoneService eirActivityZoneService;

    private final MessageLanguageService messageLanguageService;

    private final EstimateEmrSettingService estimateEmrSettingService;

    private final BusinessUnitConfigService businessUnitConfigService;

    private final EirService eirService;

    private final CompanyService companyService;

    private final TruckService truckService;

    private final PersonService personService;

    private final VesselProgrammingDetailService vesselProgrammingDetailService;

    public List<EMRSearchEstimateContainerOutput> execute(EMRSearchEstimateContainerInput.Input input) throws JsonProcessingException {

        List<String> catalogAliases = new ArrayList<>();

        catalogAliases.add(Parameter.CATALOG_ESTIMATE_BOX_ALIAS);
        catalogAliases.add(Parameter.CATALOG_ESTIMATE_TYPE_MACHINERY);
        catalogAliases.add(Parameter.CATALOG_BOX_INSPECTION_ALIAS);
        catalogAliases.add(Parameter.ACTIVITY_ZONE_EMPTY_PTI);
        catalogAliases.add(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS);
        catalogAliases.add(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);
        catalogAliases.add(Parameter.CONTAINER_ORIGIN_DESTINY_ASSISTANT_PORT);
        catalogAliases.add(Parameter.COMPANY_DOCUMENT_TYPE_OTHER);
        catalogAliases.add(Parameter.ORIGIN_CREATION_AUTOMATIC);
        catalogAliases.add(Parameter.PERSON_STATUS_ENABLED);
        catalogAliases.add(Parameter.GRADE_NO_CLASS);
        catalogAliases.add(Parameter.REGISTRY_ORIGIN_CREATION_AUTOATIC);
        catalogAliases.add(Parameter.PERSON_TYPE_DOCUMENT_OTHER);

        HashMap<String, Integer> catalogs = gesCatalogService.findIdsByAliasesIn(catalogAliases);

        List<String> shippingLinesAliases = new ArrayList<>();

        shippingLinesAliases.add(SHIPPING_LINE_SEA);
        shippingLinesAliases.add(SHIPPING_LINE_ALI);
        shippingLinesAliases.add(SHIPPING_LINE_CCNI);
        shippingLinesAliases.add(SHIPPING_LINE_HSD);
        shippingLinesAliases.add(SHIPPING_LINE_MSL);

        HashMap<String, Integer> shippingLines = shippingLineService.findIdsByShippingLineCompanies(shippingLinesAliases);


        Integer businessUnitId = businessUnitService.findParentBusinessUnitId(input.getSubBusinessUnitId());

        Container containerFound = containerService.findByContainerNumberIgnoreCaseAndActiveTrue(input.getContainerNumber());

        /*
        containerFound.getId();
        containerFound.getCatContainerType().getId();
*/
        Integer catTypeEstimateId = containerFound.getCatContainerType().getCode().equals("1") ? catalogs.get(Parameter.CATALOG_ESTIMATE_TYPE_MACHINERY) : catalogs.get(Parameter.CATALOG_ESTIMATE_BOX_ALIAS);


        String msjPendingInspection = messageLanguageService.getMessage("LIS_ESTIMADO_BUSCNT", 1, input.getLanguageId());
        String msjPendingPTI = messageLanguageService.getMessage("LIS_ESTIMADO_BUSCNT", 2, input.getLanguageId());
        String msjWithOutDamage = messageLanguageService.getMessage("LIS_ESTIMADO_BUSCNT", 3, input.getLanguageId());
        String msjDamaged = messageLanguageService.getMessage("LIS_ESTIMADO_BUSCNT", 4, input.getLanguageId());
        String msjYes = messageLanguageService.getMessage("si", 1, input.getLanguageId());

        List<EMRSearchEstimateContainerOutput> estimateList = new ArrayList<EMRSearchEstimateContainerOutput>();


        if(input.getWithoutGatein()){

            LocalDateTime nowDate = LocalDateTime.now();

            if(containerFound.getId() != null){

                Object[] eirFind = eirService.findEirIdAndTruckArrivalByQuery(containerFound.getId(), businessUnitId, input.getSubBusinessUnitId(), catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS), catalogs.get(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS), catalogs.get(Parameter.CONTAINER_ORIGIN_DESTINY_ASSISTANT_PORT));

                if(eirFind == null){

                    Integer companyDummyId = companyService.findTopByDocument(Parameter.DUMMY_COMPANY_DOCUMENT);
                    if(companyDummyId == null){
                        companyDummyId = companyService.companyRegister(Parameter.DUMMY_COMPANY_DOCUMENT, "TRUCK DUMMY #01", "", Parameter.DEFAULT_USER_ID_MASTER, null, catalogs.get(Parameter.COMPANY_DOCUMENT_TYPE_OTHER), false, "", "", catalogs.get(Parameter.ORIGIN_CREATION_AUTOMATIC), "");
                    }

                    Integer truckId = truckService.findTopByPlate(Parameter.DUMMY_VEHICLE_PLATE);
                    if(truckId == null){
                        truckId = truckService.truckRegister(Parameter.DUMMY_VEHICLE_PLATE, BigDecimal.ZERO, "", BigDecimal.ZERO, BigDecimal.ZERO, companyDummyId, Parameter.DEFAULT_USER_ID_MASTER, catalogs.get(Parameter.ORIGIN_CREATION_AUTOMATIC));
                    }

                    Integer personId = personService.findTopByIdentificationNumber(Parameter.DUMMY_PERSON_DOCUMENT);
                    if(personId == null){
                        personId = personService.personRegister(catalogs.get(Parameter.PERSON_TYPE_DOCUMENT_OTHER), "DRIVERD01", "", "DUMMY", "#01", LocalDateTime.now(), catalogs.get(Parameter.PERSON_STATUS_ENABLED), Parameter.DEFAULT_USER_ID_MASTER, "DRIVERD01", 'M', catalogs.get(Parameter.PERSON_SITUATION_EMPLOYMENT_IN_ACTIVITY), catalogs.get(Parameter.ORIGIN_CREATION_AUTOMATIC));
                    }


                    Integer vesselProgrammingDetailId = vesselProgrammingDetailService.findVesselProgrammingDetailIdByActive();



                }

            }



        } else {

            List<EmptyStockContainer> containers = stockEmptyService.findByContainerNumberAndSubUnitId(input.getContainerNumber(), input.getSubBusinessUnitId());

            containers.forEach(container -> {

                EMRSearchEstimateContainerOutput eMRSearchEstimateContainerOutput = new EMRSearchEstimateContainerOutput();

                String shippingLineCompany = eirDocumentCargoDetailService.findShippingLineNameByEirId(container.getGateInEirId());
                Integer shippingLineId = null;

                if(shippingLineCompany != null){
                    if(shippingLineCompany.equals(SHIPPING_LINE_SEA)){
                        shippingLineId = shippingLines.get(SHIPPING_LINE_MSL);
                    } else if(shippingLineCompany.equals(SHIPPING_LINE_ALI) || shippingLineCompany.equals(SHIPPING_LINE_CCNI)){
                        shippingLineId = shippingLines.get(SHIPPING_LINE_HSD);
                    }
                }


                Boolean situationInsp = eirActivityZoneService.findConcludedByEirIdAndCatZoneActivityIdAndActiveTrue(container.getGateInEirId(), catalogs.get(Parameter.CATALOG_BOX_INSPECTION_ALIAS));
                Boolean situationPTI = eirActivityZoneService.findConcludedByEirIdAndCatZoneActivityIdAndActiveTrue(container.getGateInEirId(), catalogs.get(Parameter.ACTIVITY_ZONE_EMPTY_PTI));

                String conditionBox;
                String conditionBoxDes = "";
                if(!situationInsp){
                    conditionBox = "P";
                    conditionBoxDes = msjPendingInspection;
                } else if(container.getStructureWithDamage()){
                    conditionBox = "D";
                    conditionBoxDes = msjDamaged;
                } else {
                    conditionBox = "S";
                    conditionBoxDes = msjWithOutDamage;
                }

                String conditionMachine;
                String conditionMachineDes = "";
                if(situationPTI == null){
                    if(container.getCatContainerTypeCode().equals("0")){
                        conditionMachine = "";
                    } else {
                        conditionMachine = "P";
                        conditionMachineDes = msjPendingPTI;
                    }
                } else if(!situationPTI){
                    conditionMachine = "P";
                    conditionMachineDes = msjPendingPTI;
                } else {
                    if(container.getMachineryWithDamage()){
                        conditionMachine = "D";
                        conditionMachineDes = msjDamaged;
                    } else {
                        conditionMachine = "S";
                        conditionMachineDes = msjWithOutDamage;
                    }
                }

                ContainerDTO containerData = containerService.findContainerDetailsById(container.getContainerId());


                if(shippingLineId == null){
                    shippingLineId = containerData.getShippingLineId();
                }

                String shippingLineCompanyFinal = shippingLineService.getNameById(shippingLineId);

                eMRSearchEstimateContainerOutput.setContainerId(container.getContainerId());
                eMRSearchEstimateContainerOutput.setContainerNumber(input.getContainerNumber());
                eMRSearchEstimateContainerOutput.setContainerSize(containerData.getContainerSize());
                eMRSearchEstimateContainerOutput.setContainerType(container.getCatContainerTypeDescription());
                eMRSearchEstimateContainerOutput.setContainerClass(containerData.getContainerClass());
                eMRSearchEstimateContainerOutput.setManufactureDate(containerData.getManufactureDate());
                eMRSearchEstimateContainerOutput.setReeferType(containerData.getReeferType());
                eMRSearchEstimateContainerOutput.setEngineBrand(containerData.getEngineBrand());
                eMRSearchEstimateContainerOutput.setShippingLineId(shippingLineId);
                eMRSearchEstimateContainerOutput.setShippingLine(shippingLineCompanyFinal);
                eMRSearchEstimateContainerOutput.setGateInEirId(container.getGateInEirId());
                eMRSearchEstimateContainerOutput.setGateInDate(businessUnitConfigService.getDateTimeStr(input.getSubBusinessUnitId(), container.getTruckEntryDate()));
                eMRSearchEstimateContainerOutput.setGateOutEirId(container.getGateOutEirId());
                eMRSearchEstimateContainerOutput.setGateOutDate(businessUnitConfigService.getDateTimeStr(input.getSubBusinessUnitId(), container.getTruckExitDate()));

                if(containerData.getForSaleValue() != null){
                    eMRSearchEstimateContainerOutput.setForSaleValue(msjYes);
                    eMRSearchEstimateContainerOutput.setForSale("1");
                } else {
                    eMRSearchEstimateContainerOutput.setForSaleValue("No");
                    eMRSearchEstimateContainerOutput.setForSale("0");

                }

                eMRSearchEstimateContainerOutput.setIsReefer(container.getCatContainerTypeCode());
                eMRSearchEstimateContainerOutput.setStructure(conditionBoxDes);
                eMRSearchEstimateContainerOutput.setMachinery(conditionMachineDes);

                Integer totalEMRSetting = estimateEmrSettingService.countActiveEstimatesByShippingLineAndSubBusinessUnit(shippingLineId, input.getSubBusinessUnitId());
                eMRSearchEstimateContainerOutput.setAppliesToShipping(totalEMRSetting);
                eMRSearchEstimateContainerOutput.setContainerSizeId(containerData.getContainerSizeId());
                eMRSearchEstimateContainerOutput.setContainerTypeId(containerData.getContainerTypeId());
                eMRSearchEstimateContainerOutput.setEstimatedTypeCategoryId(catTypeEstimateId);

                estimateList.add(eMRSearchEstimateContainerOutput);

            });

        }

        return estimateList;

    }

}