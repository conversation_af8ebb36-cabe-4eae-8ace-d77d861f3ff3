package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.BookingEdi;
import com.maersk.sd1.sds.controller.dto.BookingMonitoringOutputItem;
import com.maersk.sd1.sds.dto.*;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface BookingEdiRepository extends JpaRepository<BookingEdi, Integer> {
    @Query("""
       SELECT new com.maersk.sd1.sds.dto.ImoIdAndGrupoProductoDesDTO(
           a.imo.id, a.commodity,
           CASE WHEN b.catOperation.id IN (42994, 42995, 47734, 47735) THEN 43007 ELSE 43004 END
       )
       FROM Booking a
       JOIN a.vesselProgrammingDetail b
       WHERE a.id = :bookingId
       """)
    List<ImoIdAndGrupoProductoDesDTO> findImoIdAndGrupoProductoDesByBookingId(@Param("bookingId") Integer bookingId);

    @Query("""
        SELECT new com.maersk.sd1.sds.dto.NewBKDetalleDTO(
            A.id, A.reservationQuantity, A.catSize.id, A.catContainerType.id
        )
        FROM BookingDetail A
        LEFT JOIN CargoDocumentDetail DCD
            ON A.id = DCD.bookingDetail.id AND DCD.active = true
        WHERE A.booking.id = :bookingId
            AND A.active = true
            AND DCD.bookingDetail.id IS NULL
        """)
    List<NewBKDetalleDTO> fetchNewBKDetalle(@Param("bookingId") int bookingId);

    @Query("""
       SELECT new com.maersk.sd1.sds.dto.BookingDetailPositionDTO(
           A.id, A.reservationQuantity, A.catContainerType.id, A.catSize.id
       )
      FROM BookingDetail A
      LEFT JOIN CargoDocumentDetail DCD
      ON A.id = DCD.bookingDetail.id AND DCD.active = true
      WHERE A.booking.id = :bookingId
      AND A.active = true
      AND DCD.bookingDetail.id IS NULL
      ORDER BY A.id
      """)
    List<BookingDetailPositionDTO> fetchDetails(@Param("bookingId") int bookingId);

    @Query(value = """
            SELECT :bookingId, :bookingDetalleId, :nItem, :catTipoContenedorId, :catTamanoId, :cargaRefrigerada
            """, nativeQuery = true)
    void insertNewDODetalle(@Param("bookingId") int bookingId, @Param("bookingDetalleId") int bookingDetalleId,
                            @Param("nItem") int nItem, @Param("catTipoContenedorId") int catTipoContenedorId,
                            @Param("catTamanoId") int catTamanoId, @Param("cargaRefrigerada") boolean cargaRefrigerada);

    @Query(value = """
       SELECT new com.maersk.sd1.sds.dto.DocumentoCargaDetalleDTO(
           :documentoCargaId, A.producto_id, 43009, 1, 1, 0, 43012, 1,
           :cargaPeligrosa, C.cargaRefrigerada, :grupoProductoDes,
           :catCondicionCargaId, 0, 1, 47772, :usuarioRegistroId, GETDATE(),
           C.cat_tipo_contenedor_id, C.cat_tamano_id, 43014, C.booking_detalle_id,
           'edi_upd-type-size'
       )
       FROM sds.booking AS A (NOLOCK)
                                JOIN sds.booking_detalle AS C (NOLOCK)
       ON A.booking_id = C.booking_id
       WHERE A.booking_id = :bookingId
       AND A.booking_id = :tempBookingid
       ORDER BY C.DOITEM
       """, nativeQuery = true)
    List<DocumentoCargaDetalleDTO> fetchDocumentoCargaDetalleData(
            @Param("documentoCargaId") int documentoCargaId,
            @Param("bookingId") int bookingId,
            @Param("usuarioRegistroId") int usuarioRegistroId,
            @Param("catCondicionCargaId") int catCondicionCargaId,
            @Param("cargaPeligrosa") boolean cargaPeligrosa,
            @Param("grupoProductoDes") String grupoProductoDes,
            @Param("tempBookingid") int tempBookingid);


    @Modifying
    @Query(value = """
            INSERT INTO sds.documento_carga_detalle
            (documento_carga_id, producto_id, cat_embalaje_id, cantidad_manifestada, peso_manifestado,
             volumen_manifestado, cat_unidad_medida_peso_id, dice_contener, es_carga_peligrosa, es_carga_refrigerada,
             mercaderia, cat_condicion_carga_id, gateout_empty_liquidado, activo, cat_origen_creacion_id,
             usuario_registro_id, fecha_registro, cat_tipo_contenedor_manifestado_id, cat_tamano_manifestado_id,
             cat_unidad_medida_cantidad_id, booking_detalle_id, trace_doc_carga_detalle)
            VALUES (:documentoCargaId, :productoId, :catEmbalajeId, :cantidadManifestada, :pesoManifestado,
                    :volumenManifestado, :catUnidadMedidaPesoId, :diceContener, :esCargaPeligrosa, :esCargaRefrigerada,
                    :mercaderia, :catCondicionCargaId, :gateoutEmptyLiquidado, :activo, :catOrigenCreacionId,
                    :usuarioRegistroId, :fechaRegistro, :catTipoContenedorManifestadoId, :catTamanoManifestadoId,
                    :catUnidadMedidaCantidadId, :bookingDetalleId, :traceDocCargaDetalle)
            """, nativeQuery = true)
    void insertDocumentoCargaDetalle(@Param("documentoCargaId") int documentoCargaId,
                                     @Param("productoId") int productoId, @Param("catEmbalajeId") int catEmbalajeId,
                                     @Param("cantidadManifestada") int cantidadManifestada,
                                     @Param("pesoManifestado") int pesoManifestado,
                                     @Param("volumenManifestado") int volumenManifestado,
                                     @Param("catUnidadMedidaPesoId") int catUnidadMedidaPesoId,
                                     @Param("diceContener") int diceContener,
                                     @Param("esCargaPeligrosa") boolean esCargaPeligrosa,
                                     @Param("esCargaRefrigerada") boolean esCargaRefrigerada,
                                     @Param("mercaderia") String mercaderia,
                                     @Param("catCondicionCargaId") int catCondicionCargaId,
                                     @Param("gateoutEmptyLiquidado") int gateoutEmptyLiquidado,
                                     @Param("activo") int activo, @Param("catOrigenCreacionId") int catOrigenCreacionId,
                                     @Param("usuarioRegistroId") int usuarioRegistroId,
                                     @Param("fechaRegistro") String fechaRegistro,
                                     @Param("catTipoContenedorManifestadoId") int catTipoContenedorManifestadoId,
                                     @Param("catTamanoManifestadoId") int catTamanoManifestadoId,
                                     @Param("catUnidadMedidaCantidadId") int catUnidadMedidaCantidadId,
                                     @Param("bookingDetalleId") int bookingDetalleId,
                                     @Param("traceDocCargaDetalle") String traceDocCargaDetalle);

    @Query(value = """
            SELECT CASE WHEN ISNULL(a.codigo, '0') = '1' THEN 1 ELSE 0 END AS cargaRefrigerada
            FROM ges.catalogo AS a (nolock)
            WHERE a.catalogo_id = :catTipoContenedorId AND a.catalogo_padre_id = 164
            """, nativeQuery = true)
    Boolean isCargaRefrigerada(@Param("catTipoContenedorId") int catTipoContenedorId);


    @Query("""
            SELECT new com.maersk.sd1.sds.dto.PendingEdiDTO(
                b.bookingEdiSetting.id,
                b.id,
                b.originalBkEdiFileName,
                b.bkEdiCreationDate,
                b.registrationDate,
                b.shippingLine.id,
                3,
                b.bookingNumber,
                b.bkEdiReservationSituation,
                false,
                1,
                0,
                COALESCE(b.ownerCorrelative, 0)
            )
            FROM BookingEdi b
            WHERE b.bookingEdiSetting.id = :ediCoparnSetupId
            AND b.catBkEdiStatus.id = :isStateToRead
            AND b.active=true
            """)
    List<PendingEdiDTO> findPendingEdiDTOByEdiCoparnSetupId(@Param("ediCoparnSetupId") Integer ediCoparnSetupId, @Param("isStateToRead") Integer isStateToRead);

    @Query("""
            SELECT new com.maersk.sd1.sds.dto.PendingEdiDTO(
                b.bookingEdiSetting.id,
                b.id,
                b.originalBkEdiFileName,
                b.bkEdiCreationDate,
                b.registrationDate,
                b.shippingLine.id,
                3,
                b.bookingNumber,
                b.bkEdiReservationSituation,
                false,
                1,
                0,
                COALESCE(b.ownerCorrelative, 0)
            )
            FROM BookingEdi b
            WHERE b.bookingEdiSetting.id = :ediCoparnSetupId
            AND b.catBkEdiStatus.id = :isStateToProcess
            AND COALESCE(b.bookingNumber, '') <> ''
            AND COALESCE(b.bkEdiReservationSituation, '') = '9'
            AND b.bkEdiRegistrationType = '12'
            AND b.active=true
            """)
    List<PendingEdiDTO> findPendingEdiDTOByEdiCoparnSetupIdAndIsstateProcess(@Param("ediCoparnSetupId") Integer ediCoparnSetupId, @Param("isStateToProcess") Integer isStateToProcess);

    @Query("""
            SELECT new com.maersk.sd1.sds.dto.PendingEdiDTO(
                b.bookingEdiSetting.id,
                b.id,
                b.originalBkEdiFileName,
                b.bkEdiCreationDate,
                b.registrationDate,
                b.shippingLine.id,
                3,
                b.bookingNumber,
                b.bkEdiReservationSituation,
                false,
                1,
                0,
                COALESCE(b.ownerCorrelative, 0)
            )
            FROM BookingEdi b
            WHERE b.bookingEdiSetting.id = :ediCoparnSetupId
            AND b.catBkEdiStatus.id = :isStateToProcess
            AND COALESCE(b.bookingNumber, '') <> ''
            AND COALESCE(b.bkEdiReservationSituation, '') IN ('1', '5', '2')
            AND b.bkEdiRegistrationType = '12'
            AND b.active=true
            """)
    List<PendingEdiDTO> findPendingEdiDTOByEdiCoparnSetupIdAndIsstateProcess2(@Param("ediCoparnSetupId") Integer ediCoparnSetupId, @Param("isStateToProcess") Integer isStateToProcess);

    @Modifying
    @Query("""
                UPDATE BookingEdi b
                SET b.traceBkEdi2 = :traceValue
                WHERE b.id IN :ediIds
            """)
    int updateTraceEdiCoparn2ForEdiIds(@Param("traceValue") String traceValue, @Param("ediIds") List<Integer> ediIds);

    @Query("SELECT b FROM BookingEdi b WHERE b.registrationDate < :thresholdDate " +
            "AND b.catBkEdiStatus.id IN (48271, 48272) AND b.active = true")
    List<BookingEdi> findRecordsToDismiss(@Param("thresholdDate") LocalDateTime thresholdDate);

    @Query("SELECT COALESCE(b.remarkRulesName, '') FROM BookingEdi b WHERE b.id = :ediCoparnId")
    String findRemarkRulesNameById(@Param("ediCoparnId") Integer ediCoparnId);

    @Modifying
    @Query("UPDATE BookingEdi b SET b.bkEdiProcessedComment = :updatedMessage, " +
            "b.bkEdiCommentForProcess = NULL, " +
            "b.bkEdiProcessedComment2 = :updatedMessageI " +
            "WHERE b.id = :ediCoparnId")
    int updateBookingEdi(@Param("updatedMessage") String updatedMessage,
                         @Param("updatedMessageI") String updatedMessageI,
                         @Param("ediCoparnId") Integer ediCoparnId);

    @Modifying
    @Query("UPDATE BookingEdi b SET b.bkEdiProcessedComment = 'There were no changes to update.', " +
            "b.bkEdiProcessedComment2 = 'There were no changes to update.', " +
            "b.bkEdiCommentForProcess = NULL " +
            "WHERE b.id = :ediCoparnId")
    int updateNoChanges(@Param("ediCoparnId") Integer ediCoparnId);

    @Modifying
    @Query("UPDATE BookingEdi b SET b.catBkEdiStatus.id = :isBkEdiDone, " +
            "b.processedUser.id = :userRegistrationId, " +
            "b.traceBkEdi = 'EDI-Replace7', " +
            "b.dateProcessedCoparn = CURRENT_TIMESTAMP " +
            "WHERE b.id = :ediCoparnId")
    int updateProcessed(@Param("ediCoparnId") Integer ediCoparnId,
                        @Param("isBkEdiDone") Integer isBkEdiDone,
                        @Param("userRegistrationId") Integer userRegistrationId);

    @Modifying
    @Query("UPDATE BookingEdi b SET b.catBkEdiStatus.id = :isBkEdiToProcess, " +
            "b.processedUser.id = :userRegistrationId, " +
            "b.traceBkEdi = 'EDI-Replace6', " +
            "b.dateProcessedCoparn = CURRENT_TIMESTAMP, " +
            "b.bkEdiProcessedComment = CONCAT('Inconsistency between the requested quantity and the number of items in the document [', :updatedDetail, ']'), " +
            "b.bkEdiCommentForProcess = NULL " +
            "WHERE b.id = :ediCoparnId")
    int updateInconsistency(@Param("ediCoparnId") Integer ediCoparnId,
                            @Param("isBkEdiToProcess") Integer isBkEdiToProcess,
                            @Param("userRegistrationId") Integer userRegistrationId,
                            @Param("updatedDetail") String updatedDetail);

    BookingEdi findByIdAndActive(Integer id, Boolean active);

    @Modifying
    @Query("UPDATE BookingEdi b " +
            "SET b.bkEdiForwarded = '1', b.bkEdiForwardedDate = :forwardedDate " +
            "WHERE b.id = :ediCoparnId AND (b.bkEdiForwarded IS NULL OR b.bkEdiForwarded = '0')")
    int updateBkEdiForwarded(@Param("ediCoparnId") Integer ediCoparnId,
                             @Param("forwardedDate") LocalDateTime forwardedDate);

    @Query("SELECT be FROM BookingEdi be WHERE be.originalBkEdiFileName = :ediCoparnOriginalFileName AND be.bookingEdiSetting.id = :ediCoparnSettingId")
    List<BookingEdi> findByOriginalCoparnFileNameAndBookingEdiSettingId(Pageable pageable, String ediCoparnOriginalFileName, Integer ediCoparnSettingId);

    @Modifying
    @Query("UPDATE BookingEdi be SET be.originalBkEdiFileName = :coparnFileName WHERE be.id = :bookingEdiId")
    void updateFileOriginalName(Integer bookingEdiId, String coparnFileName);

    @Procedure(name = "BookingProcessArchive.processArchive1C2A")
    void processArchive1C2A(
            @Param("edi_coparn_id") Integer coparnId,
            @Param("SituacionReserva") String reservationStatus,
            @Param("unidad_negocio_id") Integer businessUnitId,
            @Param("sub_unidad_negocio_id") Integer subBusinessUnitId,
            @Param("programacion_nave_detalle_id") Integer shipScheduleDetailId,
            @Param("Booking") String booking,
            //--------------------------
            @Param("CntDimen_id") Integer containerDimensionId,
            @Param("CntTipo_id") Integer containerTypeId,
            @Param("CantidadReserva") Integer reservationQuantity,
            @Param("CntDimen2_id") Integer secondContainerDimensionId,
            @Param("CntTipo2_id") Integer secondContainerTypeId,
            @Param("CantidadReserva2") Integer secondReservationQuantity,
            //--------------------------
            @Param("Cliente_id") Integer clientId,
            @Param("ClienteRS") String clientRS,
            @Param("GrupoProductoDes") String productGroupDescription,
            @Param("producto_id") Integer productId,
            @Param("PtoEmbarque_id") Integer embarkationPortId,
            @Param("PtoDestino_id") Integer destinationPortId,
            @Param("PtoDescarga_id") Integer dischargePortId,
            @Param("Temperatura") String temperature,
            @Param("imo_id") Integer imoId,
            @Param("LineaBK_id") Integer bookingLineId,
            @Param("PesoBrutoEDI") BigDecimal grossWeightEDI,
            @Param("PesoBrutoEDI2") BigDecimal grossWeightEDI2,
            @Param("ColdTreatment") Boolean coldTreatment,
            @Param("AtmosferaControlada") Boolean controlledAtmosphere,
            @Param("usuario_registro_id") Integer userRegistrationId,
            @Param("param_sequence_details") String paramSequenceDetails
    );

    @Procedure(name = "BookingProcessArchive.processArchive5M")
    void processArchive5M(
            @Param("edi_coparn_id") Integer coparnId,
            @Param("unidad_negocio_id") Integer businessUnitId,
            @Param("sub_unidad_negocio_id") Integer subBusinessUnitId,
            @Param("programacion_nave_detalle_id") Integer shipScheduleDetailId,
            @Param("Booking") String booking,
            //--------------------------
            @Param("CntDimen_id") Integer containerDimensionId,
            @Param("CntTipo_id") Integer containerTypeId,
            @Param("CantidadReserva") Integer reservationQuantity,
            @Param("CntDimen2_id") Integer secondContainerDimensionId,
            @Param("CntTipo2_id") Integer secondContainerTypeId,
            @Param("CantidadReserva2") Integer secondReservationQuantity,
            //--------------------------
            @Param("Cliente_id") Integer clientId,
            @Param("ClienteRS") String clientRS,
            @Param("GrupoProductoDes") String productGroupDescription,
            @Param("producto_id") Integer productId,
            @Param("PtoEmbarque_id") Integer embarkationPortId,
            @Param("PtoDestino_id") Integer destinationPortId,
            @Param("PtoDescarga_id") Integer dischargePortId,
            @Param("Temperatura") String temperature,
            @Param("imo_id") Integer imoId,
            @Param("LineaBK_id") Integer bookingLineId,
            @Param("PesoBrutoEDI") BigDecimal grossWeightEDI,
            @Param("PesoBrutoEDI2") BigDecimal grossWeightEDI2,
            @Param("ColdTreatment") Boolean coldTreatment,
            @Param("AtmosferaControlada") Boolean controlledAtmosphere,
            @Param("usuario_registro_id") Integer userRegistrationId,
            @Param("param_sequence_details") String paramSequenceDetails
    );


    @Query(value = "SELECT b FROM BookingEdi b WHERE b.id = :id AND b.active = TRUE ")
    Optional<BookingEdi> findByIdAndActiveIsTrue(@Param("id") Integer id);

    @Query(value = "SELECT ges.fn_lee_linea_coparn(:line, :number1, :number2)", nativeQuery = true)
    String fnLeeLineaCoparn(@Param("line") String line, @Param("number1") Integer number1, @Param("number2") Integer number2);


    @Query("""
                SELECT CASE
                    WHEN POSITION('PECAL' IN a.originalBkEdiFileName) > 0 THEN 22
                    WHEN POSITION('PEPAI' IN a.originalBkEdiFileName) > 0 THEN 23
                    WHEN POSITION('ECGYE01' IN a.originalBkEdiFileName) > 0 THEN 30
                    WHEN POSITION('COCTGAP' IN a.originalBkEdiFileName) > 0 THEN 32
                    WHEN POSITION('OPACIFYD' IN a.originalBkEdiFileName) > 0 THEN 28
                    WHEN POSITION('ECGYEOP' IN a.originalBkEdiFileName) > 0 THEN 29
                    WHEN POSITION('BOGTWAY' IN a.originalBkEdiFileName) > 0 THEN 45
                    ELSE 0
                END
                FROM BookingEdi a
                WHERE a.id = :ediCoparnId
            """)
    Integer findSubBusinessUNitIdByFileName(@Param("ediCoparnId") Integer ediCoparnId);

    @Query(value = "SELECT b FROM BookingEdi b WHERE b.id = :id AND b.catBkEdiStatus IN :status ")
    Optional<BookingEdi> findByIdAndStatus(@Param("id") Integer id, @Param("status") List<Integer> status);

    @Query("SELECT COUNT(e) FROM BookingEdi e " +
            "WHERE e.subBusinessUnit.id = :subUnidadNegocioId " +
            "AND e.bkEdiReservationSituation = :situacionReserva " +
            "AND COALESCE(e.bkEdiVesselName, '') = COALESCE(:naveXml, '') " +
            "AND COALESCE(e.bkEdiVoyage, '') = COALESCE(:viaje, '') " +
            "AND COALESCE(e.bkEdiLoadingPort, '') = COALESCE(:ptoEmbarque, '') " +
            "AND COALESCE(e.bookingNumber, '') = COALESCE(:booking, '') " +
            "AND COALESCE(e.bkEdiDestinationPort, '') = COALESCE(:ptoDestino, '') " +
            "AND COALESCE(e.bkEdiDischargePort, '') = COALESCE(:ptoDescarga, '') " +
            "AND COALESCE(e.bkEdiIsoCode, '') = COALESCE(:codigoIso1, '') " +
            "AND COALESCE(e.bkEdiIsoCode2, '') = COALESCE(:codigoIso2, '') " +
            "AND COALESCE(e.bkEdiCustomer, '') = COALESCE(:clienteNombreXml, '') " +
            "AND COALESCE(e.bkEdiImoCode, '') = COALESCE(:codigoImo, '') " +
            "AND COALESCE(e.bkEdiShippingLine, '') = COALESCE(:lineaBkXml, '') " +
            "AND COALESCE(e.bkEdiTemperature, '') = COALESCE(:temperatura, '') " +
            "AND e.catBkEdiStatus.id = :estado")
    Integer countBySubBusinessUnitIdAndMatchingFields(
            @Param("subUnidadNegocioId") Integer subUnidadNegocioId,
            @Param("situacionReserva") String situacionReserva,
            @Param("naveXml") String naveXml,
            @Param("viaje") String viaje,
            @Param("ptoEmbarque") String ptoEmbarque,
            @Param("booking") String booking,
            @Param("ptoDestino") String ptoDestino,
            @Param("ptoDescarga") String ptoDescarga,
            @Param("codigoIso1") String codigoIso1,
            @Param("codigoIso2") String codigoIso2,
            @Param("clienteNombreXml") String clienteNombreXml,
            @Param("codigoImo") String codigoImo,
            @Param("lineaBkXml") String lineaBkXml,
            @Param("temperatura") String temperatura,
            @Param("estado") Integer estado
    );

    @Query("SELECT e.id FROM BookingEdi e " +
            "WHERE e.subBusinessUnit.id = :subUnidadNegocioId " +
            "AND e.bkEdiReservationSituation = :situacionReserva " +
            "AND COALESCE(e.bkEdiVesselName, '') = COALESCE(:naveXml, '') " +
            "AND COALESCE(e.bkEdiVoyage, '') = COALESCE(:viaje, '') " +
            "AND COALESCE(e.bkEdiLoadingPort, '') = COALESCE(:ptoEmbarque, '') " +
            "AND COALESCE(e.bookingNumber, '') = COALESCE(:booking, '') " +
            "AND COALESCE(e.bkEdiDestinationPort, '') = COALESCE(:ptoDestino, '') " +
            "AND COALESCE(e.bkEdiDischargePort, '') = COALESCE(:ptoDescarga, '') " +
            "AND COALESCE(e.bkEdiIsoCode, '') = COALESCE(:codigoIso1, '') " +
            "AND COALESCE(e.bkEdiIsoCode2, '') = COALESCE(:codigoIso2, '') " +
            "AND COALESCE(e.bkEdiCustomer, '') = COALESCE(:clienteNombreXml, '') " +
            "AND COALESCE(e.bkEdiImoCode, '') = COALESCE(:codigoImo, '') " +
            "AND COALESCE(e.bkEdiShippingLine, '') = COALESCE(:lineaBkXml, '') " +
            "AND COALESCE(e.bkEdiTemperature, '') = COALESCE(:temperatura, '') " +
            "AND e.catBkEdiStatus.id = :estado " +
            "ORDER BY e.registrationDate DESC")
    BookingEdi findTopBySubBusinessUnitIdAndMatchingFields(
            @Param("subUnidadNegocioId") Integer subUnidadNegocioId,
            @Param("situacionReserva") String situacionReserva,
            @Param("naveXml") String naveXml,
            @Param("viaje") String viaje,
            @Param("ptoEmbarque") String ptoEmbarque,
            @Param("booking") String booking,
            @Param("ptoDestino") String ptoDestino,
            @Param("ptoDescarga") String ptoDescarga,
            @Param("codigoIso1") String codigoIso1,
            @Param("codigoIso2") String codigoIso2,
            @Param("clienteNombreXml") String clienteNombreXml,
            @Param("codigoImo") String codigoImo,
            @Param("lineaBkXml") String lineaBkXml,
            @Param("temperatura") String temperatura,
            @Param("estado") Integer estado
    );

    Optional<BookingEdi> findById(Integer ediCoparnId);

    @Modifying
    @Query("UPDATE BookingEdi e SET e.catBkEdiStatus.id = 48274, e.processedUser.id = :usuarioId, e.dateProcessedCoparn = :fechaProcesado, e.bkEdiProcessedComment = :obsProcesado, e.bkEdiCommentForProcess = null WHERE e.id = :ediCoparnId")
    void updateCoparnToRejected(
            @Param("ediCoparnId") Integer ediCoparnId,
            @Param("usuarioId") Integer usuarioId,
            @Param("fechaProcesado") LocalDateTime fechaProcesado,
            @Param("obsProcesado") String obsProcesado
    );

    @Modifying
    @Transactional
    @Query("UPDATE BookingEdi b SET b.catBkEdiStatus.id = :status, b.processedUser.id = :userId, b.dateProcessedCoparn = :updatedAt, b.bkEdiProcessedComment = :comment, b.bkEdiCommentForProcess = :commentForProcess WHERE b.id = :id")
    void updateBookingEdiById(@Param("id") Integer id,
                              @Param("status") Integer isBkediDone,
                              @Param("userId") Integer userId,
                              @Param("updatedAt") LocalDateTime updatedAt,
                              @Param("comment") String comment,
                              @Param("commentForProcess") String commentForProcess);

    @Modifying
    @Transactional
    @Query("UPDATE BookingEdi b SET b.catBkEdiStatus.id = :isBkediRejected, " +
            "b.processedUser.id = :userRecordId, " +
            "b.dateProcessedCoparn = CURRENT_TIMESTAMP, " +
            "b.bkEdiProcessedComment = :comment, " +
            "b.bkEdiCommentForProcess = NULL " +
            "WHERE b.bookingNumber = :bookingNumber AND b.subBusinessUnit.id = :subBusinessUnitId " +
            "AND b.shippingLine.id = :shippingLineId " +
            "AND b.bkEdiRegistrationType = '12' AND b.id < :ediCoparnId " +
            "AND b.catBkEdiStatus.id IN (:isBkediPending, :isBkediToProcess)")
    void rejectEdiCoparn(@Param("bookingNumber") String bookingNumber,
                         @Param("subBusinessUnitId") Integer subBusinessUnitId,
                         @Param("shippingLineId") Integer shippingLineId,
                         @Param("ediCoparnId") Integer ediCoparnId,
                         @Param("isBkediRejected") Integer isBkediRejected,
                         @Param("userRecordId") Integer userRecordId,
                         @Param("isBkediPending") Integer isBkediPending,
                         @Param("isBkediToProcess") Integer isBkediToProcess,
                         @Param("comment") String comment);

    @Modifying
    @Transactional
    @Query("UPDATE BookingEdi b SET b.catBkEdiStatus.id = :isBkediRejected, " +
            "b.processedUser.id = :userRecordId, " +
            "b.dateProcessedCoparn = CURRENT_TIMESTAMP, " +
            "b.bkEdiProcessedComment = :comment, " +
            "b.bkEdiCommentForProcess = NULL " +
            "WHERE b.bookingNumber = :bookingNumber AND b.subBusinessUnit.id = :subBusinessUnitId " +
            "AND b.shippingLine.id = :shippingLineId " +
            "AND b.bkEdiRegistrationType = '12' AND b.bkEdiCreationDate < :creationDateEDI " +
            "AND b.catBkEdiStatus.id IN (:isBkediPending, :isBkediToProcess)")
    void rejectEdiCoparnByDate(@Param("bookingNumber") String bookingNumber,
                               @Param("subBusinessUnitId") Integer subBusinessUnitId,
                               @Param("shippingLineId") Integer shippingLineId,
                               @Param("creationDateEDI") String creationDateEDI,
                               @Param("isBkediRejected") Integer isBkediRejected,
                               @Param("userRecordId") Integer userRecordId,
                               @Param("isBkediPending") Integer isBkediPending,
                               @Param("isBkediToProcess") Integer isBkediToProcess,
                               @Param("comment") String comment);

    @Modifying
    @Transactional
    @Query("UPDATE BookingEdi b SET b.catBkEdiStatus.id = :isBkediRejected, " +
            "b.processedUser.id = :userRecordId, " +
            "b.dateProcessedCoparn = CURRENT_TIMESTAMP, " +
            "b.bkEdiProcessedComment = 'The EDI has been rejected, the booking already exists.', " +
            "b.bkEdiCommentForProcess = NULL " +
            "WHERE b.id = :ediCoparnId")
    void rejectEdiCoparnOriginal(@Param("ediCoparnId") Integer ediCoparnId,
                                 @Param("isBkediRejected") Integer isBkediRejected,
                                 @Param("userRecordId") Integer userRecordId);

    @Query("""
                SELECT new com.maersk.sd1.sds.dto.BookingDTO(
                    be.subBusinessUnit.id,
                    be.bookingNumber,
                    be.id,
                    sbu.name,
                    sbu.parentBusinessUnit.id,
                    pbu.name,
                    bes.allowCreateAutomaticVesselProgramming,
                    bes.allowCreateAutomaticCustomer
                )
                FROM BookingEdi be
                JOIN be.subBusinessUnit sbu
                JOIN sbu.parentBusinessUnit pbu
                JOIN be.bookingEdiSetting bes
                LEFT JOIN be.vesselProgrammingDetail vpd
                LEFT JOIN vpd.vesselProgramming vp
                LEFT JOIN vp.vessel v
                WHERE be.dateProcessedCoparn BETWEEN :#{#filterDTO.processedFromDate} AND :#{#filterDTO.processedToDate}
                AND (be.subBusinessUnit.id = CASE WHEN :#{#filterDTO.includeAllDeposits} = true THEN be.subBusinessUnit.id ELSE :#{#filterDTO.businessSubUnitId} END)
                AND be.bookingEdiSetting.id = :#{#filterDTO.ediCoparnSetupId}
                AND bes.businessUnit.id = sbu.parentBusinessUnit.id
                AND be.bookingNumber LIKE :#{#filterDTO.bookingNumber}
                AND be.catBkEdiStatus.id IN (48273, 48274, 48276)
                AND be.bkEdiRegistrationType = '12'
                AND (COALESCE(v.name, '') LIKE :#{#filterDTO.vesselName} OR COALESCE(be.bkEdiVesselName, '') LIKE :#{#filterDTO.vesselName})
                AND (COALESCE(be.bkEdiVoyage, '') LIKE :#{#filterDTO.voyage} OR COALESCE(vp.voyage, '') LIKE :#{#filterDTO.voyage})
            """)
    List<BookingDTO> getBookingData(@Param("filterDTO") BookingFilterDTO filterDTO);


    @Query("""
                SELECT new com.maersk.sd1.sds.dto.BookingDTO(
                    be.subBusinessUnit.id,
                    be.bookingNumber,
                    null,
                    sbu.name,
                    COALESCE(sbu.parentBusinessUnit.id, bes.businessUnit.id),
                    COALESCE(pbu.name, :#{#filterDTO.nameParam}),
                    bes.allowCreateAutomaticVesselProgramming,
                    bes.allowCreateAutomaticCustomer
                )
                FROM BookingEdi be
                JOIN be.bookingEdiSetting bes
                LEFT JOIN be.subBusinessUnit sbu
                LEFT JOIN sbu.parentBusinessUnit pbu
                LEFT JOIN be.vesselProgrammingDetail vpd
                LEFT JOIN vpd.vesselProgramming vp
                LEFT JOIN vp.vessel v
                WHERE be.registrationDate BETWEEN :#{#filterDTO.fromReceptionDate} AND :#{#filterDTO.toReceptionDate}
                AND (be.subBusinessUnit.id IS NULL OR be.subBusinessUnit.id =
                CASE WHEN :#{#filterDTO.includeAllDeposits} = TRUE THEN be.subBusinessUnit.id ELSE :#{#filterDTO.businessSubUnitId} END)
                AND be.bookingEdiSetting.id = :#{#filterDTO.ediCoparnSetupId}
                AND bes.businessUnit.id = :#{#filterDTO.idParam}
                AND be.bookingNumber LIKE :#{#filterDTO.bookingNumber}
                AND be.catBkEdiStatus.id = 48272
                AND be.bkEdiReservationSituation = '9'
                AND be.bkEdiRegistrationType = '12'
                AND (COALESCE(v.name, '') LIKE :#{#filterDTO.vesselName} OR COALESCE(be.bkEdiVesselName, '') LIKE :#{#filterDTO.vesselName})
                AND (COALESCE(be.bkEdiVoyage, '') LIKE :#{#filterDTO.voyage} OR COALESCE(vp.voyage, '') LIKE :#{#filterDTO.voyage})
            """)
    List<BookingDTO> getBookingDetails(@Param("filterDTO") BookingFilterDTO filterDTO);

    @Query("""
            SELECT e FROM BookingEdi e
            WHERE e.bookingNumber IN :bookingNumbers
              AND e.subBusinessUnit.id IN :businessSubUnitIds
              AND e.bkEdiRegistrationType = '12'
            """)
    List<BookingEdi> findMatchingBookingEdi(@Param("bookingNumbers") List<String> bookingNumbers,
                                            @Param("businessSubUnitIds") List<Integer> businessSubUnitIds);

    @Query(value = """
            SELECT
                a.edi_coparn_id AS ediId,
                UPPER(a.numero_booking) AS bookingNumber,
                CASE
                    WHEN a.edi_coparn_situacion_reserva = '1' THEN 'Cancellation'
                    WHEN a.edi_coparn_situacion_reserva = '5' THEN 'Replace'
                    WHEN a.edi_coparn_situacion_reserva = '9' THEN 'Original'
                    WHEN a.edi_coparn_situacion_reserva = '2' THEN 'Addition'
                END AS messageType,
                a.edi_coparn_situacion_reserva AS messageTypeId,
                a.cat_edi_coparn_estado AS coparnStatusId,
                sds.fn_CatalogoTraducidoDes(a.cat_edi_coparn_estado, :#{#inputDTO.languageId}) AS status,
                IIF(
                                    LEN(ISNULL(a.edi_coparn_fecha_creacion_edi, '')) >= 12
                                    AND LEN(ISNULL(a.edi_coparn_fecha_creacion_edi, '')) <= 14,
                                    IIF(
                                        ISDATE(LEFT(a.edi_coparn_fecha_creacion_edi, 8) + ' ' + SUBSTRING(a.edi_coparn_fecha_creacion_edi, 9, 2) + ':' + SUBSTRING(a.edi_coparn_fecha_creacion_edi, 11, 2)) = 1,
                                        FORMAT(
                                            CONVERT(DATETIME,
                                                LEFT(a.edi_coparn_fecha_creacion_edi, 8) + ' ' +
                                                SUBSTRING(a.edi_coparn_fecha_creacion_edi, 9, 2) + ':' +
                                                SUBSTRING(a.edi_coparn_fecha_creacion_edi, 11, 2) +
                                                IIF(LEN(a.edi_coparn_fecha_creacion_edi) = 14, ':' + RIGHT(a.edi_coparn_fecha_creacion_edi, 2), '')
                                            ),
                                            [ges].[fn_FormatoDateTime](:#{#inputDTO.businessUnitId})
                                        ),
                                        a.edi_coparn_fecha_creacion_edi
                                    ),
                                    a.edi_coparn_fecha_creacion_edi
                                ) AS internalEdiDate,
                a.edi_coparn_nombre_nave AS ediVessel,
                a.edi_coparn_viaje AS ediVoyage,
                d.nombre + '/' + progx.viaje AS vesselVoyage,
                a.edi_coparn_puerto_embarque AS ediLoadingPort,
                pto_e.puerto + ' - ' + pto_e.nombre AS loadingPort,
                a.edi_coparn_puerto_descarga AS ediDischargePort,
                pto_dc.puerto + ' - ' + pto_dc.nombre AS dischargePort,
                a.edi_coparn_puerto_destino AS ediDestinationPort,
                pto_dt.puerto + ' - ' + pto_dt.nombre AS destinationPort,
                a.edi_coparn_cliente_alias AS ediCustomerAlias,
                a.edi_coparn_cliente AS ediCustomerName,
                cli.documento AS customerCode,
                cli.razon_social AS customerName,
                ISNULL(a.edi_coparn_cantidad_contenedor, 0) AS ediContainerQuantity,
                ISNULL(a.edi_coparn_codigo_iso, '') + ': ' +
                                (SELECT TOP 1 tnocnty.descripcion + ' - ' + tipocntxy.descripcion
                                 FROM sds.codigo_iso AS isox (NOLOCK)
                                 INNER JOIN ges.catalogo AS tnocnty (NOLOCK) ON isox.cat_tamano_id = tnocnty.catalogo_id
                                 INNER JOIN ges.catalogo AS tipocntxy (NOLOCK) ON isox.cat_tipo_contenedor_id = tipocntxy.catalogo_id
                                 WHERE isox.codigo_iso_id = a.codigo_iso_id AND isox.activo = 1) AS ediIsoCode,
                ISNULL(tnocntx.descripcion, '') + ' - ' + ISNULL(tipocntx.descripcion, '') + ' ' + tipocntx.descricion_larga AS containerType,
                ISNULL(a.edi_coparn_cantidad_contenedor_2, 0) AS ediContainerQuantity2,
                ISNULL(a.edi_coparn_codigo_iso_2, '') + ': ' +
                                (SELECT TOP 1 tnocnty.descripcion + ' - ' + tipocntxy.descripcion
                                 FROM sds.codigo_iso AS isox (NOLOCK)
                                 INNER JOIN ges.catalogo AS tnocnty (NOLOCK) ON isox.cat_tamano_id = tnocnty.catalogo_id
                                 INNER JOIN ges.catalogo AS tipocntxy (NOLOCK) ON isox.cat_tipo_contenedor_id = tipocntxy.catalogo_id
                                 WHERE isox.codigo_iso_id = a.codigo_iso_2_id AND isox.activo = 1) AS ediIsoCode2,
                ISNULL(tnocntx2.descripcion, '') + ' - ' + ISNULL(tipocntx2.descripcion, '') + ' ' + tipocntx2.descricion_larga AS containerType2,
                a.edi_coparn_commodity AS ediCommodity,
                productox.nombre_producto AS cargoType,
                a.edi_coparn_codigo_imo AS ediImoNumber,
                imox.codigo_imo AS imoNumber,
                0 as ediGrossWeight,
                a.edi_coparn_temperatura AS ediTemperature,
                CASE WHEN a.edi_coparn_coldtreatment = 1 THEN ges.fn_MensajeTraducido('1', 1, :#{#inputDTO.languageId}) ELSE '' END AS coldTreatment,
                CASE WHEN a.edi_coparn_atmosfera_controlada = 1 THEN ges.fn_MensajeTraducido('1', 1, :#{#inputDTO.languageId}) ELSE '' END AS controlledAtmosphere,
                a.edi_coparn_linea_naviera AS ediLine,
                lineabkx.linea_naviera AS line,
                lineaconfigx.linea_naviera AS lineConfig,
                FORMAT(ges.fn_HoraLocal(:#{#inputDTO.businessUnitId}, a.fecha_registro), ges.fn_FormatoDateTime(:#{#inputDTO.businessUnitId})) AS receptionDate,
                FORMAT(ges.fn_HoraLocal(:#{#inputDTO.businessUnitId}, a.fecha_procesado_coparn), ges.fn_FormatoDateTime(:#{#inputDTO.businessUnitId})) AS processedDate,
                ISNULL(a.edi_coparn_obs_procesado, '') +
                CASE
                      WHEN ISNULL(a.edi_coparn_obs_para_proceso, '') = ''
                      THEN ''
                      ELSE ' [Nota:' + ISNULL(a.edi_coparn_obs_para_proceso, '') + ']'
                  END +
                  CASE
                      WHEN ISNULL(a.edi_coparn_ult_obs_para_proceso, '') = ''
                      THEN ''
                      ELSE ' Motivo que no se procesaba: ' + ISNULL(a.edi_coparn_ult_obs_para_proceso, '')
                  END AS processedComment,
                a.edi_coparn_nombre_archivo_original AS originalFileName,
                 CAST(:#{#inputDTO.businessUnitName} AS VARCHAR) AS businessUnit,
                 CAST(:#{#inputDTO.deposit} AS VARCHAR) AS depot,
                a.edi_coparn_reenviado AS fileForwarded,
                FORMAT(ges.fn_HoraLocal(:#{#inputDTO.businessUnitId}, a.edi_coparn_reenviado_fecha), ges.fn_FormatoDateTime(:#{#inputDTO.businessUnitId})) AS forwardingDate,
                ges.fn_MensajeTraducido(CAST(:#{#inputDTO.isCreateAutomaticVesselProgram} AS VARCHAR), 1, :#{#inputDTO.languageId}) AS createVesselProg,
                ges.fn_MensajeTraducido(CAST(:#{#inputDTO.isCreateAutomaticClient} AS VARCHAR), 1, :#{#inputDTO.languageId}) AS createCustomer,
                a.programacion_nave_detalle_id AS idVesselProgDet,
                a.seteo_edi_coparn_id AS idCoparnConfig,
                usu_pross.nombres + ' ' + usu_pross.apellido_paterno + ' ' + usu_pross.apellido_materno AS processedUser
            FROM sds.edi_coparn AS a
            LEFT JOIN sds.linea_naviera AS lineabkx ON a.linea_naviera_bk_id = lineabkx.linea_naviera_id
            LEFT JOIN sds.linea_naviera AS lineaconfigx ON a.linea_naviera_id = lineaconfigx.linea_naviera_id
            LEFT JOIN sds.programacion_nave_detalle AS progdetx ON a.programacion_nave_detalle_id = progdetx.programacion_nave_detalle_id
            LEFT JOIN sds.programacion_nave AS progx ON progdetx.programacion_nave_id = progx.programacion_nave_id
            LEFT JOIN sds.nave AS d ON progx.nave_id = d.nave_id
            LEFT JOIN sds.puerto AS pto_e ON a.puerto_embarque_id = pto_e.puerto_id
            LEFT JOIN sds.puerto AS pto_dc ON a.puerto_descarga_id = pto_dc.puerto_id
            LEFT JOIN sds.puerto AS pto_dt ON a.puerto_destino_id = pto_dt.puerto_id
            LEFT JOIN ges.empresa AS cli ON a.empresa_cliente_id = cli.empresa_id
            LEFT JOIN ges.catalogo AS tnocntx ON a.cat_tamano_contenedor_id = tnocntx.catalogo_id
            LEFT JOIN ges.catalogo AS tipocntx ON a.cat_tipo_contenedor_id = tipocntx.catalogo_id
            LEFT JOIN ges.catalogo AS tnocntx2 ON a.cat_tamano_contenedor_2_id = tnocntx2.catalogo_id
            LEFT JOIN ges.catalogo AS tipocntx2 ON a.cat_tipo_contenedor_2_id = tipocntx2.catalogo_id
            LEFT JOIN sds.producto AS productox ON a.producto_id = productox.producto_id
            LEFT JOIN sds.imo AS imox ON a.imo_id = imox.imo_id
            LEFT JOIN seg.usuario AS usu_pross ON a.usuario_procesado_id = usu_pross.usuario_id
            WHERE a.edi_coparn_id = :#{#inputDTO.ediCoparnId}
            ORDER BY a.numero_booking, a.edi_coparn_fecha_creacion_edi, a.fecha_registro
            """,
            nativeQuery = true)
    BookingMonitoringOutputItem findBookingMonitoringData(@Param("inputDTO") BookingQueryInputDTO inputDTO);
}
