package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class MenuEditRequestDTO {

    @Data
    public static class Input {

        @JsonProperty("menu_id")
        private Integer menuId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("MenuEdit")
        private Prefix prefix;

        public Root() {
            this.prefix = new Prefix();
            this.prefix.setInput(new Input());
        }
    }
}
