package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class CurrencyUpdateInput {

    @Data
    public static class Input {

        @JsonProperty("moneda_id")
        @NotNull
        private Integer monedaId;

        @JsonProperty("nombre")
        @NotNull
        @Size(max = 100)
        private String nombre;

        @JsonProperty("abreviatura")
        @NotNull
        @Size(max = 5)
        private String abreviatura;

        @JsonProperty("simbolo")
        @NotNull
        @Size(max = 5)
        private String simbolo;

        @JsonProperty("estado")
        private Integer estado;

        @JsonProperty("usuario_modificacion_id")
        @NotNull
        private Integer usuarioModificacionId;

        @JsonProperty("separador_miles")
        @Size(max = 5)
        private String separadorMiles;

        @JsonProperty("separador_decimales")
        @Size(max = 5)
        private String separadorDecimales;

        @JsonProperty("precision")
        @Size(max = 5)
        private String precisionVal;

        @JsonProperty("ICU")
        @Size(max = 5)
        private String icu;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}
