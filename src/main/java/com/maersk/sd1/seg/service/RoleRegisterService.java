package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.seg.dto.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

@org.springframework.stereotype.Service
public class RoleRegisterService {

    private static final Logger logger = LogManager.getLogger(RoleRegisterService.class);

    private final RoleRepository roleRepository;
    private final RoleMenuRepository roleMenuRepository;
    private final RoleMenuActionRepository roleMenuActionRepository;
    private final RoleServiceRepository roleServiceRepository;
    private final RoleBusinessUnitRepository roleBusinessUnitRepository;

    @Autowired
    public RoleRegisterService(RoleRepository roleRepository, RoleMenuRepository roleMenuRepository,
                               RoleMenuActionRepository roleMenuActionRepository, RoleServiceRepository roleServiceRepository,
                               RoleBusinessUnitRepository roleBusinessUnitRepository) {
        this.roleRepository = roleRepository;
        this.roleMenuRepository = roleMenuRepository;
        this.roleMenuActionRepository = roleMenuActionRepository;
        this.roleServiceRepository = roleServiceRepository;
        this.roleBusinessUnitRepository = roleBusinessUnitRepository;
    }

    @Transactional
    public RoleRegisterOutput registerRole(RoleRegisterInput.Input input) {
        RoleRegisterOutput output = new RoleRegisterOutput();
        try {
            logger.info("Checking if Role with id1='{}' already exists", input.getRoleId());
            Optional<Role> existingRole = roleRepository.findById1(input.getRoleId());
            if (existingRole.isPresent()) {
                logger.info("Role with id1='{}' found - returning error", input.getRoleId());
                output.setRespEstado(0);
                output.setRespMensaje("El Id ingresado ya existe, ingrese otro");
                return output;
            }
            Role newRole = new Role();
            newRole.setName(input.getRoleName());
            newRole.setStatus(input.getRoleState());
            User regUser = new User();
            regUser.setId(input.getUserRegistrationId());
            newRole.setRegistrationUser(regUser);
            newRole.setRegistrationDate(LocalDateTime.now());
            newRole.setId1(input.getRoleId());
            if (input.getMenuProjectDefaultId() != null) {
                Menu defaultMenu = new Menu();
                defaultMenu.setId(input.getMenuProjectDefaultId());
                newRole.setDefaultProjectMenu(defaultMenu);
            }

            newRole = roleRepository.save(newRole);
            logger.info("New role saved with db id: {}", newRole.getId());

            if (input.getRoleServices() != null && !input.getRoleServices().isEmpty()) {
                logger.info("Inserting {} RoleService records...", input.getRoleServices().size());
                for (ServiceDTO svc : input.getRoleServices()) {
                    RoleService rs = new RoleService();
                    RoleServiceId rsId = new RoleServiceId();
                    rsId.setRoleId(newRole.getId());
                    rsId.setServiceId(svc.getServiceId());
                    rs.setId(rsId);
                    rs.setRole(newRole);

                    Service service = new Service();
                    service.setId(svc.getServiceId());
                    rs.setService(service);

                    roleServiceRepository.save(rs);
                }
            }

            if (input.getMenus() != null && !input.getMenus().isEmpty()) {
                logger.info("Inserting {} RoleMenu records...", input.getMenus().size());
                for (MenuDTO menuDTO : input.getMenus()) {
                    RoleMenuId rmId = new RoleMenuId();
                    rmId.setRoleId(newRole.getId());
                    rmId.setMenuId(menuDTO.getMenuId());

                    RoleMenu rm = new RoleMenu();
                    rm.setId(rmId);
                    rm.setRole(newRole);
                    Menu menuEntity = new Menu();
                    menuEntity.setId(menuDTO.getMenuId());
                    rm.setMenu(menuEntity);

                    roleMenuRepository.save(rm);

                    if (menuDTO.getActionId() != null) {
                        RoleMenuAction rma = new RoleMenuAction();
                        RoleMenuActionId rmaId = new RoleMenuActionId();
                        rmaId.setRoleId(newRole.getId());
                        rmaId.setMenuId(menuDTO.getMenuId());
                        rmaId.setTypeActionId(menuDTO.getActionId());

                        rma.setId(rmaId);

                        rma.setRolMenu(rm);

                        Catalog cat = new Catalog();
                        cat.setId(menuDTO.getActionId());
                        rma.setCatActionType(cat);

                        roleMenuActionRepository.save(rma);
                    }
                }
            }

            if (input.getBusinessUnits() != null && !input.getBusinessUnits().isEmpty()) {
                logger.info("Inserting {} RoleBusinessUnit records...", input.getBusinessUnits().size());
                for (BusinessUnitDTO bun : input.getBusinessUnits()) {
                    RoleBusinessUnit rbu = new RoleBusinessUnit();
                    RoleUnitBusinessId rbuId = new RoleUnitBusinessId();
                    rbuId.setRoleId(newRole.getId());
                    rbuId.setUnitBusinessId(bun.getBusinessUnitId());

                    rbu.setId(rbuId);
                    rbu.setRole(newRole);

                    BusinessUnit bu = new BusinessUnit();
                    bu.setId(bun.getBusinessUnitId());
                    rbu.setBusinessUnit(bu);

                    roleBusinessUnitRepository.save(rbu);
                }
            }

            output.setRespEstado(1);
            output.setRespMensaje("Rol registrado correctamente");
            return output;
        } catch (Exception ex) {
            logger.error("Exception while registering role.", ex);
            output.setRespEstado(0);
            output.setRespMensaje(ex.getMessage());
            return output;
        }
    }
}

