package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.ImoRepository;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sds.dto.ListImoDTO;
import com.maersk.sd1.sds.dto.ListImoInput;
import com.maersk.sd1.sds.dto.ListImoOutput;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ListImoService {

    private static final Logger logger = LogManager.getLogger(ListImoService.class.getName());
    private final ImoRepository imoRepository;
    private final MessageLanguageService messageLanguageService;

    public ResponseEntity<ResponseController<ListImoOutput>> listImoService(ListImoInput.Root request) {

        if(request==null || request.getPrefix()==null || request.getPrefix().getInput()==null){
            logger.error("Invalid request");
            return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
        }

        ListImoInput.Input input = request.getPrefix().getInput();
        Integer imoId = input.getImoId();
        String imoCode = input.getImoCode();
        String description = input.getDescription();
        String detail = input.getDetail();
        String isActiveInput = input.getIsActive();

        Boolean isActive = isActiveInput.equals("1");

        String registrationDateMin = input.getRegistrationDateMin();
        String registrationDateMax = input.getRegistrationDateMax();
        String modificationDateMin = input.getModificationDateMin();
        String modificationDateMax = input.getModificationDateMax();
        int page = input.getPage()!=null?input.getPage():1;
        int size = input.getSize()!=null?input.getSize():Integer.MAX_VALUE;
        Integer languageId = input.getLanguageId();

        Integer count=imoRepository.countImo(imoId, imoCode, description, detail, isActive, registrationDateMin, registrationDateMax, modificationDateMin, modificationDateMax);
        Pageable pageable = PageRequest.of(page - 1, size);

        List<ListImoDTO> imoList = imoRepository.findImo(imoId, imoCode, description, detail, isActive, registrationDateMin, registrationDateMax, modificationDateMin, modificationDateMax, pageable);

        for(ListImoDTO item : imoList){
            item.setDescription(messageLanguageService.getMessage(item.getDescription(),1, languageId));
        }

        ListImoOutput output = new ListImoOutput();
        List<Integer> list1 = new ArrayList<>();
        list1.add(count);
        output.setTotalRecords(Collections.singletonList(list1));
        output.setData(imoList);

        return ResponseEntity.ok(new ResponseController<>(output));
    }
}
