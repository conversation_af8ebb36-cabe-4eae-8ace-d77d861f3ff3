package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.maersk.sd1.common.GenericArraySerializer;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@JsonSerialize(using = GenericArraySerializer.class)
public class EquipmentListOutputDTO {

        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("gate_type")
        private String gateType;

        @JsonProperty("depot")
        private String depot;

        @JsonProperty("equipment_number")
        private String equipmentNumber;

        @JsonProperty("cat_equipment_category")
        private Integer catEquipmentCategory;

        @JsonProperty("equipment_category_description")
        private String equipmentCategoryDescription;

        @JsonProperty("equipment_size_type")
        private String equipmentSizeType;

        @JsonProperty("iso_code")
        private String isoCode;

        @JsonProperty("equipment_grade")
        private String equipmentGrade;

        @JsonProperty("shipping_line_chassis_owner")
        private String shippingLineChassisOwner;

        @JsonProperty("gate_in_truck_arrival")
        private String gateInTruckArrival;

        @JsonProperty("gate_in_truck_departure")
        private String gateInTruckDeparture;

        @JsonProperty("operation_type")
        private String operationType;

        @JsonProperty("document_type")
        private String documentType;

        @JsonProperty("document_number")
        private String documentNumber;

        @JsonProperty("trucking_company")
        private String truckingCompany;

        @JsonProperty("driver_name")
        private String driverName;

        @JsonProperty("truck_identifier")
        private String truckIdentifier;

        @JsonProperty("chassis_dropped")
        private String chassisDropped;

        @JsonProperty("equipment_restriction")
        private String equipmentRestriction;

        @JsonProperty("seals")
        private String seals;

        @JsonProperty("modification_user_id")
        private Integer modificationUserId;

        @JsonProperty("modification_user_name")
        private String modificationUserName;

        @JsonProperty("modification_user_lastname")
        private String modificationUserLastname;

        @JsonProperty("modification_date")
        private String modificationDate;

        @JsonProperty("register_user_id")
        private Integer registerUserId;

        @JsonProperty("register_user_name")
        private String registerUserName;

        @JsonProperty("register_user_lastname")
        private String registerUserLastname;

        @JsonProperty("register_date")
        private String registerDate;

        @JsonProperty("container_operation_type")
        private String containerOperationType;

        @JsonProperty("vessel_voyage_operation")
        private String vesselVoyageOperation;

        @JsonProperty("estructura_dano")
        private Boolean estructuraDano;

        @JsonProperty("maquinaria_dano")
        private Boolean maquinariaDano;

        @JsonProperty("para_venta")
        private String paraVenta;

        @JsonProperty("equipos_dobles")
        private String equiposDobles;

        @JsonProperty("truck_departure_user_id")
        private Integer truckDepartureUserId;

        @JsonProperty("truck_departure_user_name")
        private String truckDepartureUserName;

        @JsonProperty("truck_departure_user_lastname")
        private String truckDepartureUserLastname;

        @JsonProperty("origen_creacion")
        private String origenCreacion;

        @JsonProperty("cat_movimiento_id")
        private Integer catMovimientoId;

        @JsonProperty("cat_empty_full_id")
        private Integer catEmptyFullId;

        @JsonProperty("cap_operacion_descripcion")
        private String capOperacionDescripcion;

        @JsonProperty("tipo_movimiento")
        private String tipoMovimiento;

        @JsonProperty("inspection_photos")
        private String inspectionPhotos;

        @JsonProperty("inspection_quantity")
        private String inspectionQuantity;

        @JsonProperty("eir_send_appeir_id")
        private Integer eirSendAppeirId;

        @JsonProperty("flag_send")
        private Character flagSend;

        @JsonProperty("result_message")
        private String resultMessage;

        @JsonProperty("status_code")
        private Integer statusCode;

        @JsonProperty("eir_chassis_id")
        private Integer eirChassisId;

        @JsonProperty("gate_photos")
        private String gatePhotos;

        @JsonProperty("userDeleteId")
        private Integer userDeleteId;

        @JsonProperty("userDeleteName")
        private String userDeleteName;

        @JsonProperty("userDeleteLastName")
        private String userDeleteLastName;

        @JsonProperty("deleteDate")
        private LocalDateTime deleteDate;

        @JsonProperty("active")
        private Boolean active;

}

