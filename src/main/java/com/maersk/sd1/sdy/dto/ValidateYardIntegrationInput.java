package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ValidateYardIntegrationInput {

    @Data
    public static class Input {

        @JsonProperty("sub_business_unit_local_id")
        private Integer subBusinessUnitLocalId;

        @JsonProperty("sub_business_unit_local_alias")
        private String subBusinessUnitLocalAlias;

        @JsonProperty("type_process")
        @NotNull(message = "typeProcess cannot be null")
        private String typeProcess;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}

