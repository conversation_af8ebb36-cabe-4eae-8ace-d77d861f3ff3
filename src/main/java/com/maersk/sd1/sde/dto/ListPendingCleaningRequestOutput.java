package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

/**
 * Output DTO for pending cleaning request queries.
 * Contains an inner static class for row data, plus a total count.
 */
@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ListPendingCleaningRequestOutput {
    @JsonProperty("pending_cleaning_requests")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<EstimateDetailsDTO> pendingCleaningRequests;

    @JsonProperty("total")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<List<Long>> total;

}