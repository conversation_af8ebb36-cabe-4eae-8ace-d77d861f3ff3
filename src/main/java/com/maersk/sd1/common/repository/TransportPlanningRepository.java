package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.TransportPlanning;
import com.maersk.sd1.sdf.dto.CargoDocumentDTO;
import com.maersk.sd1.sdf.dto.TransportPlanningDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface TransportPlanningRepository extends JpaRepository<TransportPlanning, Integer> {
    @Query("SELECT DISTINCT " +
            "trp.id AS transportPlanningId, " +
            "dcde.catFullReceiptReason.id AS catReceiptReasonFullId, " +
            "doca.catCargoDocumentType.id AS catCargoDocumentTypeId, " +
            "doca.cargoDocument AS reference, " +
            "doca.shippingLine.id AS shippingLineId, " +
            "doca.consigneeCompany.id AS consigneeCompanyId, " +
            "CONCAT(cnco.document, ' - ', cnco.legalName) AS consigneeCompany, " +
            "doca.shipperCompany.id AS shipperCompanyId, " +
            "CONCAT(shco.document, ' - ', shco.legalName) AS shipperCompany, " +
            "trp.comments AS comments, " +
            "CONCAT(lin.shippingLineCompany, ' - ', lin.name) AS shippingLineName " +
            "FROM TransportPlanning trp " +
            "INNER JOIN TransportPlanningDetail tpd ON trp.id = tpd.transportPlanning.id " +
            "INNER JOIN tpd.cargoDocumentDetail dcde " +
            "INNER JOIN dcde.cargoDocument doca " +
            "INNER JOIN doca.shippingLine lin " +
            "LEFT OUTER JOIN Company shco ON doca.shipperCompany.id = shco.id " +
            "LEFT OUTER JOIN Company cnco ON doca.consigneeCompany.id = cnco.id " +
            "WHERE trp.id = :id AND trp.active = true")
    List<TransportPlanningDTO> findTransportPlanningDTOById(@Param("id") Integer transportPlanningId);

    @Query("SELECT DISTINCT new com.maersk.sd1.sdf.dto.CargoDocumentDTO(cd.id, cd.vesselProgrammingDetail.id) " +
            "FROM TransportPlanning tp " +
            "INNER JOIN TransportPlanningDetail tpd on tp.id = tpd.transportPlanning.id " +
            "INNER JOIN CargoDocumentDetail cdd on tpd.cargoDocumentDetail.id = cdd.id " +
            "INNER JOIN CargoDocument cd on cdd.cargoDocument.id = cd.id " +
            "WHERE tp.id = :transportPlanningId " +
            "ORDER BY cd.id DESC")
    List<CargoDocumentDTO> findCargoDocumentByTransportPlanning(
            @Param("transportPlanningId") Integer transportPlanningId, Pageable pageable);

    @Modifying
    @Query("UPDATE TransportPlanning tp SET tp.comments = :comments, tp.modificationUser.id = :userModificationId, tp.modificationDate = CURRENT_TIMESTAMP "
            + "WHERE tp.id = :transportPlanningId")
    int updateTransportPlanningComments(@Param("transportPlanningId") Integer transportPlanningId,
                                        @Param("comments") String comments,
                                        @Param("userModificationId") Integer userModificationId);
}