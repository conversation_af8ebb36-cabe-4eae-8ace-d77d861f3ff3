package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ReglaSistemaObtenerOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("regla_sistema_id")
    private Integer reglaSistemaId;

    @JsonProperty("alias")
    private String alias;

    @JsonProperty("sistema_id")
    private Integer sistemaId;

    @JsonProperty("unidad_negocio_id")
    private Integer unidadNegocioId;

    @JsonProperty("sub_unidad_negocio_id")
    private Integer subUnidadNegocioId;

    @JsonProperty("descripcion")
    private String descripcion;

    @JsonProperty("regla")
    private String regla;

    @JsonProperty("activo")
    private Boolean activo;

    @JsonProperty("fecha_registro")
    private LocalDateTime fechaRegistro;

    @JsonProperty("fecha_modificacion")
    private LocalDateTime fechaModificacion;
}

