package com.maersk.sd1.sde.dto;


import java.math.BigDecimal;
import java.time.LocalDate;

public interface EstimateDtoInterface {

    Integer getEstimateId();

    String getEstimateType();

    String getEstimateTypeDesc();

    Integer getEirId();

    Integer getContainerId();

    String getContainerNumber();

    String getContainerType();

    Integer getContainerSize();

    String getInspector();

    String getEstimator();

    Integer getDamageCauseId();

    String getDamageCause();

    Integer getCurrencyId();

    String getCurrencyAbbreviation();

    String getCurrencySymbol();

    LocalDate getInspectionDate();

    LocalDate getApprovalDate();

    BigDecimal getTotalHours();

    BigDecimal getTotalCost();

    String getEstimateStatus();

    String getShippingLine();

    LocalDate getGateInDate();
}