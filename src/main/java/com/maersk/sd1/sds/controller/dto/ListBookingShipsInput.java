package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Data
@Builder
@AllArgsConstructor
public class ListBookingShipsInput {

    @Data
    public static class Input {

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("nombre_nave")
        private String shipName;

        @JsonProperty("viaje")
        private String voyage;

        @JsonProperty("fecha_inicio")
        private Integer languageId;

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private ListBookingShipsInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private ListBookingShipsInput.Prefix prefix;
    }
}