#Properties file
apsint.api.alias=APS
apsint.api.login.user=<EMAIL>
apsint.api.login.password=qgWY1rXbqe8Z3WUB41wDHQ==
apsint.api.login.url=https://appointmentqa.inlandservices.com/api/inlandnet/Inlandnet/security/UserServiceImp/apiUserLogin
apsint.api.getAppointment.url=https://appointmentqa.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentRestServiceImp/apsappointmentGet
apsint.api.attendAppointment.url=https://appointmentqa.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apscitaAtender
apsint.api.getAppointmentByContainer.url=https://appointmentqa.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apscitaObtenerByContenedor
apsint.api.getAppointmentByDocument.url=https://appointmentqa.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apsgetAppointmentsByDocument
apsint.api.updateAppointmentStatus.url=https://appointmentqa.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apsupdateAppointmentStatus
msk.api.apiUserLogin.sdy.loginUrl=http://localhost:8443/Inlandnet/security/UserServiceImp/apiUserLogin
msk.api.apiUserLogin.sdy.user=<EMAIL>
msk.api.apiUserLogin.sdy.password=IIrNeMFgzdmhCbSuVdRBmka+pd8Az8ETGHaGEUuwusw=
msk.api.apiUserLogin.sdy.system=9
msk.api.apiUserLogin.sdy.newZoneUrl=http://localhost:8448/ModuleSDY/moduleYard/sdy/SDYPlanificacionZonaServiceImp/sdyobtenerUbicacionNuevaZona
msk.api.apiUserLogin.sdy.plannigGateInAssignmentAndAprrove=http://localhost:8448/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyPlannigGateInAssignmentAndAprrove