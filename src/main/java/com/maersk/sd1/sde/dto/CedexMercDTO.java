package com.maersk.sd1.sde.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CedexMercDTO {
    private String cedexMercCodigo;
    private String cedexMercDescripcion;
    private Integer mercHh;
    private Integer mercCostoMaterial;
    private String ubicacionesDano;
    private Integer catEstimadoComponenteId;
    private String codigoComponente;
    private String componenteDescripcion;
    private Integer catEstimadoMetodoRepId;
    private String codigoMetodoRep;
    private String metodoDescripcion;
    private Integer mercPiezasMax;
    private String mercDimension;
    private Integer dimensionId;
    private Integer mercValorMin;
    private Integer mercValorMax;
    private String tamanoCnt;
    private String moneda;
    private String mercObservacion;


}
