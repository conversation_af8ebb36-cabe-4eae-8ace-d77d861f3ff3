package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class RepairApprovalListOutput {

    // First array contains the list of repair approvals
    private List<List<Object>> resultadoLista;

    // Second array contains the total count
    private List<List<Long>> resultado;
}
