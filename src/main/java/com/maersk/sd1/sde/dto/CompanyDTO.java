package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class CompanyDTO implements CompanyInterface {

    @JsonProperty("company_id")
    private Integer companyId;

    @JsonProperty("company_name")
    private String companyName;
}
