package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.BookingDetailRepository;
import com.maersk.sd1.sde.controller.dto.BookingDetailsListOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BookingDetailsListServiceTest {

    @Mock
    private BookingDetailRepository bookingDetailRepository;

    @InjectMocks
    private BookingDetailsListService bookingDetailsListService;

    @BeforeEach
    void setUp() {
        //setup
    }

    @Test
    void givenBookingId_WhenNoDetailsFound_ThenReturnEmptyResult() {
        Integer bookingId = 123;
        when(bookingDetailRepository.listBookingDetalleByBookingId(bookingId)).thenReturn(Collections.emptyList());

        List<BookingDetailsListOutput> result = bookingDetailsListService.getBookingDetailsList(bookingId);

        assertNotNull(result);
//        assertEquals(1, result.getRespStatus());
//        assertTrue(result.getListContainers().isEmpty());

        verify(bookingDetailRepository, times(1)).listBookingDetalleByBookingId(bookingId);
    }

    @Test
    void givenNullBookingId_WhenGetBookingDetailsList_ThenReturnError() {
        List<BookingDetailsListOutput> result = bookingDetailsListService.getBookingDetailsList(null);

        assertNotNull(result);
//        assertEquals(0, result.getRespStatus());
//        assertTrue(result.getRespMessage().contains("booking_id cannot be null"));
    }

    @Test
    void givenBookingId_WhenExceptionOccurs_ThenHandleException() {
        Integer bookingId = 123;
        when(bookingDetailRepository.listBookingDetalleByBookingId(bookingId)).thenThrow(new RuntimeException("DB Error"));

        List<BookingDetailsListOutput> result = bookingDetailsListService.getBookingDetailsList(bookingId);

        assertNotNull(result);
//        assertEquals(0, result.getRespStatus());
//        assertTrue(result.getRespMessage().contains("Error processing request"));

        verify(bookingDetailRepository, times(1)).listBookingDetalleByBookingId(bookingId);
    }
}