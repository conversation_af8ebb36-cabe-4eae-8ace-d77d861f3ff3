package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

public class VesselRegisterInput {

    @Data
    public static class Input {

        @JsonProperty("nave")
        @NotNull
        private String ship;

        @JsonProperty("call_sign")
        @Size(max = 10)
        private String callSign;

        @JsonProperty("imo_number")
        @Size(max = 10)
        private String imoNumber;

        @JsonProperty("activo")
        @NotNull
        private Boolean active;

        @JsonProperty("usuario_registro_id")
        @NotNull
        private Integer userRegistrationId;

        @JsonProperty("nombre")
        @NotNull
        private String name;

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}

