package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.PersonSignature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface PersonSignatureRepository extends JpaRepository<PersonSignature, Integer> {

    @Query("SELECT ps.uid FROM PersonSignature ps WHERE ps.person.id = :personId AND ps.active = '1'")
    List<String> findActivePersonSignatureUids(@Param("personId") Integer personId);
}