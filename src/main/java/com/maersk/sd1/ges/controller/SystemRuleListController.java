package com.maersk.sd1.ges.controller;

import com.maersk.sd1.ges.controller.dto.SystemRuleListInputDTO;
import com.maersk.sd1.ges.controller.dto.SystemRuleListOutputDTO;
import com.maersk.sd1.ges.service.SystemRuleListService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMReglaSistemaServiceImp")
public class SystemRuleListController {
    private static final Logger logger = LogManager.getLogger(SystemRuleListController.class);

    private final SystemRuleListService systemRuleListService;

    @PostMapping("/gesreglaSistemaListar")
    public ResponseEntity<ResponseController<SystemRuleListOutputDTO>> listSystemRules(@RequestBody @Valid SystemRuleListInputDTO.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure."));
            }
            SystemRuleListInputDTO.Input input = request.getPrefix().getInput();
            SystemRuleListOutputDTO result = systemRuleListService.listSystemRules(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            SystemRuleListOutputDTO outputDTO = new SystemRuleListOutputDTO();
            outputDTO.setTotalRegistros(0);
            return ResponseEntity.internalServerError().body(new ResponseController<>(outputDTO));
        }
    }
}