package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.SystemRuleRepository;
import com.maersk.sd1.sds.dto.GetRuleGeneralJsonInput;
import com.maersk.sd1.sds.dto.GetRuleGeneralJsonOutput;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Service
@RequiredArgsConstructor
public class GetRuleGeneralJsonService {

    private static final Logger logger = LogManager.getLogger(GetRuleGeneralJsonService.class.getName());

    private final SystemRuleRepository systemRuleRepository;

    @Transactional
    public ResponseEntity<ResponseController<GetRuleGeneralJsonOutput>> getRuleGeneralJsonService(GetRuleGeneralJsonInput.Root request){

        if(request == null || request.getPrefix() == null || request.getPrefix().getInput()==null){
            logger.error("The request is empty");
            return ResponseEntity.badRequest().body(new ResponseController<>("The request is empty"));
        }

        GetRuleGeneralJsonInput.Input input = request.getPrefix().getInput();
        String systemRuleId = input.getSystemRuleId();

        GetRuleGeneralJsonOutput output = systemRuleRepository.findRuleByAlias(systemRuleId);

        return ResponseEntity.ok(new ResponseController<>(output));

    }
 }
