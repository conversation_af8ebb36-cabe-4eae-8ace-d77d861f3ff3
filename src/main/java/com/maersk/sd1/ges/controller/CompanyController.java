package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.CompanyListInputDTO;
import com.maersk.sd1.ges.dto.CompanyListOutputDTO;
import com.maersk.sd1.ges.service.CompanyService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controller class to replicate the functionality of empresa_listar.
 */
@RestController
@RequestMapping("ModuleADM/ADM/module/adm/ADMEmpresaServiceImp")
public class CompanyController {

    private static final Logger logger = LogManager.getLogger(CompanyController.class.getName());

    private CompanyService companyService;

    @Autowired
    public CompanyController(CompanyService companyService)
    {
        this.companyService = companyService;
    }

    @PostMapping("/gesempresaListar")
    public ResponseEntity<ResponseController<CompanyListOutputDTO>> getCompanyList(@RequestBody @Valid CompanyListInputDTO.Root request) {
        try {
            CompanyListInputDTO.Input input = request.getPrefix().getInput();
            CompanyListOutputDTO output = companyService.getCompanyList(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while listing companies.", e);
            CompanyListOutputDTO errorOutput = new CompanyListOutputDTO();
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}

