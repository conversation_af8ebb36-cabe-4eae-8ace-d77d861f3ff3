package com.apm.business.bean;

public class PlannigGateInAssignmentAndAprroveInRequest extends JPOBaseClass {
	
	private Integer eir_id;
	private String user_modification_id;
	private Integer language_id;
	private Integer sub_business_unit_local_id;
	
	public Integer getEir_id() {
		return eir_id;
	}
	public void setEir_id(Integer eir_id) {
		this.eir_id = eir_id;
	}
	public String getUser_modification_id() {
		return user_modification_id;
	}
	public void setUser_modification_id(String user_modification_id) {
		this.user_modification_id = user_modification_id;
	}
	public Integer getLanguage_id() {
		return language_id;
	}
	public void setLanguage_id(Integer language_id) {
		this.language_id = language_id;
	}
	public Integer getSub_business_unit_local_id() {
		return sub_business_unit_local_id;
	}
	public void setSub_business_unit_local_id(Integer sub_business_unit_local_id) {
		this.sub_business_unit_local_id = sub_business_unit_local_id;
	}
	
}