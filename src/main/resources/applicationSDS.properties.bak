#Properties file
#Usuario para acceso a los servicios del SD1
aps.api.user=<EMAIL>
aps.api.password=8APe+o/6Y/rJjhaXR0L9bA==
aps.api.token.url=http://localhost:8443/Inlandnet/security/UserServiceImp/segloginAcceder
aps.api.appointmentStatus.url=http://localhost:8460/ModuleAPS/module/aps/APSSmartDepot1ServiceImp/apsgetContainerAppointmentStatus
msk.api.yard.apiUserLogin.sdy.loginUrl=http://localhost:8443/Inlandnet/security/UserServiceImp/apiUserLogin
msk.api.yard.apiUserLogin.sdy.user=<EMAIL>
msk.api.yard.apiUserLogin.sdy.password=IIrNeMFgzdmhCbSuVdRBmka+pd8Az8ETGHaGEUuwusw=
msk.api.yard.apiUserLogin.sdy.system=9
msk.api.yard.apiUserLogin.sdy.gateInUrl=http://localhost:8448/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyPlannigGateInAssignment
msk.api.yard.apiUserLogin.sdy.sdyobtenerUbicacionNuevaZona=http://localhost:8448/ModuleSDY/moduleYard/sdy/SDYPlanificacionZonaServiceImp/sdyobtenerUbicacionNuevaZona
msk.api.yard.apiUserLogin.sdy.gateOutUrl=http://localhost:8448/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyPlanningGateOut