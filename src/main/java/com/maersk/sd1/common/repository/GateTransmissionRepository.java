package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.GateTransmission;
import com.maersk.sd1.sde.dto.EdiCodecoProjection;
import com.maersk.sd1.sde.dto.PendingCodecoQueryParams;
import jakarta.persistence.Tuple;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface GateTransmissionRepository extends JpaRepository<GateTransmission, Integer> {

    List<GateTransmission> findByIdIn(List<Integer> ids);

    @Query(value = """
        SELECT
            a.eir_id AS eirId,
            A.edi_codeco_id AS idTransmision,
            sds.fn_CatalogoTraducidoDes(a.cat_estado_envio_codeco_id, :languageId) AS estadoTransmision,
            E.sistema_entrega AS sistemaEntrega,
            sds.fn_CatalogoTraducidoDesLarga(a.cat_movimiento_id, :languageId) AS inOut,
            mty_full.descripcion AS emptyFull,
            lineax.linea_naviera AS lineaDo,
            FORMAT(seg.fn_datetime_get(:subBusinessUnitId, a.fecha_actividad), ges.fn_FormatoDateTime(E.unidad_negocio_id)) AS fechaActividad,
            a.numero_contenedor AS numeroContenedor,
            a.codigo_iso_contenedor AS codigoIsoContenedor,
            a.tara_contenedor AS taraContenedor,
            a.documento_carga AS documento,
            ISNULL(a.nombre_nave, '') + '/' + ISNULL(a.numero_viaje, '') AS naveViaje,
            opex.descripcion AS operacion,
            a.peso_mercaderia AS pesoMercaderia,
            a.cliente_razon_social AS clienteRazonSocial,
            A.precinto1, A.precinto2, A.precinto3, A.precinto4,
            CASE ISNULL(a.manifiesto_anio, 0)
                WHEN 0 THEN ''
                ELSE FORMAT(a.manifiesto_anio, '0') + '-' + a.manifiesto_numero
            END AS manifiesto,
            CASE A.estructura_con_dano
                WHEN 1 THEN ges.fn_MensajeTraducido('EIR_SIT_DANO_CNT', 2, :languageId)
                ELSE ges.fn_MensajeTraducido('EIR_SIT_DANO_CNT', 1, :languageId)
            END AS situacionCaja,
            CASE A.maquinaria_con_dano
                WHEN 1 THEN ges.fn_MensajeTraducido('EIR_SIT_DANO_CNT', 2, :languageId)
                ELSE ges.fn_MensajeTraducido('EIR_SIT_DANO_CNT', 1, :languageId)
            END AS situacionMaquina,
            a.puerto_descarga AS puertoDescarga,
            a.puerto_embarque AS puertoEmbarque,
            a.vehiculo_placa AS vehiculoPlaca,
            sds.fn_CatalogoTraducidoDesLarga(a.cat_procedencia_id, :languageId) AS tipoMovimiento,
            CASE a.tipo_transaccion
                WHEN 'O' THEN 'Original'
                WHEN 'S' THEN 'Status Activity'
                ELSE a.tipo_transaccion
            END AS transaccion,
            FORMAT(seg.fn_datetime_get(:subBusinessUnitId, a.fecha_registro), ges.fn_FormatoDateTime(E.unidad_negocio_id)) AS fechaRegistro,
            sds.fn_CatalogoTraducidoDes(envio.cat_estado_envio_codeco_id, :languageId) AS estadoArchivo,
            FORMAT(seg.fn_datetime_get(:subBusinessUnitId, envio.fecha_envio_archivo_codeco), ges.fn_FormatoDateTime(E.unidad_negocio_id)) AS fechaEnvio,
            CASE
                WHEN NOT envio.email_pendiente_enviar_id IS NULL THEN 'email'
                WHEN NOT envio.sftp_pendiente_enviar_id IS NULL THEN 'sftp'
                ELSE ''
            END AS canalEnvio,
            envio.nombre_archivo_codeco AS nombreArchivo,
            CASE A.es_reenvio
                WHEN 1 THEN ges.fn_MensajeTraducido('si', 1, :languageId)
                ELSE ''
            END AS reenvio,
            usu_reenvio.nombres + ' ' + usu_reenvio.apellido_paterno + ' ' + usu_reenvio.apellido_materno AS usuarioReenvio,
            sds.fn_CatalogoTraducidoDes(a.cat_origen_creacion_id, :languageId) AS origenCreacionEir,
            localx.nombre AS local,
            a.edi_codeco_comentario AS ediCodecoComentario,
            a.seteo_edi_codeco_id AS seteoEdiCodecoId,
            ges.fn_MensajeTraducido(IIF(a.activo = 1, '1', '0'), 1, :languageId) AS activo,
            envio.contenido_archivo_codeco AS contenidoArchivoCodeco
        FROM sde.edi_codeco AS A (NOLOCK)
        INNER JOIN sde.seteo_edi_codeco AS E (NOLOCK) ON e.seteo_edi_codeco_id = A.seteo_edi_codeco_id
        INNER JOIN ges.catalogo AS mty_full (NOLOCK) ON mty_full.catalogo_id = a.cat_empty_full_id
        INNER JOIN sds.linea_naviera AS lineax (NOLOCK) ON lineax.linea_naviera_id = a.linea_naviera_doc_carga_id
        INNER JOIN seg.unidad_negocio AS localx (NOLOCK) ON a.sub_unidad_negocio_local_id = localx.unidad_negocio_id
        LEFT OUTER JOIN ges.catalogo AS opex (NOLOCK) ON opex.catalogo_id = a.cat_operacion_id
        LEFT OUTER JOIN sde.edi_codeco_envio AS envio (NOLOCK) ON a.edi_codeco_envio_id = envio.edi_codeco_envio_id
        LEFT OUTER JOIN seg.usuario AS usu_reenvio (NOLOCK) ON a.usuario_reenvio_id = usu_reenvio.usuario_id
        WHERE a.eir_id = :eirId
        ORDER BY a.fecha_registro ASC
    """, nativeQuery = true)
    List<EdiCodecoProjection> findByEirId(
            @Param("eirId") Integer eirId,
            @Param("languageId") Integer languageId,
            @Param("subBusinessUnitId") Integer subBusinessUnitId
    );

    @Query(value = """
            SELECT
                format(seg.fn_datetime_get(:#{#params.subBusinessUnitId}, B.fecha_actividad), 'yyyyMMddHHmm') AS activityDate,
                B.edi_codeco_id AS ediCodecoId,
                lin.linea_naviera AS lineAdo,
                mov_eir.descripcion AS movement,
                mtyfull.descripcion AS emptyOrFull,
                B.eir_id AS eirId,
                B.numero_contenedor AS containerNumber,
                B.nombre_nave AS vesselName,
                B.numero_viaje AS voyageNumber,
                CASE B.cat_empty_full_id
                    WHEN :#{#params.isFull} THEN B.peso_mercaderia
                    ELSE B.tara_contenedor
                END AS containerWeight,
                ISNULL(
                    CAST((
                        CASE\s
                            WHEN B.cat_empty_full_id = :#{#params.isFull} THEN B.cat_measure_weight_id
                            ELSE B.cat_measure_tare_id
                        END
                    ) AS INT),\s
                    :#{#params.weightMeasureKg}
                ) AS weightMeasureCategoryId,
                B.codigo_iso_contenedor AS isoContainerCode,
                LTRIM(RTRIM(ISNULL(B.precinto1, ''))) AS shippingSeal,
                LTRIM(RTRIM(ISNULL(B.precinto2, ''))) AS customerSeal,
                LTRIM(RTRIM(ISNULL(B.precinto3, ''))) AS customsSeal,
                LTRIM(RTRIM(ISNULL(B.precinto4, ''))) AS otherSeals,
                ISNULL(B.documento_carga, '') AS cargoDocument,
                ISNULL(ISNULL(B.documento_carga_master, B.documento_carga), '') AS masterCargoDocument,
                CASE
                    WHEN B.estructura_con_dano = 0 AND B.maquinaria_con_dano = 0 THEN '0'
                    ELSE '1'
                END AS containerStatus,
                CASE
                    WHEN B.cat_movimiento_id = :#{#params.gateOutMovementId} AND B.cat_empty_full_id = :#{#params.isFull} THEN
                        CASE RTRIM(B.precinto3)
                            WHEN '' THEN RTRIM(B.precinto4) + '/'
                            ELSE RTRIM(B.precinto3) + '/' + RTRIM(B.precinto4)
                        END
                    ELSE ''
                END AS remarks,
                ISNULL(B.vehiculo_placa, '') AS vehiclePlate,
                ISNULL(navex.call_sign, '') AS vesselCallSign,
                ISNULL(navex.imo_number, '') AS vesselImoNumber,
                ISNULL(B.cliente_razon_social, '') AS customerName,
                ISNULL(B.cliente_documento, '') AS customerDocument,
                CAST(B.cat_procedencia_id AS INT) AS originCategoryId,
                B.estructura_con_dano AS hasBoxDamage,
                B.maquinaria_con_dano AS hasMachineDamage,
                B.contenedor_refrigerado AS isRefrigeratedContainer,
                (SELECT TOP 1 z1.concluido
                 FROM sde.eir_actividad_zona AS z1
                 WHERE z1.eir_id = B.eir_id
                 AND z1.cat_actividad_zona_id = 43162
                 AND z1.activo = 1) AS requiresInspection,
                IIF(B.cat_movimiento_id = :#{#params.gateInMovementId}, 'MSK',
                    IIF(ISNULL(B.linea_naviera_doc_carga_id, 0) IN (4104, 4106), :#{#params.carrierParam1}, :#{#params.carrierParam2})
                ) AS carrierOperatorCode,
                B.programacion_nave_detalle_id AS vesselScheduleDetailId,
                CAST(B.cat_operacion_id AS INT) AS operationCategoryId,
                B.puerto_descarga AS dischargePort,
                B.puerto_embarque AS loadingPort,
                CAST(B.cat_cargo_document_type_id AS INT) AS cargoDocumentTypeId,
                CAST(B.truck_company_id AS INT) AS truckCompanyId,
                ISNULL(B.truck_company_name, '') AS truckCompanyName,
                B.date_to_send_update_edi AS ediUpdateTimestamp,
                CAST(B.tipo_transaccion AS VARCHAR) AS transactionType,
                ISNULL(B.eir_comment, '') AS eirComment,
                ISNULL(B.temperature, '') AS temperature,
                ISNULL(CAST(B.cat_temperature_measure_id AS INT), :#{#params.temperatureUnitCelsius}) AS temperatureMeasureCategoryId,
                ISNULL(B.numero_twr, '') AS twrNumber
            FROM sde.edi_codeco AS B WITH (NOLOCK)
            INNER JOIN ges.catalogo AS mov_eir WITH (NOLOCK) ON B.cat_movimiento_id = mov_eir.catalogo_id
            INNER JOIN ges.catalogo AS mtyfull WITH (NOLOCK) ON B.cat_empty_full_id = mtyfull.catalogo_id
            INNER JOIN sds.linea_naviera AS lin WITH (NOLOCK) ON B.linea_naviera_doc_carga_id = lin.linea_naviera_id
            INNER JOIN sds.nave AS navex WITH (NOLOCK) ON B.nave_id = navex.nave_id
            WHERE B.seteo_edi_codeco_id = :#{#params.ediCodecoSetupId}
            AND B.sub_unidad_negocio_local_id = :#{#params.localBusinessUnitId}
            AND B.cat_movimiento_id = :#{#params.movementCategoryId}
            AND B.cat_empty_full_id = :#{#params.emptyFullCategoryId}
            AND B.tipo_transaccion = :#{#params.transactionType}
            AND B.fecha_actividad IS NOT NULL
            AND B.cat_estado_envio_codeco_id = :#{#params.pendingStatusId}
            AND B.fecha_salida_camion IS NOT NULL
            AND DATEADD(MI, :#{#params.minutesElapsedBeforeSend}, B.fecha_salida_camion) <= GETDATE()
            AND B.activo = 1
            ORDER BY B.fecha_actividad
            """, nativeQuery = true)
    List<Tuple> findPendingCodecoData(@Param("params") PendingCodecoQueryParams params);
}