package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.CountryFilterDto;
import com.maersk.sd1.ges.dto.CountryListInputDto;
import com.maersk.sd1.ges.dto.CountryListOutputDto;
import com.maersk.sd1.common.repository.CountryRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class CountryListService {

    private static final Logger logger = LogManager.getLogger(CountryListService.class);

    private final CountryRepository countryRepository;

    @Autowired
    public CountryListService(CountryRepository countryRepository) {
        this.countryRepository = countryRepository;
    }

    @Transactional(readOnly = true)
    public CountryListOutputDto listCountries(CountryListInputDto.Input input) {
        CountryListOutputDto output = new CountryListOutputDto();
        try {
            int page = (input.getPage() == null) ? 1 : input.getPage();
            int size = (input.getSize() == null) ? 0 : input.getSize();

            LocalDateTime registrationMaxPlusOne = null;
            if (input.getRegistrationDateMax() != null) {
                registrationMaxPlusOne = input.getRegistrationDateMax().plusDays(1);
            }
            LocalDateTime modificationMaxPlusOne = null;
            if (input.getModificationDateMax() != null) {
                modificationMaxPlusOne = input.getModificationDateMax().plusDays(1);
            }

            CountryFilterDto filter = getCountryFilterDto(input, registrationMaxPlusOne, modificationMaxPlusOne);

            Long totalCount = countryRepository.countCountriesWithFilters(filter);

            if (input.getSize() == null || input.getSize() == 0) {
                size = (totalCount == 0) ? 1 : totalCount.intValue();
            }

            Pageable pageable = PageRequest.of(
                    Math.max(page - 1, 0),
                    Math.max(size, 1)
            );

            List<CountryListOutputDto.CountryDataDto> dataList = countryRepository.findCountriesWithFilters(filter, pageable);

            output.setRespEstado(1);
            output.setRespMensaje("Success");
            output.setTotalRegistros(totalCount.intValue());
            output.setCountries(dataList);
        } catch (Exception e) {
            logger.error("Error listing countries", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            output.setTotalRegistros(0);
            output.setCountries(null);
        }

        return output;
    }

    private static CountryFilterDto getCountryFilterDto(CountryListInputDto.Input input, LocalDateTime registrationMaxPlusOne, LocalDateTime modificationMaxPlusOne) {
        CountryFilterDto filter = new CountryFilterDto();
        filter.setCountryId(input.getPaisId());
        filter.setCountryName(input.getPais());
        filter.setCountryFullName(input.getNombre());
        filter.setContinentCategoryId(input.getCatContinentId());
        filter.setIsActive(input.getActive());
        filter.setMinRegistrationDate(input.getRegistrationDateMin());
        filter.setMaxRegistrationDate(input.getRegistrationDateMax());
        filter.setMaxRegistrationDatePlusOne(registrationMaxPlusOne);
        filter.setMinModificationDate(input.getModificationDateMin());
        filter.setMaxModificationDate(input.getModificationDateMax());
        filter.setMaxModificationDatePlusOne(modificationMaxPlusOne);
        return filter;
    }
}
