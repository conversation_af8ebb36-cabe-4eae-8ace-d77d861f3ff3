package com.apm.business.service.module.sde;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Jpo;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/module/sde/SDEPreasignacionServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEPreasignacionServiceImp extends SDEPreasignacionService {

	@RequestMapping(value = "/sdepreasignacionListar", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdepreasignacionListar(Jpo ppo, HttpServletRequest request) throws Exception {
		return super.sdepreasignacionListar(ppo, request);
	}

}