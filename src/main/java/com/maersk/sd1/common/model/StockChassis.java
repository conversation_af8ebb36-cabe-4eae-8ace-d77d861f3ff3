package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Chassis;
import com.maersk.sd1.common.model.EirChassis;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "stock_chassis", schema = "sdh", indexes = {
        @Index(name = "stkchax_chassis_id", columnList = "chassis_id"),
        @Index(name = "stkchax_eir_cha_gi", columnList = "eir_chassis_gatein_id"),
        @Index(name = "stkchax_eir_cha_go", columnList = "eir_chassis_gateout_id")
})
public class StockChassis {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "stock_chassis_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sub_business_unit_id", nullable = false)
    private BusinessUnit subBusinessUnit;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "chassis_id", nullable = false)
    private Chassis chassis;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_chassis_gatein_id")
    private EirChassis eirChassisGatein;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "eir_chassis_gateout_id")
    private EirChassis eirChassisGateout;

    @NotNull
    @Column(name = "in_stock_chassis", nullable = false)
    private Boolean inStockChassis = false;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @Size(max = 20)
    @Column(name = "stock_chassis_trace", length = 20)
    private String traceStockChassis;

}