package com.maersk.sd1.common.service;

import com.maersk.sd1.common.repository.MessageLanguageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class MessageLanguageService {

    MessageLanguageRepository messageLanguageRepository;

    @Autowired
    public MessageLanguageService(MessageLanguageRepository messageLanguageRepository)
    {
        this.messageLanguageRepository = messageLanguageRepository;
    }

    public String getMessage(String type, int code, int languageId) {
        String message = messageLanguageRepository.findMensaje(type, code, languageId);
        if (message == null || message.isEmpty()) {
            message = "Message (" + type + ":" + code + ":" + languageId + ")";
        }
        return message;
    }

    public String getMessage(String type, int code, int languageId,  Map<String, String> replacement) {

        String message = messageLanguageRepository.findMensaje(type, code, languageId);
        if (message == null || replacement == null) {
            return message;
        }

        // Replacing values
        for (Map.Entry<String, String> entry : replacement.entrySet()) {
            message = message.replace(entry.getKey(), entry.getValue());
        }

        return message;
    }

}