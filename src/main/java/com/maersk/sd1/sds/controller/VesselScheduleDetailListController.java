package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.VesselScheduleDetailListInput;
import com.maersk.sd1.sds.dto.VesselScheduleDetailListOutput;
import com.maersk.sd1.sds.service.VesselScheduleDetailListService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProgramacionNaveServiceImp")
public class VesselScheduleDetailListController {

    private static final Logger logger = LogManager.getLogger(VesselScheduleDetailListController.class);

    private final VesselScheduleDetailListService vesselScheduleDetailListService;

    @PostMapping("/sdslistarProgramacionNaveOperacionId")
    public ResponseEntity<ResponseController<VesselScheduleDetailListOutput>> listarProgramacionNaveDetalle(
            @RequestBody @Valid VesselScheduleDetailListInput.Root request) {
        try {

            if(request == null){
                logger.error("Request received listarProgramacionNaveDetalle: {}", request);
                VesselScheduleDetailListOutput output = new VesselScheduleDetailListOutput();
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ResponseController<>(output));
            }

            logger.info("Request received listarProgramacionNaveDetalle: {}", request);
            var input = request.getPrefix().getInput();
            VesselScheduleDetailListOutput result = vesselScheduleDetailListService.retrieveVesselProgrammingDetail(
                    input.getProgramacionNaveDetalleId(),
                    input.getLanguageId()
            );
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            VesselScheduleDetailListOutput output = new VesselScheduleDetailListOutput();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}
