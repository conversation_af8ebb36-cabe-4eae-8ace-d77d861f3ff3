package com.maersk.sd1.sde.dto;

public interface EirActivityZoneProjection {

    String getActividad();
    String getConcluido();
    String getEstructuraDanada();
    String getMaquinariaDanada();
    String getZonaResultado();
    String getFInicio();
    String getFTermino();
    String getSensor();
    String getRcd();
    String getInspeccionCompleja();
    String getComplejidadDano();
    String getInspeccionParcial();
    String getLavadoEspecial();
    String getBloquearContenedor();
    String getPrecinto();
    String getUsuarioInicio();
    String getUsuarioTermino();
    String getFCreacion();
}
