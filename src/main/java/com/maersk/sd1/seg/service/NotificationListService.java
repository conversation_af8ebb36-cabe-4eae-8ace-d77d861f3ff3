package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.Notification;
import com.maersk.sd1.common.repository.NotificationRepository;
import com.maersk.sd1.common.repository.NotificationRoleRepository;
import com.maersk.sd1.seg.controller.dto.NotificationListInput;
import com.maersk.sd1.seg.controller.dto.NotificationListOutput;
import com.maersk.sd1.seg.dto.NotificationDetailDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class NotificationListService {

    private final NotificationRepository notificationRepository;

    private final NotificationRoleRepository notificationRoleRepository;

    @Transactional
    public NotificationListOutput<List<?>> processNotificationList(NotificationListInput.Root inputRoot) {

        NotificationListInput.Input input = inputRoot.getInput();
        Integer businessUnitId = input.getBusinessUnitId();
        String roles = input.getRoles();
        int page = input.getPage();
        int size = input.getSize();

        List<Integer> notificationIdsByRoles = fetchNotificationIdsByRoles(roles);
        Integer totalRecord = notificationRepository.countByRoles(roles, notificationIdsByRoles);
        Pageable pageable = PageRequest.of(page - 1, size);
        Page<Notification> notificationPage = notificationRepository.findNotifications(businessUnitId,
                notificationIdsByRoles, roles, pageable);
        List<NotificationListOutput.NotificationItem> notificationItems = mapToNotificationItems(notificationPage.getContent());
        List<List<?>> resultArray = List.of(
                List.of(List.of(totalRecord)),
                mapNotificationsToResult(notificationItems)
        );

        return NotificationListOutput.<List<?>>builder()
                .result(resultArray)
                .build();
    }

    private List<Integer> fetchNotificationIdsByRoles(String roles) {
        if (StringUtils.isBlank(roles)) {
            return new ArrayList<>();
        }
        return notificationRoleRepository.findNotificationIdsByRoles(
                Arrays.stream(roles.split(","))
                        .map(Integer::parseInt)
                        .toList());
    }

    private List<NotificationListOutput.NotificationItem> mapToNotificationItems(List<Notification> notifications) {
        return notifications.stream()
                .map(this::mapToNotificationItem)
                .toList();
    }

    private NotificationListOutput.NotificationItem mapToNotificationItem(Notification notification) {
        return NotificationListOutput.NotificationItem.builder()
                .notificationId(notification.getId())
                .title(notification.getTitle())
                .subtitle(notification.getSubtitle())
                .icon(notification.getIcon())
                .level(notification.getLevel() != null ? String.valueOf(notification.getLevel()) : null)
                .onlyOnce(notification.getOnlyTime() != null ? String.valueOf(notification.getOnlyTime()) : null)
                .onlyAlert(notification.getOnlyAlert() != null ? String.valueOf(notification.getOnlyAlert()) : null)
                .expirationDate(notification.getExpirationDate())
                .status(notification.getStatus() != null ? String.valueOf(notification.getStatus()) : null)
                .businessUnitId(notification.getBusinessUnit().getId())
                .roles(getRolesForNotification(notification.getId()))
                .build();
    }

    private String getRolesForNotification(Integer notificationId) {
        List<NotificationDetailDTO> roles = notificationRoleRepository.findRolesByNotificationId(notificationId);
        return CollectionUtils.isEmpty(roles) ? "" : createXmlFromRoles(roles);
    }

    private String createXmlFromRoles(List<NotificationDetailDTO> roles) {
        StringBuilder xmlRoles = new StringBuilder();
        for (NotificationDetailDTO role : roles) {
            xmlRoles.append("<row><rol_id>").append(role.getRoleId())
                    .append("</rol_id><nombre>").append(role.getRoleName())
                    .append("</nombre></row>");
        }
        return xmlRoles.toString();
    }

    private List<List<?>> mapNotificationsToResult(List<NotificationListOutput.NotificationItem> notificationItems) {
        if(notificationItems == null){
            return Collections.emptyList();
        }

        return notificationItems.stream()
                .map(notification -> Arrays.asList(
                        notification.getNotificationId(),
                        notification.getTitle(),
                        notification.getSubtitle(),
                        notification.getIcon(),
                        notification.getLevel(),
                        notification.getOnlyOnce(),
                        notification.getOnlyAlert(),
                        notification.getExpirationDate(),
                        notification.getStatus(),
                        notification.getBusinessUnitId(),
                        notification.getRoles()
                ))
                .collect(Collectors.toList());
    }
}
