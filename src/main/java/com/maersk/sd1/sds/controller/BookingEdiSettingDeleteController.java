package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.BookingEdiSettingDeleteInput;
import com.maersk.sd1.sds.controller.dto.BookingEdiSettingDeleteOutput;
import com.maersk.sd1.sds.service.BookingEdiSettingDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSEdiServiceImp")
public class BookingEdiSettingDeleteController {

    private static final Logger logger = LogManager.getLogger(BookingEdiSettingDeleteController.class);

    private final BookingEdiSettingDeleteService bookingEdiSettingDeleteService;

    public BookingEdiSettingDeleteController(BookingEdiSettingDeleteService bookingEdiSettingDeleteService) {
        this.bookingEdiSettingDeleteService = bookingEdiSettingDeleteService;
    }

    @PostMapping("/sdsseteoEdiCoparnEliminar")
    public ResponseEntity<ResponseController<BookingEdiSettingDeleteOutput>> deleteBookingEdiSetting(@RequestBody @Valid BookingEdiSettingDeleteInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure."));
            }

            BookingEdiSettingDeleteInput.Input input = request.getPrefix().getInput();

            if(input.getSettingEdiCoparnId() == null || input.getUserModificationId() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Input fields cannot be null."));
            }

            BookingEdiSettingDeleteOutput output = bookingEdiSettingDeleteService.deleteBookingEdiSetting(
                    input.getSettingEdiCoparnId(),
                    input.getUserModificationId()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the delete request.", e);
            BookingEdiSettingDeleteOutput output = new BookingEdiSettingDeleteOutput();
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}