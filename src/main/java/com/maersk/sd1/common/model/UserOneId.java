package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class UserOneId implements Serializable {
    private static final long serialVersionUID = 300101094404283923L;
    @NotNull
    @Column(name = "usuario_id", nullable = false)
    private Integer userId;

    @NotNull
    @Column(name = "unidad_negocio_id", nullable = false)
    private Integer unitBusinessId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        UserOneId entity = (UserOneId) o;
        return Objects.equals(this.unitBusinessId, entity.unitBusinessId) &&
                Objects.equals(this.userId, entity.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(unitBusinessId, userId);
    }

}