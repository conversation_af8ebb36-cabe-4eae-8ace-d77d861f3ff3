package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.BookingEdiSetting;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface BookingEdiSettingRepository extends JpaRepository<BookingEdiSetting, Integer>, JpaSpecificationExecutor<BookingEdiSetting> {

    @Query("""
            SELECT DISTINCT
            a.id,
            a.shippingLine.id,
            a.catCanalRecepcionCoparn.id,
            a.catModoProcesarCoparn.id,
            a.azureId,
            a.bkEdiSftpId,
            a.bkEdiFtpId,
            a.bkEdiFolderRoute,
            a.downloadFileExtension,
            a.edi_move_route,
            a.registrationDate,
            a.filenameMask
            FROM BookingEdiSetting a
            INNER JOIN BookingEdiSettingBU b ON a.id = b.id
            WHERE
               (:isMaersk = true AND a.shippingLine.id = :maerskShippingLineId OR :isMaersk = false AND a.shippingLine.id <> :maerskShippingLineId) AND
                a.isHistorical = false AND
                a.active = true AND
                b.active = true
            ORDER BY a.registrationDate
            """)
    List<BookingEdiSetting> findBookingDetails(@Param("maerskShippingLineId") Integer shippingLineId, @Param("isMaersk") boolean isMaersk);

    @Query(value = "exec sds.obtener_configuracion_coparn :subBusinessUnitId, :languageId", nativeQuery = true)
    List<Object[]> findBookingEdiSetting(@Param("subBusinessUnitId") Integer subBusinessUnitId, @Param("languageId") Integer languageId);

    @Modifying
    @Transactional
    @Query("UPDATE BookingEdiSetting b " +
            " SET b.active = false, " +
            "     b.modificationUser = (SELECT u FROM User u WHERE u.id = :usuarioModificacionId), " +
            "     b.modificationDate = :modificationDate " +
            " WHERE b.id = :seteoEdiCoparnId")
    void deactivateBookingEdiSetting(@Param("seteoEdiCoparnId") Integer seteoEdiCoparnId,
                                     @Param("usuarioModificacionId") Integer usuarioModificacionId,
                                     @Param("modificationDate") LocalDateTime modificationDate);

    @Query("SELECT bes FROM BookingEdiSetting bes WHERE bes.id = :ediCoparnSetting")
    Optional<BookingEdiSetting> findBybookingEdiSetting(Integer ediCoparnSetting);
}