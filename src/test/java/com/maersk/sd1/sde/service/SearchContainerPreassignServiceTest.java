package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sde.dto.SearchContainerPreassignOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class SearchContainerPreassignServiceTest {

    @Mock
    private BusinessUnitRepository businessUnitRepository;
    @Mock
    private BookingDetailRepository bookingDetailRepository;
    @Mock
    private ContainerPreassignmentRepository containerPreAssignmentRepository;
    @Mock
    private ContainerRepository containerRepository;
    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;
    @Mock
    private MessageLanguageService messageLanguageService;
    @Mock
    private CatalogRepository catalogRepository;

    @InjectMocks
    private SearchContainerPreassignService searchContainerPreassignService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void searchContainerPreassignNullContainerNumberReturnsError() {
        int subBusinessUnitId = 1;
        String containerNumber = null;
        Integer bookingDetailId = 1;
        Boolean listContainerData = true;
        Integer languageId = 1;

        when(messageLanguageService.getMessage(anyString(), anyInt(), eq(languageId))).thenReturn("Container number is required.");

        SearchContainerPreassignOutput output = searchContainerPreassignService.searchContainerPreassign(Long.valueOf(subBusinessUnitId), containerNumber, bookingDetailId, listContainerData, languageId);
        assertEquals(1, output.getResponse().getEstadoMensaje());
        assertEquals("Container number is required.", output.getResponse().getMensaje());
    }

    @Test
    void searchContainerPreassignNullBookingDetailIdReturnsError() {
        // Arrange
        Long subBusinessUnitId = 1L;
        String containerNumber = "ABC123";
        Integer bookingDetailId = null;
        Boolean listContainerData = true;
        Integer languageId = 1;

        when(messageLanguageService.getMessage(anyString(), anyInt(), eq(languageId))).thenReturn("Booking detail ID is required.");

        SearchContainerPreassignOutput output = searchContainerPreassignService.searchContainerPreassign(subBusinessUnitId, containerNumber, bookingDetailId, listContainerData, languageId);
        assertEquals(1, output.getResponse().getEstadoMensaje());
        assertEquals("Booking detail ID is required.", output.getResponse().getMensaje());
    }

    @Test
    void searchContainerPreassignBookingDetailNotFoundReturnsError() {
        Long subBusinessUnitId = 1L;
        String containerNumber = "ABC123";
        Integer bookingDetailId = 1;
        Boolean listContainerData = true;
        Integer languageId = 1;

        when(bookingDetailRepository.findBookingDetailWithBooking(bookingDetailId)).thenReturn(Optional.empty());

        SearchContainerPreassignOutput output = searchContainerPreassignService.searchContainerPreassign(subBusinessUnitId, containerNumber, bookingDetailId, listContainerData, languageId);

        assertEquals(1, output.getResponse().getEstadoMensaje());
        assertEquals("No active booking detail found.", output.getResponse().getMensaje());
    }

    @Test
    void searchContainerPreassignBookingStatusCanceledReturnsError() {
        Long subBusinessUnitId = 1L;
        String containerNumber = "ABC123";
        Integer bookingDetailId = 1;
        Boolean listContainerData = true;
        Integer languageId = 1;

        BookingDetail bookingDetail = new BookingDetail();
        Booking booking = new Booking();
        booking.setCatBookingStatus(new Catalog(43062));
        bookingDetail.setBooking(booking);

        when(bookingDetailRepository.findBookingDetailWithBooking(bookingDetailId)).thenReturn(Optional.of(bookingDetail));
        when(messageLanguageService.getMessage(anyString(), anyInt(), eq(languageId))).thenReturn("Booking is canceled.");

        SearchContainerPreassignOutput output = searchContainerPreassignService.searchContainerPreassign(subBusinessUnitId, containerNumber, bookingDetailId, listContainerData, languageId);

        assertEquals(1, output.getResponse().getEstadoMensaje());
        assertEquals("Booking is canceled.", output.getResponse().getMensaje());
    }

    @Test
    void getLineIdsLineId4106ReturnsExpectedList() {
        ShippingLine shippingLine = new ShippingLine();
        shippingLine.setId(4106);

        List<Integer> lineIds = searchContainerPreassignService.getLineIds(shippingLine);

        assertEquals(5, lineIds.size());
        assertTrue(lineIds.contains(4104));
        assertTrue(lineIds.contains(4106));
        assertTrue(lineIds.contains(4102));
        assertTrue(lineIds.contains(4108));
        assertTrue(lineIds.contains(4107));
    }

    @Test
    void getLineIdsLineId4108ReturnsExpectedList() {
        ShippingLine shippingLine = new ShippingLine();
        shippingLine.setId(4108);

        List<Integer> lineIds = searchContainerPreassignService.getLineIds(shippingLine);

        assertEquals(5, lineIds.size());
        assertTrue(lineIds.contains(4104));
        assertTrue(lineIds.contains(4106));
        assertTrue(lineIds.contains(4102));
        assertTrue(lineIds.contains(4108));
        assertTrue(lineIds.contains(4107));
    }

    @Test
    void getLineIdsLineIdOtherReturnsSingleElementList() {
        ShippingLine shippingLine = new ShippingLine();
        shippingLine.setId(1234);

        List<Integer> lineIds = searchContainerPreassignService.getLineIds(shippingLine);

        assertEquals(1, lineIds.size());
        assertTrue(lineIds.contains(1234));
    }

    @Test
    void filterContainersValidContainersReturnsFilteredContainers() {
        List<Container> containers = new ArrayList<>();
        Container container1 = new Container();
        container1.setCatSize(new Catalog(1));
        container1.setCatContainerType(new Catalog(2));
        containers.add(container1);

        Container container2 = new Container();
        container2.setCatSize(new Catalog(1));
        container2.setCatContainerType(new Catalog(3));
        containers.add(container2);

        BookingDetail bookingDetail = new BookingDetail();
        bookingDetail.setCatSize(new Catalog(1));
        bookingDetail.setCatContainerType(new Catalog(2));
        bookingDetail.setRemarkRulesName("");

        List<Container> filteredContainers = searchContainerPreassignService.filterContainers(containers, bookingDetail);

        assertEquals(1, filteredContainers.size());
        assertEquals(container1, filteredContainers.getFirst());
    }

    @Test
    void filterContainersInvalidSizeReturnsEmptyList() {
        List<Container> containers = new ArrayList<>();
        Container container = new Container();
        container.setCatSize(new Catalog(2));
        containers.add(container);

        BookingDetail bookingDetail = new BookingDetail();
        bookingDetail.setCatSize(new Catalog(1));

        List<Container> filteredContainers = searchContainerPreassignService.filterContainers(containers, bookingDetail);

        assertTrue(filteredContainers.isEmpty());
    }

    @Test
    void filterContainersFlagToFlexRemarkRuleReturnsFilteredContainers() {
        List<Container> containers = new ArrayList<>();
        Container container1 = new Container();
        container1.setCatSize(new Catalog(1));
        container1.setCatContainerType(new Catalog(2));
        containers.add(container1);

        Container container2 = new Container();
        container2.setCatSize(new Catalog(1));
        container2.setCatContainerType(new Catalog(3));
        containers.add(container2);

        BookingDetail bookingDetail = new BookingDetail();
        bookingDetail.setCatSize(new Catalog(1));
        bookingDetail.setCatContainerType(new Catalog(2));
        bookingDetail.setRemarkRulesName("FLAG_TO_FLEX");

        SearchContainerPreassignService.catalogIds.put("DRY_ID", 2);
        SearchContainerPreassignService.catalogIds.put("HC_ID", 3);

        List<Container> filteredContainers = searchContainerPreassignService.filterContainers(containers, bookingDetail);

        assertEquals(2, filteredContainers.size());
        assertTrue(filteredContainers.contains(container1));
        assertTrue(filteredContainers.contains(container2));
    }

    @Test
    void buildFinalNoteContainerMaxPayloadLessThanRequiredReturnsPayloadMessage() {
        Container container = new Container();
        container.setMaximunPayload(5000);
        BookingDetail bookingDetail = new BookingDetail();
        bookingDetail.setMaximumLoadRequired(10000);
        Long subBusinessUnitId = 1L;
        Booking booking = new Booking();
        Integer languageId = 1;

        when(messageLanguageService.getMessage(anyString(), eq(46), eq(languageId)))
                .thenReturn("Container max payload is {CARGA_MAX_CONTAINER}, required is {CARGA_MAX_CONTAINER_BK}");

        String note = searchContainerPreassignService.buildFinalNote(container, bookingDetail, subBusinessUnitId, booking, languageId);
        assertEquals("Container max payload is 5000, required is 10000", note);
    }

    @ParameterizedTest
    @CsvSource({
            "1234567, %1234567",
            "1234567890, 1234567890%",
            "12345, %12345%",
            "***********, ***********",
            "' 12345 ', %12345%"
    })
    void buildContainerNumberLikePattern(String input, String expected) {
        String pattern = searchContainerPreassignService.buildContainerNumberLikePattern(input);
        assertEquals(expected, pattern);
    }

    @ParameterizedTest
    @CsvSource({
            "10, 10, true, 1, 'Over-assigned message'",
            "5, 10, true, 1, 'Over-assigned message'",
            "0, 1, true, 1, 'Over-assigned message'"
    })
    void isOverAssigned(Integer reservationQuantity, long assignedCount, boolean expectedResult, int expectedEstadoMensaje, String expectedMensaje) {
        BookingDetail bookingDetail = new BookingDetail();
        bookingDetail.setReservationQuantity(reservationQuantity);
        Integer bookingDetailId = 1;
        SearchContainerPreassignOutput output = new SearchContainerPreassignOutput();
        Integer languageId = 1;

        when(containerPreAssignmentRepository.countActivePreassignments(bookingDetailId)).thenReturn(assignedCount);
        when(messageLanguageService.getMessage(anyString(), eq(39), eq(languageId))).thenReturn("Over-assigned message");

        boolean result = searchContainerPreassignService.isOverAssigned(bookingDetail, bookingDetailId, output, languageId);

        assertEquals(expectedResult, result);
        assertEquals(expectedEstadoMensaje, output.getResponse().getEstadoMensaje());
        assertEquals(expectedMensaje, output.getResponse().getMensaje());
    }

}