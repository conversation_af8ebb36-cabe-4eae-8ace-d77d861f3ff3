package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class CompanyPersonId implements Serializable {
    private static final long serialVersionUID = 5559161742176507251L;
    @NotNull
    @Column(name = "empresa_id", nullable = false)
    private Integer companyId;

    @NotNull
    @Column(name = "persona_id", nullable = false)
    private Integer personId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        CompanyPersonId entity = (CompanyPersonId) o;
        return Objects.equals(this.companyId, entity.companyId) &&
                Objects.equals(this.personId, entity.personId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(companyId, personId);
    }

}