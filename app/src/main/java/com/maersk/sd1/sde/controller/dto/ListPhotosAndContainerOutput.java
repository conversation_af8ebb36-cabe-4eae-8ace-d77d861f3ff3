package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ListPhotosAndContainerOutput {

    @JsonProperty("data_result")
    private List<EirContainerResponse> dataResult = new ArrayList<>();

    @Data
    @JsonFormat (shape = JsonFormat.Shape.ARRAY)
    public static class EirContainerResponse {
        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("numero_contenedor")
        private String containerNumber;

        @JsonProperty("procedencia_destino")
        private String originDestination;

        @JsonProperty("fecha_eir")
        private String eirDate;

        @JsonProperty("observacion")
        private String observation;

        @JsonProperty("tipo")
        private String type;

        @JsonProperty("cliente")
        private String client;

        @JsonProperty("tipo_eir_descripcion")
        private String eirTypeDescription;

        @JsonProperty("type_cnt")
        private String containerType;

        @JsonProperty("empresa_transporte")
        private String transportCompany;

        @JsonProperty("documento")
        private String document;

        @JsonProperty("linea_naviera")
        private String shippingLine;

        @JsonProperty("conductor_id")
        private Integer conductorId;

        @JsonProperty("conductor")
        private String conductor;

        @JsonProperty("inspector_id")
        private Integer inspectorId;

        @JsonProperty("inspector")
        private String inspector;

        @JsonProperty("fecha_firma_conductor")
        private String conductorSignatureDate;

        @JsonProperty("url_firma_conductor")
        private String conductorSignatureUrl;

        @JsonProperty("url_firma_inspector")
        private String inspectorSignatureUrl;

        @JsonProperty("eir_chassis_id")
        private Integer eirChassisId;

        @JsonProperty("person_chassis_inspector_id")
        private Integer personChassisInspectorId;

        @JsonProperty("inspector_chassis")
        private String inspectorChassis;

        @JsonProperty("url_signature_inspector_chassis")
        private String urlSignatureInspectorChassis;
    }
}