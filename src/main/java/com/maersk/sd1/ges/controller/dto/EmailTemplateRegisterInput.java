package com.maersk.sd1.ges.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class EmailTemplateRegisterInput {

    @Data
    public static class Input {

        @JsonProperty("email_plantilla_padre_id")
        private Integer emailTemplateParentId;

        @JsonProperty("contenido")
        private String content;

        @JsonProperty("remitente")
        private String sender;

        @JsonProperty("destinatario")
        private String recipient;

        @JsonProperty("copia")
        private String copy;

        @JsonProperty("copia_oculta")
        private String copyHidden;

        @JsonProperty("titulo")
        private String title;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("usuario_registro_id")
        private Integer userRegistrationId;

        @JsonProperty("descripcion")
        @NotNull(message = "Description cannot be null")
        private String description;

        @JsonProperty("menu_proyecto_id")
        private Integer projectMenuId;

        @JsonProperty("email_atributo")
        private String emailAtributoJson;

        @JsonProperty("email_plantilla_rol")
        private String emailPlantillaRolJson;

        @JsonProperty("id")
        private String idRef;

        @JsonProperty("evento_despues_enviar")
        private String eventAfterSend;

        @JsonProperty("language_id")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }

    private EmailTemplateRegisterInput () {
        // private constructor
    }
}