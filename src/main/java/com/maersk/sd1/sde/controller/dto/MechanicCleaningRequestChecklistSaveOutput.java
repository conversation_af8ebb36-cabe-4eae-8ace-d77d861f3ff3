package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class MechanicCleaningRequestChecklistSaveOutput {

    @JsonProperty("resp_status")
    private Integer respStatus;

    @JsonProperty("resp_message")
    private String respMessage;
}
