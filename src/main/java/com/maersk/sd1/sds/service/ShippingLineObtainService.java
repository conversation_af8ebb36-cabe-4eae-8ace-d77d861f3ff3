package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.sds.controller.dto.ShippingLineObtainOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
public class ShippingLineObtainService {

    private static final Logger logger = LogManager.getLogger(ShippingLineObtainService.class);

    private final ShippingLineRepository shippingLineRepository;

    public ShippingLineObtainService(ShippingLineRepository shippingLineRepository) {
        this.shippingLineRepository = shippingLineRepository;
    }

    @Transactional(readOnly = true)
    public ShippingLineObtainOutput obtainShippingLine(Integer shippingLineId) {
        ShippingLineObtainOutput output = new ShippingLineObtainOutput();
        try {
            Optional<ShippingLine> shippingLineOpt = shippingLineRepository.findShippingLineById(shippingLineId);
            if (shippingLineOpt.isPresent()) {
                ShippingLine shippingLine = shippingLineOpt.get();
                output.setRespStatus(1);
                output.setRespMessage("Success");
                output.setShippingLineId(shippingLine.getId());
                output.setName(shippingLine.getName());
                output.setActive(shippingLine.getActive());
                output.setColor(shippingLine.getColor());
                output.setRegistrationDate(shippingLine.getRegistrationDate());
                output.setModificationDate(shippingLine.getModificationDate());
                output.setShippingLineCompany(shippingLine.getShippingLineCompany());
            } else {
                output.setRespStatus(0);
                output.setRespMessage("No record found for linea_naviera_id: " + shippingLineId);
            }
        } catch (Exception e) {
            logger.error("Error obtaining shipping line", e);
            output.setRespStatus(0);
            output.setRespMessage("Error obtaining shipping line: " + e.getMessage());
        }
        return output;
    }
}