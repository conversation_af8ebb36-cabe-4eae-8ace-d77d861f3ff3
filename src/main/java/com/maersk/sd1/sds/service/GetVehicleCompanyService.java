package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.TruckRepository;
import com.maersk.sd1.sds.dto.GetVehicleCompanyInput;
import com.maersk.sd1.sds.dto.GetVehicleCompanyOutput;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

@Service
@RequiredArgsConstructor
public class GetVehicleCompanyService {

    private static final Logger logger = LogManager.getLogger(GetVehicleCompanyService.class.getName());

    private final TruckRepository truckRepository;

    @Transactional
    public List<GetVehicleCompanyOutput> getVehicleCompanyService(GetVehicleCompanyInput.Input input) {
        logger.info("Entering getVehicleCompanyService method");
        Integer vehicleId = input.getVehicleId();
        String plate = input.getVehiclePlate();

        Pageable pageable = PageRequest.of(0, 1);
        List<GetVehicleCompanyOutput> result = truckRepository.findVehicleCompanyByCriteria(vehicleId, plate, pageable);

        GetVehicleCompanyOutput output = new GetVehicleCompanyOutput();

        if(result==null || result.isEmpty()) {
            return null;
        } else {
            output = result.get(0);
        }
        List<GetVehicleCompanyOutput> listOfLists = GetVehicleCompanyOutput.createListOfLists(output);

        return listOfLists;
    }
}