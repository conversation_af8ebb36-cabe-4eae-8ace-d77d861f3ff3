package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.RuleEditInput;
import com.maersk.sd1.seg.dto.RuleEditOutput;
import com.maersk.sd1.seg.service.RuleEditService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSEG/module/seg/SEGReglaServiceImp")
public class RuleEditController {

    private static final Logger logger = LogManager.getLogger(RuleEditController.class.getName());

    private final RuleEditService service;

    @Autowired
    public RuleEditController(RuleEditService service) {
        this.service = service;
    }

    @PostMapping("/segreglaEditar")
    public ResponseEntity<ResponseController<RuleEditOutput>> editRuleService(@RequestBody RuleEditInput.Root request) {
        try {
            return service.editRuleService(request);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}
