package com.maersk.sd1.common.service;

import com.maersk.sd1.common.dto.PhotosAttachment;
import com.maersk.sd1.common.model.Attachment;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.AttachmentRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class AttachmentService {

    private final AttachmentRepository attachmentRepository;

    @Transactional
    public void attachmentServiceUpdateActiveFalse(List<Integer> ids){

        List<Attachment> attachments = attachmentRepository.findAllById(ids);

        attachments.forEach(chassisEstimateEirPhoto -> {
            chassisEstimateEirPhoto.setStatus(false);
        });

        attachmentRepository.saveAll(attachments);

    }

    @Transactional
    public Integer attachmentRegister(PhotosAttachment photosAttachment, Integer userRegistrationId){

        Attachment attachment = new Attachment();

        User userRegistration = new User();
        userRegistration.setId(userRegistrationId);

        attachment.setName(photosAttachment.getName());
        attachment.setFormat(photosAttachment.getFormat());
        attachment.setWeight(photosAttachment.getWeight());
        attachment.setLocation(photosAttachment.getLocation());
        attachment.setUrl(photosAttachment.getUrl());

        if(photosAttachment.getTypeAttachment() != null){
            Catalog catAttachmentType = new Catalog();
            catAttachmentType.setId(photosAttachment.getTypeAttachment());
            attachment.setCatAttachmentType(catAttachmentType);
        }

        attachment.setStatus(true);
        attachment.setRegistrationUser(userRegistration);
        attachment.setRegistrationDate(LocalDateTime.now());

        Attachment attachmentRegistration = attachmentRepository.save(attachment);

        return attachmentRegistration.getId();

    }

}