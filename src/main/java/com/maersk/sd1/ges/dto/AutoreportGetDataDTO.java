package com.maersk.sd1.ges.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AutoreportGetDataDTO {

    private Integer automaticReportId;
    private Integer subBusinessUnitId;
    private LocalDateTime reportStartDate;
    private LocalDateTime reportEndDate;
    private Integer recurrency;
    private String columns;
    private String filterParams;
    private String alias;
    private Integer emailTemplateId;
    private Integer reportId;
    private Integer depotId;
    private Integer azureStorageConfigId;
    private String azureStorageConfigUniqueId;
    private String azurePath;
    private Boolean active;
    private String emailSubject;

    public AutoreportGetDataDTO(Integer automaticReportId,
                                Integer subBusinessUnitId,
                                LocalDateTime reportStartDate,
                                LocalDateTime reportEndDate,
                                Integer recurrency,
                                String columns,
                                String filterParams,
                                String alias,
                                Integer emailTemplateId,
                                Integer reportId,
                                Integer depotId,
                                Integer azureStorageConfigId,
                                String azureStorageConfigUniqueId,
                                String azurePath,
                                Boolean active,
                                String emailSubject) {
        this.automaticReportId = automaticReportId;
        this.subBusinessUnitId = subBusinessUnitId;
        this.reportStartDate = reportStartDate;
        this.reportEndDate = reportEndDate;
        this.recurrency = recurrency;
        this.columns = columns;
        this.filterParams = filterParams;
        this.alias = alias;
        this.emailTemplateId = emailTemplateId;
        this.reportId = reportId;
        this.depotId = depotId;
        this.azureStorageConfigId = azureStorageConfigId;
        this.azureStorageConfigUniqueId = azureStorageConfigUniqueId;
        this.azurePath = azurePath;
        this.active = active;
        this.emailSubject = emailSubject;
    }
}