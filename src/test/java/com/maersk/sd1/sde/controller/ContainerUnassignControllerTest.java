package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.ContainerUnassignInput;
import com.maersk.sd1.sde.dto.ContainerUnassignOutput;
import com.maersk.sd1.sde.service.ContainerUnassignService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ContainerUnassignControllerTest {

    @Mock
    private ContainerUnassignService containerUnassignService;

    @InjectMocks
    private ContainerUnassignController containerUnassignController;

    private ContainerUnassignInput.Root request;
    private ContainerUnassignInput.Input input;
    private ContainerUnassignOutput output;

    @BeforeEach
    void setUp() {
        input = new ContainerUnassignInput.Input();
        input.setBookingDetalleId(1);
        input.setListaPreasignacionContenedorIds("1,2");

        request = new ContainerUnassignInput.Root();
        ContainerUnassignInput.Prefix prefix = new ContainerUnassignInput.Prefix();
        prefix.setInput(input);
        request.setPrefix(prefix);

        output = new ContainerUnassignOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Todos los contenedores fueron des-asignados satisfactoriamente");
    }

    @Test
    void testDesAsignarContenedorSuccess() {
        when(containerUnassignService.unassignContainers(input)).thenReturn(output);

        ResponseEntity<ResponseController<ContainerUnassignOutput>> response = containerUnassignController.desAsignarContenedor(request);

        assertEquals(200, response.getStatusCode().value());
        assertEquals(1, Objects.requireNonNull(response.getBody()).getResult().getRespEstado());
        assertEquals("Todos los contenedores fueron des-asignados satisfactoriamente", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testDesAsignarContenedorException() {
        when(containerUnassignService.unassignContainers(input)).thenThrow(new RuntimeException("Service error"));

        ResponseEntity<ResponseController<ContainerUnassignOutput>> response = containerUnassignController.desAsignarContenedor(request);

        assertEquals(500, response.getStatusCode().value());
        assertEquals(0, Objects.requireNonNull(response.getBody()).getResult().getRespEstado());
        assertEquals("Service error", response.getBody().getResult().getRespMensaje());
    }
}