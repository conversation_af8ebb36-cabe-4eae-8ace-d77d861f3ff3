package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.UserDeletionValidationInput;
import com.maersk.sd1.seg.dto.UserDeletionValidationOutput;
import com.maersk.sd1.seg.service.UserDeletionValidationService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMUsuarioServiceImp")
public class UserDeletionValidationController {

    private static final Logger logger = LogManager.getLogger(UserDeletionValidationController.class);

    private final UserDeletionValidationService userDeletionValidationService;

    @Autowired
    public UserDeletionValidationController(UserDeletionValidationService userDeletionValidationService) {
        this.userDeletionValidationService = userDeletionValidationService;
    }

    @PostMapping("/segusuarioEliminarValidar")
    public ResponseEntity<ResponseController<UserDeletionValidationOutput>> validateUserDeletion(@Valid @RequestBody UserDeletionValidationInput.Root request) {
        try {
            Integer userId = request.getPrefix().getInput().getUserId();
            UserDeletionValidationOutput output = userDeletionValidationService.validateUserDeletion(userId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing user deletion validation.", e);
            UserDeletionValidationOutput output = new UserDeletionValidationOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
