package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.model.SftpConfig;
import com.maersk.sd1.common.repository.SftpConfigRepository;
import com.maersk.sd1.common.repository.AttachmentRepository;
import com.maersk.sd1.ges.controller.dto.SftpConfigRemoveAttachmentOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SftpConfigRemoveAttachmentService {

    private static final Logger logger = LogManager.getLogger(SftpConfigRemoveAttachmentService.class);

    private final SftpConfigRepository sftpConfigRepository;
    private final AttachmentRepository attachmentRepository;

    public SftpConfigRemoveAttachmentService(SftpConfigRepository sftpConfigRepository, AttachmentRepository attachmentRepository) {
        this.sftpConfigRepository = sftpConfigRepository;
        this.attachmentRepository = attachmentRepository;
    }

    @Transactional
    public SftpConfigRemoveAttachmentOutput removeAttachment(Integer sftpConfigId, String tipoLlave, Integer usuarioId) {
        SftpConfigRemoveAttachmentOutput response = new SftpConfigRemoveAttachmentOutput();
        try {
            SftpConfig sftpConfig = sftpConfigRepository.findById(sftpConfigId).orElse(null);

            if (sftpConfig == null) {
                response.setRespStatus(0);
                response.setRespMessage("No SftpConfig found for id: " + sftpConfigId);
                return response;
            }
            Integer publicKeyId = sftpConfig.getPublicKeyAttachedId();
            Integer privateKeyId = sftpConfig.getPrivateKeyAttachedId();

            if ("1".equals(tipoLlave)) {
                if (publicKeyId != null) {
                    sftpConfig.setPublicKeyAttachedId(null);
                    sftpConfigRepository.save(sftpConfig);
                    attachmentRepository.deleteById(publicKeyId);
                }
            } else if ("2".equals(tipoLlave)) {
                if (privateKeyId != null) {
                    sftpConfig.setPrivateKeyAttachedId(null);
                    sftpConfigRepository.save(sftpConfig);
                    attachmentRepository.deleteById(privateKeyId);
                }
            } else {
                logger.info("keyType must be '1' or '2'");
            }
            response.setRespStatus(1);
            response.setRespMessage("Adjunto eliminado correctamente");
            return response;
        } catch (Exception e) {
            logger.error("Error removing attachment from SFTP config", e);
            response.setRespStatus(0);
            response.setRespMessage(e.getMessage());
            return response;
        }
    }
}