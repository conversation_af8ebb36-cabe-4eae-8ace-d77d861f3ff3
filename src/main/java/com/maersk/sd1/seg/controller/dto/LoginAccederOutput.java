package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.maersk.sd1.seg.dto.LoginDataObtainOutput;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class LoginAccederOutput {

    @JsonProperty("resp_estado")
    private Integer respStatus;

    @JsonProperty("resp_mensaje")
    private String respMessage;

    @JsonProperty("usuario_id")
    private String userId;

    @JsonProperty("codigo")
    private String codigo;

    @JsonProperty("nombre")
    private String names;

    @JsonProperty("apellido_paterno")
    private String apellidoPaterno;

    @JsonProperty("apellido_materno")
    private String apellidoMaterno;

    @JsonProperty("empresa")
    private String empresa;

    @JsonProperty("documento")
    private String documento;

    @JsonProperty("clave_cambiar")
    private String claveCambiar;

    @JsonProperty("clave_dias")
    private Integer claveDias;

    @JsonProperty("origen")
    private String origen;

    @JsonProperty("login_datos_obtener")
    private LoginDataObtainOutput loginDataObtainOutput;
}
