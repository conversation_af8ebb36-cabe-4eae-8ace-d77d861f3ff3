package com.apm.business.service.module.sde;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.Jpo;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.Procedure;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import ohSolutions.ohRest.util.bean.Response;

@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDEApiService {

	@RequestMapping(value = "/sdeapiEmptyGateInOut", method = {RequestMethod.POST})
	@JpoClass(oauth2Enable = true)
	public Object sdeapiEmptyGateInOut(Jpo ppo, HttpServletRequest request) throws Exception {
			Procedure pResult = ppo.procedure("sde.api_empty_gate_in_out","SDE");
			pResult.input("type_move", Jpo.STRING);
			pResult.input("freight_type", Jpo.STRING);
			pResult.input("date_move", Jpo.STRING);
			pResult.input("hour_move", Jpo.STRING);
			pResult.input("equipment", Jpo.STRING);
			pResult.input("equipment_type", Jpo.STRING);
			pResult.input("equipment_size", Jpo.STRING);
			pResult.input("tare_kg", Jpo.DECIMAL);
			pResult.input("tare_lbs", Jpo.DECIMAL);
			pResult.input("pay_load_kg", Jpo.DECIMAL);
			pResult.input("pay_load_lbs", Jpo.DECIMAL);
			pResult.input("manufacture", Jpo.STRING);
			pResult.input("grade", Jpo.STRING);
			pResult.input("shipping_line_name", Jpo.STRING);
			pResult.input("shipping_line_alias", Jpo.STRING);
			pResult.input("business_unit", Jpo.STRING);
			pResult.input("vessel_name", Jpo.STRING);
			pResult.input("vessel_alias", Jpo.STRING);
			pResult.input("voyage_number", Jpo.STRING);
			pResult.input("booking_number", Jpo.STRING);
			pResult.input("reference_number", Jpo.STRING);
			pResult.input("driver_id", Jpo.STRING);
			pResult.input("driver_first_name", Jpo.STRING);
			pResult.input("driver_last_name", Jpo.STRING);
			pResult.input("driver_email", Jpo.STRING);
			pResult.input("truck_plate", Jpo.STRING);
			pResult.input("carrier_tax_code", Jpo.STRING);
			pResult.input("carrier_name", Jpo.STRING);
			pResult.input("customer_tax_code", Jpo.STRING);
			pResult.input("customer_name", Jpo.STRING);
			pResult.input("inspector_name", Jpo.STRING);
			pResult.input("seal_1", Jpo.STRING);
			pResult.input("seal_2", Jpo.STRING);
			pResult.input("seal_3", Jpo.STRING);
			pResult.input("seal_4", Jpo.STRING);
			pResult.input("date_departure_truck", Jpo.STRING);
			pResult.input("hour_departure_truck", Jpo.STRING);
			pResult.input("remarks", Jpo.STRING);
			pResult.input("language", Jpo.STRING);
			pResult.output("eir_number", Jpo.DECIMAL);
			pResult.output("result_code", Jpo.DECIMAL);
			pResult.output("result_message", Jpo.STRING);
			Object ohb_response = pResult.executeL();
			ppo.commit();
		return new Response(ohb_response);
   }

}