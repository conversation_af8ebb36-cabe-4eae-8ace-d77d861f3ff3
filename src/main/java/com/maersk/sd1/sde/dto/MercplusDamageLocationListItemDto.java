package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class MercplusDamageLocationListItemDto {
    @JsonProperty("cat_damage_location_id")
    private Integer catDamageLocationId;

    @JsonProperty("damage_location")
    private String damageLocation;

    public MercplusDamageLocationListItemDto(Integer catDamageLocationId, String damageLocation) {
        this.catDamageLocationId = catDamageLocationId;
        this.damageLocation = damageLocation;
    }

    // No-args constructor needed for JPA or projection instantiation
    public MercplusDamageLocationListItemDto() {
    }
}
