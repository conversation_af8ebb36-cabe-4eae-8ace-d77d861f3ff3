package com.maersk.sd1.sde.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonParseException;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.DamageTypeSearchOutputDTO;
import com.maersk.sd1.sde.dto.RepairCleaningRequestChangeStatusInput;
import com.maersk.sd1.sde.dto.RepairCleaningRequestChangeStatusOutput;
import com.maersk.sd1.sde.repository.SdeEirRepository;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class RepairCleaningRequestChangeStatusService {

    private static final Logger logger = LogManager.getLogger(RepairCleaningRequestChangeStatusService.class);
    private final CatalogRepository catalogRepository;
    private final EstimateEmrRepository estimateEmrRepository;
    private final SdeEirRepository sdeEirRepository;
    private final EirZoneRepository eirZoneRepository;
    private final EirActivityZoneRepository eirActivityZoneRepository;
    private final MessageLanguageRepository messageLanguageRepository;
    private final EntityManager entityManager;

    @Transactional
    public RepairCleaningRequestChangeStatusOutput repairCleaningRequestChangeStatus(RepairCleaningRequestChangeStatusInput.Input input) {

        Integer isEstimateTypeStructure = catalogRepository.findIdByAlias(Parameter.CATALOG_ESTIMATE_BOX_ALIAS);
        Integer isEstimateTypeMachinery = catalogRepository.findIdByAlias(Parameter.CATALOG_ESTIMATE_TYPE_MACHINERY);

        Integer isRepairStatusInProgress = catalogRepository.findIdByAlias(Parameter.STATUS_REPAIR_EMPTY_CONTAINER_REPAIRED_PROGRESS);

        Integer isCleaningStatusInProgress = catalogRepository.findIdByAlias(Parameter.STATUS_CLEANING_EMPTY_CONTAINER_CLEANING_PROGRESS);

        Integer isMtyActivityBoxRepair = catalogRepository.findIdByAlias(Parameter.ACTIVITY_ZONE_EMPTY_REP);
        Integer isMtyActivityMachineRepair = catalogRepository.findIdByAlias(Parameter.ACTIVITY_ZONE_EMPTY_REPM);
        Integer isMtyActivityWash = catalogRepository.findIdByAlias(Parameter.ACTIVITY_ZONE_EMPTY_LAV);
        Integer isMtyZoneBoxRepair = catalogRepository.findIdByAlias(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_REP);
        Integer isMtyZoneMachineryRepair = catalogRepository.findIdByAlias(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_REPM);
        Integer isMtyZoneWashed = catalogRepository.findIdByAlias(Parameter.ACTIVITY_ZONE_CONTAINER_EMPTY_LAV);

        Integer isCreationSourceCntRepMng = catalogRepository.findIdByAlias(Parameter.SD1_CREATIONSOURCE_CNT_REP_MNG);
        Integer isCreationSourceCntCleanMng = catalogRepository.findIdByAlias(Parameter.SD1_CREATIONSOURCE_CNT_CLEAN_MNG);


        List<Integer> estimatesListTable = new ArrayList<>();

        Integer eirId = null;
        Integer catEstimateType = null;
        Integer catRepairStructureStatusIdOld = null;
        Integer catRepairMachineryStatusIdOld = null;
        Integer catCleaningStatusIdOld = null;

        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode rootNode = objectMapper.readTree(input.getEstimatesList());
            for (JsonNode node : rootNode) {
                estimatesListTable.add(node.get("estimate_id").asInt());
            }
        } catch (JsonProcessingException e) {
            throw new JsonParseException(e);
        }

        for (Integer item : estimatesListTable) {

            Optional<EstimateEmr> optionalEstimateEmr = estimateEmrRepository.findById(item);
            if (optionalEstimateEmr.isPresent() && Boolean.TRUE.equals(optionalEstimateEmr.get().getActive())) {
                eirId = optionalEstimateEmr.get().getEir().getId();
                catEstimateType = optionalEstimateEmr.get().getCatEstimateType().getId();
            }
            if (eirId == null) {
                throw new JsonParseException("Eir ID not found");
            }

            Optional<Eir> eirOptional = sdeEirRepository.findById(eirId);
            if (eirOptional.isPresent() && Boolean.TRUE.equals(eirOptional.get().getActive())) {
                if (eirOptional.get().getCatApprovalRepBox() != null) {
                    catRepairStructureStatusIdOld = eirOptional.get().getCatApprovalRepBox().getId();
                }
                if (eirOptional.get().getCatApprovalRepMachine() != null) {
                    catRepairMachineryStatusIdOld = eirOptional.get().getCatApprovalRepMachine().getId();
                }
                if (eirOptional.get().getCatCleaningStatus() != null) {
                    catCleaningStatusIdOld = eirOptional.get().getCatCleaningStatus().getId();
                }
            }

            catRepairStructureStatusIdOld = (catRepairStructureStatusIdOld != null) ? catRepairStructureStatusIdOld : 0;
            catRepairMachineryStatusIdOld = (catRepairMachineryStatusIdOld != null) ? catRepairMachineryStatusIdOld : 0;
            catCleaningStatusIdOld = (catCleaningStatusIdOld != null) ? catCleaningStatusIdOld : 0;


            if (input.getIsCleaningInterface() == 0) {
                if (Objects.equals(catEstimateType, isEstimateTypeStructure) && input.getNextStatusId() != null) {
                    if (!catRepairStructureStatusIdOld.equals(input.getNextStatusId())) {
                        Optional<Eir> eirOptionalEdit = sdeEirRepository.findById(eirId);
                        if (eirOptionalEdit.isPresent()) {
                            Eir eir = eirOptionalEdit.get();
                            eir.setCatApprovalRepBox(new Catalog(input.getNextStatusId()));
                            if (Objects.equals(input.getNextStatusId(), isRepairStatusInProgress)) {
                                eir.setApprobalRepBoxUser(new User(input.getUserRegistrationId()));
                                eir.setApprobalRepBoxDate(LocalDateTime.now());
                            }
                            eir.setModificationDate(LocalDateTime.now());
                            eir.setModificationUser(new User(input.getUserRegistrationId()));
                            eir.setTraceEir("cnt_reps_status_01");
                            sdeEirRepository.save(eir);
                        }
                    }
                }

                if (Objects.equals(catEstimateType, isEstimateTypeMachinery) && input.getNextStatusId() != null) {
                    if (!catRepairMachineryStatusIdOld.equals(input.getNextStatusId())) {
                        Optional<Eir> eirOptionalEdit = sdeEirRepository.findById(eirId);
                        if (eirOptionalEdit.isPresent()) {
                            Eir eir = eirOptionalEdit.get();
                            eir.setCatApprovalRepMachine(new Catalog(input.getNextStatusId()));
                            if (Objects.equals(input.getNextStatusId(), isRepairStatusInProgress)) {
                                eir.setApprobalRepMachineUser(new User(input.getUserRegistrationId()));
                                eir.setApprobalRepMachineDate(LocalDateTime.now());
                            }
                            eir.setModificationDate(LocalDateTime.now());
                            eir.setModificationUser(new User(input.getUserRegistrationId()));
                            eir.setTraceEir("cnt_reps_status_02");
                            sdeEirRepository.save(eir);
                        }
                    }
                }

                if (Objects.equals(input.getNextStatusId(), isRepairStatusInProgress) &&
                        (!(Objects.equals(catEstimateType, isEstimateTypeStructure) ? catRepairStructureStatusIdOld : catRepairMachineryStatusIdOld).equals(isRepairStatusInProgress))) {


                    Integer eirZonaId = null;
                    Integer eirActividadZonaId = null;
                    Integer mtyZoneRepairId = null;
                    Integer mtyActivityRepairId = null;
                    Integer lastEirZonaId = null;
                    Integer lastEirActividadZonaId = null;

                    if (Objects.equals(catEstimateType, isEstimateTypeStructure)) {
                        mtyZoneRepairId = isMtyZoneBoxRepair;
                    } else if (catEstimateType.equals(isEstimateTypeMachinery)) {
                        mtyZoneRepairId = isMtyZoneMachineryRepair;
                    }

                    if (catEstimateType.equals(isEstimateTypeStructure)) {
                        mtyActivityRepairId = isMtyActivityBoxRepair;
                    } else if (catEstimateType.equals(isEstimateTypeMachinery)) {
                        mtyActivityRepairId = isMtyActivityMachineRepair;
                    }

                    List<EirZone> optionalEirZone = eirZoneRepository.findTopByEirAndCatContainerZoneAndActive(eirId, mtyZoneRepairId);
                    if (!optionalEirZone.isEmpty()) {
                        eirZonaId = optionalEirZone.getFirst().getId();
                    }

                    List<EirZone> optionalEirActividadZona = eirZoneRepository.findTopByEirAndActive(eirId);
                    if (!optionalEirActividadZona.isEmpty()) {
                        lastEirActividadZonaId = optionalEirActividadZona.getFirst().getId();
                    }

                    if ((eirZonaId == null ? 0 : eirZonaId) != (lastEirZonaId == null ? 0 : lastEirZonaId)) {
                        if (eirZonaId != null) {
                            Optional<EirZone> eirZoneOptionalEdit = eirZoneRepository.findById(eirZonaId);
                            if (eirZoneOptionalEdit.isPresent()) {
                                EirZone eirZone = eirZoneOptionalEdit.get();
                                eirZone.setActive(false);
                                eirZone.setDateModicacion(LocalDateTime.now());
                                eirZone.setModificationUser(new User(input.getUserRegistrationId()));
                                eirZone.setTraceEirZone("cnt_reps_status_03");
                                eirZoneRepository.save(eirZone);
                            }
                        }

                        EirZone eirZoneCreate = new EirZone();
                        eirZoneCreate.setEir(new Eir(eirId));
                        eirZoneCreate.setCatContainerZone(new Catalog(mtyZoneRepairId));
                        eirZoneCreate.setRegistrationDate(LocalDateTime.now());
                        eirZoneCreate.setRegistrationUser(new User(input.getUserRegistrationId()));
                        eirZoneCreate.setActive(true);
                        eirZoneCreate.setTraceEirZone("cnt_reps_status_04");
                        eirZoneCreate.setCatOrigenCreacionEirzona(new Catalog(isCreationSourceCntRepMng));
                        eirZoneCreate = eirZoneRepository.save(eirZoneCreate);
                        eirZonaId = eirZoneCreate.getId();
                        entityManager.flush();
                    }


                    List<EirActivityZone> optionalEirActivityZone = eirActivityZoneRepository.findTopByEirAndCatZoneActivityAndActiveTrue(eirId, mtyActivityRepairId);
                    if (!optionalEirActivityZone.isEmpty()) {
                        eirActividadZonaId = optionalEirActivityZone.getFirst().getId();
                    }

                    List<EirActivityZone> optionalLastEirActivityZone = eirActivityZoneRepository.findTopByEirAndActiveTrue(eirId);
                    if (!optionalLastEirActivityZone.isEmpty()) {
                        lastEirActividadZonaId = optionalLastEirActivityZone.getFirst().getId();
                    }

                    if ((eirActividadZonaId == null ? 0 : eirActividadZonaId) != (lastEirActividadZonaId == null ? 0 : lastEirActividadZonaId)) {
                        if (eirActividadZonaId != null) {
                            Optional<EirActivityZone> activityZoneOptional = eirActivityZoneRepository.findById(eirActividadZonaId);
                            if (activityZoneOptional.isPresent()) {
                                EirActivityZone eirActivityZone = activityZoneOptional.get();
                                eirActivityZone.setActive(false);
                                eirActivityZone.setModificationDate(LocalDateTime.now());
                                eirActivityZone.setModificationUser(new User(input.getUserRegistrationId()));
                                eirActivityZone.setTracezoneActivity("cnt_rep_status_03");
                                eirActivityZoneRepository.save(eirActivityZone);
                            }
                        }
                        EirActivityZone eirActivityZoneCreate = new EirActivityZone();
                        eirActivityZoneCreate.setEir(new Eir(eirId));
                        eirActivityZoneCreate.setCatZoneActivity(new Catalog(mtyActivityRepairId));
                        eirActivityZoneCreate.setStartDate(LocalDateTime.now());
                        eirActivityZoneCreate.setConcluded(false);
                        eirActivityZoneCreate.setStartUser(new User(input.getUserRegistrationId()));
                        eirActivityZoneCreate.setRegistrationDate(LocalDateTime.now());
                        eirActivityZoneCreate.setActive(true);
                        if (eirZonaId != null) {
                            EirZone eirZone = new EirZone();
                            eirZone.setId(eirZonaId);
                            eirActivityZoneCreate.setEirZone(eirZone);
                        }
                        eirActivityZoneCreate.setCatZoneActivityResult(new Catalog(mtyActivityRepairId));
                        eirActivityZoneCreate.setTracezoneActivity("cnt_rep_status_04");
                        eirActivityZoneCreate.setIsPartialInspection(false);
                        eirActivityZoneCreate.setCatCreacionEIROrigin(new Catalog(isCreationSourceCntRepMng));
                        eirActivityZoneRepository.save(eirActivityZoneCreate);

                    } else {
                        if (eirActividadZonaId == null) {
                            throw new JsonParseException("Eir Activity Zone ID not found");
                        }
                        Optional<EirActivityZone> activityZoneOptional = eirActivityZoneRepository.findById(eirActividadZonaId);
                        if (activityZoneOptional.isPresent()) {
                            EirActivityZone eirActivityZone = activityZoneOptional.get();
                            eirActivityZone.setStartDate(LocalDateTime.now());
                            if (eirZonaId != null) {
                                EirZone eirZone = new EirZone();
                                eirZone.setId(eirZonaId);
                                eirActivityZone.setEirZone(eirZone);
                            }
                            eirActivityZone.setStartUser(new User(input.getUserRegistrationId()));
                            eirActivityZone.setModificationDate(LocalDateTime.now());
                            eirActivityZone.setModificationUser(new User(input.getUserRegistrationId()));
                            eirActivityZone.setTracezoneActivity("cnt_clea_status_09");
                            eirActivityZoneRepository.save(eirActivityZone);
                        }
                    }


                }
            }


            if (input.getIsCleaningInterface() == 1) {
                if (input.getNextStatusId() != null && !Objects.equals(catCleaningStatusIdOld, isCleaningStatusInProgress)) {
                    Optional<Eir> eirOptionalEdit = sdeEirRepository.findById(eirId);
                    if (eirOptionalEdit.isPresent()) {
                        Eir eir = eirOptionalEdit.get();
                        eir.setCatCleaningStatus(new Catalog(input.getNextStatusId()));
                        eir.setModificationDate(LocalDateTime.now());
                        eir.setModificationUser(new User(input.getUserRegistrationId()));
                        eir.setTraceEir("cnt_clea_status_05");
                        sdeEirRepository.save(eir);
                    }


                    if (Objects.equals(input.getNextStatusId(), isCleaningStatusInProgress)) {

                        Integer eirZonaCleanId = null;
                        Integer eirActividadZonaCleanId = null;
                        Integer lastEirZonaCleanId = null;
                        Integer lastEirActividadZonaCleanId = null;

                        List<EirZone> optionalEirZone = eirZoneRepository.findTopByEirAndCatContainerZoneAndActive(eirId, isMtyZoneWashed);
                        if (!optionalEirZone.isEmpty()) {
                            eirZonaCleanId = optionalEirZone.getFirst().getId();
                        }
                        List<EirZone> optionalEirZoneLast = eirZoneRepository.findTopByEirAndActive(eirId);
                        if (!optionalEirZoneLast.isEmpty()) {
                            lastEirZonaCleanId = optionalEirZoneLast.getFirst().getId();
                        }

                        if ((eirZonaCleanId == null ? 0 : eirZonaCleanId) != (lastEirZonaCleanId == null ? 0 : lastEirZonaCleanId)) {
                            if (eirZonaCleanId != null) {
                                Optional<EirZone> eirZoneOptionalEdit = eirZoneRepository.findById(eirZonaCleanId);
                                if (eirZoneOptionalEdit.isPresent()) {
                                    EirZone eirZone = eirZoneOptionalEdit.get();
                                    eirZone.setActive(false);
                                    eirZone.setDateModicacion(LocalDateTime.now());
                                    eirZone.setModificationUser(new User(input.getUserRegistrationId()));
                                    eirZone.setTraceEirZone("cnt_clea_status_06");
                                    eirZoneRepository.save(eirZone);
                                }

                            }
                            EirZone eirZoneCreate = new EirZone();
                            eirZoneCreate.setEir(new Eir(eirId));
                            eirZoneCreate.setCatContainerZone(new Catalog(isMtyZoneWashed));
                            eirZoneCreate.setRegistrationDate(LocalDateTime.now());
                            eirZoneCreate.setRegistrationUser(new User(input.getUserRegistrationId()));
                            eirZoneCreate.setActive(true);
                            eirZoneCreate.setTraceEirZone("cnt_clea_status_07");
                            eirZoneCreate.setCatOrigenCreacionEirzona(new Catalog(isCreationSourceCntCleanMng));
                            eirZoneCreate = eirZoneRepository.save(eirZoneCreate);
                            eirZonaCleanId = eirZoneCreate.getId();
                            entityManager.flush();
                        }

                        List<EirActivityZone> optionalEirActivityZone = eirActivityZoneRepository.findTopByEirAndCatZoneActivityAndActiveTrue(eirId, isMtyActivityWash);
                        if (!optionalEirActivityZone.isEmpty()) {
                            eirActividadZonaCleanId = optionalEirActivityZone.getFirst().getId();
                        }
                        List<EirActivityZone> optionalLastEirActivityZone = eirActivityZoneRepository.findTopByEirAndActiveTrue(eirId);
                        if (!optionalLastEirActivityZone.isEmpty()) {
                            lastEirActividadZonaCleanId = optionalLastEirActivityZone.getFirst().getId();
                        }

                        if ((eirActividadZonaCleanId == null ? 0 : eirActividadZonaCleanId) != (lastEirActividadZonaCleanId == null ? 0 : lastEirActividadZonaCleanId)) {
                            if (eirActividadZonaCleanId != null) {
                                Optional<EirActivityZone> activityZoneOptional = eirActivityZoneRepository.findById(eirActividadZonaCleanId);
                                if (activityZoneOptional.isPresent()) {
                                    EirActivityZone eirActivityZone = activityZoneOptional.get();
                                    eirActivityZone.setActive(false);
                                    eirActivityZone.setModificationDate(LocalDateTime.now());
                                    eirActivityZone.setModificationUser(new User(input.getUserRegistrationId()));
                                    eirActivityZone.setTracezoneActivity("cnt_clea_status_06");
                                    eirActivityZoneRepository.save(eirActivityZone);
                                }
                            }
                            EirActivityZone eirActivityZoneCreate = new EirActivityZone();
                            eirActivityZoneCreate.setEir(new Eir(eirId));
                            eirActivityZoneCreate.setCatZoneActivity(new Catalog(isMtyActivityWash));
                            eirActivityZoneCreate.setStartDate(LocalDateTime.now());
                            eirActivityZoneCreate.setConcluded(false);
                            eirActivityZoneCreate.setStartUser(new User(input.getUserRegistrationId()));
                            eirActivityZoneCreate.setRegistrationDate(LocalDateTime.now());
                            eirActivityZoneCreate.setActive(true);
                            if (eirZonaCleanId != null) {
                                EirZone eirZone = new EirZone();
                                eirZone.setId(eirZonaCleanId);
                                eirActivityZoneCreate.setEirZone(eirZone);
                            }

                            eirActivityZoneCreate.setCatZoneActivityResult(new Catalog(isMtyActivityWash));
                            eirActivityZoneCreate.setTracezoneActivity("cnt_clea_status_07");
                            eirActivityZoneCreate.setIsPartialInspection(false);
                            eirActivityZoneCreate.setCatCreacionEIROrigin(new Catalog(isCreationSourceCntCleanMng));
                            eirActivityZoneRepository.save(eirActivityZoneCreate);
                        } else {
                            if(eirActividadZonaCleanId == null) {
                                throw new JsonParseException("Eir Activity Zone ID not found");
                            }
                            Optional<EirActivityZone> activityZoneOptional = eirActivityZoneRepository.findById(eirActividadZonaCleanId);
                            if (activityZoneOptional.isPresent()) {
                                EirActivityZone eirActivityZone = activityZoneOptional.get();
                                eirActivityZone.setStartDate(LocalDateTime.now());
                                EirZone eirZone = new EirZone();
                                eirZone.setId(eirZonaCleanId);
                                eirActivityZone.setEirZone(eirZone);
                                eirActivityZone.setStartUser(new User(input.getUserRegistrationId()));
                                eirActivityZone.setModificationDate(LocalDateTime.now());
                                eirActivityZone.setModificationUser(new User(input.getUserRegistrationId()));
                                eirActivityZone.setTracezoneActivity("cnt_clea_status_08");
                                eirActivityZoneRepository.save(eirActivityZone);
                            }
                        }
                    }
                }
            }


        }

        RepairCleaningRequestChangeStatusOutput output = new RepairCleaningRequestChangeStatusOutput();
        output.setRespStatus(1);
        output.setRespMessage(messageLanguageRepository.fnTranslatedMessage("EMR_REPAIR_MANAGEMENT", 1, input.getLanguageId()));

        return output;
    }
}

