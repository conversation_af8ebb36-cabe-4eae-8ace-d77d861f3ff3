package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.CatalogLanguageListInput;
import com.maersk.sd1.ges.dto.CatalogLanguageListOutput;
import com.maersk.sd1.ges.dto.CatalogLanguageData;
import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CatalogLanguageListService {

    private static final Logger logger = LogManager.getLogger(CatalogLanguageListService.class);

    private final CatalogLanguageRepository catalogLanguageRepository;

    @Autowired
    public CatalogLanguageListService(CatalogLanguageRepository catalogLanguageRepository) {
        this.catalogLanguageRepository = catalogLanguageRepository;
    }

    @Transactional(readOnly = true)
    public CatalogLanguageListOutput listCatalogLanguages(CatalogLanguageListInput.Input inputDto) {
        CatalogLanguageListOutput output = new CatalogLanguageListOutput();
        try {
            Integer pageParam = (inputDto.getPage() == null) ? 1 : inputDto.getPage();
            long total = catalogLanguageRepository.countByFilters(
                    inputDto.getCatalogLanguageId(),
                    inputDto.getCatalogId(),
                    inputDto.getLanguageId(),
                    inputDto.getDescription(),
                    inputDto.getLongDescription()
            );
            Integer sizeParam;
            if (inputDto.getSize() == null) {
                if (total == 0) {
                    sizeParam = 1;
                } else {
                    sizeParam = (int) total;
                }
            } else {
                sizeParam = inputDto.getSize();
            }

            Pageable pageable = PageRequest.of(pageParam - 1, sizeParam);

            Page<CatalogLanguageData> pageResult = catalogLanguageRepository.findByFilters(
                    inputDto.getCatalogLanguageId(),
                    inputDto.getCatalogId(),
                    inputDto.getLanguageId(),
                    inputDto.getDescription(),
                    inputDto.getLongDescription(),
                    pageable
            );

            output.setTotalRecords(pageResult.getTotalElements());
            output.setCatalogLanguages(pageResult.getContent());
        } catch (Exception e) {
            logger.error("Error in listCatalogLanguages: ", e);
            output.setTotalRecords(0L);
            output.setCatalogLanguages(List.of());
        }
        return output;
    }
}
