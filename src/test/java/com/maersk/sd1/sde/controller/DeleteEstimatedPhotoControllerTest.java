package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.DeleteEstimatedPhotoInput;
import com.maersk.sd1.sde.dto.DeleteEstimatedPhotoOutput;
import com.maersk.sd1.sde.service.DeleteEstimatedPhotoService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class DeleteEstimatedPhotoControllerTest {

    @InjectMocks
    private DeleteEstimatedPhotoController deleteEstimatedPhotoController;

    @Mock
    private DeleteEstimatedPhotoService deleteEstimatedPhotoService;

    @Test
    void testDeletePhoto_Success() {
        // Mock input
        DeleteEstimatedPhotoInput.Root request = new DeleteEstimatedPhotoInput.Root();
        DeleteEstimatedPhotoInput.Prefix prefix = new DeleteEstimatedPhotoInput.Prefix();
        DeleteEstimatedPhotoInput.Input input = new DeleteEstimatedPhotoInput.Input();
        input.setAdjuntoId(111); // Set sample adjuntoId
        prefix.setInput(input);
        request.setPrefix(prefix);

        // Mock service output
        DeleteEstimatedPhotoOutput output = new DeleteEstimatedPhotoOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");
        Mockito.when(deleteEstimatedPhotoService.deleteAttatchment(111)).thenReturn(output);

        // Call the controller method
        ResponseEntity<ResponseController<DeleteEstimatedPhotoOutput>> response = deleteEstimatedPhotoController.deletePhoto(request);

        // Assert the status and response
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testDeletePhoto_ServiceError() {
        // Mock input
        DeleteEstimatedPhotoInput.Root request = new DeleteEstimatedPhotoInput.Root();
        DeleteEstimatedPhotoInput.Prefix prefix = new DeleteEstimatedPhotoInput.Prefix();
        DeleteEstimatedPhotoInput.Input input = new DeleteEstimatedPhotoInput.Input();
        input.setAdjuntoId(111); // Set sample adjuntoId
        prefix.setInput(input);
        request.setPrefix(prefix);

        // Mock service exception
        Mockito.when(deleteEstimatedPhotoService.deleteAttatchment(111)).thenThrow(new RuntimeException("Service failed"));

        // Call the controller method
        ResponseEntity<ResponseController<DeleteEstimatedPhotoOutput>> response = deleteEstimatedPhotoController.deletePhoto(request);

        // Assert the status and response
        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertTrue(response.getBody().getResult().getRespMensaje().contains("Service failed"));
    }

}
