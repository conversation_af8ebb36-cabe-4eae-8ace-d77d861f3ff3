package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class UserListOutput {

    @JsonProperty("response_status")
    private Integer responseStatus;

    @JsonProperty("response_message")
    private String responseMessage;

    @JsonProperty("total_records")
    private Long totalRecords;

    @JsonProperty("users")
    private List<UserData> users;

    @Data
    public static class UserData {

        @JsonProperty("user_id")
        private Integer userId;

        @JsonProperty("alias")
        private String alias;

        @JsonProperty("email")
        private String email;

        @JsonProperty("days_since_password_change")
        private Long daysSincePasswordChange;

        @JsonProperty("names")
        private String names;

        @JsonProperty("father_last_name")
        private String fatherLastName;

        @JsonProperty("mother_last_name")
        private String motherLastName;

        @JsonProperty("company_id")
        private Integer companyId;

        @JsonProperty("company_document")
        private String companyDocument;

        @JsonProperty("company_legal_name")
        private String companyLegalName;

        @JsonProperty("status")
        private Character status;

        @JsonProperty("attached_photo_id")
        private Integer attachedPhotoId;

        @JsonProperty("attached_photo_alias")
        private String attachedPhotoAlias;

        @JsonProperty("roles")
        private List<RoleData> roles;

        @JsonProperty("companies")
        private List<CompanyData> companies;

        @JsonProperty("person_id")
        private Integer personId;
    }

    @Data
    public static class RoleData {
        @JsonProperty("role_id")
        private Integer roleId;

        @JsonProperty("name")
        private String name;
    }

    @Data
    public static class CompanyData {
        @JsonProperty("company_id")
        private Integer companyId;

        @JsonProperty("document")
        private String document;

        @JsonProperty("legal_name")
        private String legalName;
    }
}
