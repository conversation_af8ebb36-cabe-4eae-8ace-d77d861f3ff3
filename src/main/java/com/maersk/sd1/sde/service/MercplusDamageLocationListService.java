package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.dto.MercplusDamageLocationListItemDto;
import com.maersk.sd1.sde.dto.MercplusDamageLocationListOutputDto;
import com.maersk.sd1.common.repository.CatalogRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MercplusDamageLocationListService {

    private static final Logger logger = LogManager.getLogger(MercplusDamageLocationListService.class);

    private final CatalogRepository catalogRepository;

    @Autowired
    public MercplusDamageLocationListService(CatalogRepository catalogRepository) {
        this.catalogRepository = catalogRepository;
    }

    public MercplusDamageLocationListOutputDto getDamageLocations(String typeContainer) {
        MercplusDamageLocationListOutputDto outputDto = new MercplusDamageLocationListOutputDto();
        try {
            List<MercplusDamageLocationListItemDto> itemList = catalogRepository.findDamageLocationsList(typeContainer);
            outputDto.setDamageLocations(itemList);
        } catch (Exception e) {
            logger.error("Error in getDamageLocations", e);
        }
        return outputDto;
    }
}

