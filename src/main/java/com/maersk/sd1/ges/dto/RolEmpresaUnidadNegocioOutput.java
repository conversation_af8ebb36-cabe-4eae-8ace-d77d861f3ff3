package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class RolEmpresaUnidadNegocioOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("roles")
    private List<RolEmpresaUnidadNegocioItem> roles;

    @Data
    public static class RolEmpresaUnidadNegocioItem {

        @JsonProperty("catalog_id")
        private Integer catalogId;

        @JsonProperty("translated_description")
        private String translatedDescription;

        @JsonProperty("unidad_negocio_id")
        private Integer unidadNegocioId;
    }
}

