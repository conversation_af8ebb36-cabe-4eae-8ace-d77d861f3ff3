package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.DeleteEstimatedPhotoInput;
import com.maersk.sd1.sde.dto.DeleteEstimatedPhotoOutput;
import com.maersk.sd1.sde.service.DeleteEstimatedPhotoService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEEMRServiceImp")
public class DeleteEstimatedPhotoController {

    private static final Logger logger = LogManager.getLogger(DeleteEstimatedPhotoController.class);

    private final DeleteEstimatedPhotoService deleteEstimatedPhotoService;

    @PostMapping("/sdeeliminarFotoEstimado")
    public ResponseEntity<ResponseController<DeleteEstimatedPhotoOutput>> deletePhoto(
            @RequestBody @Valid DeleteEstimatedPhotoInput.Root request) {
        try {
            logger.info("Request received for deletePhoto: {}", request);

            Integer attatchmentId = request.getPrefix().getInput().getAdjuntoId();
            DeleteEstimatedPhotoOutput output = deleteEstimatedPhotoService.deleteAttatchment(attatchmentId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while deleting the photo.", e);
            DeleteEstimatedPhotoOutput output = new DeleteEstimatedPhotoOutput();
            output.setRespMensaje(e.toString());
            output.setRespEstado(0);
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

