package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class InspectionMachineryOutput {

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<Status> statuses;
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<EirDataDTO> eirData;
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<NextZoneDTO> nextZonesWithoutDamages;
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<NextZoneDTO> nextZonesWithDamages;
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<CheckListDTO> checkList;
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<DamageEstimateDetailsDTO> damages;
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<EmrPhotoDTO> emrPhotos;
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<DamagePhotoDTO> damagePhotos;

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Status {
        private String statusCode;
        private String message;
    }

}