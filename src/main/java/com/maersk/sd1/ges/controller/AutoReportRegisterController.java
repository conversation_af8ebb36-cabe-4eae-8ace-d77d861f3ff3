package com.maersk.sd1.ges.controller;

import com.maersk.sd1.ges.dto.AutoReportRegisterInput;
import com.maersk.sd1.ges.dto.AutoReportRegisterOutput;
import com.maersk.sd1.ges.service.AutoReportRegisterService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMAutoreportServiceImp")
public class AutoReportRegisterController {

    private static final Logger logger = LogManager.getLogger(AutoReportRegisterController.class);

    private final AutoReportRegisterService autoReportRegisterService;

    @PostMapping("/gesautoreportRegister")
    public ResponseEntity<ResponseController<AutoReportRegisterOutput>> sdgAutoReportRegister(@RequestBody @Valid AutoReportRegisterInput.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                AutoReportRegisterOutput errorOutput = new AutoReportRegisterOutput();
                errorOutput.setRespMensaje("Invalid request");
                errorOutput.setRespEstado(0);
                return ResponseEntity.badRequest().body(new ResponseController<>(errorOutput));
            }

            logger.info("Request received sdgAutoReportRegister: {}", request);
            AutoReportRegisterInput.Input input = request.getPrefix().getInput();

            AutoReportRegisterOutput output = autoReportRegisterService.registerAutoReport(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            AutoReportRegisterOutput errorOutput = new AutoReportRegisterOutput();
            errorOutput.setRespMensaje(e.toString());
            errorOutput.setRespEstado(0);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(errorOutput));
        }
    }
}