package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.UpdateVesselProgrammingOperationInput;
import com.maersk.sd1.sds.dto.UpdateVesselProgrammingOperationOutput;
import com.maersk.sd1.sds.service.UpdateVesselProgrammingOperationService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProgramacionNaveServiceImp")
public class UpdateVesselProgrammingOperationController {

    private static final Logger logger = LogManager.getLogger(UpdateVesselProgrammingOperationController.class.getName());

    private final UpdateVesselProgrammingOperationService updateVesselProgrammingOperationService;

    @Autowired
    public UpdateVesselProgrammingOperationController(UpdateVesselProgrammingOperationService updateVesselProgrammingOperationService)
    {
        this.updateVesselProgrammingOperationService = updateVesselProgrammingOperationService;
    }

    @PostMapping("/sdsactualizarProgramacionNaveOperacion")
    public ResponseEntity<ResponseController<UpdateVesselProgrammingOperationOutput>> update(@RequestBody @Valid UpdateVesselProgrammingOperationInput.Root request) {
        UpdateVesselProgrammingOperationOutput output = new UpdateVesselProgrammingOperationOutput();
        try {
            UpdateVesselProgrammingOperationInput.Input input = request.getPrefix().getInput();

            output = updateVesselProgrammingOperationService.updateVesselProgrammingOperation(input);

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the updateVesselProgrammingOperation request.", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }
}

