package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.DepotEditInput;
import com.maersk.sd1.sds.dto.DepotEditOutput;
import com.maersk.sd1.sds.service.DepotEditService;
import jakarta.validation.Valid;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSDepositoServiceImp")
@Log4j2
public class DepotEditController {

    private final DepotEditService depotEditService;

    @Autowired
    public DepotEditController(DepotEditService depotEditService) {
        this.depotEditService = depotEditService;
    }

    @PostMapping("/sdsdepositoEditar")
    public ResponseEntity<ResponseController<DepotEditOutput>> editDepot(@Valid @RequestBody DepotEditInput.Root request) {
        try {

            if(request.getPrefix() == null || request.getPrefix().getInput() == null) {
                DepotEditOutput output = new DepotEditOutput();
                output.setRespEstado(0);
                output.setRespMensaje("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>(output));
            }

            DepotEditInput.Input input = request.getPrefix().getInput();
            DepotEditOutput output = depotEditService.editDepot(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            log.error("An error occurred while processing the request.", e);
            DepotEditOutput output = new DepotEditOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(output));
        }
    }
}
