package com.maersk.sd1.sde.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public interface EstimateDetailsInterface {

    Integer getEstimateId();

    Integer getWo();

    Long getEstimateTypeId();

    String getEstimateType();

    Integer getEirNumber();

    LocalDateTime getGateInDate();

    String getShippingLine();

    String getContainer();

    String getContainerSize();

    String getContainerType();

    String getVessel();

    String getVoyage();

    LocalDateTime getApprovalDate();

    Integer getUserApproveId();

    String getUserApproveName();

    String getUserApproveLastname();

    Long getRepairBoxStatusId();

    String getRepairBoxStatus();

    Long getRepairMachineryStatusId();

    String getRepairMachineryStatus();

    Long getStatusEstimateId();

    String getStatusEstimate();

    Boolean getEstimateIsAutoApproval();

    Long getCurrentZoneId();

    String getCurrentZone();

    Long getNextZoneId();

    String getNextZone();

    String getYardCurrentLocation();

    Long getCleaningStatusId();

    String getCleaningStatus();

    String getTemp();

    String getLastMovementHistory();

    Integer getDwellTime();

    BigDecimal getTotalManHours();
}