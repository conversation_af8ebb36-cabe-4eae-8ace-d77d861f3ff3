package com.maersk.sd1.common.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CancellationFilterDTO {
    private Integer businessUnitId;
    private Integer cancellationType;
    private String vesselName;
    private String voyage;
    private Integer id;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private String bookingNumber;
    private Integer languageId;

}