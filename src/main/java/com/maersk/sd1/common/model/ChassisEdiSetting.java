package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "chassis_edi_setting", schema = "sdh")
public class ChassisEdiSetting {
    @Id
    @Column(name = "chassis_edi_setting_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "business_unit_id", nullable = false)
    private BusinessUnit businessUnit;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sub_business_unit_id", nullable = false)
    private BusinessUnit subBusinessUnit;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "chassis_owner_company_id", nullable = false)
    private Company chassisOwnerCompany;

    @Size(max = 50)
    @NotNull
    @Column(name = "delivery_system_name", nullable = false, length = 50)
    private String deliverySystemName;

    @Size(max = 200)
    @Column(name = "delivery_system_information", length = 200)
    private String deliverySystemInformation;

    @NotNull
    @Column(name = "chassis_edi_send_gate_in", nullable = false)
    private Boolean chassisEdiSendGateIn = false;

    @NotNull
    @Column(name = "chassis_edi_send_gate_out", nullable = false)
    private Boolean chassisEdiSendGateOut = false;

    @NotNull
    @Column(name = "chassis_edi_send_mount_tie", nullable = false)
    private Boolean chassisEdiSendMountTie = false;

    @NotNull
    @Column(name = "chassis_edi_send_dismount_tie", nullable = false)
    private Boolean chassisEdiSendDismountTie = false;

    @NotNull
    @Column(name = "chassis_edi_send_bad_order", nullable = false)
    private Boolean chassisEdiSendBadOrder = false;

    @NotNull
    @Column(name = "chassis_edi_send_good_order", nullable = false)
    private Boolean chassisEdiSendGoodOrder = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_chassis_edi_message_type_id", nullable = false)
    private Catalog catChassisEdiMessageType;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_chassis_edi_send_mode_id", nullable = false)
    private Catalog catChassisEdiSendMode;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_chassis_edi_time_zone_id", nullable = false)
    private Catalog catChassisEdiTimeZone;

    @Size(max = 15)
    @NotNull
    @Column(name = "chassis_edi_receiver_id_value", nullable = false, length = 15)
    private String chassisEdiReceiverIdValue;

    @Size(max = 100)
    @Column(name = "chassis_edi_file_extension", length = 100)
    private String chassisEdiFileExtension;

    @NotNull
    @Column(name = "minutes_elapsed_to_send", nullable = false)
    private Integer minutesElapsedToSend;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "azure_storage_chassis_edi_id")
    private AzureStorageConfig azureStorageChassisEdi;

    @Size(max = 200)
    @Column(name = "chassis_edi_receiver_email", length = 200)
    private String chassisEdiReceiverEmail;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "sftp_chassis_edi_id")
    private SftpConfig sftpChassisEdi;

    @NotNull
    @Column(name = "is_historical", nullable = false)
    private Boolean isHistorical = false;

    @Column(name = "cancellation_date")
    private LocalDateTime cancellationDate;

    @Size(max = 200)
    @Column(name = "cancellation_reason", length = 200)
    private String cancellationReason;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

}