package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

@Data
public class EstimadoTempDTO {

    @JsonProperty("estimado_emr_id")
    private Integer estimadoEmrId;

    @JsonProperty("merc_plus_estimate_number")
    private Integer mercPlusEstimateNumber;

    @JsonProperty("cat_tipo_estimado_id")
    private Long catTipoEstimadoId;

    @JsonProperty("eir_id")
    private Integer eirId;

    @JsonProperty("gate_in_date")
    private LocalDateTime gateInDate;

    @JsonProperty("linea_naviera_id")
    private Integer lineaNavieraId;

    @JsonProperty("contenedor_id")
    private Integer contenedorId;

    @JsonProperty("numero_contenedor")
    private String numeroContenedor;

    @JsonProperty("cat_tamano_id")
    private Long catTamanoId;

    @JsonProperty("cat_tipo_contenedor_id")
    private Long catTipoContenedorId;

    @JsonProperty("nave_id")
    private Integer naveId;

    @JsonProperty("viaje")
    private String viaje;

    @JsonProperty("approval_date")
    private LocalDateTime approvalDate;

    @JsonProperty("usuario_aprueba_rechaza_id")
    private Long usuarioApruebaRechazaId;

    @JsonProperty("cat_repair_box_status_id")
    private Long catRepairBoxStatusId;

    @JsonProperty("cat_repair_machinery_status_id")
    private Long catRepairMachineryStatusId;

    @JsonProperty("cat_estado_estimado_id")
    private Long catEstadoEstimadoId;

    @JsonProperty("flag_auto_approval")
    private Boolean flagAutoApproval;

    @JsonProperty("cat_cleaning_status_id")
    private Long catCleaningStatusId;

    @JsonProperty("estimado_total_horas")
    private BigDecimal estimadoTotalHoras;
}