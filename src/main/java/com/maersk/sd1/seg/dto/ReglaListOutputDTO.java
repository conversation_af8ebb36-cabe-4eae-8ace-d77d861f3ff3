package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

public class ReglaListOutputDTO {

    @Data
    public static class ListResult {

        @JsonProperty("regla_id")
        private Integer reglaId;

        @JsonProperty("parametros")
        private String parametros;

        @JsonProperty("id")
        private String id;
    }

    @Data
    public static class Output {
        @JsonProperty("listResult")
        private List<ListResult> listResult;
    }
}
