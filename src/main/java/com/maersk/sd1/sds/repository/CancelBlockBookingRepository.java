package com.maersk.sd1.sds.repository;

import com.maersk.sd1.common.model.Booking;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.sds.dto.BookingCancellationBlockingItemDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;
import java.util.List;

public interface CancelBlockBookingRepository extends JpaRepository<Booking, Integer> {

    @Query("SELECT c FROM Catalog c WHERE c.alias = :alias")
    Optional<Catalog> findCatalogByAlias(@Param("alias") String alias);

    @Query("SELECT b FROM Booking b " +
            "WHERE b.subBusinessUnit.id = :subBusinessUnitId " +
            "  AND b.bookingNumber = :bookingNumber " +
            "  AND b.catBookingStatus.id = :vigenteCatalogId " +
            "  AND b.active = true " +
            "  AND b.approvedBooking = false")
    Optional<Booking> findBookingToApprove(@Param("subBusinessUnitId") Long subBusinessUnitId,
                                           @Param("bookingNumber") String bookingNumber,
                                           @Param("vigenteCatalogId") Integer vigenteCatalogId);


    @Query("SELECT DISTINCT new com.maersk.sd1.sds.dto.BookingCancellationBlockingItemDTO( " +
            " cd.id, " +
            " b.bookingNumber, " +
            " CONCAT(v.ship, ' / ', vp.voyage), " +
            " CONCAT(catOp.description, ' - ', catOp.longDescription), " +
            " b.bookingIssueDate ) " +
            "FROM Booking b " +
            "JOIN BookingDetail bd ON bd.booking = b " +
            "JOIN CargoDocumentDetail cdd ON cdd.bookingDetail = bd " +
            "JOIN cdd.cargoDocument cd " +
            "JOIN b.vesselProgrammingDetail vpd " +
            "JOIN vpd.vesselProgramming vp " +
            "JOIN vp.vessel v " +
            "JOIN vpd.catOperation catOp " +
            "WHERE b.bookingNumber = :bookingNumber " +
            "  AND b.subBusinessUnit.id = :subBusinessUnitId " +
            "  AND b.catBookingStatus.id = :catEstadoBooking " +
            "  AND b.approvedBooking = true " +
            "  AND catOp.variable1 = 'E' " +
            "  AND b.active = true " +
            "  AND cdd.active = true " +
            "  AND vpd.active = true " +
            "  AND vp.active = true " +
            "  AND cd.active = true " +
            "GROUP BY cd.id, b.bookingNumber, v.ship, vp.voyage, catOp.description, catOp.longDescription, b.bookingIssueDate " +
            "ORDER BY b.bookingIssueDate DESC")
    List<BookingCancellationBlockingItemDTO> findBookingCancellationBlockingResults(@Param("bookingNumber") String bookingNumber,
                                                                                    @Param("subBusinessUnitId") Long subBusinessUnitId,
                                                                                    @Param("catEstadoBooking") Integer catEstadoBooking);
}