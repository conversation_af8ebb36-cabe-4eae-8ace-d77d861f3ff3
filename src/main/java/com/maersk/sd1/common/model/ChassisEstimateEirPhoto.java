package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Attachment;
import com.maersk.sd1.common.model.ChassisEstimate;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "chassis_estimate_eir_photo", schema = "sdh")
public class ChassisEstimateEirPhoto {
    @Id
    @Column(name = "chassis_estimate_eir_photo_id", nullable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chassis_estimate_id")
    private ChassisEstimate chassisEstimate;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "eir_chassis_id", nullable = false)
    private EirChassis eirChassis;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "adjunto_id", nullable = false)
    private Attachment attachment;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

}