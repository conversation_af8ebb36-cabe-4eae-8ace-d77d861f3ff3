package com.maersk.sd1.sde.repository;

import com.maersk.sd1.common.model.EirActivityZone;
import com.maersk.sd1.common.model.EirZone;
import com.maersk.sd1.common.repository.EirActivityZoneRepository;
import com.maersk.sd1.sde.dto.listactivityzone.ListActivityZoneCntDTO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SdeEirActivityZoneRepository extends EirActivityZoneRepository {

    @Query("SELECT new com.maersk.sd1.sde.dto.listactivityzone.ListActivityZoneCntDTO(" +
            "null," +
            "e.subBusinessUnit.id," +
            "e.localSubBusinessUnit.id," +
            "e.truckArrivalDate," +
            "e.catMovement.id," +
            "e.catOrigin.id," +
            "ea.eir.id," +
            "c.shippingLine.id," +
            "c.containerNumber," +
            "c.catContainerType.id," +
            "c.catSize.id," +
            "c.isoCode.id," +
            "c.catGrade.id," +
            "c.containerTare," +
            "c.catReeferType.id," +
            "az.description," +
            "null," +
            "ea.startDate," +
            "ea.id," +
            "null," +
            "e.structureWithDamage," +
            "e.machineryWithDamage," +
            "az.longDescription," +
            "null )" +
            "FROM EirActivityZone ea " +
            "inner join Eir e on e.id = ea.eir.id " +
            "inner join Container c on e.container.id = c.id " +
            "inner join Catalog az on az.id = ea.catZoneActivity.id " +
            "WHERE ea.eir.id IN :eirIds " +
            "AND e.active = true " +
            "and ea.concluded = :concluded ")
    List<ListActivityZoneCntDTO> findByEirIdsAndConcluded(@Param("eirIds") List<Integer> eirIds, @Param("concluded") Boolean concluded);


    @Query("SELECT new com.maersk.sd1.sde.dto.listactivityzone.ListActivityZoneCntDTO(" +
            "null," +
            "e.subBusinessUnit.id," +
            "e.localSubBusinessUnit.id," +
            "e.truckArrivalDate," +
            "e.catMovement.id," +
            "e.catOrigin.id," +
            "ea.eir.id," +
            "c.shippingLine.id," +
            "c.containerNumber," +
            "c.catContainerType.id," +
            "c.catSize.id," +
            "c.isoCode.id," +
            "c.catGrade.id," +
            "c.containerTare," +
            "c.catReeferType.id," +
            "null," +
            "az.description," +
            "ea.registrationDate," +
            "null," +
            "ea.id," +
            "e.structureWithDamage," +
            "e.machineryWithDamage," +
            "null," +
            "az.longDescription )" +
            "FROM EirZone ea " +
            "inner join Eir e on e.id = ea.eir.id " +
            "inner join Container c on e.container.id = c.id " +
            "inner join Catalog az on az.id = ea.catContainerZone.id " +
            "WHERE ea.eir.id IN :eirIds " +
            "and ea.active = true " +
            "order by ea.registrationDate desc")
    List<ListActivityZoneCntDTO> findActivityZoneDtoByEirIds(@Param("eirIds") List<Integer> eirIds);

    @Query("SELECT ea " +
            "FROM EirActivityZone ea " +
            "WHERE ea.eir.id IN :eirIds " +
            "and ea.active = true " +
            "and ea.catZoneActivity.id = :catActivityZoneId")
    List<EirActivityZone> findByEirIds(@Param("eirIds") List<Integer> eirIds, @Param("catActivityZoneId") Integer catActivityZoneId);

    @Query("SELECT ea " +
            "FROM EirZone ea " +
            "WHERE ea.eir.id IN :eirIds " +
            "and ea.active = true " +
            "and ea.catContainerZone.id = :catContainerZoneId")
    List<EirZone> findEirZonesByEirIds(@Param("eirIds") List<Integer> eirIds, @Param("catContainerZoneId") Integer catContainerZoneId);
}
