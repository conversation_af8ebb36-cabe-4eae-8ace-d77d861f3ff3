package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.ListActivityZoneCntInput;
import com.maersk.sd1.sde.controller.dto.ListActivityZoneCntOutput;
import com.maersk.sd1.sde.service.ListActivityZoneCntService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEActividadZonaServiceImp")
public class ListActivityZoneCntController {

    private static final Logger logger = LogManager.getLogger(ListActivityZoneCntController.class);

    private final ListActivityZoneCntService listActivityZoneCntService;

    public ListActivityZoneCntController(ListActivityZoneCntService listActivityZoneCntService) {
        this.listActivityZoneCntService = listActivityZoneCntService;
    }

    @PostMapping("/sdelistarActividadZonaCnt")
    public ResponseEntity<ResponseController<ListActivityZoneCntOutput>> search(@RequestBody @Valid ListActivityZoneCntInput.Root request) {
        try {
            ListActivityZoneCntOutput output = listActivityZoneCntService.listActivityZoneCnt(request.getPrefix().getInput());
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while list next zones for containers.", e);
            ListActivityZoneCntOutput output = new ListActivityZoneCntOutput();
            ListActivityZoneCntOutput.Response response = new ListActivityZoneCntOutput.Response();
            response.setResponseStatus(0);
            response.setResponseMessage("");
            output.setResponse(Collections.singletonList(response));
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}