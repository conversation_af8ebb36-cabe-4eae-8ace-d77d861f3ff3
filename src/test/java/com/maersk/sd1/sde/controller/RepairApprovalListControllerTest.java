package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.RepairApprovalListInput;
import com.maersk.sd1.sde.dto.RepairApprovalListOutput;
import com.maersk.sd1.sde.service.RepairApprovalListService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RepairApprovalListControllerTest {

    @Mock
    private RepairApprovalListService repairApprovalListService;

    @InjectMocks
    private RepairApprovalListController repairApprovalListController;

    private RepairApprovalListInput.Root request;
    private RepairApprovalListOutput output;

    @BeforeEach
    void setUp() {
        // Initialize request
        RepairApprovalListInput.Input input = new RepairApprovalListInput.Input();
        input.setLanguageId(1);
        // Set other required fields as needed

        RepairApprovalListInput.Prefix prefix = new RepairApprovalListInput.Prefix();
        prefix.setInput(input);

        request = new RepairApprovalListInput.Root();
        request.setPrefix(prefix);

        // Initialize expected output
        output = new RepairApprovalListOutput();
        output.setResultado(List.of(List.of(5L)));
        // Set other output fields as needed
    }

    @Test
    void testListRepairApprovals_Success() {
        // Arrange
        when(repairApprovalListService.listRepairApprovals(any(RepairApprovalListInput.Input.class)))
                .thenReturn(output);

        // Act
        ResponseEntity<ResponseController<RepairApprovalListOutput>> response =
                repairApprovalListController.listRepairApprovals(request);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(output, response.getBody().getResult());
        verify(repairApprovalListService, times(1)).listRepairApprovals(any(RepairApprovalListInput.Input.class));
    }

    @Test
    void testListRepairApprovals_NullRequest() {
        // Act
        ResponseEntity<ResponseController<RepairApprovalListOutput>> response =
                repairApprovalListController.listRepairApprovals(null);

        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    void testListRepairApprovals_NullPrefix() {
        // Arrange
        request.setPrefix(null);

        // Act
        ResponseEntity<ResponseController<RepairApprovalListOutput>> response =
                repairApprovalListController.listRepairApprovals(request);

        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    void testListRepairApprovals_ServiceException() {
        // Arrange
        when(repairApprovalListService.listRepairApprovals(any(RepairApprovalListInput.Input.class)))
                .thenThrow(new RuntimeException("Service error"));

        // Act
        ResponseEntity<ResponseController<RepairApprovalListOutput>> response =
                repairApprovalListController.listRepairApprovals(request);

        // Assert
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
    }
}