package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.BookingBlockCancellationDetailRepository;
import com.maersk.sd1.common.repository.BookingRepository;
import com.maersk.sd1.common.repository.CargoDocumentRepository;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sde.dto.BookingBlockCancellationReleaseOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.Mockito.*;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class BookingBlockCancellationReleaseServiceTest {

    @Mock
    private BookingBlockCancellationDetailRepository bookingBlockCancellationDetailRepository;

    @Mock
    private BookingRepository bookingRepository;

    @Mock
    private CargoDocumentRepository cargoDocumentRepository;

    @Mock
    private MessageLanguageService messageLanguageService;

    @InjectMocks
    private BookingBlockCancellationReleaseService bookingBlockCancellationReleaseService;

    private Integer cancelBlockBookingId;
    private String comment;
    private Integer reasonCategory;
    private Integer userId;
    private Integer languageId;

    @BeforeEach
    void setUp() {
        cancelBlockBookingId = 1;
        comment = "Test Comment";
        reasonCategory = 2;
        userId = 123;
        languageId = 1;
    }

    @Test
    void Given_ActiveBookingAndCargoDocument_When_ReleaseBookingIsCalled_Then_BookingAndCargoDocumentAreUpdated() {
        when(bookingBlockCancellationDetailRepository.findFirstDetailWithActiveBooking(cancelBlockBookingId))
                .thenReturn(List.of(1, 1)); // Simulate found booking and cargo document IDs
        when(bookingBlockCancellationDetailRepository.updateBookingBlockCancellationDetail(cancelBlockBookingId, comment, reasonCategory, userId))
                .thenReturn(1);
        when(bookingRepository.updateBookingStatusToActive(1, userId)).thenReturn(1);
        when(cargoDocumentRepository.updateCargoDocumentStatusToActive(1, userId)).thenReturn(1);
        when(messageLanguageService.getMessage("CNC_BLQ_BK", 2, languageId)).thenReturn("Success");

        BookingBlockCancellationReleaseOutput output = bookingBlockCancellationReleaseService.releaseBooking(
                cancelBlockBookingId, comment, reasonCategory, userId, languageId);

        assertEquals(1, output.getResponseStatus());
        assertEquals("Success", output.getResponseMessage());

        verify(bookingBlockCancellationDetailRepository).findFirstDetailWithActiveBooking(cancelBlockBookingId);
        verify(bookingBlockCancellationDetailRepository).updateBookingBlockCancellationDetail(cancelBlockBookingId, comment, reasonCategory, userId);
        verify(bookingRepository).updateBookingStatusToActive(1, userId);
        verify(cargoDocumentRepository).updateCargoDocumentStatusToActive(1, userId);
        verify(messageLanguageService).getMessage("CNC_BLQ_BK", 2, languageId);
    }

    @Test
    void Given_NoActiveBooking_When_ReleaseBookingIsCalled_Then_BookingAndCargoDocumentAreNotUpdated() {

        when(bookingBlockCancellationDetailRepository.findFirstDetailWithActiveBooking(cancelBlockBookingId))
                .thenReturn(List.of());
        when(bookingBlockCancellationDetailRepository.updateBookingBlockCancellationDetail(cancelBlockBookingId, comment, reasonCategory, userId))
                .thenReturn(1);
        when(messageLanguageService.getMessage("CNC_BLQ_BK", 2, languageId)).thenReturn("Success");

        BookingBlockCancellationReleaseOutput output = bookingBlockCancellationReleaseService.releaseBooking(
                cancelBlockBookingId, comment, reasonCategory, userId, languageId);

        assertEquals(1, output.getResponseStatus());
        assertEquals("Success", output.getResponseMessage());

        verify(bookingRepository, times(0)).updateBookingStatusToActive(anyInt(), anyInt());
        verify(cargoDocumentRepository, times(0)).updateCargoDocumentStatusToActive(anyInt(), anyInt());
    }

    @Test
    void Given_ExceptionDuringReleaseBooking_When_ReleaseBookingIsCalled_Then_ErrorMessageIsReturned() {
        when(bookingBlockCancellationDetailRepository.findFirstDetailWithActiveBooking(cancelBlockBookingId))
                .thenThrow(new RuntimeException("Database error"));

        BookingBlockCancellationReleaseOutput output = bookingBlockCancellationReleaseService.releaseBooking(
                cancelBlockBookingId, comment, reasonCategory, userId, languageId);

        assertEquals(0, output.getResponseStatus());
        assertEquals("Database error", output.getResponseMessage());
    }
}
