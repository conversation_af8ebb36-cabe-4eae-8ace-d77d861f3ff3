package com.maersk.sd1.ges.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.ges.dto.ConsultorReportInputDto;
import com.maersk.sd1.ges.dto.ConsultorReportInputDto.Parametro;
import com.maersk.sd1.ges.dto.ConsultorReportOutputDto;
import com.maersk.sd1.ges.expection.CompanyRegistrationException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.SqlParameter;
import org.springframework.stereotype.Service;

import java.sql.CallableStatement;
import java.sql.Types;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ConsultorReportService {

    private final JdbcTemplate jdbcTemplate;

    public ConsultorReportService(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public ConsultorReportOutputDto executeReport(ConsultorReportInputDto.Root request) {
        ConsultorReportInputDto.Input inputDto = request.getPrefix().getInput();
        String procedureName = inputDto.getNombreStore();
        List<Parametro> parametros = inputDto.getParametros();

        Map<String, Object> paramMap = new HashMap<>();
        List<SqlParameter> sqlParameters = new ArrayList<>();

        for (Parametro param : parametros) {
            String paramName = param.getParameter();
            String paramType = param.getTipo();
            Object paramValue = parseType(paramType);

            paramMap.put(paramName, paramValue);
            sqlParameters.add(new SqlParameter(paramName, getSqlType(paramType)));
        }

        paramMap.put("pagina", inputDto.getPage());
        paramMap.put("cantidad", inputDto.getSize());
        sqlParameters.add(new SqlParameter("pagina", Types.INTEGER));
        sqlParameters.add(new SqlParameter("cantidad", Types.INTEGER));
        paramMap.put("nombre_store", procedureName);
        sqlParameters.add(new SqlParameter("nombre_store", Types.VARCHAR));

        String serializedParametros = serializeParametros(parametros);
        paramMap.put("parametros", serializedParametros);
        sqlParameters.add(new SqlParameter("parametros", Types.VARCHAR));

        Map<String, Object> result = jdbcTemplate.call(con -> {
            StringBuilder callString = new StringBuilder("{call " + procedureName + "(");
            for (int i = 0; i < sqlParameters.size(); i++) {
                if (i > 0) {
                    callString.append(", ");
                }
                callString.append("?");
            }
            callString.append(")}");

            CallableStatement cs = con.prepareCall(callString.toString());
            int index = 1;
            for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                cs.setObject(index++, entry.getValue());
            }
            return cs;
        }, sqlParameters);

        List<Map<String, Object>> objetoConsulta = (List<Map<String, Object>>) result.get("#result-set-1");
        Map<String, Object> objetoTitulo = (Map<String, Object>) result.get("#result-set-2");
        int total = (int) result.get("total");

        return new ConsultorReportOutputDto(objetoConsulta, objetoTitulo, total);
    }

    public Object parseType(String tipoDesc) {
        return switch (tipoDesc.toUpperCase()) {
            case "STRING" -> "";
            case "INTEGER" -> 0;
            case "CHARACTER" -> ' ';
            case "DATE" -> new java.sql.Date(System.currentTimeMillis());
            default -> null;
        };
    }

    public int getSqlType(String tipoDesc) {
        return switch (tipoDesc.toUpperCase()) {
            case "STRING" -> Types.VARCHAR;
            case "INTEGER" -> Types.INTEGER;
            case "CHARACTER" -> Types.CHAR;
            case "DATE" -> Types.DATE;
            default -> Types.VARCHAR;
        };
    }
    public String serializeParametros(List<Parametro> parametros) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.writeValueAsString(parametros);
        } catch (JsonProcessingException ex) {
            throw new CompanyRegistrationException("Error registering Company: " + ex.getMessage(), ex);
        }
    }
}