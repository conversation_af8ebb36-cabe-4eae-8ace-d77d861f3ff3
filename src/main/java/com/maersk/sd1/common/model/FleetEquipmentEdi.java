package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.FleetEquipmentEdiSetting;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "fleet_equipment_edi", schema = "sds")
public class FleetEquipmentEdi {
    @Id
    @Column(name = "fleet_equipment_edi_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "fleet_equipment_edi_setting_id", nullable = false)
    private FleetEquipmentEdiSetting fleetEquipmentEdiSetting;

    @Size(max = 200)
    @Column(name = "fleet_equipment_edi_file_name", length = 200)
    private String fleetEquipmentEdiFileName;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_fleet_equip_edi_state_id", nullable = false)
    private Catalog catFleetEquipEdiState;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shipping_line_id")
    private ShippingLine shippingLine;

    @Size(max = 250)
    @Column(name = "fleet_equipment_edi_comment_processed", length = 250)
    private String fleetEquipmentEdiCommentProcessed;

    @Size(max = 250)
    @Column(name = "fleet_equipment_edi_comment_toprocess", length = 250)
    private String fleetEquipmentEdiCommentToprocess;

    @Column(name = "fleet_equipment_edi_process_date")
    private LocalDateTime fleetEquipmentEdiProcessDate;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

}