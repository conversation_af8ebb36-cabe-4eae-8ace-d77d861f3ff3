msk.sdg.api.url.appointmentSearchEquipment=https://appointmentprep.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apsappointmentSearchByEquipment
msk.sdg.api.url.appointmentSearchDocument=https://appointmentprep.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apsappointmentSearchByDocument
msk.sdg.api.url.attendAppointment=https://appointmentprep.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apscitaAtender
msk.api.apiUserLogin.aps=http://appointmentprep.inlandservices.com/api/inlandnet/Inlandnet/security/UserServiceImp/apiUserLoginv2
msk.api.apiUserLogin.aps.user=<EMAIL>
msk.api.apiUserLogin.aps.password=/B6u7i2pKxSeXf/M0UoFXnjdv+OJ4XjEkgG9ExicuP4=
msk.api.apiUserLogin.aps.system=APS
msk.sdg.api.url.comodatoSearchByEquipment=https://tucontenedor360.inlandservices.com/ModuleTCO/Business?method=tcoblsContenedorObtener&package=module.tco&class=TCOConsultaServiceImp
msk.api.apiUserLogin.sdy.loginUrl=https://inlandnet.dev.maersk-digital.net/Inlandnet/security/UserServiceImp/apiUserLogin
msk.api.apiUserLogin.sdy.user=<EMAIL>
msk.api.apiUserLogin.sdy.password=WhWR8BKZCxI2YWOF0rA5YV2xUv+O7gJv5yt8nJ4RlSU=
msk.api.apiUserLogin.sdy.system=9
msk.api.apiUserLogin.sdy.gateInUrl=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyPlannigGateInAssignment
msk.api.apiUserLogin.sdy.gateOutUrl=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyPlanningGateOut
msk.api.apiUserLogin.sdy.searchContainer=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdybuscarContenedoresParaSalida
msk.api.apiUserLogin.sdy.plannigGateInAssignmentAndAprrove=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyPlannigGateInAssignmentAndAprrove
msk.api.apiUserLogin.sdy.afterTruckDepartureCreateWorkOrder=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyAfterTruckDepartureCreateWorkOrder
msk.cat.type_movement.gateout=43081