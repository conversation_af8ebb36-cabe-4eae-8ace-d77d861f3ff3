package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class CedexMercListItemDTO {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("is_reefer")
    private Boolean isReefer;

    @JsonProperty("estimate_type")
    private String estimateType;

    @JsonProperty("component_code")
    private String componentCode;

    @JsonProperty("repair_method_code")
    private String repairMethodCode;

    @JsonProperty("damage_location")
    private String damageLocation;

    @JsonProperty("merc_min_value")
    private Integer mercMinValue;

    @JsonProperty("merc_max_value")
    private Integer mercMaxValue;

    // Dimension string that includes the dimension code plus any unit translations
    @JsonProperty("merc_dimension")
    private String mercDimension;

    @JsonProperty("engine_brand_code")
    private String engineBrandCode;

    @JsonProperty("merc_max_pieces")
    private Integer mercMaxPieces;

    @JsonProperty("merc_hh")
    private Integer mercHh;

    @JsonProperty("merc_material_cost")
    private Integer mercMaterialCost;

    @JsonProperty("cedex_merc_code")
    private String cedexMercCode;

    @JsonProperty("cedex_merc_desc")
    private String cedexMercDesc;

    @JsonProperty("currency_id")
    private Integer currencyId;

    @JsonProperty("currency")
    private String currencyAbbreviation;

    @JsonProperty("merc_obs")
    private String mercObs;

    @JsonProperty("active")
    private Boolean active;

    @JsonProperty("user_registration_id")
    private Integer userRegistrationId;

    @JsonProperty("registration_date")
    private String registrationDate;

    @JsonProperty("user_modification_id")
    private Integer userModificationId;

    @JsonProperty("modification_date")
    private String modificationDate;

    @JsonProperty("user_registration_name")
    private String userRegistrationName;

    @JsonProperty("user_registration_lastname")
    private String userRegistrationLastname;

    @JsonProperty("user_modification_name")
    private String userModificationName;

    @JsonProperty("user_modification_lastname")
    private String userModificationLastname;

    @JsonProperty("repair_group_translation")
    private String repairGroupTranslation;

    @JsonProperty("type_code_group_translation")
    private String typeCodeGroupTranslation;

    @JsonProperty("shipping_line_id")
    private Integer shippingLineId;

    @JsonProperty("shipping_line")
    private String shippingLine;

    @JsonProperty("customer_id")
    private Integer customerId;

    @JsonProperty("customer_name")
    private String customerName;


    @JsonProperty("cat_equipment_category_id")
    private Integer catEquipmentCategoryId;

    @JsonProperty("cat_equipment_category_translation")
    private String catEquipmentCategoryTranslation;
}