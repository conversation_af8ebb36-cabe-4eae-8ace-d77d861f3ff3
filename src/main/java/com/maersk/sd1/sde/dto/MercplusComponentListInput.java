package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class MercplusComponentListInput {

    @Data
    public static class Input {
        // No specific fields required based on the stored procedure logic
        // If needed, add fields here with @NotNull, @Size, etc.
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        @NotNull
        private Prefix prefix;
    }
}