package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sde.controller.dto.MechanicCleaningRequestChecklistSaveInput;
import com.maersk.sd1.sde.controller.dto.MechanicCleaningRequestChecklistSaveOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.domain.PageRequest;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static com.maersk.sd1.common.Parameter.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

class MechanicCleaningRequestChecklistSaveServiceTest {

    @Mock
    private GESCatalogService catalogService;

    @Mock
    private EstimateEmrDetailRepository estimateEmrDetailRepository;

    @Mock
    private AttachmentRepository attachmentRepository;

    @Mock
    private EstimateEmrDetailPhotoRepository estimateEmrDetailPhotoRepository;

    @Mock
    private EirRepository eirRepository;

    @Mock
    private EirZoneRepository eirZoneRepository;

    @Mock
    private EirActivityZoneRepository eirActivityZoneRepository;

    @Mock
    private EstimateEmrRepository estimateEmrRepository;

    @Mock
    private MessageLanguageService messageLanguageService;

    @InjectMocks
    private MechanicCleaningRequestChecklistSaveService mechanicCleaningRequestChecklistSaveService;

    private List<String> catalogsAliases = List.of(
            STATUS_REPAIR_EMPTY_CONTAINER_PENDING,
            STATUS_REPAIR_EMPTY_CONTAINER_REPAIRED,
            STATUS_REPAIR_EMPTY_CONTAINER_REPAIRED_PROGRESS,
            STATUS_CLEANING_EMPTY_CONTAINER_PENDING,
            STATUS_CLEANING_EMPTY_CONTAINER_DIRTY,
            STATUS_CLEANING_EMPTY_CONTAINER_CLEANING_PROGRESS,
            STATUS_CLEANING_EMPTY_CONTAINER_CLEANED,
            ESTIMATE_CREATED_STATUS,
            ESTIMATE_FINALIZED_STATUS,
            ESTIMATE_REJECTED,
            CATALOG_ESTIMATE_BOX_ALIAS,
            CATALOG_ESTIMATE_TYPE_MACHINERY,
            ACTIVITY_ZONE_EMPTY_INSP,
            ACTIVITY_ZONE_EMPTY_PTI,
            ACTIVITY_ZONE_EMPTY_REP,
            ACTIVITY_ZONE_EMPTY_REPM,
            ACTIVITY_ZONE_EMPTY_LAV,
            ACTIVITY_ZONE_EMPTY_DD,
            ACTIVITY_ZONE_EMPTY_DM,
            ACTIVITY_ZONE_EMPTY_OK,
            ACTIVITY_ZONE_CONTAINER_EMPTY_SINP,
            ACTIVITY_ZONE_CONTAINER_EMPTY_PTI,
            ACTIVITY_ZONE_CONTAINER_EMPTY_DD,
            ACTIVITY_ZONE_CONTAINER_EMPTY_DM,
            ACTIVITY_ZONE_CONTAINER_EMPTY_REP,
            ACTIVITY_ZONE_CONTAINER_EMPTY_REPM,
            ACTIVITY_ZONE_CONTAINER_EMPTY_LAV,
            ACTIVITY_ZONE_CONTAINER_EMPTY_OK,
            ACTIVITY_ZONE_CONTAINER_EMPTY_VEN,
            ACTIVITY_ZONE_CONTAINER_EMPTY_DRCH,
            CATALOG_ESTIMATE_STATUS_SUBMITTED,
            CATALOG_ESTIMATE_STATUS_APPROVED,
            CATALOG_ESTIMATE_STATUS_REPAIR_COMPLETED,
            CATALOG_CREATION_SOURCE_REPAIR_CHECKLIST,
            CATALOG_CREATION_SOURCE_CLEANING_CHECKLIST
    );

    private PageRequest firstRecord = PageRequest.of(0, 1);

    private void mockCatalogService() {
        HashMap<String, Integer> catalogIds = new HashMap<>();
        for (String alias : catalogsAliases.stream().distinct().toList()) {
            catalogIds.put(alias, catalogsAliases.indexOf(alias));
        }
        when(catalogService.findIdsByAliases(anyList())).thenReturn(catalogIds);
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenValidInputWithOnlyDamageAndItemsAllItemsBeingChecked_whenMechanicCleaningRequestChecklistSave_thenReturnStatus1() {
        // GIVEN
        mockCatalogService();
        MechanicCleaningRequestChecklistSaveInput.Input input = createInputWithOnlyDamageItemsAllChecked();
        mockRepositoriesForDamageItemsAllChecked();

        // WHEN
        MechanicCleaningRequestChecklistSaveOutput result = mechanicCleaningRequestChecklistSaveService.mechanicCleaningRequestChecklistSave(input);

        // THEN
        assertEquals(1, result.getRespStatus());
        assertEquals("Success", result.getRespMessage());
    }

    private MechanicCleaningRequestChecklistSaveInput.Input createInputWithOnlyDamageItemsAllChecked() {
        return MechanicCleaningRequestChecklistSaveInput.Input.builder()
                .damagesList("[{\"estimate_detail_id\":181040,\"selected\":true,\"mechanicWorkDoneFlag\":true," +
                        "\"damagePhotos\":[{\"id\":\"737ebe15-056a-407c-b5d9-586cffbb0144\",\"nombre\":\"foto_01\",\"formato\":\"jpg\",\"peso\":562619,\"ubicacion\":\"repairs/\",\"tipo_adjunto\":48028,\"accion\":\"N\",\"url\":\"https://maerskapmtisstgsd1prod.blob.core.windows.net/sde/repairs%2F737ebe15-056a-407c-b5d9-586cffbb0144?sv=2020-08-04&ss=bfqt&srt=sco&sp=rwdlacupitfx&se=2026-01-01T04:19:39Z&st=2021-11-17T20:19:39Z&spr=https&sig=ES4aiDG1dqMC8qOXC4rx0gTMyrLSsWpa%2Bh1UzGDo7qY%3D\",\"adjunto_id\":true}]}]")

                .languageId(1)
                .registrationUserId(1)
                .build();
    }

    private void mockRepositoriesForDamageItemsAllChecked() {
        // Define variables for repeated literal values
        int estimateEmrDetailId = 181040;
        int estimateEmrId = 1001;
        int eirId = 2001;
        int attachmentId = 3001;
        String attachmentId1 = "737ebe15-056a-407c-b5d9-586cffbb0144";
        int eirZoneId = 4001;
        int eirActivityZoneId = 5001;
        int lastEirActivityZoneId = 6001;

        int catalogEstimateBoxAliasIndex = catalogsAliases.stream().distinct().toList().indexOf(CATALOG_ESTIMATE_BOX_ALIAS);
        int estimateCreatedStatusIndex = catalogsAliases.stream().distinct().toList().indexOf(ESTIMATE_CREATED_STATUS);
        int estimateFinalizedStatusIndex = catalogsAliases.stream().distinct().toList().indexOf(ESTIMATE_FINALIZED_STATUS);
        int catalogEstimateStatusSubmittedIndex = catalogsAliases.stream().distinct().toList().indexOf(CATALOG_ESTIMATE_STATUS_SUBMITTED);
        int catalogEstimateStatusApprovedIndex = catalogsAliases.stream().distinct().toList().indexOf(CATALOG_ESTIMATE_STATUS_APPROVED);
        int catalogEstimateStatusRepairCompletedIndex = catalogsAliases.stream().distinct().toList().indexOf(CATALOG_ESTIMATE_STATUS_REPAIR_COMPLETED);
        int activityZoneContainerEmptyRepIndex = catalogsAliases.stream().distinct().toList().indexOf(ACTIVITY_ZONE_CONTAINER_EMPTY_REP);
        int activityZoneEmptyRepIndex = catalogsAliases.stream().distinct().toList().indexOf(ACTIVITY_ZONE_EMPTY_REP);
        int statusRepairEmptyContainerRepairedIndex = catalogsAliases.stream().distinct().toList().indexOf(STATUS_REPAIR_EMPTY_CONTAINER_REPAIRED);
        int statusCleaningEmptyContainerCleanedIndex = catalogsAliases.stream().distinct().toList().indexOf(STATUS_CLEANING_EMPTY_CONTAINER_CLEANED);

        // Mock repository calls for all damage items being checked
        when(estimateEmrDetailRepository.findByEstimateIds(anyList())).thenReturn(List.of(
                EstimateEmrDetail.builder()
                        .id(estimateEmrDetailId)
                        .mechanicWorkDoneFlag(false)
                        .estimateEmr(EstimateEmr.builder()
                                .id(estimateEmrId)
                                .eir(Eir.builder().id(eirId).build())
                                .catEstimateType(Catalog.builder().id(catalogEstimateBoxAliasIndex).build())
                                .build())
                        .build()
        ));
        when(attachmentRepository.saveAll(anyList())).thenReturn(List.of(Attachment.builder()
                .id(attachmentId)
                .id1(attachmentId1)
                .build()));
        when(estimateEmrDetailRepository.findAllByEirIdAndEstimateType(eirId, List.of(estimateCreatedStatusIndex, estimateFinalizedStatusIndex, catalogEstimateStatusSubmittedIndex, catalogEstimateStatusApprovedIndex, catalogEstimateStatusRepairCompletedIndex)))
                .thenReturn(List.of(
                        EstimateEmrDetail.builder()
                                .id(estimateEmrDetailId)
                                .mechanicWorkDoneFlag(true)
                                .estimateEmr(EstimateEmr.builder()
                                        .id(estimateEmrId)
                                        .catEstimateType(Catalog.builder()
                                                .id(catalogEstimateBoxAliasIndex)
                                                .build())
                                        .build())
                                .catCleaningType(null)
                                .build()
                ));
        when(eirZoneRepository.findLastByEirIdAndContainerZoneAndActive(firstRecord, eirId, activityZoneContainerEmptyRepIndex, true))
                .thenReturn(Collections.emptyList());
        when(eirZoneRepository.saveAndFlush(any(EirZone.class))).thenReturn(EirZone.builder()
                .id(eirZoneId)
                .build());
        when(eirActivityZoneRepository.findLastByEirIdAndActivityZoneIdAndActive(firstRecord, eirId, activityZoneEmptyRepIndex, true))
                .thenReturn(Collections.emptyList());
        when(eirActivityZoneRepository.save(any(EirActivityZone.class))).thenReturn(EirActivityZone.builder()
                .id(eirActivityZoneId)
                .build());
        when(estimateEmrDetailRepository.existsByEstimateEmrIdAndMechanicWorkDoneFlagAndActive(estimateEmrId, false, true))
                .thenReturn(null);
        when(eirRepository.existsByIdAndCatApprovalBoxAndCatApprovalMachineryAndCleaningStatus(eirId, statusRepairEmptyContainerRepairedIndex, statusRepairEmptyContainerRepairedIndex, statusCleaningEmptyContainerCleanedIndex))
                .thenReturn(true);
        when(eirActivityZoneRepository.findLastByEirIdAndActivity(firstRecord, eirId, true))
                .thenReturn(List.of(EirActivityZone.builder()
                        .id(lastEirActivityZoneId)
                        .build()));
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn("Success");
    }


    @Test
    void givenValidInputWithOnlyCleaningItemsAndAllItemsBeingChecked_whenMechanicCleaningRequestChecklistSave_thenReturnStatus1() {
        // GIVEN
        mockCatalogService();
        MechanicCleaningRequestChecklistSaveInput.Input input = createInputWithOnlyCleaningItemsAllChecked();
        mockRepositoriesForCleaningItemsAllChecked();

        // WHEN
        MechanicCleaningRequestChecklistSaveOutput result = mechanicCleaningRequestChecklistSaveService.mechanicCleaningRequestChecklistSave(input);

        // THEN
        assertEquals(1, result.getRespStatus());
        assertEquals("Success", result.getRespMessage());
    }

    private MechanicCleaningRequestChecklistSaveInput.Input createInputWithOnlyCleaningItemsAllChecked() {
        return MechanicCleaningRequestChecklistSaveInput.Input.builder()
                .damagesList("[{\"estimate_detail_id\":181041,\"selected\":true,\"mechanicWorkDoneFlag\":true,\"catCleaningType\":3001}]")
                .languageId(1)
                .registrationUserId(1)
                .build();
    }

    private void mockRepositoriesForCleaningItemsAllChecked() {
        // Define variables for repeated literal values
        int estimateEmrDetailId = 181041;
        int estimateEmrId = 1002;
        int eirId = 2002;
        int catalogId = 3001;
        int eirZoneId = 4002;
        int eirActivityZoneId = 5002;

        int estimateCreatedStatusIndex = catalogsAliases.stream().distinct().toList().indexOf(ESTIMATE_CREATED_STATUS);
        int estimateFinalizedStatusIndex = catalogsAliases.stream().distinct().toList().indexOf(ESTIMATE_FINALIZED_STATUS);
        int catalogEstimateStatusSubmittedIndex = catalogsAliases.stream().distinct().toList().indexOf(CATALOG_ESTIMATE_STATUS_SUBMITTED);
        int catalogEstimateStatusApprovedIndex = catalogsAliases.stream().distinct().toList().indexOf(CATALOG_ESTIMATE_STATUS_APPROVED);
        int catalogEstimateStatusRepairCompletedIndex = catalogsAliases.stream().distinct().toList().indexOf(CATALOG_ESTIMATE_STATUS_REPAIR_COMPLETED);
        int activityZoneContainerEmptyLavIndex = catalogsAliases.stream().distinct().toList().indexOf(ACTIVITY_ZONE_CONTAINER_EMPTY_LAV);
        int activityZoneEmptyLavIndex = catalogsAliases.stream().distinct().toList().indexOf(ACTIVITY_ZONE_EMPTY_LAV);
        int statusRepairEmptyContainerRepairedIndex = catalogsAliases.stream().distinct().toList().indexOf(STATUS_REPAIR_EMPTY_CONTAINER_REPAIRED);
        int statusCleaningEmptyContainerCleanedIndex = catalogsAliases.stream().distinct().toList().indexOf(STATUS_CLEANING_EMPTY_CONTAINER_CLEANED);

        // Mock repository calls for all cleaning items being checked
        when(estimateEmrDetailRepository.findByEstimateIds(anyList())).thenReturn(List.of(
                EstimateEmrDetail.builder()
                        .id(estimateEmrDetailId)
                        .mechanicWorkDoneFlag(false)
                        .estimateEmr(EstimateEmr.builder()
                                .id(estimateEmrId)
                                .eir(Eir.builder().id(eirId).build())
                                .catEstimateType(Catalog.builder().id(catalogId).build())
                                .build())
                        .build()
        ));
        when(estimateEmrDetailRepository.findAllByEirIdAndEstimateType(eirId, List.of(estimateCreatedStatusIndex, estimateFinalizedStatusIndex, catalogEstimateStatusSubmittedIndex, catalogEstimateStatusApprovedIndex, catalogEstimateStatusRepairCompletedIndex)))
                .thenReturn(List.of(
                        EstimateEmrDetail.builder()
                                .id(estimateEmrDetailId)
                                .mechanicWorkDoneFlag(true)
                                .catCleaningType(Catalog.builder().id(catalogId).build())
                                .build()
                ))
                .thenReturn(List.of(
                        EstimateEmrDetail.builder()
                                .id(estimateEmrDetailId)
                                .mechanicWorkDoneFlag(true)
                                .catCleaningType(Catalog.builder().id(catalogId).build())
                                .build()));
        when(eirZoneRepository.findLastByEirIdAndContainerZoneAndActive(firstRecord, eirId, activityZoneContainerEmptyLavIndex, true))
                .thenReturn(Collections.emptyList());
        when(eirZoneRepository.saveAndFlush(any(EirZone.class)))
                .thenReturn(EirZone.builder()
                        .id(eirZoneId)
                        .build());
        when(eirActivityZoneRepository.findLastByEirIdAndActivityZoneIdAndActive(firstRecord, eirId, activityZoneEmptyLavIndex, true))
                .thenReturn(Collections.emptyList());
        when(eirActivityZoneRepository.save(any(EirActivityZone.class)))
                .thenReturn(EirActivityZone.builder()
                        .id(eirActivityZoneId)
                        .build());
        when(estimateEmrDetailRepository.existsByEstimateEmrIdAndMechanicWorkDoneFlagAndActive(estimateEmrId, false, true))
                .thenReturn(null);
        when(eirRepository.existsByIdAndCatApprovalBoxAndCatApprovalMachineryAndCleaningStatus(eirId, statusRepairEmptyContainerRepairedIndex, statusRepairEmptyContainerRepairedIndex, statusCleaningEmptyContainerCleanedIndex))
                .thenReturn(false);
        when(messageLanguageService.getMessage(anyString(), anyInt(), anyInt())).thenReturn("Success");
    }
}