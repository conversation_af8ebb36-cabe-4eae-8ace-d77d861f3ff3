package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.CedexSetupCorrectInput;
import com.maersk.sd1.sde.dto.CedexSetupCorrectOutput;
import com.maersk.sd1.sde.service.CedexSetupCorrectService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@RestController
@RequiredArgsConstructor
@RequestMapping("ModuleSDE/module/sde/SDEEMRServiceImp")
public class CedexSetupCorrectController {

    private static final Logger logger = LogManager.getLogger(CedexSetupCorrectController.class);

    private final CedexSetupCorrectService cedexSetupCorrectService;

    @PostMapping("/sdecedexSetupCorrect")
    public ResponseEntity<ResponseController<CedexSetupCorrectOutput>> processCedexSetupCorrect(@RequestBody @Valid CedexSetupCorrectInput.Root request) {
        CedexSetupCorrectOutput output = new CedexSetupCorrectOutput();
        try {
            CedexSetupCorrectInput.Input input = request.getPrefix().getInput();

            // Validate required fields (Null checks beyond @NotNull annotation)
            if (input.getSubBusinessUnitId() == null || input.getShippingLineId() == null) {
                throw new IllegalArgumentException("sub_business_unit_id or shipping_line_id cannot be null");
            }

            output = cedexSetupCorrectService.processCedexSetupCorrect(
                    input.getSubBusinessUnitId() != null ? input.getSubBusinessUnitId() : BigDecimal.ZERO,
                    input.getInspectionType(),
                    input.getShippingLineId(),
                    input.getContainerTypeId(),
                    input.getContainerSizeId(),
                    input.getCedexSetup()
            );

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing cedex_setup_correct.", e);
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}

