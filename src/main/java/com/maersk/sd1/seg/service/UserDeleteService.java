package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.seg.dto.UserDeleteOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class UserDeleteService {

    private static final Logger logger = LogManager.getLogger(UserDeleteService.class);

    private final UserRepository userRepository;

    @Autowired
    public UserDeleteService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Transactional
    public UserDeleteOutput deleteUser(Integer userId, Integer userAdminId) {
        UserDeleteOutput output = new UserDeleteOutput();
        try {
            userRepository.deleteRolesByUserId(userId);
            userRepository.deleteCompaniesByUserId(userId);
            userRepository.deleteProjectsByUserId(userId);
            userRepository.deleteEmailTemplatesByUserId(userId);
            userRepository.deleteTasksByUserId(userId);
            userRepository.deleteUserConfigByUserId(userId);
            userRepository.deletePreferencesByUserId(userId);

            userRepository.deleteNotificationTemplatesByUserId(userId);

            output.setRespEstado(1);
            output.setRespMensaje("Usuario eliminado correctamente");
            logger.info("User with id  has been deleted successfully by admin ");
        } catch (Exception e) {
            logger.error("Error deleting user with id ");
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }
        return output;
    }
}

