package com.maersk.sd1.common.dto;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class AttachmentLoadFilesDTO {


    @SerializedName("adjunto_id")
    private Integer adjuntoId;

    @SerializedName("nombre")
    private String nombre;

    @SerializedName("descripcion")
    private String descripcion;

    @SerializedName("tipo")
    private Integer tipo;

    @SerializedName("accion")
    private String accion;

    @SerializedName("id")
    private String id;

    @SerializedName("tipo_adjunto_id")
    private Integer tipoAdjuntoId;

    @SerializedName("numero")
    private String numero;
}
