package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.experimental.UtilityClass;

@Data
@UtilityClass
public class InspectionMachineryInput {

    @Data
    public static class Input {
        @JsonProperty("unidad_negocio_id")
        @NotNull(message = "unidad_negocio_id cannot be null")
        private Long businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        @NotNull(message = "sub_unidad_negocio_id cannot be null")
        private Long subBusinessUnitId;

        @JsonProperty("sub_unidad_negocio_local_id")
        @NotNull(message = "sub_unidad_negocio_local_id cannot be null")
        private Long subBusinessUnitLocalId;

        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("contenedor")
        @Size(max = 11, message = "contenedor must be at most 11 characters")
        private String container;

        @JsonProperty("en_stock")
        private String enStock;

        @JsonProperty("filterBox")
        private Integer filterBox;

        @JsonProperty("idioma_id")
        @NotNull(message = "idioma_id cannot be null")
        private Integer idiomaId;

        @JsonProperty("pending_inspection")
        private Boolean pendingInspection;

        @JsonProperty("usuario_id")
        @NotNull(message = "usuario_id cannot be null")
        private Integer userId;

        @JsonProperty("Page")
        @NotNull(message = "page cannot be null")
        @Min(value = 1, message = "page must be >= 1")
        private Integer page;

        @JsonProperty("Size")
        @NotNull(message = "size cannot be null")
        @Min(value = 1, message = "size must be >= 1")
        @Max(value = 1000, message = "size cannot exceed 1000")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}