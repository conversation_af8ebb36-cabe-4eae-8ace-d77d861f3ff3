package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class RepairApprovalListInput {

    @Data
    public static class Input {
        @JsonProperty("sub_unidad_negocio_id")
        @NotNull
        private Integer subBusinessUnitId;

        @JsonProperty("sub_unidad_negocio_local_id")
        @NotNull
        private Integer subBusinessUnitLocalId;

        @JsonProperty("tipo_reparacion_id")
        @NotNull
        private Integer repairTypeId;

        @JsonProperty("estado_aprobacion_id")
        @NotNull
        private Integer approvalStatusId;

        @JsonProperty("lista_contenedores")
        private String containerList = "[]";

        @JsonProperty("linea_naviera_id")
        private Integer shippingLineId;

        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("en_stock")
        private Boolean inStock;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer languageId;

        @JsonProperty("page")
        @NotNull
        @Min(1)
        private Integer page;

        @JsonProperty("size")
        @NotNull
        @Min(1)
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}