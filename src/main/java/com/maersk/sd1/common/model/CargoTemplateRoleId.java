package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class CargoTemplateRoleId implements Serializable {
    private static final long serialVersionUID = 8305581032564212727L;
    @NotNull
    @Column(name = "rol_id", nullable = false)
    private Integer roleId;

    @NotNull
    @Column(name = "carga_plantilla_id", nullable = false)
    private Integer cargoTemplateId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        CargoTemplateRoleId entity = (CargoTemplateRoleId) o;
        return Objects.equals(this.roleId, entity.roleId) &&
                Objects.equals(this.cargoTemplateId, entity.cargoTemplateId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(roleId, cargoTemplateId);
    }

}