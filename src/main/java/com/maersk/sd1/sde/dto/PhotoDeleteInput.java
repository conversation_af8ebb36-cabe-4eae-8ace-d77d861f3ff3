package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class PhotoDeleteInput {

    @Data
    public static class Input {

        @JsonProperty("eir_id")
        @NotNull(message = "eir_id must not be null")
        private Integer eirId;

        @JsonProperty("adjunto_id")
        @NotNull(message = "adjunto_id must not be null")
        private Integer adjuntoId;

        @JsonProperty("usuario_modificacion_id")
        @NotNull(message = "usuario_modificacion_id must not be null")
        private Integer userModificationId;

        @JsonProperty("equipment_category_id")
        @NotNull(message = "equipment_category_id must not be null")
        private Integer equipmentCategoryId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}

