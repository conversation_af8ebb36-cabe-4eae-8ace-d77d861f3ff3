package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.ShippingLineRemoveInput;
import com.maersk.sd1.sds.controller.dto.ShippingLineRemoveOutput;
import com.maersk.sd1.sds.service.ShippingLineRemoveService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSLineaNavieraServiceImp")
public class ShippingLineRemoveController {

    private static final Logger logger = LogManager.getLogger(ShippingLineRemoveController.class);

    private final ShippingLineRemoveService shippingLineRemoveService;

    public ShippingLineRemoveController(ShippingLineRemoveService shippingLineRemoveService) {
        this.shippingLineRemoveService = shippingLineRemoveService;
    }

    @PostMapping("/sdslineaNavieraEliminar")
    public ResponseEntity<ResponseController<ShippingLineRemoveOutput>> removeShippingLine(@RequestBody @Valid ShippingLineRemoveInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure."));
            }
            ShippingLineRemoveInput.Input input = request.getPrefix().getInput();
            ShippingLineRemoveOutput output = shippingLineRemoveService.removeShippingLine(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the remove shipping line request.", e);
            ShippingLineRemoveOutput output = new ShippingLineRemoveOutput();
            output.setRespStatus(0);
            output.setRespMessage(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}