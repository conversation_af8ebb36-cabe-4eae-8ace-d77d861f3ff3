package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.service.TruckObtainService;
import com.maersk.sd1.sds.dto.TruckObtainInput;
import com.maersk.sd1.sds.dto.TruckObtainOutput;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSVehiculoServiceImp")
public class TruckObtainController {

    private static final Logger logger = LogManager.getLogger(TruckObtainController.class);

    private final TruckObtainService truckObtainService;

    @Autowired
    public TruckObtainController(TruckObtainService truckObtainService) {
        this.truckObtainService = truckObtainService;
    }


    @PostMapping("/sdsvehiculoObtener")
    public ResponseEntity<ResponseController<TruckObtainOutput>> obtainTruck(@RequestBody @Valid TruckObtainInput.Root request) {
        try {
            Integer vehicleId = request.getPrefix().getInput().getVehicleId();
            TruckObtainOutput output = truckObtainService.obtainTruck(vehicleId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing obtainTruck.", e);
            TruckObtainOutput output = new TruckObtainOutput();
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}