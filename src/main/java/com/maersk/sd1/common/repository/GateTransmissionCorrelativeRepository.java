package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.GateTransmissionCorrelative;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface GateTransmissionCorrelativeRepository extends JpaRepository<GateTransmissionCorrelative, Integer> {

    @Query("SELECT MAX(c.correlativeNumberReference) FROM GateTransmissionCorrelative c WHERE c.identifierEmitter = :identifierEmitter")
    Integer findMaxCorrelativeNumberByIdentifierEmitter(String identifierEmitter);
}