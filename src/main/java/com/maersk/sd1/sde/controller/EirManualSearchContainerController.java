package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;

import com.maersk.sd1.sde.dto.EirManualSearchContainerInput;
import com.maersk.sd1.sde.dto.EirManualSearchContainerOutput;
import com.maersk.sd1.sde.service.EirManualSearchContainerService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDE/module/sde/SDEEIRServiceImp")
@Log4j2
public class EirManualSearchContainerController {

    private final EirManualSearchContainerService service;

    @PostMapping("/sdeeirManualSearchContainer")
    public ResponseEntity<ResponseController<EirManualSearchContainerOutput>> manualSearch(@Valid @RequestBody EirManualSearchContainerInput.Root request) {
        try {
            EirManualSearchContainerInput.Input input = request.getPrefix().getInput();
            log.info("Request received for manualSearch container - businessUnitId: {}, subBusinessUnitId: {}, subBusinessUnitLocalId: {}, containerNumber: {}, eirTypeId: {}, moveTypeId: {}, idiomaId: {}",
                    input.getBusinessUnitId(),
                    input.getSubBusinessUnitId(),
                    input.getSubBusinessUnitLocalId(),
                    input.getContainerNumber(),
                    input.getEirTypeId(),
                    input.getMoveTypeId(),
                    input.getIdiomaId()
            );
            EirManualSearchContainerOutput result = service.manualSearchContainer(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            log.error("An error occurred while searching EIR manually.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(e.getMessage()));
        }
    }
}
