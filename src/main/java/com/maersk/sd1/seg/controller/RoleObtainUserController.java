package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.RoleObtainUserInput;
import com.maersk.sd1.seg.controller.dto.RoleObtainUserOutput;
import com.maersk.sd1.seg.service.RoleObtainUserService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMRolServiceImp")
public class RoleObtainUserController {

    private static final Logger logger = LogManager.getLogger(RoleObtainUserController.class);

    private final RoleObtainUserService service;

    @PostMapping("/segrolObtenerUsuario")
    public ResponseEntity<ResponseController<RoleObtainUserOutput>> obtenerRolUsuario(@RequestBody @Valid RoleObtainUserInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
            }
            RoleObtainUserInput.Input input = request.getPrefix().getInput();
            RoleObtainUserOutput result = service.obtenerRolUsuario(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing obtenerRolUsuario.", e);
            RoleObtainUserOutput errorOutput = new RoleObtainUserOutput();
            // In a real scenario, we might want to set some error message or code.
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}