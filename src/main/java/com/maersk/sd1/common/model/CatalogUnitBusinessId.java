package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class CatalogUnitBusinessId implements Serializable {
    private static final long serialVersionUID = 3481308955852666257L;
    @NotNull
    @Column(name = "catalogo_id", nullable = false)
    private Integer catalogId;

    @NotNull
    @Column(name = "unidad_negocio_id", nullable = false)
    private Integer unitBusinessId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        CatalogUnitBusinessId entity = (CatalogUnitBusinessId) o;
        return Objects.equals(this.unitBusinessId, entity.unitBusinessId) &&
                Objects.equals(this.catalogId, entity.catalogId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(unitBusinessId, catalogId);
    }

}