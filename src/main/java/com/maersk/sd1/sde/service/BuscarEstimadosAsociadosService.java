package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sde.dto.BuscarEstimadosAsociadosInput;
import com.maersk.sd1.sde.dto.BuscarEstimadosAsociadosOutput;
import com.maersk.sd1.sde.dto.BuscarEstimadosAsociadosOutput.BuscarEstimadosAsociadosOutputDetail;
import com.maersk.sd1.common.repository.EstimateEmrRepository;
import com.maersk.sd1.common.model.EstimateEmr;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class BuscarEstimadosAsociadosService {

    private static final Logger logger = LogManager.getLogger(BuscarEstimadosAsociadosService.class);

    private final EstimateEmrRepository estimateEmrRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;
    private final MessageLanguageRepository messageLanguageRepository;
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yy HH:mm");

    @Transactional(readOnly = true)
    public BuscarEstimadosAsociadosOutput buscarEstimadosAsociados(
            BuscarEstimadosAsociadosInput.Input input
    ) {
        logger.info("[Service] buscarEstimadosAsociados called with input: {}", input);
        BuscarEstimadosAsociadosOutput output = new BuscarEstimadosAsociadosOutput();
        List<BuscarEstimadosAsociadosOutputDetail> detailList = new ArrayList<>();

        try {
            // sin_gatein = 0 (false)
            logger.info("[Service] Searching estimates with gate-in (eirId = {}).", input.getEirId());
            List<EstimateEmr> estimateEmrList = estimateEmrRepository
                    .findByEirIdAndActiveTrueOrderByEstimateDateInspectionAsc(input.getEirId());

            for (EstimateEmr emr : estimateEmrList) {
                detailList.add(mapToDetail(emr, input.getLanguageId()));
            }

            output.setDetails(detailList);
            return output;
        } catch (Exception ex) {
            logger.error("[Service] Exception in buscarEstimadosAsociados", ex);
            output.setDetails(new ArrayList<>());
            return output;
        }
    }

    private BuscarEstimadosAsociadosOutputDetail mapToDetail(EstimateEmr emr, Integer languageId) {
        BuscarEstimadosAsociadosOutputDetail detail = new BuscarEstimadosAsociadosOutputDetail();

        // date format
        if (emr.getEstimateDateInspection() != null) {
            detail.setFechaInspeccion(emr.getEstimateDateInspection().format(FORMATTER));
        }

        // emulate sds.fn_CatalogoTraducidoDes(a.cat_tipo_estimado_id,@idioma_id)
        String tipoEstimado = catalogLanguageRepository.fnCatalogoTraducidoDes(emr.getCatEstimateType().getId(), languageId);
        detail.setTipoEstimado(tipoEstimado);

        // estimado_emr_id
        detail.setEstimadoId(String.valueOf(emr.getId()));

        // emulate sds.fn_CatalogoTraducidoDes(a.cat_estado_estimado_id,@idioma_id)
        String estadoEstimado = catalogLanguageRepository.fnCatalogoTraducidoDes(emr.getCatEstimateStatus().getId(), languageId);
        detail.setEstadoEstimado(estadoEstimado);

        return detail;
    }
}