package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.System;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.seg.dto.BusinessUnitRegisterInput;
import com.maersk.sd1.seg.dto.BusinessUnitRegisterOutput;
import com.maersk.sd1.common.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
@Log4j2
public class BusinessUnitRegisterService {

    private final BusinessUnitRepository businessUnitRepository;
    private final BusinessUnitConfigRepository businessUnitConfigRepository;
    private final BusinessUnitCurrencyRepository businessUnitCurrencyRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public BusinessUnitRegisterOutput registerBusinessUnit(BusinessUnitRegisterInput.Input input) {
        BusinessUnitRegisterOutput output = new BusinessUnitRegisterOutput();
        try {
            long aliasCount = businessUnitRepository.countByBusinesUnitAliasAndSystem_Id(
                    input.getAlias(), input.getSystemId());
            if (aliasCount > 0) {
                output.setRespEstado(2);
                String msg = messageLanguageRepository.findAdmMessage("BUN_ALIAS_ERROR", input.getLanguageId(),null);
                output.setRespMensaje(msg);
                return output;
            }

            BusinessUnit newBusinessUnit = createAndSaveBusinessUnit(input);

            createAndSaveConfigs(input, newBusinessUnit);

            createAndSaveCurrencies(input, newBusinessUnit);

            output.setRespEstado(1);
            String successMessage = messageLanguageRepository.findAdmMessage("BUN_SUCCESS_REGISTER", input.getLanguageId(),null);
            output.setRespMensaje(successMessage);
            return output;
        } catch (Exception e) {

            log.error("Error registering business unit", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return output;
        }
    }

    private BusinessUnit createAndSaveBusinessUnit(BusinessUnitRegisterInput.Input input) {
        BusinessUnit businessUnit = new BusinessUnit();
        businessUnit.setName(input.getName());
        businessUnit.setStatus(input.getStatus());

        User regUser = new User();
        regUser.setId(input.getUserId());
        businessUnit.setRegistrationUser(regUser);
        businessUnit.setRegistrationDate(LocalDateTime.now());

        if (input.getParentBusinessUnitId() != null) {
            BusinessUnit parent = new BusinessUnit();
            parent.setId(input.getParentBusinessUnitId());
            businessUnit.setParentBusinessUnit(parent);
        }

        if (input.getSystemId() != null) {
            System sys = new System();
            sys.setId(input.getSystemId());
            businessUnit.setSystem(sys);
        }

        businessUnit.setBusinesUnitAlias(input.getAlias());
        return businessUnitRepository.save(businessUnit);
    }

    private void createAndSaveConfigs(BusinessUnitRegisterInput.Input input, BusinessUnit newBusinessUnit) {
        List<BusinessUnitConfig> toSave = new ArrayList<>();
        for (BusinessUnitRegisterInput.Config cfg : input.getConfigurations()) {
            BusinessUnitConfig config = new BusinessUnitConfig();
            config.setBusinessUnit(newBusinessUnit);

            Catalog catalog = new Catalog();
            catalog.setId(cfg.getConfigurationType());
            config.setCatConfigurationType(catalog);

            config.setValue(cfg.getValue());
            config.setStatus(cfg.getStatus());

            User regUser = new User();
            regUser.setId(input.getUserId());
            config.setRegistrationUser(regUser);
            config.setRegistrationDate(LocalDateTime.now());

            toSave.add(config);
        }
        businessUnitConfigRepository.saveAll(toSave);
    }

    private void createAndSaveCurrencies(BusinessUnitRegisterInput.Input input, BusinessUnit newBusinessUnit) {
        List<BusinessUnitCurrency> toSave = new ArrayList<>();
        for (BusinessUnitRegisterInput.Currency c : input.getCurrencies()) {
            BusinessUnitCurrency currency = new BusinessUnitCurrency();
            UnitBusinessCoinId id = new UnitBusinessCoinId();
            id.setUnitBusinessId(newBusinessUnit.getId());
            id.setCoinId(c.getCurrencyId());

            currency.setId(id);
            currency.setBusinessUnit(newBusinessUnit);

            Currency coinEntity = new Currency();
            coinEntity.setId(c.getCurrencyId());
            currency.setCurrency(coinEntity);

            currency.setPredefined(c.getPredefined());
            toSave.add(currency);
        }
        businessUnitCurrencyRepository.saveAll(toSave);
    }
}
