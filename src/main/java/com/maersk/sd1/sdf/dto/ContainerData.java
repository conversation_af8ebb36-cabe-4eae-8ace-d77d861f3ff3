package com.maersk.sd1.sdf.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
public class ContainerData {
    private Integer transportPlanningDetailId;
    private String container;
    private Integer containerId;
    private Integer truckerCompanyId;
    private String truckerCompany;
    private String transportDate;
    private String isoCode;
    private Integer isoCodeId;
    private BigDecimal reportedWeight;
    private Integer reportedWeightUnit;
    private String catUnitMeasureWeight;
    private String seal1;
    private String seal2;
    private String seal3;
    private String seal4;
    private String catStateTrkPlanningAlias;
    private Integer edit;
    private Integer flagErase;
    private Integer equipmentFamilyId;
    private Integer equipmentSizeId;
    private Integer equipmentTypeId;
    private String isReefer;
    private Integer reeferTypeId;
    private List<ImoChecklist> imoChecklist;

    @Data
    @AllArgsConstructor
    public static class ImoChecklist {
        private Integer imoId;
        private String imoCode;
        private String description;
        private Boolean selected;
    }
}
