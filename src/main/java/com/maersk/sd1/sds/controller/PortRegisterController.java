package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.dto.PortRegisterInput;
import com.maersk.sd1.sds.dto.PortRegisterOutput;
import com.maersk.sd1.sds.service.PortRegisterService;
import com.maersk.sd1.common.controller.dto.ResponseController; // Hypothetical common wrapper
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import jakarta.validation.Valid;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSPuertoServiceImp")
public class PortRegisterController {

    private static final Logger logger = LogManager.getLogger(PortRegisterController.class);

    private final PortRegisterService portRegisterService;

    @Autowired
    public PortRegisterController(PortRegisterService portRegisterService) {
        this.portRegisterService = portRegisterService;
    }

    @PostMapping("/sdspuertoRegistrar")
    public ResponseEntity<ResponseController<PortRegisterOutput>> registerPort(@RequestBody @Valid PortRegisterInput.Root request) {
        try {

            if (request.getPrefix() == null || request.getPrefix().getInput() == null) {
                throw new IllegalArgumentException("Invalid request. Prefix or input is missing.");
            }

            PortRegisterInput.Input input = request.getPrefix().getInput();
            PortRegisterOutput output = portRegisterService.registerPort(
                    input.getPort(),
                    input.getName(),
                    input.getCountryId(),
                    input.getActive(),
                    input.getUserRegistrationId(),
                    input.getLanguageId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the registerPort request.", e);
            PortRegisterOutput errorOutput = new PortRegisterOutput();
            errorOutput.setRespEstado(0);
            errorOutput.setRespMensaje(e.toString());
            errorOutput.setRespNewId(0);

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(errorOutput));
        }
    }
}
