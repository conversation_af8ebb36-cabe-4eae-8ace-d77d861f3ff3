package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.PortMaster;
import com.maersk.sd1.common.model.PortMasterEntityPropertyPK;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface PortMasterRepository extends JpaRepository<PortMaster, PortMasterEntityPropertyPK> {

  @Query(value = "select p from PortMaster p where p.portCode = :portCode")
  List<PortMaster> findByPortCode(@Param("portCode") String portCode);


  @Query(value = "select  p from PortMaster p where p.portCode like :portCode order by p.portCode desc")
  Optional<PortMaster> findTop1PortCodeLikeOrderByPortCodeDesc(@Param("portCode") String portCode);
}