package com.maersk.sd1.common.service;

import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class ReeferTypeUtilsService {

    private static final String STANDAR = "STANDAR";

    private ReeferTypeUtilsService() {
    }

    public static int getReeferTypeDefault(String contenedor) {
        String letra = contenedor.substring(0, 4).toUpperCase();
        Integer ctnClase = null;

        List<String> atmosfera = Arrays.asList("MCHU", "MCAU");
        List<String> superFreezer = Arrays.asList("MSFU");
        List<String> magnum = Arrays.asList("MWMU");
        List<String> coldTreatment = Arrays.asList("DAYU", "PONU", "MWCU");

        if (atmosfera.contains(letra)) {
            ctnClase = getCatalogoId("ATMOSFERA");
        } else if (superFreezer.contains(letra)) {
            ctnClase = getCatalogoId("SUPERFREEZER");
        } else if (magnum.contains(letra)) {
            ctnClase = getCatalogoId("MAGNUN");
        } else if (coldTreatment.contains(letra)) {
            if (isColdTreatmentContainer(contenedor)) {
                ctnClase = getCatalogoId("COLDTREATMENT");
            } else {
                ctnClase = getCatalogoId(STANDAR);
            }
        } else {
            ctnClase = getCatalogoId(STANDAR);
        }

        return ctnClase != null ? ctnClase : 0;
    }

    private static int getCatalogoId(String descripcion) {
        // Simulate database call
        switch (descripcion) {
            case "ATMOSFERA":
                return 1;
            case "SUPERFREEZER":
                return 2;
            case "MAGNUN":
                return 3;
            case "COLDTREATMENT":
                return 4;
            case STANDAR:
                return 5;
            default:
                return 0;
        }
    }

    private static boolean isColdTreatmentContainer(String contenedor) {
        List<String[]> coldTreatmentRanges = Arrays.asList(
                new String[]{"DAYU6703755", "DAYU6704746"},
                new String[]{"DAYU6705254", "DAYU6709999"},
                new String[]{"PONU4978508", "PONU4995995"},
               
                new String[]{"MWCU5232107", "MWCU5232771"}
        );

        for (String[] range : coldTreatmentRanges) {
            if (contenedor.compareTo(range[0]) >= 0 && contenedor.compareTo(range[1]) <= 0) {
                return true;
            }
        }
        return false;
    }


}
