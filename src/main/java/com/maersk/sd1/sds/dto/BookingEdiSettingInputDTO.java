package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@UtilityClass
public class BookingEdiSettingInputDTO {

    @Data
    public static class BookingEdiSettingDetailInput {
        @JsonProperty("sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("aplica_envio_copia_coparn")
        private Boolean applySendCopy;

        @JsonProperty("cat_canal_reenvio_coparn_id")
        private Integer catForwardChannelId;

        @JsonProperty("sftp_reenvio_coparn_id")
        private String forwardSftpId;

        @JsonProperty("ftp_reenvio_coparn_id")
        private String forwardFtpId;

        @JsonProperty("carpeta_reenvio_coparn_ruta")
        private String forwardFolderRoute;
    }

    @Data
    public static class Input {

        @JsonProperty("linea_naviera_id")
        @NotNull
        private Integer shippingLineId;

        @JsonProperty("cat_canal_recepcion_coparn_id")
        @NotNull
        private Integer catReceptionChId;

        @JsonProperty("cat_modo_procesar_coparn_id")
        @NotNull
        private Integer catProcessModeId;

        @JsonProperty("edi_coparn_descripcion")
        @Size(max = 250)
        private String bkEdiDescription;

        @JsonProperty("azure_id")
        @Size(max = 100)
        private String azureId;

        @JsonProperty("sftp_coparn_id")
        @Size(max = 100)
        private String bkEdiSftpId;

        @JsonProperty("ftp_coparn_id")
        @Size(max = 100)
        private String bkEdiFtpId;

        @JsonProperty("carpeta_coparn_ruta")
        @Size(max = 200)
        private String bkEdiFolderRoute;

        @JsonProperty("extension_archivo_descargar")
        @Size(max = 10)
        private String downloadFileExtension;

        @JsonProperty("ruta_mover_edi")
        @Size(max = 200)
        private String ediMoveRoute;

        @JsonProperty("permitir_crear_prog_nave_automatico")
        @NotNull
        private Boolean allowCreateAutomaticVesselProgramming;

        @JsonProperty("permitir_crear_cliente_automatico")
        @NotNull
        private Boolean allowCreateAutomaticCustomer;

        @JsonProperty("es_historico")
        @NotNull
        private Boolean isHistorical;

        @JsonProperty("fecha_debaja")
        @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime deactivationDate;

        @JsonProperty("motivo_debaja")
        @Size(max = 200)
        private String deactivationReason;

        @JsonProperty("activo")
        @NotNull
        private Boolean active;

        @JsonProperty("usuario_registro_id")
        @NotNull
        private Integer userRegistrationId;

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("detalle")
        private List<BookingEdiSettingDetailInput> detail;

        @JsonProperty("cat_bkedi_message_type_id")
        private Integer catBkEdiMessageTypeId;

        @JsonProperty("cat_owner_edi_booking_id")
        private Integer catOwnerEdiBookingId;

        @JsonProperty("filename_mask")
        @Size(max = 100)
        private String filenameMask;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}

