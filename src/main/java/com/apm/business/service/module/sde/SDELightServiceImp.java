package com.apm.business.service.module.sde;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.servlet.http.HttpServletRequest;
import ohSolutions.ohJpo.dao.JpoClass;
import ohSolutions.ohJpo.dao.JpoUtil;
import ohSolutions.ohRest.util.bean.Response;
import ohSolutions.ohJpo.dao.Jpo;

import org.json.JSONObject;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.apm.business.service.external.APSUser;
import com.apm.business.service.external.Connection;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import kong.unirest.HttpResponse;
import kong.unirest.RequestBodyEntity;
import kong.unirest.Unirest;

@RestController
@RequestMapping("/module/sde/SDELightServiceImp")
@JpoClass(source = "dsinland", oauth2Enable = true)
public class SDELightServiceImp extends SDELightService {

	// Listado de unidades de negocio que usan el APS
	List<Integer> businessUnitsAPS = Arrays.asList(new Integer[] { 2,5 });

	@RequestMapping(value = "/sdsbuscarBookingGateOutEmptyLight", method = { RequestMethod.POST })
	@JpoClass(oauth2Enable = true)
	public Object sdsbuscarBookingGateOutEmptyLight(Jpo ppo, HttpServletRequest request) throws Exception {
		Response res = (Response) super.sdsbuscarBookingGateOutEmptyLight(ppo, request);
		if (businessUnitsAPS.contains(Integer.parseInt(ppo.getData("SDE", "unidad_negocio_id")))) {
			JSONObject obj = new JSONObject(res);
			List<Object> result = obj.optJSONArray("result").toList();

			// Usuario para las llamadas a las API's del APS
			Connection connection = new Connection(JpoUtil.getPropertie("SDE", "apsint.api.login.user"),JpoUtil.getPropertie("SDE", "apsint.api.login.password"));

			// Si no se tiene el id de una cita, buscarla por el numero de contenedor
			// Armar la llamada al servicio del APS
			Map<String, Object> body_t = new HashMap<String, Object>();
			Map<String, Object> f = new HashMap<String, Object>();
			Map<String, String> data = new HashMap<String, String>();

			data.put("documento", ppo.getData("SDE", "numero_booking"));
			data.put("unidad_negocio_id", ppo.getData("SDE", "unidad_negocio_id"));
			data.put("estado_cita", "2");

			f.put("F", data);
			body_t.put("APS", f);

			Gson prettyGson = new GsonBuilder().setPrettyPrinting().create();
			String body_f = prettyGson.toJson(body_t);

			RequestBodyEntity resp;
			try {
				String auth = "Bearer ";
				auth = auth.concat(connection.getToken());
				resp = Unirest.post(JpoUtil.getPropertie("SDE", "apsint.api.getAppointmentByDocument.url"))
						.header("Authorization", auth).body(body_f);
				HttpResponse<String> responseAPI = resp.asString();
				JSONObject objCita = new JSONObject(responseAPI.getBody());
				List<Object> resultCita = objCita.optJSONArray("result").toList();
				result.add(resultCita);

			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				return e.getMessage();
			}

			obj.put("result", result);
			return obj.toString();
		}
		return res;
	}

	@RequestMapping(value = "/sdegateOutEmptyLightRegistrar", method = { RequestMethod.POST })
	@JpoClass(oauth2Enable = true)
	public Object sdegateOutEmptyLightRegistrar(Jpo ppo, HttpServletRequest request) throws Exception {
		// Usuario para las llamadas a las API's del APS
		Connection connection = new Connection(JpoUtil.getPropertie("SDE", "apsint.api.login.user"),JpoUtil.getPropertie("SDE", "apsint.api.login.password"));

		Response response = (Response) super.sdegateOutEmptyLightRegistrar(ppo, request);

		if (businessUnitsAPS.contains(Integer.parseInt(ppo.getData("SDE", "unidad_negocio_id")))) {
			List<Object> result = (List<Object>) response.getResult();
			if (Integer.parseInt(result.get(1).toString()) == 1) {
				// Si el proceso fue hecho correctamente, se debe de atender la cita
				// Armar la llamada al servicio del APS
				Map<String, Object> body_t = new HashMap<String, Object>();
				Map<String, Object> f = new HashMap<String, Object>();
				Map<String, String> data = new HashMap<String, String>();

				data.put("cita_id", ppo.getData("SDE", "numero_documento_externo"));
				data.put("usuario_atencion_id", ppo.getData("SDE", "usuario_registro_id"));

				f.put("F", data);
				body_t.put("APS", f);

				Gson prettyGson = new GsonBuilder().setPrettyPrinting().create();
				String body_f = prettyGson.toJson(body_t);

				RequestBodyEntity resp;
				try {
					String auth = "Bearer ";
					auth = auth.concat(connection.getToken());
					resp = Unirest.post(JpoUtil.getPropertie("SDE", "apsint.api.attendAppointment.url"))
							.header("Authorization", auth).body(body_f);
					HttpResponse<String> responseAPI = resp.asString();
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
					ppo.rollback();
					return e.getMessage();
				}
			}
		}

		return response;
	}

}