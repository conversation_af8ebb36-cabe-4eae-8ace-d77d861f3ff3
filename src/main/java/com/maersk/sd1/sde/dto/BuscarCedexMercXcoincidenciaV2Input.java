package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;

@UtilityClass
public class BuscarCedexMercXcoincidenciaV2Input {

    @Data
    public static class Input {
        @JsonProperty("sub_unidad_negocio_id")
        private Long subUnidadNegocioId;

        @JsonProperty("cat_tipo_estimado_id")
        private Long catTipoEstimadoId;

        @JsonProperty("es_reefer")
        private Boolean esReefer;

        @JsonProperty("moneda_id")
        private Long monedaId;

        @JsonProperty("cat_estimado_ubicacion_dano_id")
        private Long catEstimadoUbicacionDanoId;

        @JsonProperty("cat_estimado_componente_id")
        private Long catEstimadoComponenteId;

        @JsonProperty("cat_estimado_metodo_rep_id")
        private Long catEstimadoMetodoRepId;

        @JsonProperty("cat_estimado_dimension_tipo_id")
        private Long catEstimadoDimensionTipoId;

        @JsonProperty("dimension_largo")
        @Positive
        private BigDecimal dimensionLargo;

        @JsonProperty("dimension_ancho")
        @Positive
        private BigDecimal dimensionAncho;

        @JsonProperty("piezas_reparar")
        @Positive
        private Integer piezasReparar;

        @JsonProperty("cat_tamano_contenedor_id")
        private Long catTamanoContenedorId;

        @JsonProperty("idioma_id")
        private Integer idiomaId;

        @JsonProperty("unit_size_id")
        private Long unitSizeId;

        @JsonProperty("shipping_line_id")
        private Integer shippingLineId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        @NotNull
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        @NotNull
        private Prefix prefix;
    }
}