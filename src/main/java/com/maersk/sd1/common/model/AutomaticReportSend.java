package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.Nationalized;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "automatic_report_send", schema = "ges")
public class AutomaticReportSend {
    @Id
    @Column(name = "automatic_report_send_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "automatic_report_id", nullable = false)
    private AutomaticReport automaticReport;

    @Size(max = 300)
    @Column(name = "file_name", length = 300)
    private String fileName;

    @Size(max = 300)
    @Column(name = "file_url", length = 300)
    private String fileUrl;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_status_id", nullable = false)
    private Catalog catStatus;

    @NotNull
    @Column(name = "active", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "user_registration_id", nullable = false)
    private User registrationUser;

    @NotNull
    @Column(name = "registration_date", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_modification_id")
    private User modificationUser;

    @Column(name = "modification_date")
    private LocalDateTime modificationDate;

    @Column(name = "send_email_date")
    private LocalDateTime sendEmailDate;

    @Column(name = "total_register")
    private Integer totalRegister;

    @Nationalized
    @Lob
    @Column(name = "json_filter")
    private String jsonFilter;

}