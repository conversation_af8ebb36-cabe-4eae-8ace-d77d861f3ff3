package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.InspectionChecklist;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.InspectionChecklistRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sde.dto.InspectionChecklistRegisterOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@RequiredArgsConstructor
@Service
public class InspectionChecklistRegisterService {

    private static final Logger logger = LogManager.getLogger(InspectionChecklistRegisterService.class);
    private final InspectionChecklistRepository inspectionChecklistRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public InspectionChecklistRegisterOutput registerInspectionChecklist(Integer subBusinessUnitId, Integer catInspectionTypeId,
                                                                         Integer orderNumber, String description,
                                                                         Integer userRegistrationId, Boolean active,
                                                                         Integer languageId) {
        InspectionChecklistRegisterOutput output = new InspectionChecklistRegisterOutput();
        try {
            // Build references without extra DB fetches, assuming IDs are valid.
            BusinessUnit businessUnit = new BusinessUnit();
            businessUnit.setId(subBusinessUnitId);

            Catalog catalog = new Catalog();
            catalog.setId(catInspectionTypeId);

            User registrationUser = new User();
            registrationUser.setId(userRegistrationId);

            // Create new InspectionChecklist entity
            InspectionChecklist checklist = InspectionChecklist.builder()
                    .subBusinessUnit(businessUnit)
                    .catInspectionType(catalog)
                    .orderNumber(orderNumber)
                    .description(description)
                    .registrationUser(registrationUser)
                    .registrationDate(LocalDateTime.now())
                    .active(active)
                    .build();

            // Persist entity
            InspectionChecklist savedChecklist = inspectionChecklistRepository.save(checklist);

            // Retrieve newly generated ID
            Integer newId = savedChecklist.getId();

            // Retrieve success message from message language repository using domain = 'GENERAL' and code = 9
            String message = messageLanguageRepository.fnTranslatedMessage("GENERAL", 9, languageId);

            output.setRespEstado(1);
            output.setRespMensaje(message);
            output.setRespNewId(newId);
        } catch (Exception e) {
            logger.error("Error while registering inspection checklist", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            output.setRespNewId(0);
        }
        return output;
    }
}