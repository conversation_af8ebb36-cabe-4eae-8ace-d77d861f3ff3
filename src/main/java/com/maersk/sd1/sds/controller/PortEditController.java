package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.PortEditInput;
import com.maersk.sd1.sds.dto.PortEditOutput;
import com.maersk.sd1.sds.service.PortEditService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSPuertoServiceImp")
public class PortEditController {

    private static final Logger logger = LogManager.getLogger(PortEditController.class);

    private final PortEditService portEditService;

    @Autowired
    public PortEditController(PortEditService portEditService) {
        this.portEditService = portEditService;
    }

    @PostMapping("/sdspuertoEditar")
    public ResponseEntity<ResponseController<PortEditOutput>> editPort(@RequestBody @Valid PortEditInput.Root request) {
        try {

            if(request.getPrefix() == null || request.getPrefix().getInput() == null) {
                throw new IllegalArgumentException("The request body is empty.");
            }

            PortEditInput.Input input = request.getPrefix().getInput();
            PortEditOutput portEditOutput = portEditService.editPort(
                    input.getPuertoId(),
                    input.getPuerto(),
                    input.getNombre(),
                    input.getPaisId(),
                    input.getActivo(),
                    input.getUsuarioModificacionId(),
                    input.getIdiomaId()
            );
            return ResponseEntity.ok(new ResponseController<>(portEditOutput));
        } catch (Exception e) {
            logger.error("An error occurred while processing the update port request.", e);
            PortEditOutput portEditOutput = new PortEditOutput();
            portEditOutput.setRespMensaje(e.getMessage());
            portEditOutput.setRespEstado(0);
            return ResponseEntity.status(500).body(new ResponseController<>(portEditOutput));
        }
    }
}