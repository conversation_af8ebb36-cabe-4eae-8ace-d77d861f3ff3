package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.BusinessUnit;
import com.maersk.sd1.common.model.EstimateEmrSetting;
import com.maersk.sd1.common.model.ShippingLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;

public interface EstimateEmrSettingRepository extends JpaRepository<EstimateEmrSetting, Integer> {

    @Query("SELECT COUNT(e) FROM EstimateEmrSetting e WHERE e.active = true AND e.shippingLine.id = :shippingLineId AND e.subBusinessUnit.id = :subBusinessUnitId")
    Integer countActiveEstimatesByShippingLineAndSubBusinessUnit(@Param("shippingLineId") Integer shippingLineId,
                                                                 @Param("subBusinessUnitId") Integer subBusinessUnitId);

    Optional<EstimateEmrSetting> findByShippingLineAndSubBusinessUnitAndActive(
            ShippingLine shippingLine,
            BusinessUnit subBusinessUnit,
            Boolean active
    );

    @Query("SELECT 'S' FROM EstimateEmrSetting s " +
            "WHERE s.shippingLine.id = :shippingLineId " +
            "AND s.subBusinessUnit.id = :subBusinessUnitId " +
            "AND s.active = true")
    Optional<String> findStatusByParams(@Param("shippingLineId") Integer shippingLineId,
                                        @Param("subBusinessUnitId") Integer  subBusinessUnitId);

    @Query("SELECT CASE WHEN COUNT(e) > 0 THEN 1 ELSE 0 END " +
            "FROM EstimateEmrSetting e " +
            "WHERE e.shippingLine.id = :shippingLineId " +
            "AND e.subBusinessUnit.id = :subBusinessUnitId " +
            "AND e.active = true")
    Integer checkIfAppliesMercSending(@Param("shippingLineId") Integer shippingLineId,
                                      @Param("subBusinessUnitId") Integer subBusinessUnitId);

}