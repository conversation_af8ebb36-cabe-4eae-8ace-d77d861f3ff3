package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.CompanyRepository;
import com.maersk.sd1.common.repository.CurrencyRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.sde.dto.EstimateAutoApprovalConfigEditInput;
import com.maersk.sd1.sde.dto.EstimateAutoApprovalConfigEditOutput;
import com.maersk.sd1.common.repository.EstimateAutoApprovalConfigRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class EstimateAutoApprovalConfigEditService {

    private static final Logger logger = LogManager.getLogger(EstimateAutoApprovalConfigEditService.class);

    private final EstimateAutoApprovalConfigRepository estimateAutoApprovalConfigRepository;
    private final CatalogRepository catalogRepository;
    private final MessageLanguageRepository messageLanguageRepository;
    private final CurrencyRepository currencyRepository;
    private final CompanyRepository companyRepository;
    private final ShippingLineRepository shippingLineRepository;
    private final UserRepository userRepository;

    @Transactional
    public EstimateAutoApprovalConfigEditOutput editEstimateAutoApprovalConfig(EstimateAutoApprovalConfigEditInput.Input inputDto) {
        EstimateAutoApprovalConfigEditOutput output = new EstimateAutoApprovalConfigEditOutput();
        Integer respEstado = 0;
        String respMensaje;

        try {
            // Find "sd1_equipment_category_container" from Catalog
            Catalog containerCatalog = catalogRepository.findByAlias("sd1_equipment_category_container");
            Integer containerCatalogId = containerCatalog != null ? containerCatalog.getId() : null;

            // 1) Retrieve the record to update
            EstimateAutoApprovalConfig config = estimateAutoApprovalConfigRepository.findById(inputDto.getEstimateAutoApprovalConfigId())
                    .orElseThrow(() -> new IllegalArgumentException("No configuration found with the given ID."));

            // 2) Check container scenario
            if (inputDto.getCatEquipmentCategoryId().equals(containerCatalogId)) {
                // First check for structure=1 / machinery=1 duplication
                boolean duplicated = checkStructureMachineryTrueDuplicate(inputDto);
                if (duplicated) {
                    respEstado = 2;
                    respMensaje = messageLanguageRepository.fnTranslatedMessage("ESTIMATE_AUTO_APPROVAL", 2, inputDto.getLanguageId());
                    output.setRespEstado(respEstado);
                    output.setRespMensaje(respMensaje);
                    return output;
                }

                // Second check for structure/machinery param duplication
                boolean paramDuplicated = checkStructureMachineryParamDuplicate(inputDto);
                if (paramDuplicated) {
                    // attempt to retrieve the duplicated record ID to include in message
                    Integer duplicatedId = getDuplicatedIdContainerParam(inputDto);
                    respEstado = 2;
                    if (duplicatedId != null) {
                        respMensaje = messageLanguageRepository.fnTranslatedMessage("ESTIMATE_AUTO_APPROVAL", 2, inputDto.getLanguageId())
                                + " ID: " + duplicatedId;
                    } else {
                        respMensaje = messageLanguageRepository.fnTranslatedMessage("ESTIMATE_AUTO_APPROVAL", 2, inputDto.getLanguageId());
                    }
                    output.setRespEstado(respEstado);
                    output.setRespMensaje(respMensaje);
                    return output;
                }

                // set customer null
                inputDto.setCustomerId(null);
            } else {
                // else scenario check duplications
                boolean chassisDuplicated = checkChassisDuplicate(inputDto);
                if (chassisDuplicated) {
                    // attempt to retrieve the duplicated record ID to include in message
                    Integer duplicatedId = getDuplicatedIdChassisCase(inputDto);
                    respEstado = 2;
                    String baseMsg = messageLanguageRepository.fnTranslatedMessage("ESTIMATE_AUTO_APPROVAL", 2, inputDto.getLanguageId());
                    if (duplicatedId != null) {
                        respMensaje = baseMsg + " ID: " + duplicatedId;
                    } else {
                        respMensaje = baseMsg;
                    }
                    output.setRespEstado(respEstado);
                    output.setRespMensaje(respMensaje);
                    return output;
                }

                // set shippingLine null
                inputDto.setShippingLineId(null);
            }

            // 3) Now update the entity
            updateEstimateAutoApprovalConfigEntity(config, inputDto);

            // 4) Success
            respEstado = 1;
            respMensaje = messageLanguageRepository.fnTranslatedMessage("ESTIMATE_AUTO_APPROVAL", 1, inputDto.getLanguageId());
            output.setRespEstado(respEstado);
            output.setRespMensaje(respMensaje);

        } catch (Exception ex) {
            logger.error("Error updating EstimateAutoApprovalConfig", ex);
            respEstado = 0;
            // In a real scenario, we could log this error or retrieve a specific message
            respMensaje = ex.getMessage();
            output.setRespEstado(respEstado);
            output.setRespMensaje(respMensaje);
        }
        return output;
    }

    private boolean checkStructureMachineryTrueDuplicate(EstimateAutoApprovalConfigEditInput.Input inputDto) {
        List<EstimateAutoApprovalConfig> existing = estimateAutoApprovalConfigRepository
                .findByLocalSubBusinessUnitIdAndStructureEstimateFlagAndMachineryEstimateFlagAndCatEquipmentCategoryIdAndCatEquipmentTypeIdAndShippingLineIdAndIdNot(
                        inputDto.getSubBusinessLocalId(),
                        true,
                        true,
                        inputDto.getCatEquipmentCategoryId(),
                        inputDto.getCatEquipmentTypeId() == null ? 0 : inputDto.getCatEquipmentTypeId(),
                        inputDto.getShippingLineId() == null ? 0 : inputDto.getShippingLineId(),
                        inputDto.getEstimateAutoApprovalConfigId()
                );
        return !existing.isEmpty();
    }

    private boolean checkStructureMachineryParamDuplicate(EstimateAutoApprovalConfigEditInput.Input inputDto) {
        List<EstimateAutoApprovalConfig> existing = estimateAutoApprovalConfigRepository
                .findByLocalSubBusinessUnitIdAndStructureEstimateFlagAndMachineryEstimateFlagAndCatEquipmentCategoryIdAndCatEquipmentTypeIdAndShippingLineIdAndIdNotOrderById(
                        inputDto.getSubBusinessLocalId(),
                        inputDto.getStructureEstimateFlag(),
                        inputDto.getMachineryEstimateFlag(),
                        inputDto.getCatEquipmentCategoryId(),
                        inputDto.getCatEquipmentTypeId() == null ? 0 : inputDto.getCatEquipmentTypeId(),
                        inputDto.getShippingLineId() == null ? 0 : inputDto.getShippingLineId(),
                        inputDto.getEstimateAutoApprovalConfigId()
                );
        return !existing.isEmpty();
    }

    private Integer getDuplicatedIdContainerParam(EstimateAutoApprovalConfigEditInput.Input inputDto) {
        List<EstimateAutoApprovalConfig> existing = estimateAutoApprovalConfigRepository.findByLocalSubBusinessUnitIdAndStructureEstimateFlagAndMachineryEstimateFlagAndCatEquipmentCategoryIdAndCatEquipmentTypeIdAndShippingLineIdAndIdNotOrderById(
                inputDto.getSubBusinessLocalId(),
                inputDto.getStructureEstimateFlag(),
                inputDto.getMachineryEstimateFlag(),
                inputDto.getCatEquipmentCategoryId(),
                inputDto.getCatEquipmentTypeId() == null ? 0 : inputDto.getCatEquipmentTypeId(),
                inputDto.getShippingLineId() == null ? 0 : inputDto.getShippingLineId(),
                inputDto.getEstimateAutoApprovalConfigId()
        );
        if (!existing.isEmpty()) {
            return existing.get(0).getId();
        }
        return null;
    }

    private boolean checkChassisDuplicate(EstimateAutoApprovalConfigEditInput.Input inputDto) {
        List<EstimateAutoApprovalConfig> existing = estimateAutoApprovalConfigRepository
                .findByLocalSubBusinessUnitIdAndStructureEstimateFlagAndCatEquipmentCategoryIdAndCatEquipmentTypeIdAndCustomerIdAndIdNot(
                        inputDto.getSubBusinessLocalId(),
                        true, // structure_estimate_flag=1 in the else scenario check
                        inputDto.getCatEquipmentCategoryId(),
                        inputDto.getCatEquipmentTypeId() == null ? 0 : inputDto.getCatEquipmentTypeId(),
                        inputDto.getCustomerId() == null ? 0 : inputDto.getCustomerId(),
                        inputDto.getEstimateAutoApprovalConfigId()
                );
        return !existing.isEmpty();
    }

    private Integer getDuplicatedIdChassisCase(EstimateAutoApprovalConfigEditInput.Input inputDto) {
        List<EstimateAutoApprovalConfig> existing = estimateAutoApprovalConfigRepository.findByLocalSubBusinessUnitIdAndStructureEstimateFlagAndCatEquipmentCategoryIdAndCatEquipmentTypeIdAndCustomerIdAndIdNot(
                inputDto.getSubBusinessLocalId(),
                true,
                inputDto.getCatEquipmentCategoryId(),
                inputDto.getCatEquipmentTypeId() == null ? 0 : inputDto.getCatEquipmentTypeId(),
                inputDto.getCustomerId() == null ? 0 : inputDto.getCustomerId(),
                inputDto.getEstimateAutoApprovalConfigId()
        );
        if (!existing.isEmpty()) {
            return existing.get(0).getId();
        }
        return null;
    }

    private void updateEstimateAutoApprovalConfigEntity(EstimateAutoApprovalConfig config, EstimateAutoApprovalConfigEditInput.Input inputDto) {
        // fetch or build references
        // currency = 2 always
        Currency currency = currencyRepository.findById(2)
                .orElseThrow(() -> new IllegalStateException("Default currency with ID=2 not found."));
        config.setCurrency(currency);

        // shipping line (can be null)
        if (inputDto.getShippingLineId() != null) {
            ShippingLine shippingLine = shippingLineRepository.findById(inputDto.getShippingLineId())
                    .orElseThrow(() -> new IllegalArgumentException("ShippingLine not found with ID=" + inputDto.getShippingLineId()));
            config.setShippingLine(shippingLine);
        } else {
            config.setShippingLine(null);
        }

        // customer (can be null)
        if (inputDto.getCustomerId() != null) {
            Company customer = companyRepository.findById(inputDto.getCustomerId())
                    .orElseThrow(() -> new IllegalArgumentException("Customer not found with ID=" + inputDto.getCustomerId()));
            config.setCustomer(customer);
        } else {
            config.setCustomer(null);
        }

        // structure flag, machinery flag
        config.setStructureEstimateFlag(inputDto.getStructureEstimateFlag());
        config.setMachineryEstimateFlag(inputDto.getMachineryEstimateFlag());

        // threshold value
        config.setThresholdValue(inputDto.getThresholdValue());

        // active
        config.setActive(inputDto.getActive());

        // catEquipmentType
        if (inputDto.getCatEquipmentTypeId() != null && inputDto.getCatEquipmentTypeId() != 0) {
            Catalog catEquipmentType = new Catalog(inputDto.getCatEquipmentTypeId());
            config.setCatEquipmentType(catEquipmentType);
        } else {
            config.setCatEquipmentType(null);
        }

        //catEquipmentCategory
        if (inputDto.getCatEquipmentCategoryId() != null && inputDto.getCatEquipmentCategoryId() != 0) {
            Catalog catEquipmentCategory = new Catalog(inputDto.getCatEquipmentCategoryId());
            config.setCatEquipmentCategory(catEquipmentCategory);
        } else {
            config.setCatEquipmentCategory(null);
        }

        // modification user
        User modificationUser = userRepository.findById(inputDto.getUserId())
                .orElseThrow(() -> new IllegalArgumentException("User not found with ID=" + inputDto.getUserId()));
        config.setModificationUser(modificationUser);
        config.setModificationDate(LocalDateTime.now());

        // Save
        estimateAutoApprovalConfigRepository.save(config);
    }
}
