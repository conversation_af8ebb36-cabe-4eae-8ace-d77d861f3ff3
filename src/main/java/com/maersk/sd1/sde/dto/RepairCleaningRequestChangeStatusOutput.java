package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class RepairCleaningRequestChangeStatusOutput {

    @JsonProperty("resp_status")
    private Integer respStatus;

    @JsonProperty("resp_message")
    private String respMessage;

    @JsonProperty("message_list")
    private String messageList;
}
