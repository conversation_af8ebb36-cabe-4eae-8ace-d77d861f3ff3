package com.maersk.sd1.sde.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Input DTO mirroring the stored procedure params
 * Follows the prefix and root structure from reference.
 */
public class EDIConfigurationListInput {

    @Data
    public static class Input {
        /**
         * Optional filters
         * Using wrapper types to allow null checks.
         */
        @JsonProperty("unidad_negocio_id")
        private BigDecimal businessUnitId;

        @JsonProperty("sub_unidad_negocio_id")
        private BigDecimal subBusinessUnitId;

        @JsonProperty("linea_naviera_id")
        private Integer shippingLineId;

        @JsonProperty("sistema_entrega")
        private String systemDelivery;

        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("fecha_registro_min")
        private LocalDateTime registrationDateMin;

        @JsonProperty("fecha_registro_max")
        private LocalDateTime registrationDateMax;

        /**
         * Paging parameters
         */
        @JsonProperty("page")
        @Min(value = 1, message = "page must be 1 or greater")
        private Integer page;

        @JsonProperty("size")
        @Min(value = 1, message = "size must be 1 or greater")
        private Integer size;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}

