package com.maersk.sd1.sde.controller;

import com.maersk.sd1.sde.dto.ListPendingRepairRequestInputDTO;
import com.maersk.sd1.sde.dto.ListPendingRepairRequestOutput;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.service.ListPendingRepairRequestService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ListPendingRepairRequestControllerTest {

    @InjectMocks
    private ListPendingRepairRequestController controller;

    @Mock
    private ListPendingRepairRequestService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void Given_ValidRequest_When_ListPendingCleaningRequest_Then_ReturnsResponseEntityWithOutput() {
        // Arrange
        ListPendingRepairRequestInputDTO.Root request = new ListPendingRepairRequestInputDTO.Root();
        ListPendingRepairRequestInputDTO.Prefix prefix = new ListPendingRepairRequestInputDTO.Prefix();
        ListPendingRepairRequestInputDTO.Input input = new ListPendingRepairRequestInputDTO.Input();
        prefix.setInput(input); // Set the Input object to the Prefix
        request.setPrefix(prefix); // Set the Prefix object to the Root
        ListPendingRepairRequestOutput output = new ListPendingRepairRequestOutput();
        output.setTotal(List.of(List.of(1L)));

        when(service.getPendingCleaningRequests(input)).thenReturn(output);

        // Act
        ResponseEntity<ResponseController<ListPendingRepairRequestOutput>> response = controller.listPendingCleaningRequest(request);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getResult().getTotal().get(0).getFirst());
        verify(service, times(1)).getPendingCleaningRequests(input);
    }

    @Test
    void Given_ServiceThrowsException_When_ListPendingCleaningRequest_Then_ReturnsErrorResponse() {
        // Arrange
        ListPendingRepairRequestInputDTO.Root request = new ListPendingRepairRequestInputDTO.Root();
        ListPendingRepairRequestInputDTO.Prefix prefix = new ListPendingRepairRequestInputDTO.Prefix();
        ListPendingRepairRequestInputDTO.Input input = new ListPendingRepairRequestInputDTO.Input();
        prefix.setInput(input); // Assuming Prefix has a setInput method
        request.setPrefix(prefix); // Assuming Root has a setPrefix method

        when(service.getPendingCleaningRequests(input)).thenThrow(new RuntimeException("Service error"));

        // Act
        ResponseEntity<ResponseController<ListPendingRepairRequestOutput>> response = controller.listPendingCleaningRequest(request);

        // Assert
        assertNotNull(response);
        assertEquals(500, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertNull(response.getBody().getResult().getPendingCleaningRequests());
        assertEquals(0L, response.getBody().getResult().getTotal().get(0).getFirst());
        verify(service, times(1)).getPendingCleaningRequests(input);
    }

}