package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.PrinterListInputDTO;
import com.maersk.sd1.sds.dto.PrinterListOutputDTO;
import com.maersk.sd1.sds.service.PrinterListService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSPrinterServiceImp")
public class PrinterListController {

    private static final Logger logger = LoggerFactory.getLogger(PrinterListController.class);

    private final PrinterListService printerListService;

    @Autowired
    public PrinterListController(PrinterListService printerListService) {
        this.printerListService = printerListService;
    }

    @PostMapping("/sdsprinterList")
    public ResponseEntity<ResponseController<PrinterListOutputDTO>> getPrinterList(@RequestBody PrinterListInputDTO.Root request) {
        logger.info("Received request for sdsPrinterList with input: {}", request);

        try {
            PrinterListOutputDTO response = printerListService.getPrinterList(request);
            logger.info("Successfully retrieved printer list response: {}", response);

            return ResponseEntity.ok(new ResponseController<>(response));
        } catch (Exception e) {
            logger.error("Error occurred while retrieving printer list: {}", e.getMessage(), e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
