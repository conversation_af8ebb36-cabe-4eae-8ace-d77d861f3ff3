package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EirDocumentCargoDetail;
import com.maersk.sd1.sde.dto.DocumentDTO;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface EirDocumentCargoDetailRepository extends JpaRepository<EirDocumentCargoDetail, Integer> {
    @Query("select e from EirDocumentCargoDetail e where e.eir.id = :id and e.active = true")
    EirDocumentCargoDetail findByEirAndActiveTrue(@Param("id") Integer id);

    @Query("select e from EirDocumentCargoDetail e where e.eir.id = :eirId")
    EirDocumentCargoDetail findOneByEirId(@Param("eirId") Integer eirId);

    List<EirDocumentCargoDetail> findByEirId(Integer eirId);

    List<EirDocumentCargoDetail> findByEirIdAndActiveIsTrue(Integer eirId);

    @Query("SELECT e FROM EirDocumentCargoDetail e WHERE e.eir.id = :eirId ORDER BY e.id DESC")
    EirDocumentCargoDetail findTopByEirIdOrderByIdDesc(@Param("eirId") Integer eirId);

    boolean existsByCargoDocumentDetailIdAndActiveTrue(Integer cargoDocumentDetailId);

    @Query("SELECT e FROM EirDocumentCargoDetail e WHERE e.cargoDocumentDetail.id = :cargoDocumentDetail AND e.active = true")
    List<EirDocumentCargoDetail> findByDocumentoCargaDetalleIdAndActivo(@Param("cargoDocumentDetail") Integer cargoDocumentDetail);

    @Query("select E from EirDocumentCargoDetail E " +
            "where E.eir.id IN :eirId")
    List<EirDocumentCargoDetail> findManyByEirId(List<Integer> eirId);


    @Query("""
    SELECT COUNT(1)
    FROM EirDocumentCargoDetail edodx
    JOIN edodx.cargoDocumentDetail dodx
    JOIN edodx.eir eirx
    WHERE dodx.cargoDocument.id = :documentoCargaId
      AND eirx.catEmptyFull.id = :isFull
      AND eirx.catMovement.id = :isGateOut
      AND edodx.active = true
      AND dodx.active = true
      AND eirx.active = true
    """)
    Integer countAssignedContainers(@Param("documentoCargaId") Integer documentoCargaId,
                                    @Param("isFull") Integer isFull,
                                    @Param("isGateOut") Integer isGateOut);

    @Query("SELECT e FROM EirDocumentCargoDetail e " +
            "INNER JOIN e.eir eirx " +
            "WHERE e.eir.id = eirx.id " +
            "AND eirx.catMovement.id = :isGateOut " +
            "AND eirx.catEmptyFull.id = :isEmpty " +
            "AND e.active = true " +
            "AND eirx.active = true")
    List<EirDocumentCargoDetail> findActiveEirDocumentCargoDetails(@Param("isGateOut") Integer isGateOut,
                                                                   @Param("isEmpty") Integer isEmpty);



    @Query("SELECT edcd FROM EirDocumentCargoDetail edcd "
            + "JOIN edcd.eir e "
            + "WHERE edcd.active = true "
            + "  AND e.active = true "
            + "  AND e.container.id = :containerId "
            + "  AND e.subBusinessUnit.id = :subBusinessUnitId "
            + "  AND e.catMovement.alias = '43081' " // gate out alias
            + "  AND e.catEmptyFull.alias = '43083' ") // empty alias
    List<EirDocumentCargoDetail> findActiveGateOutEmpty(@Param("containerId") Integer containerId,
                                                        @Param("subBusinessUnitId") Long subBusinessUnitId);

    // Additional logic for verifying if there's a doc cargo detail for that container in gate-out with no closure.
    // The stored procedure's approach uses eir_documento_carga_detalle to see if there's an existing doc cargo detail.

    @Query("SELECT edcd FROM EirDocumentCargoDetail edcd "
            + "JOIN edcd.eir e "
            + "JOIN edcd.cargoDocumentDetail cdd "
            + "WHERE edcd.active = true "
            + "  AND e.active = true "
            + "  AND e.container.id = :containerId "
            + "  AND e.subBusinessUnit.id = :subBusinessUnitId "
            + "  AND cdd.id = edcd.cargoDocumentDetail.id ")
    List<EirDocumentCargoDetail> findAllValidDocuments(@Param("containerId") Integer containerId,
                                                       @Param("subBusinessUnitId") Long subBusinessUnitId);

    @Query("SELECT DISTINCT cd.cargoDocument.cargoDocument " +
            "FROM EirDocumentCargoDetail ed " +
            "JOIN ed.cargoDocumentDetail cd " +
            "JOIN cd.cargoDocument cdoc " +
            "WHERE ed.eir.id = :eirId " +
            "AND ed.active = true " +
            "AND cdoc.active = true")
    List<String> findCargoDocumentsByEirId(@Param("eirId") Integer eirId);


    @Query("SELECT e.cargoDocumentDetail.id FROM EirDocumentCargoDetail e WHERE e.cargoDocumentDetail.id IN :cargoDocumentDetailIds AND e.active = true")
    List<Integer> findActiveEirCargoDocumentDetailByIds(@Param("cargoDocumentDetailIds") List<Integer> detailIds);

    @Query("""
            SELECT eirdocade FROM EirDocumentCargoDetail eirdocade
            WHERE eirdocade.eir.id = :eirId AND eirdocade.active = true AND eirdocade.cargoDocumentDetail.active = true
            """)
    Optional<EirDocumentCargoDetail> findActiveWithDetailActiveByEirId(Integer eirId);

    @Query("""
            SELECT eirdocade FROM EirDocumentCargoDetail eirdocade
            WHERE eirdocade.eir.id = :eirId AND eirdocade.active = true
            """)
    Optional<EirDocumentCargoDetail> findActiveByEirId(Integer eirId);

    @Modifying
    @Query("""
            UPDATE EirDocumentCargoDetail edcd
            SET edcd.active = false,
            edcd.modificationUser.id = :modificationUserId,
            edcd.modificationDate = CURRENT_TIMESTAMP,
            edcd.traceEirDocDetail = :trace
            WHERE edcd.cargoDocumentDetail.id = :cargoDocumentDetailId
            AND edcd.eir.id = :eirId AND edcd.active = true
            """)
    void disableByCargoDocumentDetailAndEir(Integer cargoDocumentDetailId, Integer eirId, Integer modificationUserId, String trace);

    @Query("SELECT e FROM EirDocumentCargoDetail e WHERE e.eir.id = :eirId AND e.active = :active")
    List<EirDocumentCargoDetail> findByEirIdAndActive(@Param("eirId") Integer eirId, @Param("active") Boolean active);

    @Query("SELECT e FROM EirDocumentCargoDetail e WHERE e.cargoDocumentDetail.id IN :documentoCargaDetalleIds AND e.active = true")
    List<EirDocumentCargoDetail> findByDocumentoCargaDetalleIdsAndActivo(@Param("documentoCargaDetalleIds") List<Integer> documentoCargaDetalleIds);

    @Query("SELECT ed FROM EirDocumentCargoDetail ed "
            + "JOIN ed.eir e "
            + "WHERE e.container.id = :containerId "
            + "  AND e.active = true "
            + "  AND e.localSubBusinessUnit.id = :subBusinessUnitLocalId "
            + "  AND e.catMovement.id = :gateOutId "
            + "  AND e.catEmptyFull.id = :emptyId "
            + "  AND ed.active = true")
    List<EirDocumentCargoDetail> findEirDocsForContainerGateOut(
            @Param("containerId") Integer containerId,
            @Param("subBusinessUnitLocalId") Integer subBusinessUnitLocalId,
            @Param("gateOutId") Integer gateOutId,
            @Param("emptyId") Integer emptyId
    );

    @Query("SELECT new com.maersk.sd1.sde.dto.DocumentDTO(c.cargoDocument, linx.name, CONCAT(cat.description, ' - ')) " +
            "FROM EirDocumentCargoDetail t2 " +
            "INNER JOIN CargoDocumentDetail b ON t2.cargoDocumentDetail.id = b.id " +
            "INNER JOIN CargoDocument c ON b.cargoDocument.id = c.id " +
            "INNER JOIN ShippingLine linx ON c.shippingLine.id = linx.id " +
            "LEFT JOIN Catalog cat ON cat.id = c.catCargoDocumentType.id " +
            "WHERE t2.eir.id = :eirId AND t2.active = true")
    List<DocumentDTO> findDocumentsByEirId(@Param("eirId") Integer eirId);

    @Query("SELECT e.cargoDocumentDetail.cargoDocument.cargoDocument " +
            "FROM EirDocumentCargoDetail e " +
            "WHERE e.eir.id = :eirId ")
    List<String> findCargoDocuments(@Param("eirId") Integer eirId);

    @Query("SELECT cd.cargoDocument.consigneeCompany.legalName " +
            "FROM EirDocumentCargoDetail e " +
            "JOIN e.cargoDocumentDetail cd " +
            "WHERE e.eir.id = :eirId ")
    String findTopConsignee(@Param("eirId") Integer eirId);

    @Query("SELECT cd.cargoDocument.shipperCompany.legalName " +
            "FROM EirDocumentCargoDetail e " +
            "JOIN e.cargoDocumentDetail cd " +
            "WHERE e.eir.id = :eirId " )
    String findTopShipper(@Param("eirId") Integer eirId);

    @Query("SELECT edcd FROM EirDocumentCargoDetail edcd " +
            "WHERE edcd.eir.id = :eirId AND edcd.active = true")
    List<EirDocumentCargoDetail> findCargoDetailByEir(@Param("eirId") Integer eirId);

    @Query(value = "SELECT pncimo.imo_id " +
            "FROM sde.eir_documento_carga_detalle edcd " +
            "     JOIN sds.documento_carga_detalle dcd ON edcd.documento_carga_detalle_id = dcd.documento_carga_detalle_id " +
            "     JOIN sds.documento_carga dca ON dca.documento_carga_id = dcd.documento_carga_id " +
            "     JOIN sds.programacion_nave_detalle pnd ON pnd.programacion_nave_detalle_id = dca.programacion_nave_detalle_id " +
            "     JOIN sds.programacion_nave_contenedor pnc ON pnc.programacion_nave_detalle_id = pnd.programacion_nave_detalle_id AND pnc.contenedor_id = dcd.contenedor_id " +
            "     JOIN sds.programacion_nave_contenedor_imo pncimo ON pncimo.programacion_nave_contenedor_id = pnc.programacion_nave_contenedor_id " +
            "WHERE edcd.eir_id = :eirId " +
            "  AND edcd.activo = 1 " +
            "  AND pnc.activo = 1 " +
            "  AND pncimo.activo = 1 " +
            "GROUP BY pncimo.imo_id",
            nativeQuery = true)
    List<Integer> findImoIdsFromJoins(@Param("eirId") Integer eirId);

    @Query("SELECT lin.shippingLineCompany " +
            "FROM EirDocumentCargoDetail eirDoc " +
            "JOIN eirDoc.cargoDocumentDetail docDetail " +
            "JOIN docDetail.cargoDocument doc " +
            "JOIN doc.shippingLine lin on lin.id = doc.shippingLine.id " +
            "WHERE eirDoc.id = :eirId")
    String findShippingLineNameByEirId(@Param("eirId") Integer eirId);

    @Query("SELECT e, cp FROM EirDocumentCargoDetail e " +
            "inner join CargoDocumentDetail cdd on cdd.id = e.cargoDocumentDetail.id " +
            "inner join ContainerPreassignment cp on cp.cargoDocumentDetail.id = cdd.id " +
            "WHERE e.eir.id = :eirId AND e.active = true")
    List<Object[]> findByEirIdJoinDocumentCargoDetail(Integer eirId);

    @Query("SELECT e, cp FROM EirDocumentCargoDetail e " +
            "inner join CargoDocumentDetail cdd on cdd.id = e.cargoDocumentDetail.id " +
            "inner join ContainerPreassignment cp on cp.cargoDocumentDetail.id = cdd.id " +
            "WHERE e.eir.id = :eirId AND e.active = true " +
            "order by cp.registrationDate desc")
    List<Object[]> findTop1ByEirIdJoinDocumentCargoDetailJoinContainer(@Param("eirId") Integer eirId);

    @Query("select edc from EirDocumentCargoDetail edc where edc.eir.id = :eirId")
    Optional<EirDocumentCargoDetail> findTop1ByEirId(@Param("eirId") @NotNull Integer eirId);


}