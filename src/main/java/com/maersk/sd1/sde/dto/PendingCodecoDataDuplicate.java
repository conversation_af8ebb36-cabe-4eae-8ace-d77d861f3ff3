package com.maersk.sd1.sde.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PendingCodecoDataDuplicate {

    private String activityDate;
    private Integer ediCodecoId;
    private String lineAdo;
    private String movement;
    private String emptyOrFull;
    private Integer eirId;
    private String containerNumber;
    private String vesselName;
    private String voyageNumber;
    private BigDecimal containerWeight;
    private Integer weightMeasureCategoryId;
    private String isoContainerCode;
    private String shippingSeal;
    private String customerSeal;
    private String customsSeal;
    private String otherSeals;
    private String cargoDocument;
    private String masterCargoDocument;
    private String containerStatus;
    private String remarks;
    private String vehiclePlate;
    private String vesselCallSign;
    private String vesselImoNumber;
    private String customerName;
    private String customerDocument;
    private Integer originCategoryId;
    private Boolean hasBoxDamage;
    private Boolean hasMachineDamage;
    private Boolean isRefrigeratedContainer;
    private Boolean requiresInspection;
    private String carrierOperatorCode;
    private Integer vesselScheduleDetailId;
    private Integer operationCategoryId;
    private String dischargePort;
    private String loadingPort;
    private Integer cargoDocumentTypeId;
    private Integer truckCompanyId;
    private String truckCompanyName;
    private LocalDateTime ediUpdateTimestamp;
    private String transactionType;
    private String eirComment;
    private String temperature;
    private Integer temperatureMeasureCategoryId;
    private String twrNumber;
}
