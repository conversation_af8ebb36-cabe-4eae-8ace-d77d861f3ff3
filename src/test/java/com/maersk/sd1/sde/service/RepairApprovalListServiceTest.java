package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.dto.RepairApprovalListInput;
import com.maersk.sd1.sde.dto.RepairApprovalListOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RepairApprovalListServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private RepairApprovalListService repairApprovalListService;

    private RepairApprovalListInput.Input validStructureInput;
    private RepairApprovalListInput.Input validMachineryInput;
    private List<Map<String, Object>> mockResultSet;

    @BeforeEach
    void setUp() {
        // Setup valid structure repair input
        validStructureInput = new RepairApprovalListInput.Input();
        validStructureInput.setRepairTypeId(48030); // Structure repair
        validStructureInput.setSubBusinessUnitId(1);
        validStructureInput.setSubBusinessUnitLocalId(2);
        validStructureInput.setLanguageId(1);
        validStructureInput.setPage(0);
        validStructureInput.setSize(10);

        // Setup valid machinery repair input
        validMachineryInput = new RepairApprovalListInput.Input();
        validMachineryInput.setRepairTypeId(48031); // Machinery repair
        validMachineryInput.setSubBusinessUnitId(1);
        validMachineryInput.setSubBusinessUnitLocalId(2);
        validMachineryInput.setLanguageId(1);
        validMachineryInput.setPage(0);
        validMachineryInput.setSize(10);

        // Setup mock result set
        mockResultSet = new ArrayList<>();
        Map<String, Object> row = new HashMap<>();
        row.put("TipoReparacion", "Structure");
        row.put("eir_id", 123);
        row.put("linea_naviera", "Maersk");
        row.put("contenedor", "MAEU1234567");
        row.put("TipoCnt", "40HC");
        row.put("Venta", "Sale");
        row.put("ZonaActual", "Zone A");
        row.put("Check1_Seleccionable", 1);
        row.put("SiguienteZona_Check1", "Zone B");
        row.put("Check2_Seleccionable", 0);
        row.put("SiguienteZona_Check2", "Zone C");
        row.put("ObsAprobacion", "Observation");
        row.put("Nave", "Ship");
        row.put("Viaje", "Voyage");
        row.put("FechaAprobacion", "2023-01-01");
        row.put("UsuarioAprobacion", "User");
        row.put("usuario_registro_nombres", "John");
        row.put("usuario_registro_apellidos", "Doe");
        row.put("fecha_eir", "2023-01-01");
        row.put("NombreLocal", "Local");
        mockResultSet.add(row);
    }

    @Test
    void testListRepairApprovals_StructureRepair_Success() {
        // Arrange
        when(jdbcTemplate.queryForList(anyString())).thenReturn(mockResultSet);

        // Act
        RepairApprovalListOutput output = repairApprovalListService.listRepairApprovals(validStructureInput);

        // Assert
        assertNotNull(output);
        assertEquals(1, output.getResultadoLista().size());
        assertEquals(1, output.getResultado().size());
        assertEquals(1L, output.getResultado().get(0).get(0));
        verify(jdbcTemplate).queryForList(contains("sde.listar_aprobacion_rep_estructura"));
    }

    @Test
    void testListRepairApprovals_MachineryRepair_Success() {
        // Arrange
        when(jdbcTemplate.queryForList(anyString())).thenReturn(mockResultSet);

        // Act
        RepairApprovalListOutput output = repairApprovalListService.listRepairApprovals(validMachineryInput);

        // Assert
        assertNotNull(output);
        assertEquals(1, output.getResultadoLista().size());
        verify(jdbcTemplate).queryForList(contains("sde.listar_aprobacion_rep_maquinaria"));
    }

    @Test
    void testListRepairApprovals_InvalidRepairTypeId_ReturnsEmptyResult() {
        // Arrange
        RepairApprovalListInput.Input invalidInput = new RepairApprovalListInput.Input();
        invalidInput.setRepairTypeId(99999); // Invalid repair type ID

        // Act
        RepairApprovalListOutput output = repairApprovalListService.listRepairApprovals(invalidInput);

        // Assert
        assertNotNull(output);
        assertTrue(output.getResultadoLista() == null || output.getResultadoLista().isEmpty());
        verify(jdbcTemplate, never()).queryForList(anyString());
    }

    @Test
    void testListRepairApprovals_ExceptionThrown_ReturnsEmptyResult() {
        // Arrange
        when(jdbcTemplate.queryForList(anyString())).thenThrow(new RuntimeException("Database error"));

        // Act
        RepairApprovalListOutput output = repairApprovalListService.listRepairApprovals(validStructureInput);

        // Assert
        assertNotNull(output);
        assertTrue(output.getResultadoLista() == null || output.getResultadoLista().isEmpty());
    }
}