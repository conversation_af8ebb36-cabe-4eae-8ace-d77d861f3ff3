package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Output DTO collecting all data returned by
 * the logic previously handled by the stored procedure.
 */
@Data
public class RoleObtainOutputDTO {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    // 1) 'rol' data
    @JsonProperty("role_info")
    private RoleDto roleInfo;

    // 2) 'rol_menu' data
    @JsonProperty("role_menu_list")
    private List<RoleMenuDto> roleMenuList;

    // 3) 'rol_menu_accion' data
    @JsonProperty("role_menu_action_list")
    private List<RoleMenuActionDto> roleMenuActionList;

    // 4) 'rol_unidad_negocio' data
    @JsonProperty("role_business_unit_list")
    private List<RoleBusinessUnitDto> roleBusinessUnitList;

    // 5) Distinct systems for this role's menus
    @JsonProperty("system_list")
    private List<SystemMenuDto> systemList;

    // 6) Filtered business units for the above systems
    @JsonProperty("business_unit_list")
    private List<BusinessUnitDto> businessUnitList;

    /**
     * Dto for Role.
     * Matches: SELECT rol_id, nombre, estado, id, menu_proyecto_defecto_id
     */
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class RoleDto {
        @JsonProperty("rol_id")
        private Integer roleId;

        @JsonProperty("nombre")
        private String roleName;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("id")
        private String uniqueRoleCode;

        @JsonProperty("menu_proyecto_defecto_id")
        private Integer defaultMenuId;
    }

    /**
     * Dto for RoleMenu.
     * Matches: SELECT rol_id, menu_id
     */
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class RoleMenuDto {
        @JsonProperty("rol_id")
        private Integer roleId;

        @JsonProperty("menu_id")
        private Integer menuId;
    }

    /**
     * Dto for RoleMenuAction.
     * Matches: SELECT rol_id, menu_id, tipo_accion_id
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RoleMenuActionDto {
        @JsonProperty("rol_id")
        private Integer roleId;

        @JsonProperty("menu_id")
        private Integer menuId;

        @JsonProperty("tipo_accion_id")
        private Integer typeActionId;
    }

    /**
     * Dto for RoleBusinessUnit.
     * Matches: SELECT rol_id, unidad_negocio_id
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RoleBusinessUnitDto {
        @JsonProperty("rol_id")
        private Integer roleId;

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;
    }

    /**
     * Dto for the distinct systems.
     * Matches part of the logic: SIS.descripcion, SIS.sistema_id, SIS.icono
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SystemMenuDto {
        @JsonProperty("sistema_id")
        private Integer systemId;

        @JsonProperty("sistema_descripcion")
        private String systemDescription;

        @JsonProperty("sistema_icono")
        private String systemIcon;
    }

    /**
     * Dto for the business units returned.
     * Matches the final query's columns:
     *  unidad_negocio_id, unidad_negocio_nombre, unidad_negocio_padre_id, unidad_negocio_padre_nombre,
     *  unidad_negocio_padre_padre_id, sistema_id
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BusinessUnitDto {
        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("unidad_negocio_nombre")
        private String businessUnitName;

        @JsonProperty("unidad_negocio_padre_id")
        private Integer parentBusinessUnitId;

        @JsonProperty("unidad_negocio_padre_nombre")
        private String parentBusinessUnitName;

        @JsonProperty("unidad_negocio_padre_padre_id")
        private Integer parentOfParentBusinessUnitId;

        @JsonProperty("sistema_id")
        private Integer systemId;
    }
}
