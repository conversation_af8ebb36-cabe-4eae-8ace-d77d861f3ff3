package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EmrInspectionPhoto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface EmrInspectionPhotoRepository extends JpaRepository<EmrInspectionPhoto, Integer> {

    @Query("SELECT ep FROM EmrInspectionPhoto ep " +
            "WHERE ep.emrInspection.id = :inspectionId AND ep.active = true")
    List<EmrInspectionPhoto> findPhotosByInspection(@Param("inspectionId") Integer inspectionId);
}