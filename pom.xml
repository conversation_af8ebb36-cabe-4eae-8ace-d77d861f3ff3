<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.3.5</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>Maersk-Global</groupId>
	<artifactId>ModuleSDE</artifactId>
	<version>snapshot</version>
	<name>ModuleSDE</name>
	<properties>
        <java.version>23</java.version>

        <spring-cloud.version>2023.0.3</spring-cloud.version>
        <chaos-monkey-spring-boot.version>3.1.0</chaos-monkey-spring-boot.version>
        <jolokia-core.version>1.7.1</jolokia-core.version>

        <!-- Workaround for resolving the issue while using the provided lombok dependency in upgraded Java version.
        Should be removed while future upgrading-->
		<!--<lombok.version>1.18.30</lombok.version>-->

        <docker.image.registry>acrsd1dev.azurecr.io</docker.image.registry>
        <docker.image.exposed.port>9090</docker.image.exposed.port>
        <docker.image.dockerfile.dir>${basedir}</docker.image.dockerfile.dir>
        <!-- podman is also supported -->
        <container.executable>docker</container.executable>
        <!-- By default, the OCI image is build for the linux/amd64 platform -->
        <!-- For Apple Silicon M2 Chip you have to change it to the linux/arm64 -->
        <container.platform>linux/amd64</container.platform>
        <!-- The -load option is a shortcut for or -output=type=docker -->
        <!-- Could be changed by the -push option !-->
        <container.build.extraarg>--load</container.build.extraarg>
		<poi.version>5.2.3</poi.version>
		        <!-- Add Sonar properties -->
        <sonar.host.url>${env.SONAR_HOST_URL:http://localhost:9000}</sonar.host.url>
        <sonar.projectKey>${project.artifactId}</sonar.projectKey>
        <sonar.projectName>${project.name}</sonar.projectName>
        <sonar.projectVersion>${project.version}</sonar.projectVersion>
        <sonar.sourceEncoding>UTF-8</sonar.sourceEncoding>
        <sonar.language>java</sonar.language>
        
        <!-- Source paths -->
        <sonar.sources>src/main/java/com/maersk/sd1/sde</sonar.sources>
        <sonar.tests>src/test/java</sonar.tests>
        
        <!-- Exclusions -->
        <sonar.exclusions>
            **/generated/**,
            **/generated-sources/**,
            **/src/test/**,
            **/model/**,
            **/dto/**,
            **/entity/**
        </sonar.exclusions>
        
        <sonar.coverage.exclusions>
            **/generated/**,
            **/generated-sources/**,
            **/model/**,
            **/dto/**,
            **/entity/**,
            **/exception/**,
            **/configuration/**
        </sonar.coverage.exclusions>
        
        <!-- Java specific settings -->
        <sonar.java.source>${java.version}</sonar.java.source>
        <sonar.java.binaries>target/classes</sonar.java.binaries>
        <sonar.java.test.binaries>target/test-classes</sonar.java.test.binaries>
        
        <!-- Coverage configuration -->
        <sonar.coverage.jacoco.xmlReportPaths>${project.build.directory}/site/jacoco/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>

    </properties>
	<dependencies>
	<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-registry-prometheus</artifactId>
</dependency>
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-tracing</artifactId>
</dependency>
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-tracing-bridge-brave</artifactId>
</dependency>
<dependency>
    <groupId>io.zipkin.reporter2</groupId>
    <artifactId>zipkin-reporter-brave</artifactId>
</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.11.0</version>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
		</dependency>
		<dependency>
			<groupId>com.sendgrid</groupId>
			<artifactId>sendgrid-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.konghq</groupId>
			<artifactId>unirest-java</artifactId>
			<version>2.3.14</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>30.1.1-jre</version>
		</dependency>
		<dependency>
    <groupId>jakarta.servlet</groupId>
    <artifactId>jakarta.servlet-api</artifactId>
    <version>6.0.0</version>
    <scope>provided</scope>
    </dependency>
<dependency>
    <groupId>jakarta.persistence</groupId>
    <artifactId>jakarta.persistence-api</artifactId>
    <version>3.1.0</version>
</dependency>
		<dependency>
			<groupId>maersklogistics</groupId>
			<artifactId>ohRest</artifactId>
			<version>1.0.5</version>
		</dependency>
		<dependency>
			<groupId>maersklogistics</groupId>
			<artifactId>ohrestconnection</artifactId>
			<version>1.0.9</version>
		</dependency>
		<dependency>
			<groupId>maersklogistics</groupId>
			<artifactId>ohazure</artifactId>
			<version>1.2.0</version>
		</dependency>

<!-- Coverage configuration -->					
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.30</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
			<version>2.6.0</version>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20240303</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.17.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5</artifactId>

		</dependency>
		<dependency>
			<groupId>org.skyscreamer</groupId>
			<artifactId>jsonassert</artifactId>
			<version>1.5.3</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>com.vaadin.external.google</groupId>
					<artifactId>android-json</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.6</version>
		</dependency>
		<dependency>
			<groupId>com.mashape.unirest</groupId>
			<artifactId>unirest-java</artifactId>
			<version>1.4.9</version>
    </dependency>
		<dependency>
			<groupId>org.mapstruct</groupId>
			<artifactId>mapstruct</artifactId>
			<version>1.6.3</version>
		</dependency>
				<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
		<dependency>
			<groupId>org.dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>2.1.3</version>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>${java.version}</source>
					<target>${java.version}</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>1.18.30</version>
						</path>
						<path>
							<groupId>org.mapstruct</groupId>
							<artifactId>mapstruct-processor</artifactId>
							<version>1.6.3</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<image>
						<name>${docker.image.registry}/sd1-maersk-modulesde-dev:v1.5</name>
						<publish>false</publish>
						<env>
							<BP_JVM_VERSION>${java.version}</BP_JVM_VERSION>
						</env>
					</image>
					<layers>
						<enabled>true</enabled>
					</layers>
				</configuration>
			</plugin>
					<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>0.8.12</version>
				<executions>
					<execution>
						<id>prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>report</id>
						<phase>verify</phase>
						<goals>
							<goal>report</goal>
						</goals>
						<configuration>
							<excludes>
								<!-- Exclude model package -->
								<exclude>com/maersk/sd1/common/**/*</exclude>
								<exclude>com/maersk/sd1/adm/**/*</exclude>
								<exclude>com/maersk/sd1/dbo/**/*</exclude>
								<exclude>com/maersk/sd1/ges/**/*</exclude>
								<exclude>com/maersk/sd1/seg/**/*</exclude>
								<exclude>com/maersk/sd1/sdh/**/*</exclude>								
								<exclude>com/maersk/sd1/sdf/**/*</exclude>								
								<exclude>com/maersk/sd1/sdg/**/*</exclude>									
								<exclude>com/maersk/sd1/sds/**/*</exclude>
								<exclude>com/maersk/sd1/sdy/**/*</exclude>
								<exclude>com/apm/business/**/*</exclude>
								<exclude>com/maersk/sd1/sde/dto/**/*</exclude>
								<exclude>com/maersk/sd1/sde/exception/**/*</exclude>
								<exclude>com/maersk/sd1/sde/repository/**/*</exclude>
								<exclude>com/maersk/sd1/sde/serializer/**/*</exclude>
				        </excludes>
						</configuration>
					</execution>
					<execution>
						<id>check</id>
						<goals>
							<goal>check</goal>
						</goals>
						<configuration>
							<rules>
								<rule>
									<element>BUNDLE</element>
									<limits>
										<limit>
											<counter>LINE</counter>
											<value>COVEREDRATIO</value>
											<minimum>0.60</minimum>
										</limit>
									</limits>
								</rule>
							</rules>
							<excludes>
								<!-- Exclude model package -->
								<exclude>com/maersk/sd1/common/**/*</exclude>
								<exclude>com/maersk/sd1/adm/**/*</exclude>
								<exclude>com/maersk/sd1/dbo/**/*</exclude>
								<exclude>com/maersk/sd1/ges/**/*</exclude>
								<exclude>com/maersk/sd1/seg/**/*</exclude>
								<exclude>com/maersk/sd1/sdh/**/*</exclude>
								<exclude>com/maersk/sd1/sdf/**/*</exclude>
								<exclude>com/maersk/sd1/sdg/**/*</exclude>
								<exclude>com/maersk/sd1/sds/**/*</exclude>
								<exclude>com/maersk/sd1/sdy/**/*</exclude>
								<exclude>com/apm/business/**/*</exclude>
								<exclude>com/maersk/sd1/sde/dto/**/*</exclude>
								<exclude>com/maersk/sd1/sde/exception/**/*</exclude>
								<exclude>com/maersk/sd1/sde/repository/**/*</exclude>
								<exclude>com/maersk/sd1/sde/serializer/**/*</exclude>
							</excludes>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.sonarsource.scanner.maven</groupId>
				<artifactId>sonar-maven-plugin</artifactId>
				<version>3.10.0.2594</version>
			</plugin>
		</plugins>
	</build>
		<repositories>
		<repository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
		</repository>
		<repository>
			<id>spring-snapshots</id>
			<name>Spring Snapshots</name>
			<url>https://repo.spring.io/snapshot</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>mvn-repo</id>
			<url>https://maven.pkg.github.com/Maersk-Global/ohrest</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
		</pluginRepository>
		<pluginRepository>
			<id>spring-snapshots</id>
			<name>Spring Snapshots</name>
			<url>https://repo.spring.io/snapshot</url>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>



</project>
