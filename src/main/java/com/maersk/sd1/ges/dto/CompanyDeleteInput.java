package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CompanyDeleteInput {

    @Data
    public static class Input {
        @JsonProperty("empresa_id")
        @NotNull(message = "empresa_id cannot be null")
        private Integer empresaId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}

