package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TemplateGetInputDTO {
    @Data
    public static class Input {
        @JsonProperty("template_name")
        private String templateName;

        @JsonProperty("language_id")
        private Integer languageId;
    }
    @Data
    public static class Prefix {
        @JsonProperty("F")
        private TemplateGetInputDTO.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private TemplateGetInputDTO.Prefix prefix;
    }
}
