package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class RoleObtainerVariousOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("roles")
    private List<RoleDTO> roles;

    @JsonProperty("role_menus")
    private List<RoleMenuDTO> roleMenus;

    @JsonProperty("role_business_units")
    private List<RoleBusinessUnitDTO> roleBusinessUnits;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RoleDTO {
        @JsonProperty("rol_id")
        private Integer rolId;

        @JsonProperty("nombre")
        private String name;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("id")
        private String id1;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RoleMenuDTO {
        @JsonProperty("rol_id")
        private Integer rolId;

        @JsonProperty("menu_id")
        private Integer menuId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RoleBusinessUnitDTO {
        @JsonProperty("rol_id")
        private Integer rolId;

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("nombre")
        private String businessUnitName;
    }
}