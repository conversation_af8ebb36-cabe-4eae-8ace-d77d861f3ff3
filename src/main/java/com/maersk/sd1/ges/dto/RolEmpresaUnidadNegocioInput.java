package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class RolEmpresaUnidadNegocioInput {

    private RolEmpresaUnidadNegocioInput() {
        // Private constructor to hide the implicit public one
    }

    public static RolEmpresaUnidadNegocioInput create() {
        return new RolEmpresaUnidadNegocioInput();
    }

    @Data
    public static class Input {
        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;

        private Input() {
            // Private constructor to hide the implicit public one
        }

        public static Input create() {
            return new Input();
        }
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;

        private Prefix() {
            // Private constructor to hide the implicit public one
        }

        public static Prefix create() {
            return new Prefix();
        }
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;

        private Root() {
            // Private constructor to hide the implicit public one
        }

        public static Root create() {
            return new Root();
        }
    }
}
