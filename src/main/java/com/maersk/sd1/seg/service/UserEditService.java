package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.CompanyUser;
import com.maersk.sd1.common.model.CompanyUserId;
import com.maersk.sd1.common.model.OauthAccessHistory;
import com.maersk.sd1.common.model.OauthAccessToken;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.seg.dto.UserEditOutputDTO;
import com.maersk.sd1.common.repository.CompanyUserRepository;
import com.maersk.sd1.common.repository.OauthAccessTokenRepository;
import com.maersk.sd1.common.repository.OauthAccessHistoryRepository;
import com.maersk.sd1.common.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@RequiredArgsConstructor
@Service
public class UserEditService {

    private static final Logger logger = LogManager.getLogger(UserEditService.class);

    private final UserRepository userRepository;
    private final CompanyUserRepository companyUserRepository;
    private final OauthAccessTokenRepository oauthAccessTokenRepository;
    private final OauthAccessHistoryRepository oauthAccessHistoryRepository;

    @Transactional
    public UserEditOutputDTO editUser(Integer userId,
                                      String newId,
                                      String editId,
                                      String mail,
                                      String names,
                                      String firstLastName,
                                      String secondLastName,
                                      Integer companyId,
                                      String userStatus,
                                      Integer userModificationId,
                                      List<Integer> companyIds) {
        UserEditOutputDTO output = new UserEditOutputDTO();
        try {
            // 1. Check if we are in 'create' mode (editId='1') for alias duplicates
            if ("1".equals(editId) && newId != null && Boolean.TRUE.equals(userRepository.existsByAliasIgnoreCase(newId.trim()))) {
                output.setRespEstado(2);
                output.setRespMensaje("El Id existe, ingrese otro");
                return output;
            }


            // 2. If editId != '1' and newId is null, we must fetch the current alias from DB
            String alias = newId;
            if (!"1".equals(editId) && (newId == null || newId.trim().isEmpty())) {
                User existingUser = userRepository.findById(userId).orElse(null);
                if (existingUser != null) {
                    alias = existingUser.getAlias();
                }
            }

            // 3. Perform the update on the user
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                // user does not exist, can't update
                output.setRespEstado(0);
                output.setRespMensaje("Usuario no encontrado");
                return output;
            }

            // Convert mail and alias to uppercase
            String upperMail = mail == null ? null : mail.toUpperCase();
            String upperAlias = alias == null ? null : alias.toUpperCase();

            user.setAlias(upperAlias);
            user.setMail(upperMail);
            user.setNames(names);
            user.setFirstLastName(firstLastName);
            user.setSecondLastName(secondLastName);
            if (companyId != null) {
                Company company = new Company();
                company.setId(companyId);
                user.setCompany(company);
            } else {
                user.setCompany(null);
            }

            // set user status
            if (userStatus != null && !userStatus.isEmpty()) {
                user.setStatus(userStatus.charAt(0));
            }

            // set modification user
            if (userModificationId != null) {
                User userMod = new User(userModificationId);
                user.setModificationUser(userMod);
            }

            user.setModificationDate(LocalDateTime.now());

            userRepository.save(user);

            // 4. delete from empresa_usuario for that user
            companyUserRepository.deleteByIdUserId(userId);

            // 5. re-insert with the new company list
            if (companyIds != null && !companyIds.isEmpty()) {
                List<CompanyUser> listToInsert = new ArrayList<>();
                for (Integer cId : companyIds) {
                    CompanyUser cu = new CompanyUser();
                    CompanyUserId cuid = new CompanyUserId();
                    cuid.setCompanyId(cId);
                    cuid.setUserId(userId);
                    cu.setId(cuid);
                    cu.setUser(user);
                    Company company = new Company();
                    company.setId(cId);
                    cu.setCompany(company);
                    listToInsert.add(cu);
                }
                companyUserRepository.saveAll(listToInsert);
            }

            // 6. if userStatus = '0', update oauth tokens -> logout
            if ("0".equals(userStatus)) {
                // user id as string
                String pUserName = userId.toString();

                // find tokens
                List<OauthAccessToken> tokens = oauthAccessTokenRepository.findByUserName(pUserName);
                if (tokens != null && !tokens.isEmpty()) {
                    List<String> authIds = new ArrayList<>();
                    for (OauthAccessToken token : tokens) {
                        authIds.add(token.getAuthenticationId());
                    }

                    // find in oauth_access_history where authenticationId in (?) and logoutDate is null
                    List<OauthAccessHistory> sessions = oauthAccessHistoryRepository.findByAuthenticationIdInAndLogoutDateIsNull(authIds);
                    oauthAccessHistoryRepository.bulkLogout(sessions);
                }
            }

            // success
            output.setRespEstado(1);
            output.setRespMensaje("Usuario actualizado correctamente");
            return output;
        } catch (Exception e) {
            logger.error("Error updating user", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return output;
        }
    }
}

