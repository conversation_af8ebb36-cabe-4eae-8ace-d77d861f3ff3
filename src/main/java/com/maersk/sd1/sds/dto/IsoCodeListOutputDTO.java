package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;


@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class IsoCodeListOutputDTO {
    @JsonProperty("total")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<IsoCodeListOutputTotal> total;

    @JsonProperty("records")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<IsoCodeListRecords> records;
}
