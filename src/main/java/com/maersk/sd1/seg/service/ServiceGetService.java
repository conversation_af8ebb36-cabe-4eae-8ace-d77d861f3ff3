package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.controller.dto.ServiceGetOutput;
import com.maersk.sd1.seg.controller.dto.ServiceGetOutput.ServiceData;
import com.maersk.sd1.seg.controller.dto.ServiceGetOutput.RolData;
import com.maersk.sd1.seg.dto.ServiceGetServiceDto;
import com.maersk.sd1.seg.dto.ServiceGetRoleDto;
import com.maersk.sd1.common.repository.ServiceRepository;
import com.maersk.sd1.common.repository.RoleServiceRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class ServiceGetService {

    private static final Logger logger = LogManager.getLogger(ServiceGetService.class);

    private final ServiceRepository serviceRepository;
    private final RoleServiceRepository roleServiceRepository;

    public ServiceGetService(ServiceRepository serviceRepository, RoleServiceRepository roleServiceRepository) {
        this.serviceRepository = serviceRepository;
        this.roleServiceRepository = roleServiceRepository;
    }

    @Transactional(readOnly = true)
    public ServiceGetOutput getServicioData(Integer serviceId) {
        ServiceGetOutput output = new ServiceGetOutput();
        try {
            ServiceGetServiceDto serviceDto = serviceRepository.findServicioById(serviceId)
                    .orElseThrow(() -> new RuntimeException("Service not found for id: " + serviceId));

            ServiceData serviceData = new ServiceData();
            serviceData.setServiceId(serviceDto.getServicioId());
            serviceData.setNames(serviceDto.getNombre());
            serviceData.setProctectedIndicator(serviceDto.getIndicadorProtegido());
            serviceData.setStatus(serviceDto.getEstado());
            serviceData.setUserRegisterId(serviceDto.getUsuarioRegistroId());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            if (serviceDto.getFechaRegistro() != null) {
                serviceData.setFetchRegister(serviceDto.getFechaRegistro().format(formatter));
            }

            serviceData.setUserModificationId(serviceDto.getUsuarioModificacionId());
            if (serviceDto.getFechaModificacion() != null) {
                serviceData.setFetchModification(serviceDto.getFechaModificacion().format(formatter));
            }

            List<ServiceGetRoleDto> roleDtos = roleServiceRepository.findRolesByServiceId(serviceId);
            List<RolData> rolDataList = roleDtos.stream().map(r -> {
                RolData rd = new RolData();
                rd.setRolId(r.getRolId());
                rd.setServiceId(r.getServiceId());
                rd.setNames(r.getNombre());
                return rd;
            }).toList();

            output.setService(serviceData);
            output.setListRoles(rolDataList);
            output.setRespStatus(1);
            output.setRespMessage("Success");
        } catch (Exception e) {
            logger.error("Error retrieving service and roles.", e);
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
        }
        return output;
    }
}