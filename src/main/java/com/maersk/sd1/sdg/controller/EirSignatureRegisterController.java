package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdg.controller.dto.EirSignatureRegisterInput;
import com.maersk.sd1.sdg.controller.dto.EirSignatureRegisterOutput;
import com.maersk.sd1.sdg.service.EirSignatureRegisterService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;


@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDG/module/sdg/SDGtruckDepartureServiceImp")
public class EirSignatureRegisterController {

    private static final Logger logger = LogManager.getLogger(EirSignatureRegisterController.class.getName());

    private final EirSignatureRegisterService eirSignatureRegisterService;

    @PostMapping("/sdgeirSignatureRegister")
    public ResponseEntity<ResponseController<EirSignatureRegisterOutput>> sdgeirSignatureRegister(@RequestBody @Valid EirSignatureRegisterInput.Root request) {
        try {
            logger.info("Request received sdgeirSignatureRegister: {}", request);
            EirSignatureRegisterInput.Input input = request.getPrefix().getInput();
            EirSignatureRegisterOutput eirSignatureRegisterOutput = eirSignatureRegisterService.registerEirSignature(input.getEirId(), input.getDriverDateSignature(), input.getUrlDriverSignature(),
                    input.getUrlInspectorSignature(), input.getUserRegistrationId(), input.getUrlSignatureChassisInspector(), input.getLanguageId());
            return ResponseEntity.ok(new ResponseController<>(eirSignatureRegisterOutput));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            EirSignatureRegisterOutput eirSignatureRegisterOutput = new EirSignatureRegisterOutput();
            eirSignatureRegisterOutput.setRespMensaje(e.toString());
            eirSignatureRegisterOutput.setRespEstado(0);
            return ResponseEntity.status(500).body(new ResponseController<>(eirSignatureRegisterOutput));
        }
    }
}
