package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.CompanyConfigId;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "empresa_config", schema = "ges")
public class CompanyConfig {
    @EmbeddedId
    private CompanyConfigId id;

    @MapsId("companyId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "empresa_id", nullable = false)
    private Company company;

    @MapsId("typeConfigurationId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "tipo_configuracion_id", nullable = false)
    private Catalog catConfigurationType;

    @Size(max = 100)
    @NotNull
    @Column(name = "valor", nullable = false, length = 100)
    private String value;

}