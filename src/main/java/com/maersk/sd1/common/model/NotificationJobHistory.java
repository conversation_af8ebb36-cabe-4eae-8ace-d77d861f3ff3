package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "notificacion_job_historial", schema = "seg")
public class NotificationJobHistory {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "notificacion_job_historial_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "notificacion_job_id", nullable = false)
    private NotificationJob notificacionJob;

    @NotNull
    @Column(name = "fecha", nullable = false)
    private LocalDateTime date;

    @Column(name = "indicador_email_ejecutado")
    private Character indicatorExecutedEmail;

    @Column(name = "indicador_web_ejecutado")
    private Character indicatorWebExecuted;

    @Column(name = "indicador_app_ejecutado")
    private Character indicatorAppExecuted;

}