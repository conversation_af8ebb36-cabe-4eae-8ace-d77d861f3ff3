package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sde.dto.BuscarCedexMercXCodigoObservableInput;
import com.maersk.sd1.sde.dto.BuscarCedexMercXCodigoObservableOutput;
import com.maersk.sd1.common.repository.CedexMercRepository;
import com.maersk.sd1.common.model.CedexMerc;
import com.maersk.sd1.sde.dto.CedexMercDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * Service implementing the logic replicated from buscar_cedex_merc_xcodigo_observable.
 * Applies all conditions and queries the repository.
 */
@Service
public class BuscarCedexMercXCodigoObservableService {

    private static final Logger logger = LogManager.getLogger(BuscarCedexMercXCodigoObservableService.class);

    private final CedexMercRepository cedexMercRepository;
    private final CatalogRepository catalogRepository;

    @Autowired
    public BuscarCedexMercXCodigoObservableService(CedexMercRepository cedexMercRepository,
                                                   CatalogRepository catalogRepository) {
        this.cedexMercRepository = cedexMercRepository;
        this.catalogRepository = catalogRepository;
    }

    /**
     * Main method to filter CedexMerc records.
     *
     * @param input The input DTO with all fields needed for filtering.
     * @return An output DTO that includes the list of matching records.
     */
    @Transactional(readOnly = true)
    public List<BuscarCedexMercXCodigoObservableOutput> buscarCedexMerc(BuscarCedexMercXCodigoObservableInput.Input input) {
        List<BuscarCedexMercXCodigoObservableOutput> itemOutputs = new ArrayList<>();
        try {
            Long subUnidadNegocioId = input.getSubUnidadNegocioId();
            String cedexMercCode = input.getCedexMercCodigo();
            String cedexMercDescr = input.getCedexMercDescripcion();
            Long catTipoEstimadoId = input.getCatTipoEstimadoId();
            String esReeferInput = input.getEsReefer();

            Integer isEquipmentTypeContainer = catalogRepository.findIdByAlias("sd1_equipment_category_container");


            Boolean esReefer = esReeferInput.equals("1");

            Long monedaId = input.getMonedaId();
            Long catUbicacionDanoId = input.getCatEstimadoUbicacionDanoId();
            Long catComponenteId = input.getCatEstimadoComponenteId();
            Long catMetodoRepId = input.getCatEstimadoMetodoRepId();
            Long catDimensionTipoId = input.getCatEstimadoDimensionTipoId();
            Integer piezasReparar = input.getPiezasReparar();
            Integer shippingLineId = input.getShippingLineId();

            // If shippingLineId is null or 4102, set it to 4104.
            if (shippingLineId == null || shippingLineId == 4102) {
                shippingLineId = 4104;
            }

            // Get localizacionDano if catUbicacionDanoId is not null.
            String localizacionDano = "";
            if (catUbicacionDanoId != null) {
                Catalog damageCatalog = catalogRepository.findById(catUbicacionDanoId.intValue()).orElse(null);
                if (damageCatalog != null && damageCatalog.getDescription() != null) {
                    // mimic LEFT(RTRIM(description), 2)
                    String trimmed = damageCatalog.getDescription().trim();
                    localizacionDano = trimmed.length() > 2 ? trimmed.substring(0, 2) : trimmed;
                }
            }

            // If input codes are null, set them to empty string for logic.
            if (cedexMercCode == null) cedexMercCode = "";
            if (cedexMercDescr == null) cedexMercDescr = "";

            // For the component code or method code or dimension, if null, set them to empty.
            String componentCode = "";
            String methodCode = "";
            String dimensionStr = "";
            if (catComponenteId != null) {
                Catalog catComp = catalogRepository.findById(catComponenteId.intValue()).orElse(null);
                if (catComp != null) {
                    componentCode = catComp.getDescription();
                }
            }

            if (catMetodoRepId != null) {
                Catalog catMetodo = catalogRepository.findById(catMetodoRepId.intValue()).orElse(null);
                if (catMetodo != null) {
                    methodCode = catMetodo.getDescription();
                }
            }

            if (catDimensionTipoId != null) {
                Catalog catDim = catalogRepository.findById(catDimensionTipoId.intValue()).orElse(null);
                if (catDim != null) {
                    dimensionStr = catDim.getDescription();
                }
            }

            // Check if user typed empty strings for codes.
            if (componentCode == null) componentCode = "";
            if (methodCode == null) methodCode = "";
            if (dimensionStr == null) dimensionStr = "";

            // If all three main codes are empty, set piezasReparar to 0.
            if (componentCode.isEmpty() && methodCode.isEmpty() && localizacionDano.isEmpty()) {
                piezasReparar = 0;
            }

            // Default if piezasReparar is null.
            if (piezasReparar == null) {
                piezasReparar = 0;
            }

            // Now call the repository method.
            List<CedexMercDTO> results = cedexMercRepository.findCedexMerc(
                    subUnidadNegocioId,
                    catTipoEstimadoId,
                    esReefer,
                    shippingLineId,
                    isEquipmentTypeContainer,
                    cedexMercCode.isBlank() ? null : cedexMercCode,
                    cedexMercDescr.isBlank()? null : cedexMercDescr,
                    componentCode.isBlank() ? null : componentCode,
                    localizacionDano.isBlank() ? null : localizacionDano,
                    methodCode.isBlank()? null : methodCode,
                    dimensionStr.isBlank() ? null : dimensionStr,
                    piezasReparar == 0 ? null : piezasReparar,
                    monedaId

            );

            // Construct the output list.

            for (CedexMercDTO cm : results) {
                BuscarCedexMercXCodigoObservableOutput item = new BuscarCedexMercXCodigoObservableOutput();
                item.setCedexMercCodigo(cm.getCedexMercCodigo());
                item.setCedexMercDescripcion(cm.getCedexMercDescripcion());
                item.setMercHH(cm.getMercHh());
                item.setMercCostoMaterial(cm.getMercCostoMaterial());
                item.setUbicacionesDano(cm.getUbicacionesDano());

                // We can read the joined descriptions from catalogs if needed, but here we store direct descriptions.
                // We know component desc can be found in cm.getComponentCode() matching a Catalog, but replicate procedure's logic.
                // For clarity, let's just set them to the code values.
                item.setCatEstimadoComponenteId(String.valueOf(cm.getCatEstimadoComponenteId()));
                item.setCodigoComponente(cm.getCodigoComponente());
                item.setComponenteDescripcion(cm.getComponenteDescripcion());
                item.setCatEstimadoMetodoRepId(String.valueOf(cm.getCatEstimadoMetodoRepId()));
                item.setCodigoMetodoRep(cm.getCodigoMetodoRep());
                item.setMetodoDescripcion(cm.getMetodoDescripcion());
                item.setMercPiezasMax(cm.getMercPiezasMax());
                item.setDimensionId(String.valueOf(cm.getDimensionId()));
                item.setMercDimension(cm.getMercDimension());
                item.setMercValorMin(cm.getMercValorMin());
                item.setMercValorMax(cm.getMercValorMax());
                item.setTamanoCnt(cm.getTamanoCnt());
                // currency name
                if (cm.getMoneda() != null) {
                    item.setMoneda(cm.getMoneda());
                }
                item.setMercObservacion(cm.getMercObservacion());

                itemOutputs.add(item);
            }

        } catch (Exception e) {
            logger.error("Error in buscarCedexMerc Service", e);
        }
        return itemOutputs;
    }
}

