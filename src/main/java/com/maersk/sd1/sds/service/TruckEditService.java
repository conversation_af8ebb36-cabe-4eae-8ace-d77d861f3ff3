
package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.Truck;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.sds.dto.TruckEditOutput;
import com.maersk.sd1.sds.dto.TruckEditRequest;
import com.maersk.sd1.common.repository.TruckRepository;
import jakarta.transaction.Transactional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class TruckEditService {

    private static final Logger logger = LogManager.getLogger(TruckEditService.class);

    private final TruckRepository truckRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Autowired
    public TruckEditService(TruckRepository truckRepository,
                            MessageLanguageRepository messageLanguageRepository) {
        this.truckRepository = truckRepository;
        this.messageLanguageRepository = messageLanguageRepository;
    }

    @Transactional
    public TruckEditOutput editTruck(TruckEditRequest request) {
        TruckEditOutput output = new TruckEditOutput();
        try {
            Truck existingTruck = truckRepository.findById(request.getId())
                    .orElse(null);
            if (existingTruck == null) {
                output.setRespEstado(0);
                String notFoundMsg = messageLanguageRepository.fnTranslatedMessage("INS_VEHICULO", 1, request.getLanguageId());
                output.setRespMensaje("Vehicle not found: " + notFoundMsg);
                return output;
            }

            String oldPlate = existingTruck.getPlate();
            if (!oldPlate.equalsIgnoreCase(request.getPlate())) {
                boolean plateExists = truckRepository.existsByPlateIgnoreCaseAndIdNot(request.getPlate(), request.getId());
                if (plateExists) {

                    Integer duplicatedTruckId = truckRepository.findByPlateIgnoreCaseAndIdNot(request.getPlate(), request.getId())
                            .map(Truck::getId)
                            .orElse(0);
                    String duplicateMsg = messageLanguageRepository.fnTranslatedMessage("INS_VEHICULO", 1, request.getLanguageId());
                    duplicateMsg = duplicateMsg.replace("{IDX}", duplicatedTruckId.toString());

                    output.setRespEstado(2);
                    output.setRespMensaje(duplicateMsg);
                    return output;
                }
            }

            existingTruck.setPlate(request.getPlate());
            existingTruck.setPayload(request.getPayload());
            existingTruck.setModel(request.getModel());
            existingTruck.setNetWeight(request.getNetWeight());
            existingTruck.setBruteWeight(request.getBruteWeight());
            Company company = new Company();
            company.setId(request.getTransportCompanyId());
            existingTruck.setTransportCompany(company);
            existingTruck.setActive(request.getActive());
            User modUser = new User();
            modUser.setId(request.getUserModificationId());
            existingTruck.setModificationUser(modUser);
            existingTruck.setModificationDate(LocalDateTime.now());

            truckRepository.save(existingTruck);

            output.setRespEstado(1);
            String successMsg = messageLanguageRepository.fnTranslatedMessage("GENERAL", 10, request.getLanguageId());
            output.setRespMensaje(successMsg);

        } catch (Exception ex) {
            logger.error("Error updating Truck:", ex);
            output.setRespEstado(0);
            output.setRespMensaje(ex.getMessage());
        }
        return output;
    }
}