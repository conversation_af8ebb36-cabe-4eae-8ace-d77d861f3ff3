package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.BuscarCedexMercXCodigoInput;
import com.maersk.sd1.sde.dto.BuscarCedexMercXCodigoOutput;
import com.maersk.sd1.sde.dto.UpdateEstimateEmrInput;
import com.maersk.sd1.sde.dto.UpdateEstimateEmrOutput;
import com.maersk.sd1.sde.service.BuscarCedexMercXCodigoService;
import com.maersk.sd1.sde.service.UpdateEstimateEmrService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("ModuleSDE/module/sde/SDEEMRServiceImp")
public class UpdateEstimateEmrController {

    private static final Logger logger = LogManager.getLogger(UpdateEstimateEmrController.class);

    private final UpdateEstimateEmrService updateEstimateEmrService;

    @Autowired
    public UpdateEstimateEmrController(UpdateEstimateEmrService updateEstimateEmrService) {
        this.updateEstimateEmrService = updateEstimateEmrService;
    }

    /**
     * Controller method that replicates stored procedure logic.
     */
    @PostMapping("/sdeactualizarEstimadoEmr")
    public ResponseEntity<ResponseController<UpdateEstimateEmrOutput>> updateEstimateEmr(
            @RequestBody @Valid UpdateEstimateEmrInput.Root request) {
        try {
            UpdateEstimateEmrInput.Input input = request.getPrefix().getInput();
            UpdateEstimateEmrOutput outputList = updateEstimateEmrService.updateEstimateEmr(input);
            return ResponseEntity.ok(new ResponseController<>(outputList));
        } catch (Exception e) {
            logger.error("An error occurred while processing sdeactualizarEstimadoEmr", e);
            return ResponseEntity.internalServerError().body(new ResponseController<>("Internal Error"));
        }
    }
}
