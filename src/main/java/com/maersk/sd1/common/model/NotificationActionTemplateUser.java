package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "notificacion_accion_plantilla_usuario", schema = "seg")
public class NotificationActionTemplateUser {
    @EmbeddedId
    private NotificationActionTemplateUserId id;

    @MapsId("userId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_id", nullable = false)
    private User user;

    @MapsId("notificationActionTemplateId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "notificacion_accion_plantilla_id", nullable = false)
    private NotificationActionTemplate notificationActionTemplate;

    @Column(name = "fecha_leido")
    private LocalDateTime dateRead;

}