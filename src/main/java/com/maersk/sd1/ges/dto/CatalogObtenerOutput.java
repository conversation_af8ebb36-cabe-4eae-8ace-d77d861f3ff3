package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class CatalogObtenerOutput {

    @JsonProperty("resp_estado")
    private Integer respEstado;

    @JsonProperty("resp_mensaje")
    private String respMensaje;

    @JsonProperty("parent")
    private ParentCatalogDTO parent;

    @JsonProperty("children")
    private List<ChildCatalogDTO> children;

    @Data
    public static class ParentCatalogDTO {
        @JsonProperty("catalogo_id")
        private Integer catalogId;

        @JsonProperty("descripcion")
        private String description;

        @JsonProperty("descricion_larga")
        private String longDescription;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("catalogo_padre_id")
        private Integer parentCatalogId;

        @JsonProperty("codigo")
        private String code;

        @JsonProperty("variable_1")
        private String variable1;

        @JsonProperty("variable_2")
        private String variable2;

        @JsonProperty("variable_3")
        private Integer variable3;

        @JsonProperty("alias")
        private String alias;
    }

    @Data
    public static class ChildCatalogDTO {
        @JsonProperty("catalogo_id")
        private Integer catalogId;

        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;

        @JsonProperty("descripcion")
        private String description;

        @JsonProperty("descricion_larga")
        private String longDescription;

        @JsonProperty("variable_1")
        private String variable1;

        @JsonProperty("variable_2")
        private String variable2;

        @JsonProperty("variable_3")
        private Integer variable3;

        @JsonProperty("estado")
        private Boolean status;

        @JsonProperty("codigo")
        private String code;

        @JsonProperty("idiomas")
        private List<IdiomaDTO> idiomas;

        @JsonProperty("unidades")
        private List<UnidadNegocioDTO> unidades;

        @JsonProperty("alias")
        private String alias;
    }

    @Data
    public static class IdiomaDTO {
        @JsonProperty("idioma_id")
        private Integer idiomaId;

        @JsonProperty("descripcion")
        private String description;

        @JsonProperty("descricion_larga")
        private String longDescription;
    }

    @Data
    public static class UnidadNegocioDTO {
        @JsonProperty("unidad_negocio_id")
        private Integer businessUnitId;
    }
}

