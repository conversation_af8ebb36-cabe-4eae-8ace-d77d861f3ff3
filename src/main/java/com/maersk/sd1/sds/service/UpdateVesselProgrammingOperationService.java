package com.maersk.sd1.sds.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.model.VesselProgrammingCutoff;
import com.maersk.sd1.common.model.VesselProgrammingDetail;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.common.repository.VesselProgrammingCutoffRepository;
import com.maersk.sd1.common.repository.VesselProgrammingDetailRepository;
import com.maersk.sd1.sds.dto.UpdateVesselProgrammingOperationInput;
import com.maersk.sd1.sds.dto.UpdateVesselProgrammingOperationOutput;
import jakarta.persistence.EntityNotFoundException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

@Service
public class UpdateVesselProgrammingOperationService {

    private static final Logger logger = LogManager.getLogger(UpdateVesselProgrammingOperationService.class);

    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    private VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository;

    private ShippingLineRepository shippingLineRepository;

    @Autowired
    public  UpdateVesselProgrammingOperationService(VesselProgrammingDetailRepository vesselProgrammingDetailRepository,
                                                    VesselProgrammingCutoffRepository vesselProgrammingCutoffRepository,
                                                    ShippingLineRepository shippingLineRepository)
    {
        this.vesselProgrammingDetailRepository = vesselProgrammingDetailRepository;
        this.vesselProgrammingCutoffRepository = vesselProgrammingCutoffRepository;
        this.shippingLineRepository = shippingLineRepository;
    }

    /**
     * Internal DTO for parsing the cutoffRetiroVacios array.
     */
    private static class CutoffRetiroVacios {
        public Integer shippingLineId;
        public String pickupDry;    // e.g. "20210401 17:00"
        public String pickupReefer; // e.g. "20210401 18:00"
    }

    /**
     * Main operation that replicates the stored procedure logic.
     * @return output DTO with respEstado / respMensaje.
     */
    @Transactional
    public UpdateVesselProgrammingOperationOutput updateVesselProgrammingOperation(UpdateVesselProgrammingOperationInput.Input input) {

        UpdateVesselProgrammingOperationOutput output = new UpdateVesselProgrammingOperationOutput();
        output.setRespEstado(0);
        output.setRespMensaje("");

        try {
            // 1) Locate VesselProgrammingDetail
            Optional<VesselProgrammingDetail> optDetail = vesselProgrammingDetailRepository.findById(input.getDetailId());
            if (optDetail.isEmpty()) {
                throw new EntityNotFoundException("No VesselProgrammingDetail found for id=" + input.getDetailId());
            }
            VesselProgrammingDetail detail = optDetail.get();

            // 2) Update detail fields
            detail.setManifestYear(input.getManifestYear());
            detail.setManifestNumber(input.getManifestNumber());
            detail.setBeginningOperation(input.getBeginningOperation());
            detail.setEndingOperation(input.getEndingOperation());
            detail.setManifestCustomsDate(input.getManifestCustomsDate());
            detail.setBeginningReturnAppointment(input.getBeginningReturnAppointment());
            detail.setDryPortCutOffDate(input.getDryPortCutOffDate());
            detail.setReeferPortCutoffDate(input.getReeferPortCutOffDate());
            detail.setDryyDepositCutoffDate(input.getDryDepositCutOffDate());
            detail.setReeferDepositCutoffDate(input.getReeferDepositCutOffDate());
            detail.setExpoEcuAppointmentsBegginingDate(input.getExpoEcuAppointmentsBegginingDate());

            // For the stored procedure, we had user_modificacion_id, fecha_modificacion.
            // We'll just store them in detail's modificationUser / modificationDate.
            User modificationUser = new User();
            modificationUser.setId(input.getUserId());
            detail.setModificationUser(modificationUser);
            detail.setModificationDate(LocalDateTime.now());

            vesselProgrammingDetailRepository.save(detail);

            // 3) Process cutoffRetiroVacios JSON
            boolean isEmptyCutoffRetiroVacios = (input.getCutoffRetiroVacios() == null || input.getCutoffRetiroVacios().trim().isEmpty() || "[]".equalsIgnoreCase(input.getCutoffRetiroVacios().trim()));

            /* If the JSON is not empty, parse it.
               Then: for each line, update/insert the VesselProgrammingCutoff.
               Also handle logic to deactivate removed lines. */

            if (!isEmptyCutoffRetiroVacios) {
                // parse JSON
                ObjectMapper mapper = new ObjectMapper();
                List<CutoffRetiroVacios> parsedList = new ArrayList<>();
                try {
                    parsedList = mapper.readValue(input.getCutoffRetiroVacios(), new TypeReference<List<CutoffRetiroVacios>>(){});
                } catch (Exception e) {
                    // If parse fails, treat as empty
                    logger.error("cutoffRetiroVacios JSON parse error", e);
                    parsedList = new ArrayList<>();
                }

                // Remove or mark line entries with zero or missing shippingLineId or shippingLine not found.
                Iterator<CutoffRetiroVacios> it = parsedList.iterator();
                while (it.hasNext()) {
                    CutoffRetiroVacios c = it.next();
                    if (c.shippingLineId != null && c.shippingLineId == 0) {
                        c.shippingLineId = null;
                    }
                }

                // Deactivate items not present in new set.
                // We get all active cutoffs for this detail
                List<VesselProgrammingCutoff> activeCutoffs = vesselProgrammingCutoffRepository.findActiveByDetail(detail);
                for (VesselProgrammingCutoff cutoff : activeCutoffs) {
                    Integer existingLineId = (cutoff.getShippingLine() != null) ? cutoff.getShippingLine().getId() : null;

                    boolean found = parsedList.stream().anyMatch(c -> {
                        if (c.shippingLineId == null && existingLineId == null) {
                            return true;
                        } else if (c.shippingLineId != null && existingLineId != null) {
                            return c.shippingLineId.intValue() == existingLineId.intValue();
                        }
                        return false;
                    });

                    if (!found) {
                        // deactivate it
                        cutoff.setActive(false);
                        // set modification user
                        User modUser = new User();
                        modUser.setId(input.getUserId());
                        cutoff.setModificationUser(modUser);
                        cutoff.setModificationDate(LocalDateTime.now());
                        vesselProgrammingCutoffRepository.save(cutoff);
                    }
                }

                // For each new line, either update or create.
                for (CutoffRetiroVacios c : parsedList) {

                    boolean hasDry = (c.pickupDry != null && !c.pickupDry.isEmpty());
                    boolean hasReefer = (c.pickupReefer != null && !c.pickupReefer.isEmpty());

                    if (!hasDry && !hasReefer) {
                        // no values to store => skip
                        continue;
                    }

                    // find the shipping line if c.shippingLineId not null
                    ShippingLine shippingLine = null;
                    if (c.shippingLineId != null) {
                        shippingLine = shippingLineRepository.findByIdAndActive(c.shippingLineId, true);
                        if (shippingLine == null) {
                            // skip because shipping line with that ID is not active or not found
                            continue;
                        }
                    }

                    // find cutoff if exists
                    VesselProgrammingCutoff existingCutoff = vesselProgrammingCutoffRepository.findByDetailAndShippingLineId(detail,
                            (shippingLine != null ? shippingLine.getId() : null));

                    // parse the date/time from string
                    LocalDateTime retiroDryDate = parseDateTime(c.pickupDry);
                    LocalDateTime retiroReeferDate = parseDateTime(c.pickupReefer);

                    if (existingCutoff == null) {
                        // create new
                        VesselProgrammingCutoff newCutoff = new VesselProgrammingCutoff();
                        newCutoff.setVesselProgrammingDetail(detail);
                        newCutoff.setShippingLine(shippingLine);
                        newCutoff.setDateCutoffRetreatEmptyDry(retiroDryDate);
                        newCutoff.setDateCutoffRetreatEmptyReefer(retiroReeferDate);
                        newCutoff.setActive(true);

                        // user registro
                        User regUser = new User();
                        regUser.setId(input.getUserId());
                        newCutoff.setRegistrationUser(regUser);
                        newCutoff.setRegistrationDate(LocalDateTime.now());

                        vesselProgrammingCutoffRepository.save(newCutoff);
                    } else {
                        existingCutoff.setDateCutoffRetreatEmptyDry(retiroDryDate);
                        existingCutoff.setDateCutoffRetreatEmptyReefer(retiroReeferDate);
                        existingCutoff.setActive(true);

                        User modUser = new User();
                        modUser.setId(input.getUserId());
                        existingCutoff.setModificationUser(modUser);
                        existingCutoff.setModificationDate(LocalDateTime.now());

                        vesselProgrammingCutoffRepository.save(existingCutoff);
                    }
                }

            } else {
                // If cutoffRetiroVacios is empty => deactivate all cutoff
                List<VesselProgrammingCutoff> allActiveCutoffs = vesselProgrammingCutoffRepository.findActiveByDetail(optDetail.get());
                for (VesselProgrammingCutoff cutoff : allActiveCutoffs) {
                    cutoff.setActive(false);
                    User modUser = new User();
                    modUser.setId(input.getUserId());
                    cutoff.setModificationUser(modUser);
                    cutoff.setModificationDate(LocalDateTime.now());
                    vesselProgrammingCutoffRepository.save(cutoff);
                }
            }

            // If everything is successful
            output.setRespEstado(1);
            output.setRespMensaje("Operation completed successfully.");

        } catch (Exception e) {
            logger.error("Error while updating vessel programming operation", e);
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }

        return output;
    }

    /**
     * Utility method to parse date/time from string format "yyyyMMdd HH:mm".
     * If parse fails, returns null.
     */
    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd HH:mm");
            return LocalDateTime.parse(dateStr, formatter);
        } catch (Exception e) {
            // fallback or parse error => return null
            logger.error("Unable to parse date string: {}", dateStr, e);

            return null;
        }
    }
}
