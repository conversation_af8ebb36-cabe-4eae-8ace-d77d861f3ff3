package com.maersk.sd1.dbo.service;

import com.maersk.sd1.seg.dto.MenuLoginInput;
import com.maersk.sd1.seg.dto.MenuLoginOutput;
import com.maersk.sd1.seg.service.MenuLoginService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@RequiredArgsConstructor
@Service
public class ListMenuService {

    private static final Logger logger = LogManager.getLogger(ListMenuService.class);

    private final MenuLoginService menuLoginService;

    public List<MenuLoginOutput> getMenu(MenuLoginInput input) {

        try {
            logger.info("Fetching menu using parameters {}", input);

            return menuLoginService.menuLoginService(input);

        } catch (Exception e) {
            logger.error("Error fetching menu from stored procedure.", e);
        }
        return Collections.emptyList();
    }
}