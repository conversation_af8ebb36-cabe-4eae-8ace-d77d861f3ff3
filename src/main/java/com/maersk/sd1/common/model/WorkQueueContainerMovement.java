package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "cola_trabajo_movimiento_contenedor", schema = "sdy")
public class WorkQueueContainerMovement {
    @Id
    @Column(name = "cola_trabajo_movimiento_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cola_trabajo_id", nullable = false)
    private WorkQueue workQueue;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_tipo_movimiento_id", nullable = false)
    private Catalog CatMovementType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_procedencia_id")
    private Catalog catOrigin;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @Column(name = "fecha_registro")
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "patio_id")
    private Yard yard;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_empty_full_id")
    private Catalog catEmptyFull;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bloque_id")
    private Block block;

}