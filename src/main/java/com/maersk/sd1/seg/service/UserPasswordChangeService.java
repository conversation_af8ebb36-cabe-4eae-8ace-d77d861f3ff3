package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.controller.dto.UserPasswordChangeInput;
import com.maersk.sd1.seg.controller.dto.UserPasswordChangeOutput;
import com.maersk.sd1.common.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@RequiredArgsConstructor
@Service
public class UserPasswordChangeService {

    private static final Logger logger = LogManager.getLogger(UserPasswordChangeService.class);

    private final UserRepository userRepository;

    @Transactional
    public UserPasswordChangeOutput changePassword(UserPasswordChangeInput.Input input) {
        UserPasswordChangeOutput output = new UserPasswordChangeOutput();
        try {
            logger.info("Starting password change for userId={} and origin={}", input.getUserId(), input.getOrigin());

            if ("IND".equalsIgnoreCase(input.getOrigin())) {
                LocalDateTime futureDate60Days = LocalDateTime.now().plusDays(60);
                LocalDateTime now = LocalDateTime.now();

                int rowsUpdated = userRepository.updatePasswordForINDCase(
                        input.getUserId(),
                        input.getKey(),
                        futureDate60Days,
                        input.getUserId(),
                        now
                );

                logger.info("Password updated. Rows affected: {}", rowsUpdated);
            }

            output.setRespStatus(1);
            output.setRespMessage("Contraseña cambiada correctamente");
        } catch (Exception e) {
            logger.error("Error changing password for userId={}", input.getUserId(), e);
            output.setRespStatus(0);
            output.setRespMessage(e.getMessage());
        }
        return output;
    }
}