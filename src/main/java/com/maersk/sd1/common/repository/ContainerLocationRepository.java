package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.ContainerLocation;
import com.maersk.sd1.sdg.dto.LocationContainer;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ContainerLocationRepository extends JpaRepository<ContainerLocation, Integer> {
    List<ContainerLocation> findByContainerId(Integer containerId);

    @Query("SELECT new com.maersk.sd1.sdg.dto.LocationContainer(cl.block, cl.cell, cl.level, cl.id) FROM ContainerLocation cl WHERE cl.container.id = :containerId")
    List<LocationContainer> findLocationContainerById(Integer containerId);

    @Modifying
    @Transactional
    @Query("update ContainerLocation set container = null where container.id = :containerId")
    void updateContainerIdNull(@Param("containerId") Integer containerId);

    @Query(value = "SELECT sdy.fn_get_container_location(:containerId)", nativeQuery = true)
    String getContainerLocation(@Param("containerId") Integer containerId);

    @Query("SELECT cl FROM ContainerLocation cl " +
            "WHERE cl.container.id = :containerId " +
            "ORDER BY cl.cell.id ASC")
    List<ContainerLocation> findByContainerIdOrderByCellAsc(@Param("containerId") Integer containerId);
}