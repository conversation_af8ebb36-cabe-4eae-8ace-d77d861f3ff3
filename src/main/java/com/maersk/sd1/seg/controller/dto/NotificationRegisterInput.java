package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class NotificationRegisterInput {

    @Data
    public static class Input {
        @JsonProperty("business_unit_id")
        private int businessUnitId;

        @JsonProperty("title")
        private String title;

        @JsonProperty("subtitle")
        private String subtitle;

        @JsonProperty("description")
        private String description;

        @JsonProperty("icon")
        private String icon;

        @JsonProperty("level")
        private char level;

        @JsonProperty("once")
        private char once;

        @JsonProperty("single_alert")
        private char singleAlert;

        @JsonProperty("expiration_date")
        private LocalDateTime expirationDate;

        @JsonProperty("notification_status")
        private char notificationStatus;

        @JsonProperty("roles")
        private String roles;

        @JsonProperty("users")
        private String users;

        @JsonProperty("user_id")
        private int userId;

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }
}