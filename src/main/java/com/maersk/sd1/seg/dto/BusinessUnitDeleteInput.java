package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class BusinessUnitDeleteInput {

    @Data
    public static class Input {

        @JsonProperty("unidad_negocio_id")
        @NotNull(message = "unidad_negocio_id cannot be null")
        private Integer unidadNegocioId;

        @JsonProperty("usuario_id")
        @NotNull(message = "usuario_id cannot be null")
        private Integer usuarioId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }
}

