#Properties file
#Usuario para acceso a los servicios del SD1
aps.api.user=<EMAIL>
aps.api.password=/B6u7i2pKxSeXf/M0UoFXnjdv+OJ4XjEkgG9ExicuP4=
aps.api.token.url=https://appointmentprep.inlandservices.com/api/inlandnet/Inlandnet/security/UserServiceImp/apiUserLogin
aps.api.appointmentStatus.url=https://appointmentprep.inlandservices.com/api/moduleaps/ModuleAPS/module/aps/APSAppointmentServiceImp/apsgetContainerAppointmentStatus
msk.api.yard.apiUserLogin.sdy.loginUrl=https://inlandnet.dev.maersk-digital.net/Inlandnet/security/UserServiceImp/apiUserLogin
msk.api.yard.apiUserLogin.sdy.user=<EMAIL>
msk.api.yard.apiUserLogin.sdy.password=WhWR8BKZCxI2YWOF0rA5YV2xUv+O7gJv5yt8nJ4RlSU=
msk.api.yard.apiUserLogin.sdy.system=9
msk.api.yard.apiUserLogin.sdy.gateInUrl=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyPlannigGateInAssignment
msk.api.yard.apiUserLogin.sdy.sdyobtenerUbicacionNuevaZona=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionZonaServiceImp/sdyobtenerUbicacionNuevaZona
msk.api.yard.apiUserLogin.sdy.gateOutUrl=https://modulesdy.dev.maersk-digital.net/ModuleSDY/moduleYard/sdy/SDYPlanificacionServiceImp/sdyPlanningGateOut