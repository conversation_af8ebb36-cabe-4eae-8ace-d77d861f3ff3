package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.seg.dto.RoleObtainOutputDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RoleObtainService {

    private final RoleRepository roleRepository;
    private final RoleMenuRepository roleMenuRepository;
    private final RoleMenuActionRepository roleMenuActionRepository;
    private final RoleBusinessUnitRepository roleBusinessUnitRepository;
    private final ProjectSystemRepository projectSystemRepository;
    private final BusinessUnitRepository businessUnitRepository;

    @Autowired
    public RoleObtainService(RoleRepository roleRepository,
                             RoleMenuRepository roleMenuRepository,
                             RoleMenuActionRepository roleMenuActionRepository,
                             RoleBusinessUnitRepository roleBusinessUnitRepository,
                             ProjectSystemRepository projectSystemRepository,
                             BusinessUnitRepository businessUnitRepository) {
        this.roleRepository = roleRepository;
        this.roleMenuRepository = roleMenuRepository;
        this.roleMenuActionRepository = roleMenuActionRepository;
        this.roleBusinessUnitRepository = roleBusinessUnitRepository;
        this.projectSystemRepository = projectSystemRepository;
        this.businessUnitRepository = businessUnitRepository;
    }

    public RoleObtainOutputDTO getRoleData(Integer roleId) {
        RoleObtainOutputDTO output = new RoleObtainOutputDTO();
        try {
            RoleObtainOutputDTO.RoleDto roleDto = roleRepository.findRoleDtoById(roleId);
            if (roleDto == null) {
                output.setRespEstado(1);
                output.setRespMensaje("Success");
                return output;
            }

            List<RoleObtainOutputDTO.RoleMenuDto> roleMenuList = roleMenuRepository.findRoleMenuDtoByRoleId(roleId);
            List<RoleObtainOutputDTO.RoleMenuActionDto> roleMenuActionList = roleMenuActionRepository.findRoleMenuActionDtoByRoleId(roleId);
            List<RoleObtainOutputDTO.RoleBusinessUnitDto> roleBusinessUnitList = roleBusinessUnitRepository.findRoleBusinessUnitDtoByRoleId(roleId);
            List<RoleObtainOutputDTO.SystemMenuDto> systemList = projectSystemRepository.findDistinctSystemsByRoleId(roleId);

            List<Integer> systemIds = systemList.stream()
                    .map(RoleObtainOutputDTO.SystemMenuDto::getSystemId)
                    .toList();

            List<RoleObtainOutputDTO.BusinessUnitDto> businessUnitList = businessUnitRepository.findBusinessUnitsBySystemIds(systemIds);

            output.setRespEstado(1);
            output.setRespMensaje("Success");
            output.setRoleInfo(roleDto);
            output.setRoleMenuList(roleMenuList);
            output.setRoleMenuActionList(roleMenuActionList);
            output.setRoleBusinessUnitList(roleBusinessUnitList);
            output.setSystemList(systemList);
            output.setBusinessUnitList(businessUnitList);
        } catch (Exception e) {
            output.setRespEstado(0);
            output.setRespMensaje("Error: " + e.getMessage());
        }
        return output;
    }
}