package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class PersonObtainInput {

    private PersonObtainInput()
    { }

    @Data
    public static class Input {

        @JsonProperty("persona_id")
        @NotNull(message = "persona_id cannot be null")
        private Integer personaId;

        private Input() {}

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
        private Prefix() {}

    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix ges;

        private Root() {}

    }
}
