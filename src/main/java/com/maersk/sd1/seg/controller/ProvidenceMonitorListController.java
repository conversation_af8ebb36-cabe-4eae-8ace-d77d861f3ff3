package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.ProvidenceMonitorListInput;
import com.maersk.sd1.seg.dto.ProvidenceMonitorListOutput;
import com.maersk.sd1.seg.service.ProvidenceMonitorListService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@RestController
@RequestMapping("/ModuleSEG/module/seg/ADMProvidenceServiceImp")
public class ProvidenceMonitorListController {

    private static final Logger logger = LogManager.getLogger(ProvidenceMonitorListController.class.getName());

    private final ProvidenceMonitorListService service;

    @Autowired
    public ProvidenceMonitorListController(ProvidenceMonitorListService service) {
        this.service = service;
    }

    @PostMapping("/segprovidenciaMonitoreoListar")
    public ResponseEntity<ResponseController<ProvidenceMonitorListOutput>> providenceMonitorListService(@RequestBody ProvidenceMonitorListInput.Root request) {
        try {
            return service.providenceMonitorListService(request);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}