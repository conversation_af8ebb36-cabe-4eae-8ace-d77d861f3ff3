package com.maersk.sd1.sds.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

import com.maersk.sd1.sds.controller.dto.ServiceCoparnProcessFile9Input;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.BookingEdiUtils;
import com.maersk.sd1.sds.dto.CoparnServiceProcessFileOutput;
import com.maersk.sd1.sds.dto.copanrServiceProcessFile.TbCnts;
import com.maersk.sd1.sds.dto.copanrServiceProcessFile.TbContent;
import com.maersk.sd1.sds.dto.copanrServiceProcessFile.TbvEntry;


@Service
public class CoparnServiceProcessFileService {

    private final CatalogRepository catalogRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final BookingEdiRepository bookingEdiRepository;
    private final BookingEdiSettingRepository bookingEdiSettingRepository;
    private final BookingEdiFileRepository ediFileRepository;
    private final SystemRuleRepository systemRuleRepository;
    private final VesselRepository vesselRepository;
    private final PortRepository portRepository;
    private final BookingEdiSettingBURepository bookingEdiSettingBURepository;
    private final PortMasterRepository portMasterRepository;
    private final IsoCodeRepository isoCodeRepository;
    private final CompanyRepository companyRepository;
    private final CompanyRoleRepository companyRoleRepository;
    private final ImoRepository imoRepository;
    private final VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    private final VesselProgrammingRepository vesselProgrammingRepository;
    private final ProductRepository productRepository;
    private final ServiceCoparnProcessFile9Service serviceCoparnProcessFile9Service;

    @Autowired
    public CoparnServiceProcessFileService(SystemRuleRepository systemRuleRepository, CatalogRepository catalogRepository, BusinessUnitRepository businessUnitRepository, BookingEdiRepository bookingEdiRepository, BookingEdiSettingRepository bookingEdiSettingRepository, CompanyRoleRepository companyRoleRepository, BookingEdiFileRepository ediFileRepository, VesselRepository vesselRepository, PortRepository portRepository, ImoRepository imoRepository, VesselProgrammingDetailRepository vesselProgrammingDetailRepository, BookingEdiSettingBURepository bookingEdiSettingBURepository, PortMasterRepository portMasterRepository, ProductRepository productRepository, IsoCodeRepository isoCodeRepository, CompanyRepository companyRepository, VesselProgrammingRepository vesselProgrammingRepository, ServiceCoparnProcessFile9Service serviceCoparnProcessFile9Service) {
        this.systemRuleRepository = systemRuleRepository;
        this.catalogRepository = catalogRepository;
        this.businessUnitRepository = businessUnitRepository;
        this.bookingEdiRepository = bookingEdiRepository;
        this.bookingEdiSettingRepository = bookingEdiSettingRepository;
        this.companyRoleRepository = companyRoleRepository;
        this.ediFileRepository = ediFileRepository;
        this.vesselRepository = vesselRepository;
        this.portRepository = portRepository;
        this.imoRepository = imoRepository;
        this.vesselProgrammingDetailRepository = vesselProgrammingDetailRepository;
        this.bookingEdiSettingBURepository = bookingEdiSettingBURepository;
        this.portMasterRepository = portMasterRepository;
        this.productRepository = productRepository;
        this.isoCodeRepository = isoCodeRepository;
        this.companyRepository = companyRepository;
        this.vesselProgrammingRepository = vesselProgrammingRepository;
        this.serviceCoparnProcessFile9Service = serviceCoparnProcessFile9Service;
    }


    public CoparnServiceProcessFileOutput processFile(Integer ediCoparnId, Integer seteoEdiCoparnId) throws JsonProcessingException {
        String copanrFileContentORI = "";
        String coparnFileContent = "";
        Integer shippingLineId = null;

        Integer n = null;
        Boolean isCreateScheduleAutomatically = null;
        Boolean allowCreateAutomaticClient = null;
        Integer programmingVesselDetailId = null;
        Integer imoId;


        String dateUNB = "";
        String trecordType = "";
        StringBuilder creationDateEDI = new StringBuilder();
        String reservationSituation = "";
        String dateDTM = "";
        String booking = "";
        String bookingRelease = "";
        String refAHP = "";
        String voyage = "";
        String vesselCodeListQualifier = "";
        String vesselCallSign = "";
        String vesselLloydNumber = "";
        String vesselXml = "";
        String depotColombia = "";
        String depotColumbus = "";
        String loc98DepotPos31 = "";
        String xShipmentPort = "";
        String loc98Depot = "";
        String xDestinationPort = "";
        String xDichargePort = "";
        String clientCodeSCV = "";
        String clientNameXml = "";
        String lineBKXml = "";
        String clientCustomer = "";
        String clientShipper = "";
        String depotCodeHSDPER = "";
        String depotCodeHSDECU = "";
        String productGroupDes = "";
        String isForColdTreatment = "";
        String codigoIMO = "";
        String containerCodeISO1 = "";
        String containerCodeISO2 = "";
        Integer reserveAmount1 = 0;
        Integer reserveAmount2 = 0;
        String netWeight = "";
        String tare = "";
        String oxygen = "";
        String carbonDioxide = "";
        String temperature = "";
        String temperatureUnit = "";
        String r403RefDepotLocationCode = "";
        String r404RefDepotPortName = "";

        Integer vesselProgrammingId = null;
        String creationDate = null;
        Integer lineBKId;
        Integer subBusinessUnitId = null;
        Integer businessUnitId;
        Integer shipmentPortId = null;
        Integer dischargePortId = null;
        Integer destinationPortId = null;
        Integer cntDimenId = null;
        Integer codeIsoId = null;
        Integer cntDimen2Id = null;
        Integer cntTypeId = null;
        Integer codeIso2Id = null;
        Integer cntType2Id = null;
        String clientRS = null;
        Integer clientId = null;
        Integer vesselId = null;
        Integer productId = null;
        String clientNameXML2;
        Boolean coldTreatment = false;
        Boolean controlledAtmosphere = false;
        Integer catEdiCoparnStatus = null;
        Integer catBkEdiMessageTypeId = null;
        String sSegmento = null;


        List<TbContent> tbContent = new ArrayList<>();
        List<TbCnts> tbCnts = new ArrayList<>();
        List<String> tbSecHLL = new ArrayList<>();
        List<Integer> tbVessels = new ArrayList<>();


        Integer isEdiMessageTypeCoparn = catalogRepository.findIdByAlias(Parameter.SD1_BKEDI_MESSAGE_TYPE_COPARN);
        Integer isEdiMessageType301 = catalogRepository.findIdByAlias(Parameter.SD1_BKEDI_MESSAGE_TYPE_301);

        Integer isBkediPending = catalogRepository.findIdByAlias(Parameter.IS_BKEDI_PENDING);
        Integer isBkediToProcess = catalogRepository.findIdByAlias(Parameter.IS_STATE_TO_PROCESS);
        Integer isBkediRejected = catalogRepository.findIdByAlias(Parameter.IS_BKEDI_REJECTED);
        Integer isBkediNotValid = catalogRepository.findIdByAlias(Parameter.IS_BKEDI_NOT_VALID);

        Integer isCreationSourceBkedi = catalogRepository.findIdByAlias(Parameter.CAT_42989_AUTO_COPARN);

        Integer isUnCostaRicaId = businessUnitRepository.findIdByAlias(Parameter.COSTA_RICA_BUSINESSUNIT_ALIAS);
        Integer tipoDocIde = 48280;
        Integer rolClienteId = 43154;
        Integer unitBusinessId = null;
        String bookingNumberTemp = null;
        String remark;
        boolean flagToFlex = false;

        User user = new User();
        user.setId(1);

        Optional<BookingEdi> optionalBookingEdi = bookingEdiRepository.findByIdAndActiveIsTrue(ediCoparnId);
        if (optionalBookingEdi.isPresent() && (optionalBookingEdi.get().getCatBkEdiStatus().getId().equals(isBkediPending) ||
                Objects.equals(optionalBookingEdi.get().getCatBkEdiStatus().getId(), isBkediToProcess))) {
            shippingLineId = optionalBookingEdi.get().getShippingLine().getId();

            Optional<BookingEdiSetting> optionalBookingEdiSetting = bookingEdiSettingRepository.findById(seteoEdiCoparnId);
            if (optionalBookingEdiSetting.isPresent()) {
                isCreateScheduleAutomatically = optionalBookingEdiSetting.get().getAllowCreateAutomaticVesselProgramming();
                allowCreateAutomaticClient = optionalBookingEdiSetting.get().getAllowCreateAutomaticCustomer();
                catBkEdiMessageTypeId = optionalBookingEdiSetting.get().getCatBkEdiMessageType().getId();
                unitBusinessId = optionalBookingEdiSetting.get().getBusinessUnit().getId();
            }

            Optional<BookingEdiFile> optionalBookingEdiFile = ediFileRepository.findByBookingEdiId(ediCoparnId);
            if (optionalBookingEdiFile.isPresent()) {
                copanrFileContentORI = optionalBookingEdiFile.get().getBkEdiContent();
            }


            if (Objects.equals(catBkEdiMessageTypeId, isEdiMessageTypeCoparn)) {
                if (!copanrFileContentORI.contains("\r\n")) {
                    coparnFileContent = copanrFileContentORI.replace("'", "'|");
                } else {
                    coparnFileContent = copanrFileContentORI.replace("'\r\n", "'|");
                }
                String[] splitValues = coparnFileContent.split("\\|");

                for (int i = 0; i < splitValues.length; i++) {
                    String valor = splitValues[i].trim();
                    if (!valor.isEmpty()) {
                        tbContent.add(new TbContent(i + 1, valor));
                    }
                }

                int maxPos = tbContent.stream().mapToInt(TbContent::getPos).max().orElse(0);

                for (n = 1; n <= maxPos; n++) {

                    Integer finalN = n;
                    String sLine = tbContent.stream()
                            .filter(item -> item.getPos() == finalN)
                            .map(TbContent::getValue)
                            .findFirst()
                            .orElse(null);

                    if (sLine != null) {
                        sSegmento = BookingEdiUtils.fnLeeLineaCoparn(sLine, 1, 1);
                    }

                    if ("UNB".equals(sSegmento)) {
                        dateUNB = BookingEdiUtils.fnLeeLineaCoparn(sLine, 5, 1) + BookingEdiUtils.fnLeeLineaCoparn(sLine, 5, 2);
                    }

                    if ("BGM".equals(sSegmento)) {
                        trecordType = BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1);
                        String xvalue = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                        if (xvalue == null) {
                            xvalue = "";
                        }
                        if (xvalue.length() == 14 && isDate(xvalue.substring(0, 8))) {
                            creationDateEDI = new StringBuilder(xvalue);
                        }
                        reservationSituation = BookingEdiUtils.fnLeeLineaCoparn(sLine, 4, 1);
                    }

                    if ("DTM".equals(sSegmento) && dateDTM.isEmpty() && "137".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1))) {
                        String xvalue1 = BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 2);
                        if (xvalue1.length() == 10) {
                            dateDTM = xvalue1;
                        }
                    }

                    if ((creationDateEDI.isEmpty()) && !dateDTM.isEmpty()) {
                        creationDateEDI = new StringBuilder(dateDTM);
                    }
                    if ((creationDateEDI.isEmpty()) && !dateUNB.isEmpty()) {
                        creationDateEDI = new StringBuilder(dateUNB);
                    }


                    if (creationDateEDI.length() == 12) {
                        creationDateEDI.append("00");
                    } else if (creationDateEDI.length() == 10 && shippingLineId == 4102) {
                        creationDateEDI = new StringBuilder("20" + creationDateEDI + "00");
                    }
                    if (creationDateEDI.length() > 14) {
                        creationDateEDI = new StringBuilder(creationDateEDI.substring(0, 14));
                    }

                    if ("RFF".equals(sSegmento)) {
                        if ("BN".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && booking.isEmpty()) {
                            booking = BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 2);
                        }
                        if ("REO".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && bookingRelease.isEmpty()) {
                            bookingRelease = BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 2);
                        }
                        if ("AHP".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && refAHP.isEmpty()) {
                            refAHP = BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 2);
                        }
                        if ("ANN".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && booking.isEmpty()) {
                            booking = BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 2);
                        }
                        if ("SQ".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1))) {
                            tbSecHLL.add(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 2));
                        }
                    }

                    if ("TDT".equals(sSegmento) && "20".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1))) {
                        voyage = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                        vesselCodeListQualifier = BookingEdiUtils.fnLeeLineaCoparn(sLine, 9, 2);
                        if ("146".equals(vesselCodeListQualifier)) {
                            vesselLloydNumber = BookingEdiUtils.fnLeeLineaCoparn(sLine, 9, 1);
                        } else {
                            vesselCallSign = BookingEdiUtils.fnLeeLineaCoparn(sLine, 9, 1);
                        }
                        vesselXml = BookingEdiUtils.fnLeeLineaCoparn(sLine, 9, 4);
                    }

                    if ("LOC".equals(sSegmento)) {
                        if ("98".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1))) {
                            if (depotColombia.isEmpty())
                                depotColombia = BookingEdiUtils.fnLeeLineaCoparn(sLine, 4, 1);
                            String sPto1 = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                            if (xShipmentPort.isEmpty() && !sPto1.isEmpty()) xShipmentPort = sPto1;
                            if (loc98Depot.isEmpty())
                                loc98Depot = BookingEdiUtils.fnLeeLineaCoparn(sLine, 4, 1);
                            if (depotColumbus.isEmpty())
                                depotColumbus = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 4);
                            if (loc98DepotPos31.isEmpty())
                                loc98DepotPos31 = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                        }
                        if ("9".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && xShipmentPort.isEmpty()) {
                            xShipmentPort = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                        }
                        if ("165".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && xShipmentPort.isEmpty()) {
                            xShipmentPort = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                        }
                        if ("8".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && xDestinationPort.isEmpty()) {
                            xDestinationPort = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                        }
                        if ("11".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && xDichargePort.isEmpty()) {
                            xDichargePort = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                        }
                    }

                    if ("NAD".equals(sSegmento)) {
                        if ("CZ".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1))) {
                            if (clientCodeSCV.isEmpty())
                                clientCodeSCV = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                            if (clientNameXml.isEmpty())
                                clientNameXml = BookingEdiUtils.fnLeeLineaCoparn(sLine, 4, 1);
                        }
                        if ("CA".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && lineBKXml.isEmpty()) {
                            lineBKXml = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                        }
                        if ("CU".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && clientCustomer.isEmpty()) {
                            clientCustomer = BookingEdiUtils.fnLeeLineaCoparn(sLine, 5, 1);
                        }
                        if ("SH".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && clientShipper.isEmpty()) {
                            clientShipper = BookingEdiUtils.fnLeeLineaCoparn(sLine, 5, 1);
                        }
                        if ("CL".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && depotCodeHSDPER.isEmpty()) {
                            depotCodeHSDPER = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                        }
                        if ("MR".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && depotCodeHSDECU.isEmpty()) {
                            depotCodeHSDECU = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                        }
                    }

                    if ("FTX".equals(sSegmento)) {
                        if ("AAA".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && productGroupDes.isEmpty()) {
                            productGroupDes = BookingEdiUtils.fnLeeLineaCoparn(sLine, 5, 1) + BookingEdiUtils.fnLeeLineaCoparn(sLine, 5, 2);
                            if (productGroupDes.length() > 250) {
                                productGroupDes = productGroupDes.substring(0, 250);
                            }
                        }
                        if ("HAN".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1)) && isForColdTreatment.isEmpty()) {
                            isForColdTreatment = BookingEdiUtils.fnLeeLineaCoparn(sLine, 4, 1);
                        }
                    }

                    if ("DGS".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 1, 1)) && codigoIMO.isEmpty()) {
                        codigoIMO = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                    }

                    if ("EQD".equals(sSegmento)) {
                        if (containerCodeISO1.isEmpty() && containerCodeISO2.isEmpty()) {
                            containerCodeISO1 = BookingEdiUtils.fnLeeLineaCoparn(sLine, 4, 1);
                        } else if (!containerCodeISO1.isEmpty() && containerCodeISO2.isEmpty()) {
                            containerCodeISO2 = BookingEdiUtils.fnLeeLineaCoparn(sLine, 4, 1);
                        }
                    }

                    if ("EQN".equals(sSegmento)) {
                        if (containerCodeISO1 != null && !containerCodeISO1.isEmpty() && containerCodeISO2.isEmpty()) {
                            reserveAmount1 += Integer.parseInt(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1));
                        } else if (containerCodeISO1 != null && containerCodeISO2 != null && !containerCodeISO1.isEmpty() && !containerCodeISO2.isEmpty()) {
                            if (containerCodeISO2.equals(containerCodeISO1)) {
                                containerCodeISO2 = "";
                                reserveAmount1 += Integer.parseInt(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1));
                            } else {
                                reserveAmount2 += Integer.parseInt(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1));
                            }
                        }
                    }

                    if ("MEA".equals(sSegmento) && "AAE".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 1))) {
                        if ("G".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1)) && netWeight.isEmpty()) {
                            netWeight = BookingEdiUtils.fnLeeLineaCoparn(sLine, 4, 2);
                        }
                        if ("T".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1)) && tare.isEmpty()) {
                            tare = BookingEdiUtils.fnLeeLineaCoparn(sLine, 4, 2);
                        }
                        if ("ZO".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1)) && oxygen.isEmpty()) {
                            oxygen = BookingEdiUtils.fnLeeLineaCoparn(sLine, 4, 2);
                        }
                        if ("CD".equals(BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1)) && carbonDioxide.isEmpty()) {
                            carbonDioxide = BookingEdiUtils.fnLeeLineaCoparn(sLine, 4, 2);
                        }
                    }

                    if ("TMP".equals(sSegmento)) {
                        if (temperature.isEmpty()) temperature = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 1);
                        if (temperatureUnit.isEmpty())
                            temperatureUnit = BookingEdiUtils.fnLeeLineaCoparn(sLine, 3, 2);
                    }

                    if ("CNT".equals(sSegmento) && Objects.equals(unitBusinessId, isUnCostaRicaId)) {
                        reserveAmount1 = Integer.parseInt(BookingEdiUtils.fnLeeLineaCoparn(sLine, 2, 2));
                        containerCodeISO2 = "";
                        reserveAmount2 = 0;
                    }

                }

            }

            if (Objects.equals(catBkEdiMessageTypeId, isEdiMessageType301)) {
                if (copanrFileContentORI.contains("~")) {
                    copanrFileContentORI = copanrFileContentORI.replace("~", "");
                }
                coparnFileContent = copanrFileContentORI.replace("\r\n", "")
                        .replace("\n", "")
                        .replace("ST*", "|ST*")
                        .replace("GS*", "|GS*")
                        .replace("B1*", "|B1*")
                        .replace("Y3*", "|Y3*")
                        .replace("Y4*", "|Y4*")
                        .replace("N9*", "|N9*")
                        .replace("N1*", "|N1*")
                        .replace("N3*", "|N3*")
                        .replace("N4*", "|N4*")
                        .replace("R4*", "|R4*")
                        .replace("DTM*", "|DTM*")
                        .replace("LX*", "|LX*")
                        .replace("K1*", "|K1*")
                        .replace("N7*", "|N7*")
                        .replace("L0*", "|L0*")
                        .replace("L5*", "|L5*")
                        .replace("H1*", "|H1*")
                        .replace("V1*", "|V1*")
                        .replace("V9*", "|V9*")
                        .replace("SE*", "|SE*")
                        .replace("GE*", "|GE*")
                        .replace("IEA*", "|IEA*");

                String[] splitArray = coparnFileContent.split("\\|");

                for (int i = 0; i < splitArray.length; i++) {
                    String valor = splitArray[i];
                    tbContent.add(new TbContent(i + 1, valor));
                }


                int maxPos = tbContent.stream().mapToInt(TbContent::getPos).max().orElse(0);

                for (n = 1; n <= maxPos; n++) {
                    Integer finalN1 = n;
                    String sLine301 = tbContent.stream()
                            .filter(contenido -> contenido.getPos() == finalN1)
                            .map(TbContent::getValue)
                            .findFirst()
                            .orElse(null);


                    List<TbvEntry> tbv = new ArrayList<>();
                    String[] splitValues = sLine301.split("\\*");

                    for (int i = 0; i < splitValues.length; i++) {
                        tbv.add(new TbvEntry(i + 1, splitValues[i].trim()));
                    }


                    sSegmento = tbv.stream()
                            .filter(entry -> entry.getIndex() == 1)
                            .map(TbvEntry::getValue)
                            .findFirst()
                            .orElse("");

                    if ("ISA".equals(sSegmento)) {
                        String value10 = tbv.stream()
                                .filter(entry -> entry.getIndex() == 10)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");
                        String value11 = tbv.stream()
                                .filter(entry -> entry.getIndex() == 11)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");
                        dateUNB = value10 + value11;
                    }


                    if ("GS".equals(sSegmento)) {
                        String value5 = tbv.stream()
                                .filter(entry -> entry.getIndex() == 5)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");
                        String value6 = tbv.stream()
                                .filter(entry -> entry.getIndex() == 6)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");
                        creationDateEDI = new StringBuilder(value5 + value6);
                    }

                    if ("B1".equals(sSegmento)) {
                        lineBKXml = tbv.stream()
                                .filter(entry -> entry.getIndex() == 2)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");

                        reservationSituation = tbv.stream()
                                .filter(entry -> entry.getIndex() == 5)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");

                        bookingNumberTemp = tbv.stream()
                                .filter(entry -> entry.getIndex() == 3)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");
                    }

                    if (creationDateEDI.length() == 12) {
                        creationDateEDI.append("00");
                    }

                    if ("Y4".equals(sSegmento)) {
                        if (containerCodeISO1.isEmpty() && containerCodeISO2.isEmpty()) {
                            containerCodeISO1 = tbv.stream()
                                    .filter(entry -> entry.getIndex() == 7)
                                    .map(TbvEntry::getValue)
                                    .findFirst()
                                    .orElse("");
                        } else {
                            if (!containerCodeISO1.isEmpty() && containerCodeISO2.isEmpty()) {
                                containerCodeISO2 = tbv.stream()
                                        .filter(entry -> entry.getIndex() == 7)
                                        .map(TbvEntry::getValue)
                                        .findFirst()
                                        .orElse("");
                            }
                        }

                        if (!containerCodeISO1.isEmpty() && containerCodeISO2.isEmpty()) {
                            reserveAmount1 += tbv.stream()
                                    .filter(entry -> entry.getIndex() == 6)
                                    .mapToInt(entry -> Integer.parseInt(entry.getValue()))
                                    .findFirst()
                                    .orElse(0);
                        }

                        if (!containerCodeISO1.isEmpty() && !containerCodeISO2.isEmpty()) {
                            if (containerCodeISO2.equals(containerCodeISO1)) {
                                containerCodeISO2 = "";
                                reserveAmount1 += tbv.stream()
                                        .filter(entry -> entry.getIndex() == 6)
                                        .mapToInt(entry -> Integer.parseInt(entry.getValue()))
                                        .findFirst()
                                        .orElse(0);
                            } else {
                                reserveAmount2 += tbv.stream()
                                        .filter(entry -> entry.getIndex() == 6)
                                        .mapToInt(entry -> Integer.parseInt(entry.getValue()))
                                        .findFirst()
                                        .orElse(0);
                            }
                        }
                    }

                    if ("N9".equals(sSegmento)) {


                        booking = tbv.stream()
                                .filter(entry -> entry.getIndex() == 3)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");
                    }

                    if ("N1".equals(sSegmento)) {
                        boolean isSh = tbv.stream()
                                .filter(entry -> entry.getIndex() == 2)
                                .anyMatch(entry -> "SH".equals(entry.getValue()));

                        if (isSh) {
                            clientShipper = tbv.stream()
                                    .filter(entry -> entry.getIndex() == 3)
                                    .map(TbvEntry::getValue)
                                    .findFirst()
                                    .orElse("");
                        }
                    }

                    if ("R4".equals(sSegmento)) {
                        String valueAtIndex2 = tbv.stream()
                                .filter(entry -> entry.getIndex() == 2)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");

                        if ("R".equals(valueAtIndex2)) {
                            r403RefDepotLocationCode = tbv.stream()
                                    .filter(entry -> entry.getIndex() == 4)
                                    .map(TbvEntry::getValue)
                                    .findFirst()
                                    .orElse("");
                            r404RefDepotPortName = tbv.stream()
                                    .filter(entry -> entry.getIndex() == 5)
                                    .map(TbvEntry::getValue)
                                    .findFirst()
                                    .orElse("");
                        }

                        if ("L".equals(valueAtIndex2)) {
                            String valueAtIndex3 = tbv.stream()
                                    .filter(entry -> entry.getIndex() == 3)
                                    .map(TbvEntry::getValue)
                                    .findFirst()
                                    .orElse("");
                            if ("UN".equals(valueAtIndex3)) {
                                xShipmentPort = tbv.stream()
                                        .filter(entry -> entry.getIndex() == 4)
                                        .map(TbvEntry::getValue)
                                        .findFirst()
                                        .orElse("");
                            }
                        }

                        if ("D".equals(valueAtIndex2)) {
                            String valueAtIndex3 = tbv.stream()
                                    .filter(entry -> entry.getIndex() == 3)
                                    .map(TbvEntry::getValue)
                                    .findFirst()
                                    .orElse("");
                            if ("UN".equals(valueAtIndex3)) {
                                xDichargePort = tbv.stream()
                                        .filter(entry -> entry.getIndex() == 4)
                                        .map(TbvEntry::getValue)
                                        .findFirst()
                                        .orElse("");
                            }
                        }

                        if ("1".equals(valueAtIndex2)) {
                            String valueAtIndex3 = tbv.stream()
                                    .filter(entry -> entry.getIndex() == 3)
                                    .map(TbvEntry::getValue)
                                    .findFirst()
                                    .orElse("");
                            if ("UN".equals(valueAtIndex3)) {
                                xDestinationPort = tbv.stream()
                                        .filter(entry -> entry.getIndex() == 4)
                                        .map(TbvEntry::getValue)
                                        .findFirst()
                                        .orElse("");
                            }
                        }
                    }

                    if ("W09".equals(sSegmento)) {
                        temperature = tbv.stream()
                                .filter(entry -> entry.getIndex() == 3)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");

                        temperatureUnit = tbv.stream()
                                .filter(entry -> entry.getIndex() == 4)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");


                    }

                    if ("K1".equals(sSegmento)) {
                        String value2 = tbv.stream()
                                .filter(entry -> entry.getIndex() == 2)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");
                        String value3 = tbv.stream()
                                .filter(entry -> entry.getIndex() == 3)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");

                        remark = (value2 + " " + value3).trim().toUpperCase();

                        if (!remark.isEmpty()) {
                            List<SystemRule> systemRules =
                                    systemRuleRepository.findSystemRuleByAliasAndActive("sd1_edi_booking_remark_rules", true);
                            SystemRule activeRule = systemRules.getFirst();

                            if (activeRule != null) {
                                JSONArray jsonArray = new JSONArray(activeRule.getRule());
                                JSONObject ruleJson = jsonArray.getJSONObject(0);
                                String listOfKeywords = ruleJson.getString("list_of_keyword");

                                List<String> keywords = Arrays.stream(listOfKeywords.split(","))
                                        .map(String::toUpperCase)
                                        .toList();

                                for (String keyword : keywords) {
                                    if (remark.contains(keyword)) {
                                        flagToFlex = true;
                                        break;
                                    }
                                }
                            }
                        }
                    }


                    if ("N7".equals(sSegmento)) {
                        TbCnts equipmentDetails = new TbCnts(
                                tbv.stream().filter(entry -> entry.getIndex() == 2).map(TbvEntry::getValue).findFirst().orElse(null),  // equip_initial
                                tbv.stream().filter(entry -> entry.getIndex() == 3).map(TbvEntry::getValue).findFirst().orElse(null),  // equip_number
                                Integer.parseInt(tbv.stream().filter(entry -> entry.getIndex() == 4).map(TbvEntry::getValue).findFirst().orElse(null)),  // weight
                                tbv.stream().filter(entry -> entry.getIndex() == 5).map(TbvEntry::getValue).findFirst().orElse(null),  // weight_qualifier
                                Integer.parseInt(tbv.stream().filter(entry -> entry.getIndex() == 6).map(TbvEntry::getValue).findFirst().orElse(null)),  // tare
                                tbv.stream().filter(entry -> entry.getIndex() == 11).map(TbvEntry::getValue).findFirst().orElse(null), // ownership_code
                                tbv.stream().filter(entry -> entry.getIndex() == 12).map(TbvEntry::getValue).findFirst().orElse(null), // equip_code
                                tbv.stream().filter(entry -> entry.getIndex() == 17).map(TbvEntry::getValue).findFirst().orElse(null), // tare_qualifier
                                tbv.stream().filter(entry -> entry.getIndex() == 18).map(TbvEntry::getValue).findFirst().orElse(null), // weight_unit
                                tbv.stream().filter(entry -> entry.getIndex() == 19).map(TbvEntry::getValue).findFirst().orElse(null), // equip_check_digit
                                tbv.stream().filter(entry -> entry.getIndex() == 20).map(TbvEntry::getValue).findFirst().orElse(null)  // equip_type
                        );

                        tbCnts.add(equipmentDetails);
                    }

                    if ("L5".equals(sSegmento)) {
                        productGroupDes = tbv.stream()
                                .filter(entry -> entry.getIndex() == 3)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse(null);
                    }


                    if ("H1".equals(sSegmento)) {
                        if (codigoIMO == null || codigoIMO.isEmpty()) {
                            codigoIMO = tbv.stream()
                                    .filter(entry -> entry.getIndex() == 3)
                                    .map(TbvEntry::getValue)
                                    .findFirst()
                                    .orElse(null);


                        }

                        String imdgNumber = tbv.stream()
                                .filter(entry -> entry.getIndex() == 3)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse("");

                        if (!imdgNumber.isEmpty()) {
                            productGroupDes = (productGroupDes != null ? productGroupDes : "") + " | IMDG number " + imdgNumber;
                        }
                    }

                    if ("V1".equals(sSegmento)) {

                        vesselCodeListQualifier = tbv.stream()
                                .filter(entry -> entry.getIndex() == 9)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse(null);


                        vesselXml = tbv.stream()
                                .filter(entry -> entry.getIndex() == 3)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse(null);


                        voyage = tbv.stream()
                                .filter(entry -> entry.getIndex() == 5)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse(null);


                        vesselLloydNumber = null;
                        vesselCallSign = null;
                        String valueAtIndex2 = tbv.stream()
                                .filter(entry -> entry.getIndex() == 2)
                                .map(TbvEntry::getValue)
                                .findFirst()
                                .orElse(null);

                        if ("L".equals(vesselCodeListQualifier)) {
                            vesselLloydNumber = valueAtIndex2;
                        } else {
                            vesselCallSign = valueAtIndex2;
                        }
                    }


                }

            }


        }

        if ((booking == null || booking.isEmpty()) && (bookingNumberTemp != null && !bookingNumberTemp.isEmpty())) {
            booking = bookingNumberTemp;
        }


        JSONObject jsonObject = new JSONObject();
        jsonObject.put("TipoRegistro", trecordType);
        jsonObject.put("FechaCreacionEDI", creationDateEDI.toString());
        jsonObject.put("SituacionReserva", reservationSituation);
        jsonObject.put("Booking", booking);
        jsonObject.put("Booking_Release", bookingRelease);
        jsonObject.put("Ref_AHP", refAHP);
        jsonObject.put("Viaje_edi", voyage);
        jsonObject.put("Nave_CodeListQualifier_edi", vesselCodeListQualifier);
        jsonObject.put("Nave_CallSign_edi", vesselCallSign);
        jsonObject.put("Nave_LloydNumber_edi", vesselLloydNumber);
        jsonObject.put("Nave_edi", vesselXml);
        jsonObject.put("Deposito_Colombia", depotColombia);
        jsonObject.put("Deposito_Columbus", depotColumbus);
        jsonObject.put("xPtoEmbarque", xShipmentPort);
        jsonObject.put("Loc98_Deposito", loc98Depot);
        jsonObject.put("PtoDestino_edi", xDestinationPort);
        jsonObject.put("PtoDescarga_edi", xDichargePort);
        jsonObject.put("Cliente_CodigoSCV_edi", clientCodeSCV);
        jsonObject.put("Cliente_Nombre_edi", clientNameXml);
        jsonObject.put("LineaBK_edi", lineBKXml);
        jsonObject.put("Cliente_Customer", clientCustomer);
        jsonObject.put("Cliente_Shipper", clientShipper);
        jsonObject.put("CodigoDepot_HSD_PER", depotCodeHSDPER);
        jsonObject.put("CodigoDepot_HSD_ECU", depotCodeHSDECU);
        jsonObject.put("Mercaderia_edi", productGroupDes);
        jsonObject.put("Para_ColdTreatment", isForColdTreatment);
        jsonObject.put("CodigoIMO_edi", codigoIMO);
        jsonObject.put("Contenedor_CodigoISO1", containerCodeISO1);
        jsonObject.put("Contenedor_CodigoISO2", containerCodeISO2);
        jsonObject.put("CantidadReserva1", reserveAmount1);
        jsonObject.put("CantidadReserva2", reserveAmount2);
        jsonObject.put("PesoNeto", netWeight);
        jsonObject.put("Tara", tare);
        jsonObject.put("Oxigeno", oxygen);
        jsonObject.put("DioxidoCarbono", carbonDioxide);
        jsonObject.put("Temperatura", temperature);
        jsonObject.put("UnidadTemperatura", temperatureUnit);
        jsonObject.put("Loc98_Depot_Pos3_1", loc98DepotPos31);
        jsonObject.put("R403_RefDepot_LocationCode", r403RefDepotLocationCode);
        jsonObject.put("R404_RefDepot_PortName", r404RefDepotPortName);
        jsonObject.put("details", tbCnts.toString());
        jsonObject.put("sequence_details", tbSecHLL.toString());


        Optional<BookingEdiFile> optionalBookingEdiFile = ediFileRepository.findById(ediCoparnId);
        if (optionalBookingEdiFile.isPresent()) {
            BookingEdiFile bookingEdiFile = optionalBookingEdiFile.get();
            bookingEdiFile.setBkEdiJson(jsonObject.toString());
            bookingEdiFile.setDateModificationCoparn(LocalDateTime.now());
            ediFileRepository.save(bookingEdiFile);
        }
        Optional<BookingEdi> optionalBookingEdi1 = bookingEdiRepository.findByIdAndActiveIsTrue(ediCoparnId);
        if (optionalBookingEdi1.isPresent()) {
            catEdiCoparnStatus = optionalBookingEdi1.get().getCatBkEdiStatus().getId();
        }
        if (catEdiCoparnStatus != null && (catEdiCoparnStatus.equals(isBkediPending) || catEdiCoparnStatus.equals(isBkediToProcess))) {

            if ("COT".equals(isForColdTreatment)) {
                coldTreatment = true;
            }
            if ("COA".equals(isForColdTreatment)) {
                controlledAtmosphere = true;
            }


            if (trecordType == null || trecordType.isEmpty()) {
                trecordType = "12";
            }


            if (reservationSituation == null) {
                reservationSituation = "";
            }
            reservationSituation = reservationSituation.trim();
            switch (reservationSituation) {
                case "D":
                    reservationSituation = "1";
                    break;
                case "N":
                    reservationSituation = "9";
                    break;
                case "U", "R":
                    reservationSituation = "5";
                    break;
                default:
                    break;
            }


            if (shippingLineId == 4102 || shippingLineId == 4105) {
                trecordType = "12";
            }


            if (shippingLineId == 4102 && (lineBKXml == null || lineBKXml.isEmpty())) {
                lineBKXml = "HSD";
            }


            lineBKId = shippingLineId;


            if (shippingLineId == 4104) {
                String lineaBkXml = lineBKXml != null ? lineBKXml : "";
                if (lineaBkXml.startsWith("SEA")) {
                    lineBKId = 4106; // SEALAND
                } else {
                    lineBKId = 4104; // MAERSK LINE
                }
            }


            if (shippingLineId == 4103) {

                if (bookingRelease != null && !bookingRelease.isEmpty()) {
                    booking = bookingRelease;
                }


                if (dateUNB != null && dateUNB.length() == 10) {
                    creationDate = ("20" + dateUNB + "00");
                }


                if (clientCustomer == null || clientCustomer.isEmpty()) {
                    clientNameXml = clientShipper;
                } else {
                    clientNameXml = clientCustomer;
                }


                if (xDestinationPort == null || xDestinationPort.isEmpty()) {
                    xDestinationPort = "ZZZHLL";
                }
                if (xDichargePort == null || xDichargePort.isEmpty()) {
                    xDichargePort = "ZZZHLL";
                }


                if (dateUNB != null && dateUNB.length() == 10) {
                    dateUNB = "20" + dateUNB.substring(0, 6);
                    if (isValidDate(dateUNB)) {
                        creationDate = "20" + dateUNB + "00";
                    }
                }


                if (vesselXml != null && !vesselXml.isEmpty()) {
                    List<Vessel> vessels = vesselRepository.findNaveIdByNameAndActiveTrue(vesselXml);
                    for (Vessel vessel : vessels) {
                        tbVessels.add(vessel.getId());
                    }
                }


                if (tbVessels.isEmpty() && vesselCallSign != null && !vesselCallSign.isEmpty()) {
                    List<Vessel> vesselsImo = vesselRepository.findNaveIdByImoAndActiveTrue(vesselCallSign);
                    if (vesselsImo.size() == 1) {
                        for (Vessel vessel : vesselsImo) {
                            tbVessels.add(vessel.getId());
                        }
                    }
                }


                if (tbVessels.isEmpty() && vesselLloydNumber != null && !vesselLloydNumber.isEmpty()) {
                    List<Vessel> vesselsImo = vesselRepository.findNaveIdByImoAndActiveTrue(vesselLloydNumber);
                    if (vesselsImo.size() == 1) {
                        for (Vessel vessel : vesselsImo) {
                            tbVessels.add(vessel.getId());
                        }
                    }
                }
                Optional<Port> optionalPort;

                if (Objects.equals(xShipmentPort, "") || !isNumeric(xShipmentPort)) {
                    optionalPort = Optional.empty();
                } else {
                    optionalPort = portRepository.findById(Integer.valueOf(xShipmentPort));
                }

                if (optionalPort.isPresent()) {
                    shipmentPortId = optionalPort.get().getId();
                }


                if (shipmentPortId != null && shipmentPortId != 0) {
                    List<BookingEdiSettingBU> bookingEdiSettingBUS = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, 22);
                    if ("PECLL".equals(xShipmentPort) && !bookingEdiSettingBUS.isEmpty()) {
                        subBusinessUnitId = 22; // CALLAO
                    }
                    List<BookingEdiSettingBU> bookingEdiSettingBUS1 = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, 23);
                    if ("PEPAI".equals(xShipmentPort) &&
                            !bookingEdiSettingBUS1.isEmpty()) {
                        subBusinessUnitId = 23; // PAITA
                    }
                }

                if (subBusinessUnitId == null || subBusinessUnitId == 0) {
                    Integer subUnidadNegocioIdTemp = bookingEdiRepository.findSubBusinessUNitIdByFileName(ediCoparnId);

                    if (subUnidadNegocioIdTemp != null && subUnidadNegocioIdTemp != 0) {
                        List<BookingEdiSettingBU> bookingEdiSettingBUS2 = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, subUnidadNegocioIdTemp);
                        boolean isConfigured = !bookingEdiSettingBUS2.isEmpty();
                        if (isConfigured) {
                            subBusinessUnitId = subUnidadNegocioIdTemp;
                        }
                    }
                }
                List<BookingEdiSettingBU> bookingEdiSettingBUS3;
                bookingEdiSettingBUS3 = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, 29);

                if ("ECGYEOP".equals(loc98Depot) &&
                        !bookingEdiSettingBUS3.isEmpty()) {
                    subBusinessUnitId = 29; // OPACIF ZAL
                }

                bookingEdiSettingBUS3 = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, 22);

                if ("CLLAF".equals(trimOrEmpty(depotCodeHSDPER)) &&
                        !bookingEdiSettingBUS3.isEmpty()) {
                    subBusinessUnitId = 22; // CALLAO
                }

                bookingEdiSettingBUS3 = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, 23);
                if ("PAIAE".equals(trimOrEmpty(depotCodeHSDPER)) &&
                        !bookingEdiSettingBUS3.isEmpty()) {
                    subBusinessUnitId = 23; // PAITA
                }

                bookingEdiSettingBUS3 = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, 28);
                if ("ECGYEOPAM".equals(depotCodeHSDECU) &&
                        !bookingEdiSettingBUS3.isEmpty()) {
                    subBusinessUnitId = 28; // NAD+MR+ECGYEOPAM -> Opacif Sur
                }

                bookingEdiSettingBUS3 = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, 30);
                if ("ECGYEOPAC".equals(depotCodeHSDECU) &&
                        !bookingEdiSettingBUS3.isEmpty()) {
                    subBusinessUnitId = 30; // NAD+MR+ECGYEOPAC -> Opacif Norte
                }

                bookingEdiSettingBUS3 = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, 45);
                if ("BOORU".equals(trimOrEmpty(loc98DepotPos31)) &&
                        !bookingEdiSettingBUS3.isEmpty()) {
                    subBusinessUnitId = 45; // ORURO
                }

                bookingEdiSettingBUS3 = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, 47);
                if ("BOCBB".equals(trimOrEmpty(loc98DepotPos31)) &&
                        !bookingEdiSettingBUS3.isEmpty()) {
                    subBusinessUnitId = 47; // COCHABAMBA
                }

                bookingEdiSettingBUS3 = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, 50);
                if ("CRPTC".equals(trimOrEmpty(loc98DepotPos31)) &&
                        !bookingEdiSettingBUS3.isEmpty()) {
                    subBusinessUnitId = 50; // Caldera
                }

                bookingEdiSettingBUS3 = bookingEdiSettingBURepository.findByIdAndSubBusinessUnitAndActiveTrue(seteoEdiCoparnId, 52);
                if ("CRLIO".equals(trimOrEmpty(loc98DepotPos31)) &&
                        !bookingEdiSettingBUS3.isEmpty()) {
                    subBusinessUnitId = 52; // Limón
                }

                if (subBusinessUnitId == null || subBusinessUnitId == 0) {
                    long count = bookingEdiSettingBURepository.findByIdAndActiveTrue(seteoEdiCoparnId).size();

                    if (count == 1) {
                        subBusinessUnitId = bookingEdiSettingBURepository.findByIdAndActiveTrue(seteoEdiCoparnId).getFirst().getSubBusinessUnit().getId();
                    }
                }

                if (subBusinessUnitId != null && subBusinessUnitId > 0) {
                    businessUnitId = businessUnitRepository.findParentBusinessUnitId(subBusinessUnitId);

                    if (xDestinationPort != null && !xDestinationPort.trim().isEmpty()) {
                        Optional<Port> optionalPort1 = portRepository.findByPort(xDestinationPort);
                        if (optionalPort1.isPresent()) {
                            destinationPortId = optionalPort1.get().getId();
                        }

                        if (destinationPortId == null) {
                            List<PortMaster> portMasters = portMasterRepository.findByPortCode(xDestinationPort);
                            if (!portMasters.isEmpty()) {
                                Optional<Port> portList = portRepository.findByPort(xDestinationPort);
                                if (portList.isEmpty()) {
                                    Port port = new Port();
                                    port.setPort(portMasters.getFirst().getPortCode());
                                    port.setName(portMasters.getFirst().getDescription());
                                    port.setActive(true);
                                    port.setRegistrationDate(LocalDateTime.now());

                                    port.setRegistrationUser(user);
                                    Catalog catalog = new Catalog();
                                    catalog.setId(isCreationSourceBkedi);
                                    port.setCatCreationOrigin(catalog);
                                    portRepository.save(port);
                                } else {
                                    Optional<Port> optionalPortEdit = portRepository.findByIdAndActiveTrue(xDestinationPort);
                                    if (optionalPortEdit.isPresent()) {
                                        Port port = optionalPortEdit.get();
                                        port.setActive(true);
                                        port.setRegistrationDate(LocalDateTime.now());

                                        port.setRegistrationUser(user);
                                        portRepository.save(port);
                                    }
                                }

                            } else {
                                String xPtoTemp = xDestinationPort.substring(0, 4) + "%";

                                Optional<PortMaster> puertoSunatOpt = portMasterRepository.findTop1PortCodeLikeOrderByPortCodeDesc(xPtoTemp);
                                if (puertoSunatOpt.isPresent()) {
                                    xDestinationPort = puertoSunatOpt.get().getPortCode();

                                    Optional<Port> puertoOpt = portRepository.findByPort(xDestinationPort);
                                    if (puertoOpt.isEmpty()) {

                                        Port newPort = new Port();
                                        newPort.setPort(xDestinationPort.trim());
                                        newPort.setName(puertoSunatOpt.get().getDescription().trim());
                                        newPort.setActive(true);
                                        newPort.setRegistrationDate(LocalDateTime.now());

                                        newPort.setRegistrationUser(user);
                                        Catalog catalog = new Catalog();
                                        catalog.setId(isCreationSourceBkedi);
                                        newPort.setCatCreationOrigin(catalog);
                                        portRepository.save(newPort);
                                    } else {

                                        Port port = puertoOpt.get();
                                        if (Boolean.FALSE.equals(port.getActive())) {
                                            port.setActive(true);

                                            port.setModificationUser(user);
                                            port.setModificationDate(LocalDateTime.now());
                                            portRepository.save(port);
                                        }
                                    }
                                } else {

                                    xPtoTemp = xDestinationPort.substring(0, 3) + "%";
                                    puertoSunatOpt = portMasterRepository.findTop1PortCodeLikeOrderByPortCodeDesc(xPtoTemp);
                                    if (puertoSunatOpt.isPresent()) {
                                        xDestinationPort = puertoSunatOpt.get().getPortCode();

                                        Optional<Port> puertoOpt = portRepository.findByPort(xDestinationPort);
                                        if (puertoOpt.isEmpty()) {

                                            Port newPort = new Port();
                                            newPort.setPort(xDestinationPort.trim());
                                            newPort.setName(puertoSunatOpt.get().getDescription().trim());
                                            newPort.setActive(true);
                                            newPort.setRegistrationDate(LocalDateTime.now());

                                            newPort.setRegistrationUser(user);
                                            Catalog catalog = new Catalog();
                                            catalog.setId(isCreationSourceBkedi);
                                            newPort.setCatCreationOrigin(catalog);
                                            portRepository.save(newPort);
                                        } else {

                                            Port port = puertoOpt.get();
                                            if (Boolean.FALSE.equals(port.getActive())) {
                                                port.setActive(true);

                                                port.setModificationUser(user);
                                                port.setModificationDate(LocalDateTime.now());
                                                portRepository.save(port);
                                            }
                                        }
                                    }
                                }
                                Optional<Port> portOptional = portRepository.findByPortAndActiveTrue(xDestinationPort);
                                if (portOptional.isPresent()) {
                                    destinationPortId = portOptional.get().getId();
                                }

                            }
                        }

                        Optional<Port> portOptionalEdit = portRepository.findByIdAndActiveFalse(destinationPortId);
                        if (portOptionalEdit.isPresent()) {
                            Port port = portOptionalEdit.get();
                            port.setActive(true);
                            port.setModificationDate(LocalDateTime.now());

                            port.setModificationUser(user);
                            portRepository.save(port);
                        }


                    }

                    if ((xDichargePort == null || xDichargePort.isEmpty()) && destinationPortId != null && destinationPortId > 0) {
                        dischargePortId = destinationPortId;
                    }

                    if ((dischargePortId == null || dischargePortId == 0) && xDichargePort != null && !xDichargePort.isEmpty()) {

                        Optional<Port> portOptional = portRepository.findByPort(xDichargePort);
                        if (portOptional.isPresent()) {
                            dischargePortId = portOptional.get().getId();
                        }


                        if (portOptional.isPresent()) {
                            Port port = portOptional.get();
                            port.setActive(true);

                            port.setModificationUser(user);
                            port.setModificationDate(LocalDateTime.now());
                            portRepository.save(port);

                        } else {

                            List<PortMaster> portMasters = portMasterRepository.findByPortCode(xDichargePort);

                            if (!portMasters.isEmpty()) {

                                Optional<Port> portOptional1 = portRepository.findByPort(xDichargePort);

                                if (portOptional1.isEmpty()) {
                                    Port newPort = new Port();
                                    newPort.setPort(portMasters.getFirst().getPortCode());
                                    newPort.setName(portMasters.getFirst().getDescription());
                                    newPort.setActive(true);
                                    newPort.setRegistrationDate(LocalDateTime.now());

                                    newPort.setRegistrationUser(user);
                                    Catalog catalog = new Catalog();
                                    catalog.setId(isCreationSourceBkedi);
                                    newPort.setCatCreationOrigin(catalog);
                                    portRepository.save(newPort);
                                } else {
                                    Port port = portOptional1.get();
                                    port.setActive(true);
                                    port.setModificationDate(LocalDateTime.now());

                                    port.setModificationUser(user);
                                    portRepository.save(port);

                                }
                            } else {

                                String xPtoTemp = xDichargePort.substring(0, 4) + "%";
                                Optional<PortMaster> closestPtoDescarga = portMasterRepository.findTop1PortCodeLikeOrderByPortCodeDesc(xPtoTemp);

                                if (closestPtoDescarga.isPresent()) {
                                    xDichargePort = closestPtoDescarga.get().getPortCode();
                                    Optional<Port> optionalPort1 = portRepository.findByPort(xDichargePort);
                                    if (optionalPort1.isEmpty()) {
                                        Port newPort = new Port();
                                        newPort.setPort(xDichargePort.trim());
                                        newPort.setName(closestPtoDescarga.get().getDescription());
                                        newPort.setActive(true);
                                        newPort.setRegistrationDate(LocalDateTime.now());

                                        newPort.setRegistrationUser(user);
                                        Catalog catalog = new Catalog();
                                        catalog.setId(isCreationSourceBkedi);
                                        newPort.setCatCreationOrigin(catalog);
                                        portRepository.save(newPort);
                                    } else {
                                        Port port = optionalPort1.get();
                                        port.setActive(true);
                                        port.setModificationDate(LocalDateTime.now());

                                        port.setModificationUser(user);
                                        portRepository.save(port);

                                    }
                                } else {
                                    xPtoTemp = xDestinationPort.substring(0, 3) + "%";
                                    closestPtoDescarga = portMasterRepository.findTop1PortCodeLikeOrderByPortCodeDesc(xPtoTemp);
                                    Optional<Port> optionalPort1 = portRepository.findByPort(xDichargePort);
                                    if (closestPtoDescarga.isPresent()) {
                                        xDichargePort = closestPtoDescarga.get().getPortCode();

                                        if (optionalPort1.isEmpty()) {
                                            Port newPort = new Port();
                                            newPort.setPort(xDichargePort.trim());
                                            newPort.setName(closestPtoDescarga.get().getDescription());
                                            newPort.setActive(true);
                                            newPort.setRegistrationDate(LocalDateTime.now());

                                            Catalog catalog = new Catalog();
                                            catalog.setId(isCreationSourceBkedi);
                                            newPort.setCatCreationOrigin(catalog);
                                            portRepository.save(newPort);
                                        } else {
                                            Port port = optionalPort1.get();
                                            port.setActive(true);
                                            port.setModificationDate(LocalDateTime.now());

                                            port.setModificationUser(user);
                                            portRepository.save(port);

                                        }
                                    }
                                }
                            }
                            Optional<Port> portOptional2 = portRepository.findByPort(xDichargePort);
                            if (portOptional2.isPresent()) {
                                dischargePortId = portOptional.get().getId();
                            }

                        }
                    }

                    if ((destinationPortId == null || destinationPortId == 0) && (dischargePortId != null && dischargePortId != 0)) {
                        destinationPortId = dischargePortId;
                    }

                    Optional<IsoCode> optionalIsoCode = isoCodeRepository.findByIsoCodeAndActiveTrue(containerCodeISO1);
                    if (optionalIsoCode.isPresent()) {
                        cntDimenId = optionalIsoCode.get().getCatSize().getId();
                        cntTypeId = optionalIsoCode.get().getCatContainerType().getId();
                        codeIsoId = optionalIsoCode.get().getId();
                    }

                    if (containerCodeISO2 != null && !containerCodeISO2.isEmpty()) {
                        Optional<IsoCode> optionalIsoCode2 = isoCodeRepository.findByIsoCodeAndActiveTrue(containerCodeISO2);
                        if (optionalIsoCode2.isPresent()) {
                            cntDimen2Id = optionalIsoCode2.get().getCatSize().getId();
                            cntType2Id = optionalIsoCode2.get().getCatContainerType().getId();

                            codeIso2Id = optionalIsoCode2.get().getId();
                        }
                    }

                    if ((cntDimen2Id == null || cntDimen2Id == 0) || (cntType2Id == null || cntType2Id == 0)) {
                        reserveAmount2 = 0;
                    }


                    if (clientCodeSCV != null && !clientCodeSCV.isEmpty()) {

                        Integer empresaCount = companyRepository.countByAliasAndActiveTrue(clientCodeSCV);

                        if (empresaCount == 1) {
                            Optional<Company> comapny = companyRepository.findByAliasAndActiveTrue(clientCodeSCV);
                            if (comapny.isPresent()) {
                                clientId = comapny.get().getId();
                                clientRS = comapny.get().getLegalName();
                            }
                        }
                    }

                    if ((clientNameXml.isEmpty()) &&
                            (clientShipper != null && !clientShipper.isEmpty())) {
                        clientNameXml = clientShipper;
                    }


                    if (clientNameXml != null && !clientNameXml.isEmpty() &&
                            (clientId == null || clientId == 0)) {

                        int count = companyRepository.countByLegalNameAndActiveTrue(clientNameXml.trim().toUpperCase());

                        if (count == 1) {

                            Optional<Company> empresa = companyRepository.findByLegalNameAndActiveTrue(clientNameXml.trim().toUpperCase());
                            if (empresa.isPresent()) {
                                clientId = empresa.get().getId();
                                clientRS = empresa.get().getLegalName();
                            }
                        }


                        if ((clientId == null || clientId == 0) && count > 1 && clientCodeSCV != null && !clientCodeSCV.isEmpty()) {

                            count = companyRepository.countByLegalNameAndAliasAndActiveTrue(clientNameXml.trim().toUpperCase(), clientCodeSCV);

                            if (count == 1) {

                                Optional<Company> empresa = companyRepository.findByLegalNameAndAliasAndActiveTrue(clientNameXml.trim().toUpperCase(), clientCodeSCV);
                                if (empresa.isPresent()) {
                                    clientId = empresa.get().getId();
                                    clientRS = empresa.get().getLegalName();
                                }
                            }
                        }

                        if ((clientId == null || clientId == 0) && count > 1 && clientCodeSCV != null && !clientCodeSCV.isEmpty()) {

                            count = companyRepository.countByLegalNameAndAliasAndDocumentAndActiveTrue(
                                    clientNameXml.trim().toUpperCase(),
                                    clientCodeSCV,
                                    clientCodeSCV
                            );

                            if (count == 1) {

                                Optional<Company> empresa = companyRepository.findByLegalNameAndAliasAndDocumentAndActiveTrue(
                                        clientNameXml.trim().toUpperCase(),
                                        clientCodeSCV,
                                        clientCodeSCV
                                );

                                if (empresa.isPresent()) {
                                    clientId = empresa.get().getId();
                                    clientRS = empresa.get().getLegalName();
                                }
                            }
                        }

                        if ((clientId == null || clientId == 0) && n == 0) {

                            n = companyRepository.countByNombreFiltrado(clientNameXml.trim().toUpperCase());

                            if (n == 1) {

                                Optional<Company> empresa = companyRepository.findByFilteredName(clientNameXml.trim().toUpperCase());
                                if (empresa.isPresent()) {
                                    clientId = empresa.get().getId();
                                    clientRS = empresa.get().getLegalName();
                                }
                            }

                            if ((clientId == null || clientId == 0) && n > 1 && clientCodeSCV != null && !clientCodeSCV.isEmpty()) {

                                n = companyRepository.countByNameAndAlias(clientNameXml.trim().toUpperCase(), clientCodeSCV);
                                if (n == 1) {
                                    Optional<Company> empresa = companyRepository.findByNameAndAlias(clientNameXml.trim().toUpperCase(), clientCodeSCV);
                                    if (empresa.isPresent()) {
                                        clientId = empresa.get().getId();
                                        clientRS = empresa.get().getLegalName();
                                    }
                                }
                            }

                            if (clientId == null || clientId == 0) {

                                clientNameXML2 = clearSuffixes(clientNameXml);

                                if (clientNameXML2 != null) {
                                    n = companyRepository.countByExactName(clientNameXML2);
                                    if (n == 1) {
                                        Optional<Company> empresa = companyRepository.findByExactName(clientNameXML2);
                                        if (empresa.isPresent()) {
                                            clientId = empresa.get().getId();
                                            clientRS = empresa.get().getLegalName();
                                        }
                                    }
                                }
                            }
                        }

                        if ((clientId == null || clientId == 0)
                                && clientCodeSCV != null && !clientCodeSCV.isEmpty()
                                && clientCodeSCV.length() <= 15
                                && Boolean.TRUE.equals(allowCreateAutomaticClient)) {


                            clientNameXml = clientNameXml.replace("'", "´");

                            String clientCodeScvOri = clientCodeSCV;


                            clientRS = clientNameXml.length() > 100 ? clientNameXml.substring(0, 100) : clientNameXml;


                            Integer qCli = companyRepository.countByDocumentoPrefix(clientCodeSCV);

                            if (qCli > 0) {
                                clientCodeSCV = clientCodeSCV + String.format("%02d", qCli + 1);
                            }


                            Company newCompany = new Company();
                            BusinessUnit bu = new BusinessUnit();
                            bu.setId(businessUnitId);
                            newCompany.setBusinessUnit(bu);
                            Catalog catalog = new Catalog();
                            catalog.setId(tipoDocIde);
                            newCompany.setCatDocumentType(catalog);
                            newCompany.setSuspended(false);
                            newCompany.setDocument(clientCodeSCV);
                            newCompany.setLegalName(clientRS);
                            newCompany.setCommercialName("");
                            newCompany.setStatus(true);
                            newCompany.setRegistrationUser(user);
                            newCompany.setCompanyAlias(clientCodeScvOri);
                            Catalog catalog2 = new Catalog();
                            catalog2.setId(isCreationSourceBkedi);
                            newCompany.setCatCompanyCreationOrigin(isCreationSourceBkedi);
                            newCompany.setRegistrationDate(LocalDateTime.now());

                            newCompany = companyRepository.save(newCompany);


                            clientId = newCompany.getId();
                        }


                        if ((clientId == null || clientId == 0)
                                && clientNameXml != null && !clientNameXml.isEmpty()
                                && Boolean.TRUE.equals(allowCreateAutomaticClient)) {


                            clientNameXml = clientNameXml.replace("'", "´");


                            String clientRs = clientNameXml.length() > 100 ? clientNameXml.substring(0, 100) : clientNameXml;


                            int qCompany = companyRepository.countByDocumentoPrefix("CUS");
                            String companyCode = "CUS" + String.format("%02d", qCompany + 1);


                            Company newCompany = new Company();
                            BusinessUnit bu = new BusinessUnit();
                            bu.setId(businessUnitId);
                            newCompany.setBusinessUnit(bu);
                            Catalog catalog = new Catalog();
                            catalog.setId(tipoDocIde);
                            newCompany.setCatDocumentType(catalog);
                            newCompany.setSuspended(false);
                            newCompany.setDocument(companyCode);
                            newCompany.setLegalName(clientRs);
                            newCompany.setCommercialName("");
                            newCompany.setStatus(true);

                            newCompany.setRegistrationUser(user);
                            newCompany.setCatCompanyCreationOrigin(isCreationSourceBkedi);
                            newCompany.setRegistrationDate(LocalDateTime.now());

                            newCompany = companyRepository.save(newCompany);


                            clientId = newCompany.getId();
                        }


                        if (clientId != null && clientId > 0
                                && clientCodeSCV != null && !clientCodeSCV.isEmpty()
                                && clientCodeSCV.length() <= 50) {
                            companyRepository.updateAliasIfEmpty(clientCodeSCV, clientId);
                        }


                        if (clientId == null || clientId == 0) {

                            Integer clienteDummyId = companyRepository.findDummyCustomerId(Arrays.asList("DUMMY", "DUMMYCUS"));

                            if (clienteDummyId == null || clienteDummyId == 0) {
                                Company dummyCompany = new Company();
                                Catalog catalog = new Catalog();
                                catalog.setId(tipoDocIde);
                                dummyCompany.setCatDocumentType(catalog);
                                dummyCompany.setSuspended(false);
                                dummyCompany.setDocument("DUMMYCUS");
                                dummyCompany.setLegalName("DUMMY CUSTOMER");
                                dummyCompany.setCommercialName("");
                                dummyCompany.setStatus(true);

                                dummyCompany.setRegistrationUser(user);
                                dummyCompany.setCatCompanyCreationOrigin(isCreationSourceBkedi);
                                dummyCompany.setRegistrationDate(LocalDateTime.now());


                                dummyCompany = companyRepository.save(dummyCompany);
                                clienteDummyId = dummyCompany.getId();
                            }


                            clientId = clienteDummyId;
                            clientRS = "DUMMY CUSTOMER";
                        }

                        if (clientId != null && clientId != 0) {

                            boolean existeRol = companyRoleRepository.existsByCompanyIdAndRoleTypeId(clientId, rolClienteId);


                            if (!existeRol) {
                                CompanyRole nuevoRol = new CompanyRole();
                                Company company = new Company();
                                company.setId(clientId);
                                nuevoRol.setCompany(company);
                                Catalog catalog = new Catalog();
                                catalog.setId(rolClienteId);
                                nuevoRol.setCatRoleType(catalog);
                                companyRoleRepository.save(nuevoRol);
                            }
                        }


                        if (codigoIMO.isEmpty()) {
                            codigoIMO = null;
                        }

                        imoId = null;
                        if (codigoIMO != null) {

                            imoId = imoRepository.findImoIdByImoCodeAndActive(codigoIMO, true);
                        }


                        if (Boolean.TRUE.equals(isCreateScheduleAutomatically) && tbVessels.isEmpty()) {
                            if (vesselXml != null && !vesselXml.isEmpty()) {

                                String naveTemp = vesselXml.trim().toUpperCase();
                                String naveAbrev = naveTemp.replace(" ", "");


                                if (!vesselRepository.existsByName(naveTemp)) {

                                    Vessel newVessel = new Vessel();
                                    newVessel.setCallSign(naveAbrev);
                                    newVessel.setShip(naveTemp);
                                    newVessel.setCallSign(vesselCallSign);
                                    newVessel.setImoNumber(vesselLloydNumber);
                                    newVessel.setActive(true);

                                    newVessel.setRegistrationUser(user);
                                    newVessel.setRegistrationDate(LocalDateTime.now());
                                    Catalog catalog = new Catalog();
                                    catalog.setId(isCreationSourceBkedi);
                                    newVessel.setCatVesselCreationOrigin(catalog);


                                    Vessel createdVessel = vesselRepository.save(newVessel);
                                    tbVessels.add(createdVessel.getId());
                                }
                            }


                            if (vesselXml == null || vesselXml.isEmpty()) {
                                Integer dummyNaveId = vesselRepository.findIdByName("DUMMY VESSEL");
                                if (dummyNaveId == null || dummyNaveId == 0) {

                                    Vessel dummyVessel = new Vessel();
                                    dummyVessel.setShip("DUMMYVESSEL");
                                    dummyVessel.setName("DUMMY VESSEL");
                                    dummyVessel.setCallSign(vesselCallSign);
                                    dummyVessel.setImoNumber(vesselLloydNumber);
                                    dummyVessel.setActive(true);

                                    dummyVessel.setRegistrationUser(user);
                                    dummyVessel.setRegistrationDate(LocalDateTime.now());
                                    Catalog catalog = new Catalog();
                                    catalog.setId(isCreationSourceBkedi);
                                    dummyVessel.setCatVesselCreationOrigin(catalog);


                                    Vessel dummyNaveGuardada = vesselRepository.save(dummyVessel);
                                    dummyNaveId = dummyNaveGuardada.getId();
                                }

                                tbVessels.add(dummyNaveId);
                            }
                        }


                        voyage = (voyage != null) ? voyage : "";

                        if (Boolean.TRUE.equals(isCreateScheduleAutomatically)) {
                            if (shipmentPortId == null || shipmentPortId == 0) {
                                shipmentPortId = 1; // PORT DUMMY
                            }
                            if (destinationPortId == null || destinationPortId == 0) {
                                destinationPortId = 1;
                            }
                            if (dischargePortId == null || dischargePortId == 0) {
                                dischargePortId = 1;
                            }
                            if (voyage.isEmpty()) {
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMM");
                                voyage = LocalDateTime.now().format(formatter);
                            }
                        }


                        if (tbVessels != null && !tbVessels.isEmpty() && voyage != null && !voyage.isEmpty()) {

                            Integer qPrognav = vesselProgrammingDetailRepository.countByCriteria(subBusinessUnitId, voyage, tbVessels);


                            if (qPrognav == 1) {
                                programmingVesselDetailId = vesselProgrammingDetailRepository.findIdByCriteria(subBusinessUnitId, voyage, tbVessels);
                            }
                        }


                        if (tbVessels != null && !tbVessels.isEmpty() && voyage != null && !voyage.isEmpty() && (voyage.length() > 1 && !isNumeric(voyage.substring(voyage.length() - 1)))) {

                            String viajeAx = voyage.substring(0, voyage.length() - 1);
                            String viajeAx2 = viajeAx;
                            if (viajeAx.length() == 3 && viajeAx.startsWith("0")) {
                                viajeAx2 = "0" + viajeAx;
                            }

                            String viajeAx3 = viajeAx;
                            if (viajeAx2.length() == 4 && !viajeAx2.equals(viajeAx)) {
                                viajeAx3 = viajeAx2 + voyage.charAt(voyage.length() - 1);
                            }

                            String viajeAx4 = viajeAx;
                            if (voyage.length() == 4 && !voyage.startsWith("0") && isNumeric(voyage.substring(voyage.length() - 1)) && voyage.endsWith("R")) {
                                viajeAx4 = "0" + voyage;
                            }

                            count = vesselProgrammingDetailRepository.countProgramacionNaveDetalle(subBusinessUnitId, List.of(viajeAx, viajeAx2, viajeAx3, viajeAx4), 43002);
                            if (count == 1) {
                                programmingVesselDetailId = vesselProgrammingDetailRepository.findProgramacionNaveDetalleId(subBusinessUnitId, List.of(viajeAx, viajeAx2, viajeAx3, viajeAx4), 43002);
                            }

                        }

                        if (tbVessels != null && tbVessels.size() == 1) {
                            vesselId = tbVessels.getFirst();
                        }


                        if (tbVessels != null && tbVessels.size() == 1 && voyage != null && !voyage.isEmpty() && programmingVesselDetailId == null) {

                            Optional<VesselProgramming> vesselProgrammingOptional = vesselProgrammingRepository.findBySubBusinessUnitIdAndShipIdAndVoyageAndActive(subBusinessUnitId, vesselId, voyage, 1);

                            vesselProgrammingId = vesselProgrammingOptional.map(VesselProgramming::getId).orElse(0);

                            if (vesselProgrammingId == 0) {

                                VesselProgramming newVesselProgramming = new VesselProgramming();
                                BusinessUnit businessUnit = new BusinessUnit();
                                businessUnit.setId(businessUnitId);
                                newVesselProgramming.setBusinessUnit(businessUnit);
                                BusinessUnit subbusinessUnit = new BusinessUnit();
                                subbusinessUnit.setId(subBusinessUnitId);
                                newVesselProgramming.setSubBusinessUnit(subbusinessUnit);
                                Vessel vessel = new Vessel();
                                vessel.setId(vesselId);
                                newVesselProgramming.setVessel(vessel);
                                newVesselProgramming.setVoyage(voyage);
                                newVesselProgramming.setEtaDate(LocalDateTime.now());
                                newVesselProgramming.setEtdDate(LocalDateTime.now());
                                newVesselProgramming.setActive(true);
                                newVesselProgramming.setRegistrationDate(LocalDateTime.now());

                                newVesselProgramming.setRegistrationUser(user);
                                Catalog catalog = new Catalog();
                                catalog.setId(isCreationSourceBkedi);
                                newVesselProgramming.setCatCreationOrigin(catalog);

                                vesselProgrammingRepository.save(newVesselProgramming);
                                vesselProgrammingId = newVesselProgramming.getId();
                            }


                        }

                        if (tbVessels != null && tbVessels.size() == 1 && voyage != null && !voyage.isEmpty() && programmingVesselDetailId == null) {

                            Optional<VesselProgrammingDetail> programacionNaveDetalle = vesselProgrammingDetailRepository.findByProgramScheduleIdAndOperationCategoryIdAndActive(vesselProgrammingId, 43002, 1);

                            programmingVesselDetailId = programacionNaveDetalle.map(VesselProgrammingDetail::getId).orElse(0);

                            if (programmingVesselDetailId == 0) {

                                VesselProgrammingDetail newVesselProgrammingDetail = new VesselProgrammingDetail();
                                VesselProgramming vesselProgramming = new VesselProgramming();
                                vesselProgramming.setId(vesselProgrammingId);
                                newVesselProgrammingDetail.setVesselProgramming(vesselProgramming);
                                Catalog catalog = new Catalog();
                                catalog.setId(43002);
                                newVesselProgrammingDetail.setCatOperation(catalog);  // 'EF' Embarque Full
                                newVesselProgrammingDetail.setManifestYear(String.valueOf(LocalDate.now().getYear()));
                                newVesselProgrammingDetail.setManifestNumber("");
                                newVesselProgrammingDetail.setBeginningOperation(LocalDateTime.now());

                                newVesselProgrammingDetail.setRegistrationUser(user);
                                newVesselProgrammingDetail.setActive(true);
                                newVesselProgrammingDetail.setRegistrationDate(LocalDateTime.now());
                                Catalog catalog2 = new Catalog();
                                catalog2.setId(isCreationSourceBkedi);
                                newVesselProgrammingDetail.setCatCreationOrigin(catalog2);

                                vesselProgrammingDetailRepository.save(newVesselProgrammingDetail);
                                programmingVesselDetailId = newVesselProgrammingDetail.getId();
                            }
                        }


                        if (productGroupDes != null && !productGroupDes.trim().isEmpty()) {

                            Optional<Integer> optionalProductId = productRepository.findTopByActiveAndCommodity1Like(productGroupDes.trim());

                            if (optionalProductId.isPresent()) {
                                productId = optionalProductId.get();
                            }


                            optionalProductId = productRepository.findTopByActiveAndCommodity2Like(productGroupDes.trim());

                            if (optionalProductId.isPresent()) {
                                productId = optionalProductId.get();
                            }

                        }

                        if (productId == null || productId == 0) {
                            productId = 1;
                        }

                        Optional<BookingEdi> bookingEdiOptional = bookingEdiRepository.findByIdAndStatus(ediCoparnId, List.of(isBkediPending, isBkediToProcess));

                        if (bookingEdiOptional.isPresent()) {
                            BookingEdi bookingEdi = bookingEdiOptional.get();
                            BusinessUnit bu = new BusinessUnit();
                            bu.setId(subBusinessUnitId);
                            bookingEdi.setSubBusinessUnit(bu);

                            bookingEdi.setBkEdiRegistrationType(trecordType);
                            bookingEdi.setBkEdiReservationSituation(reservationSituation);
                            bookingEdi.setBkEdiCreationDate(
                                    (creationDate == null || creationDate.isEmpty())
                                            ? dateFormat(bookingEdi.getRegistrationDate())
                                            : creationDate
                            );
                            bookingEdi.setBkEdiShippingLine(lineBKXml);
                            ShippingLine bkShippingLine = new ShippingLine();
                            bkShippingLine.setId(lineBKId);
                            bookingEdi.setBkShippingLine(bkShippingLine);
                            bookingEdi.setBkEdiVesselName(vesselXml);
                            bookingEdi.setBkEdiVoyage(voyage);
                            Vessel vessel = new Vessel();
                            vessel.setId(vesselId);
                            bookingEdi.setVessel(vessel);
                            VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
                            vesselProgrammingDetail.setId(programmingVesselDetailId);
                            bookingEdi.setVesselProgrammingDetail(vesselProgrammingDetail);
                            bookingEdi.setBkEdiLoadingPort(xShipmentPort);
                            Port loadingPort = new Port();
                            loadingPort.setId(shipmentPortId);
                            bookingEdi.setLoadingPort(loadingPort);
                            bookingEdi.setBkEdiDestinationPort(xDestinationPort);
                            Port destinationPort = new Port();
                            destinationPort.setId(destinationPortId);
                            bookingEdi.setDestinationPort(destinationPort);
                            bookingEdi.setBkEdiDischargePort(xDichargePort);
                            Port dischargePort = new Port();
                            dischargePort.setId(dischargePortId);
                            bookingEdi.setDischargePort(dischargePort);
                            bookingEdi.setBookingNumber(booking);
                            bookingEdi.setBkEdiContainerQuantity(reserveAmount1);
                            bookingEdi.setBkEdiIsoCode(containerCodeISO1);
                            IsoCode isoCode = new IsoCode();
                            isoCode.setId(codeIsoId);
                            bookingEdi.setIsoCode(isoCode);
                            Catalog catContainerSize = new Catalog();
                            catContainerSize.setId(cntDimenId);
                            bookingEdi.setCatContainerSize(catContainerSize);
                            Catalog catContainerType = new Catalog();
                            catContainerType.setId(cntTypeId);
                            bookingEdi.setCatContainerType(catContainerType);
                            bookingEdi.setBkEdiContainerQuantity2(reserveAmount2 != null ? reserveAmount2 : 0);

                            bookingEdi.setBkEdiIsoCode2(containerCodeISO2);
                            IsoCode isoCode2 = new IsoCode();
                            isoCode2.setId(codeIso2Id);
                            bookingEdi.setIsoCode2(isoCode2);
                            Catalog catContainerSize2 = new Catalog();
                            catContainerSize2.setId(cntDimen2Id);
                            bookingEdi.setCatContainerType2(catContainerSize2);
                            Catalog catContainerType2 = new Catalog();
                            catContainerType2.setId(cntType2Id);
                            bookingEdi.setCatContainerType2(catContainerType2);
                            bookingEdi.setBkEdiCustomer(clientNameXml);
                            Company clientCompany = new Company();
                            clientCompany.setId(clientId);
                            bookingEdi.setClientCompany(clientCompany);
                            bookingEdi.setBkEdiCustomerAlias(clientCodeSCV);
                            bookingEdi.setBkEdiCommodity(productGroupDes);
                            Product product = new Product();
                            product.setId(productId);
                            bookingEdi.setProduct(product);
                            bookingEdi.setBkEdiTemperature(temperature != null ? temperature.substring(0, Math.min(temperature.length(), 10)) : null);
                            bookingEdi.setBkEdiImoCode(codigoIMO != null ? codigoIMO.substring(0, Math.min(codigoIMO.length(), 10)) : null);
                            Imo imo = new Imo();
                            imo.setId(imoId);
                            bookingEdi.setImo(imo);
                            bookingEdi.setBkEdiCommentForProcess(null);
                            bookingEdi.setBkEdiColdtreatment(coldTreatment);
                            bookingEdi.setBkEdiAtmosphereControlled(controlledAtmosphere);
                            bookingEdi.setBkEdiColombiaDepot(depotColombia);
                            Catalog catBkEdiStatus = new Catalog();
                            catBkEdiStatus.setId(isBkediToProcess);
                            bookingEdi.setCatBkEdiStatus(catBkEdiStatus);
                            bookingEdi.setRemarkRulesName(Boolean.TRUE.equals(flagToFlex) ? "FLAG_TO_FLEX" : "");

                            bookingEdiRepository.save(bookingEdi);
                        }

                        if (shippingLineId == 4102) {
                            Integer duplicateCount = bookingEdiRepository.countBySubBusinessUnitIdAndMatchingFields(
                                    subBusinessUnitId,
                                    "9",
                                    vesselXml,
                                    voyage,
                                    xShipmentPort,
                                    booking,
                                    xDestinationPort,
                                    xDichargePort,
                                    containerCodeISO1,
                                    containerCodeISO2,
                                    clientNameXml,
                                    codigoIMO,
                                    lineBKXml,
                                    temperature,
                                    isBkediToProcess
                            );

                            if (duplicateCount > 1) {
                                BookingEdi bookingEdiTemp = bookingEdiRepository.findTopBySubBusinessUnitIdAndMatchingFields(
                                        subBusinessUnitId,
                                        "9",
                                        vesselXml,
                                        voyage,
                                        xShipmentPort,
                                        booking,
                                        xDestinationPort,
                                        xDichargePort,
                                        containerCodeISO1,
                                        containerCodeISO2,
                                        clientNameXml,
                                        codigoIMO,
                                        lineBKXml,
                                        temperature,
                                        isBkediToProcess
                                );

                                Catalog catBkEdiStatusRejected = new Catalog();
                                catBkEdiStatusRejected.setId(isBkediRejected);
                                bookingEdiTemp.setCatBkEdiStatus(catBkEdiStatusRejected);

                                bookingEdiTemp.setProcessedUser(user);
                                bookingEdiTemp.setDateProcessedCoparn(LocalDateTime.now());
                                bookingEdiTemp.setBkEdiProcessedComment("El EDI llegó antes HSD1//HSD2.");
                                bookingEdiTemp.setBkEdiCommentForProcess(null);
                                bookingEdiRepository.save(bookingEdiTemp);
                            }


                        }
                        Optional<BookingEdi> bookingEdi = bookingEdiRepository.findById(ediCoparnId);
                        if (bookingEdi.isPresent() && Objects.equals(trecordType, "11")) {
                            BookingEdi bookingEdi1 = bookingEdi.get();
                            Catalog catIsBkEdiInvalid = new Catalog();
                            catIsBkEdiInvalid.setId(isBkediNotValid);
                            bookingEdi1.setCatBkEdiStatus(catIsBkEdiInvalid);

                            bookingEdi1.setProcessedUser(user);
                            bookingEdi1.setDateProcessedCoparn(LocalDateTime.now());
                            bookingEdiRepository.save(bookingEdi1);
                        }
                        String sequenceDetails = null;
                        if (tbSecHLL != null && !tbSecHLL.isEmpty()) {
                            ObjectMapper objectMapper = new ObjectMapper();
                            sequenceDetails = objectMapper.writeValueAsString(tbSecHLL);
                        }


                        boolean b = (cntTypeId != null ? cntTypeId : 0) + (cntType2Id != null ? cntType2Id : 0) > 0;
                        boolean b1 = (cntDimenId != null ? cntDimenId : 0) + (cntDimen2Id != null ? cntDimen2Id : 0) > 0;
                        if ("12".equals(trecordType) &&
                                "9".equals(reservationSituation) &&
                                programmingVesselDetailId != null && programmingVesselDetailId > 0 &&
                                shipmentPortId != null && shipmentPortId > 0 &&
                                destinationPortId != null && destinationPortId > 0 &&
                                booking != null && !booking.trim().isEmpty() &&
                                (Objects.equals(catEdiCoparnStatus, isBkediPending) || Objects.equals(catEdiCoparnStatus, isBkediToProcess)) &&
                                b1 &&
                                b &&
                                lineBKId != null && lineBKId > 0 &&
                                clientId != null && clientId > 0) {

                            ServiceCoparnProcessFile9Input serviceCoparnProcessFile9Input = new ServiceCoparnProcessFile9Input();
                            serviceCoparnProcessFile9Input.setBooking(booking);
                            serviceCoparnProcessFile9Input.setEdiCoparnId(ediCoparnId);
                            serviceCoparnProcessFile9Input.setUnitBusinessId(unitBusinessId);
                            serviceCoparnProcessFile9Input.setSubUnitBusinessId(subBusinessUnitId);
                            serviceCoparnProcessFile9Input.setProgrammingNaveDetailId(programmingVesselDetailId);
                            serviceCoparnProcessFile9Input.setCntDimenId(cntDimenId);
                            serviceCoparnProcessFile9Input.setCntTypeId(cntTypeId);
                            serviceCoparnProcessFile9Input.setQuantityReserve(reserveAmount1);
                            serviceCoparnProcessFile9Input.setCntDimen2Id(cntDimen2Id);
                            serviceCoparnProcessFile9Input.setCntType2Id(cntType2Id);
                            serviceCoparnProcessFile9Input.setQuantityReserve2(reserveAmount2);
                            serviceCoparnProcessFile9Input.setCustomerId(clientId);
                            serviceCoparnProcessFile9Input.setClientRS(clientRS);
                            serviceCoparnProcessFile9Input.setProductGroupDescription(productGroupDes);
                            serviceCoparnProcessFile9Input.setProductId(productId);
                            serviceCoparnProcessFile9Input.setPortOfEmbarqueId(shipmentPortId);
                            serviceCoparnProcessFile9Input.setPortOfDestinoId(destinationPortId);
                            serviceCoparnProcessFile9Input.setPortOfDescargaId(dischargePortId);
                            serviceCoparnProcessFile9Input.setTemperature(temperature);
                            serviceCoparnProcessFile9Input.setImoId(imoId);
                            serviceCoparnProcessFile9Input.setLineABkId(lineBKId);
                            serviceCoparnProcessFile9Input.setGrossWeightEDI(0);
                            serviceCoparnProcessFile9Input.setGrossWeightEDI2(0);
                            serviceCoparnProcessFile9Input.setColdTreatment(coldTreatment);
                            serviceCoparnProcessFile9Input.setControlledAtmosphere(controlledAtmosphere);
                            serviceCoparnProcessFile9Input.setUserRecordId(user.getId());
                            serviceCoparnProcessFile9Input.setPassCoparn5x9(false);
                            serviceCoparnProcessFile9Input.setParamSequenceDetails(sequenceDetails);
                            serviceCoparnProcessFile9Service.serviceCoparnProcessFile9Service(serviceCoparnProcessFile9Input);

                        }

                        if ("12".equals(trecordType) &&
                                ("1".equals(reservationSituation) || "2".equals(reservationSituation)) &&
                                programmingVesselDetailId != null && programmingVesselDetailId > 0 &&
                                shipmentPortId != null && shipmentPortId > 0 &&
                                destinationPortId != null && destinationPortId > 0 &&
                                booking != null && !booking.trim().isEmpty() &&
                                b1 &&
                                b &&
                                lineBKId != null && lineBKId > 0 &&
                                clientId != null && clientId > 0) {


                            bookingEdiRepository.processArchive1C2A(
                                    ediCoparnId,
                                    reservationSituation,
                                    unitBusinessId,
                                    subBusinessUnitId,
                                    programmingVesselDetailId,
                                    booking,
                                    cntDimenId,
                                    cntTypeId,
                                    reserveAmount1,
                                    cntDimen2Id,
                                    cntType2Id,
                                    reserveAmount2,
                                    clientId,
                                    clientRS,
                                    productGroupDes,
                                    productId, shipmentPortId, destinationPortId, dischargePortId,
                                    temperature, imoId, lineBKId, BigDecimal.valueOf(0),
                                    BigDecimal.valueOf(0), coldTreatment, controlledAtmosphere,
                                    user.getId(), sequenceDetails);
                        }

                        if ("12".equals(trecordType) &&
                                "5".equals(reservationSituation) &&
                                programmingVesselDetailId != null && programmingVesselDetailId > 0 &&
                                shipmentPortId != null && shipmentPortId > 0 &&
                                destinationPortId != null && destinationPortId > 0 &&
                                booking != null && !booking.trim().isEmpty() &&
                                b1 &&
                                b &&
                                lineBKId != null && lineBKId > 0 &&
                                clientId != null && clientId > 0) {

                            bookingEdiRepository.processArchive5M(
                                    ediCoparnId,
                                    unitBusinessId,
                                    subBusinessUnitId,
                                    programmingVesselDetailId,
                                    booking,
                                    cntDimenId,
                                    cntTypeId,
                                    reserveAmount1,
                                    cntDimen2Id,
                                    cntType2Id,
                                    reserveAmount2,
                                    clientId,
                                    clientRS,
                                    productGroupDes,
                                    productId, shipmentPortId, destinationPortId, dischargePortId,
                                    temperature, imoId, lineBKId, BigDecimal.valueOf(0),
                                    BigDecimal.valueOf(0), coldTreatment, controlledAtmosphere,
                                    user.getId(), sequenceDetails);
                        }

                        if ("12".equals(trecordType) &&
                                (programmingVesselDetailId == null || programmingVesselDetailId == 0 ||
                                        shipmentPortId == null || shipmentPortId == 0 ||
                                        booking == null || booking.trim().isEmpty() ||
                                        destinationPortId == null || destinationPortId == 0 ||
                                        dischargePortId == null || dischargePortId == 0 ||
                                        cntDimenId == null || cntDimenId == 0 ||
                                        cntTypeId == null || cntTypeId == 0 ||
                                        clientId == null || clientId == 0 ||
                                        reserveAmount1 == null || reserveAmount1 == 0)) {

                            Optional<BookingEdi> optionalBookingEdi2 = bookingEdiRepository.findByIdAndStatus(ediCoparnId, List.of(isBkediPending, isBkediToProcess));
                            if (optionalBookingEdi2.isPresent()) {
                                BookingEdi bookingEdi1 = optionalBookingEdi2.get();

                                String ediCoparnObsParaProceso = buildObservationMessage(
                                        vesselXml, vesselId, tbVessels,
                                        shipmentPortId, xShipmentPort,
                                        voyage,
                                        programmingVesselDetailId,
                                        booking,
                                        destinationPortId, xDestinationPort,
                                        dischargePortId, xDichargePort,
                                        cntDimenId, containerCodeISO1,
                                        clientId, clientCodeSCV,
                                        shippingLineId, allowCreateAutomaticClient,
                                        reserveAmount1
                                );


                                String ediCoparnUltObsParaProceso = buildObservationMessage(
                                        vesselXml, vesselId, tbVessels,
                                        shipmentPortId, xShipmentPort,
                                        voyage,
                                        programmingVesselDetailId,
                                        booking,
                                        destinationPortId, xDestinationPort,
                                        dischargePortId, xDichargePort,
                                        cntDimenId, containerCodeISO1,
                                        clientId, clientCodeSCV,
                                        shippingLineId, allowCreateAutomaticClient,
                                        reserveAmount1
                                );


                                bookingEdi1.setDateProcessedCoparn(LocalDateTime.now());
                                bookingEdi1.setBkEdiCommentForProcess(ediCoparnObsParaProceso.substring(0, Math.min(ediCoparnObsParaProceso.length(), 255)));
                                bookingEdi1.setBkEdiLastCommentForProcess(ediCoparnUltObsParaProceso.substring(0, Math.min(ediCoparnUltObsParaProceso.length(), 255)));

                                bookingEdiRepository.save(bookingEdi1);
                            }
                        }


                        if ("12".equals(trecordType) &&
                                ((isNullOrEmpty(vesselCallSign) && isNullOrEmpty(vesselLloydNumber) && isNullOrEmpty(vesselXml)) ||
                                        isNullOrEmpty(voyage) ||
                                        isNullOrEmpty(xShipmentPort) ||
                                        isNullOrEmpty(booking) ||
                                        (isNullOrEmpty(containerCodeISO1) && isNullOrEmpty(containerCodeISO2)) ||
                                        (reserveAmount1 == null || reserveAmount1 == 0) && (reserveAmount2 == null || reserveAmount2 == 0))) {

                            Optional<BookingEdi> optionalBookingEdi2 = bookingEdiRepository.findByIdAndStatus(ediCoparnId, List.of(isBkediPending, isBkediToProcess));
                            if (optionalBookingEdi2.isPresent()) {
                                BookingEdi bookingEdi1 = optionalBookingEdi2.get();
                                Catalog catIsBkEdiRejected = new Catalog();
                                catIsBkEdiRejected.setId(isBkediRejected);
                                bookingEdi1.setCatBkEdiStatus(catIsBkEdiRejected);
                                bookingEdi1.setBkEdiProcessedComment("Rejected, some mandatory fields have no information.");
                                bookingEdi1.setDateProcessedCoparn(LocalDateTime.now());

                                bookingEdi1.setProcessedUser(user);
                                bookingEdiRepository.save(bookingEdi1);
                            }
                        }

                    }

                } else {
                    Optional<BookingEdi> optionalBookingEdi2 = bookingEdiRepository.findByIdAndStatus(ediCoparnId, List.of(isBkediPending, isBkediToProcess));
                    if (optionalBookingEdi2.isPresent()) {
                        BookingEdi bookingEdi1 = optionalBookingEdi2.get();
                        Catalog catIsBkEdiRejected = new Catalog();
                        catIsBkEdiRejected.setId(isBkediRejected);
                        bookingEdi1.setCatBkEdiStatus(catIsBkEdiRejected);
                        bookingEdi1.setBkEdiProcessedComment("Could not locate sub business unit, says: " + xShipmentPort);
                        bookingEdi1.setDateProcessedCoparn(LocalDateTime.now());

                        bookingEdi1.setProcessedUser(user);
                        bookingEdiRepository.save(bookingEdi1);
                    }
                }
            }
        }

        CoparnServiceProcessFileOutput response = new CoparnServiceProcessFileOutput();
        response.setRespStatus(1);
        return response;
    }


    public String buildObservationMessage(
            String vesselXml, Integer vesselId, List<Integer> tbVessels,
            Integer shipmentPortId, String xShipmentPort,
            String voyage,
            Integer vesselProgramingDetailId,
            String booking,
            Integer destinationPortId, String xDestinationPort,
            Integer dischargePortId, String xDischargePort,
            Integer cntDimenId, String containerCodeISO1,
            Integer clientId, String clientCodeSCV,
            Integer shippingLineId, boolean allowCreateClientAutomatic,
            Integer reserveAmount1
    ) {
        StringBuilder message = new StringBuilder();
        String notExist = " not exist in SD1.";

        if (isVesselXmlPresentAndVesselEmptyAndTbVesselEmpty(vesselXml, vesselId, tbVessels)) {
            message.append(" *Nave ").append(vesselXml).append(notExist);
        }
        if (isNullOrEmpty(vesselXml)) {
            message.append(" *Vessel EDI in blank.");
        }
        if (shipmentPortId == null || shipmentPortId == 0) {
            message.append(" *Port of Loading ").append(xShipmentPort).append(notExist);
        }
        if (isNullOrEmpty(voyage)) {
            message.append(" *Voyage EDI in blank.");
        }
        if (vesselProgramingDetailId == null || vesselProgramingDetailId == 0) {
            message.append(" *Vessel Schedule ").append(vesselXml).append("/").append(voyage).append(" not generated.");
        }
        if (isNullOrEmpty(booking)) {
            message.append(" *Booking EDI in blank.");
        }
        if ((destinationPortId == null || destinationPortId == 0) && !isNullOrEmpty(xDestinationPort)) {
            message.append(" *Port of Destination ").append(xDestinationPort).append(notExist);
        }
        if (isNullOrEmpty(xDestinationPort) && (destinationPortId == null || destinationPortId == 0)) {
            message.append(" *Port of Destination EDI in blank.");
        }
        if (dischargePortId == null || dischargePortId == 0) {
            message.append(" *Port of Discharge ").append(xDischargePort).append(notExist);
        }
        if (cntDimenId == null || cntDimenId == 0) {
            message.append(" *ISO Code ").append(containerCodeISO1).append(notExist);
        }
        if ((clientId == null || clientId == 0) && !isNullOrEmpty(clientCodeSCV)) {
            message.append(" *Customer SD1 in blank.");
        }
        if (isNullOrEmpty(clientCodeSCV) && shippingLineId == 4104 && !allowCreateClientAutomatic) {
            message.append(" *Customer SCV EDI in blank.");
        }
        if (reserveAmount1 == null || reserveAmount1 == 0) {
            message.append(" *Quantity Containers EDI in blank.");
        }

        return message.toString();
    }

    private boolean isVesselXmlPresentAndVesselEmptyAndTbVesselEmpty(String vesselXml, Integer vesselId, List<Integer> tbVessels) {
        return !isNullOrEmpty(vesselXml) && (vesselId == null || vesselId == 0) && (tbVessels == null || tbVessels.isEmpty());
    }

    private boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }


    private String dateFormat(LocalDateTime registrationDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        return registrationDate.format(formatter);
    }

    private boolean isNumeric(String str) {
        return str != null && str.matches("\\d+");
    }


    public String clearSuffixes(String name) {
        String[] suffixes = {
                " S.A.C.", " S A C", " SAC", " SOCIEDAD ANONIMA CERRADA",
                " S A A", " SAA", " SOCIEDAD ANONIMA ABIERTA",
                " S.A.", " S A", " SA", " SOCIEDAD ANONIMA",
                " S R L", " SRL", " SOC.COM.RESPONS. LTDA",
                " E I R L", " EIRL", " EMPRESA INDIVIDUAL DE RESP. LTDA"
        };

        for (String suffix : suffixes) {
            if (name.toUpperCase().contains(suffix)) {
                return name.toUpperCase().replace(suffix, "").trim();
            }
        }

        return null;
    }

    private static boolean isDate(String dateStr) {
        try {
            LocalDate.parse(dateStr, DateTimeFormatter.BASIC_ISO_DATE);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isValidDate(String date) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            LocalDate.parse(date, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    private String trimOrEmpty(String value) {
        return value == null ? "" : value.trim();
    }


}
