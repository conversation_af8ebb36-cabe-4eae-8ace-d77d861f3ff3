package com.maersk.sd1.sdf.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class EmrInspectionGetInput {

    @Data
    public static class Input {
        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;

        @JsonProperty("business_unit_id")
        @NotNull
        private Integer businessUnitId;

        @JsonProperty("sub_business_unit_id")
        @NotNull
        private Integer subBusinessUnitId;

        @JsonProperty("emr_inspection_id")
        @NotNull
        private Integer emrInspectionId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDF")
        private Prefix prefix;
    }
}
