package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.controller.dto.EquipmentFindInputDTO;
import com.maersk.sd1.sds.controller.dto.EquipmentFindOutputDTO;
import com.maersk.sd1.sds.service.EquipmentFindService;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDS/module/sds/")
public class EquipmentFindController {

    private static final Logger logger = LogManager.getLogger(EquipmentFindController.class.getName());

    private final EquipmentFindService equipmentFindService;

    @PostMapping("SDSContenedorServiceImp/sdsequipmentFind/")
    public ResponseEntity<ResponseController<EquipmentFindOutputDTO>> sdsEquipmentFind(@RequestBody EquipmentFindInputDTO.Root request) {
        try {
            EquipmentFindInputDTO.Input input = request.getPrefix().getInput();

            if (input == null || input.getEquipmentNumber() == null || input.getEquipmentNumber().isEmpty()
                    || input.getLanguageId() == null) {
                return ResponseEntity.ok(new ResponseController<>(Constants.INVALID_INPUT));
            }

            EquipmentFindOutputDTO outputDTO = equipmentFindService.findEquipment(input);
            return ResponseEntity.ok(new ResponseController<>(outputDTO));

        } catch (Exception e) {
            logger.error("An error occurred while processing the equipment find request.", e);
            EquipmentFindOutputDTO errorOutput = new EquipmentFindOutputDTO();
            errorOutput.setRespState(0);
            errorOutput.setRespMessage("Internal server error: " + e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}