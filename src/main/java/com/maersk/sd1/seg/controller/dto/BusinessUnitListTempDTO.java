package com.maersk.sd1.seg.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;

@AllArgsConstructor
@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class BusinessUnitListTempDTO {
    private Integer businessUnitId;
    private String name;
    private Boolean status;
    private Integer parentBusinessUnitId;
    private String parentBusinessUnitName;
    private Integer systemId;
    private String systemName;
    private String systemIcon;
    private String configuration;
    private String currency;
    private String businessUnitAlias;
}