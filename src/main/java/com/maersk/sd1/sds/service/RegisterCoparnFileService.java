package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.BookingEdiFileRepository;
import com.maersk.sd1.common.repository.BookingEdiFileRepository1;
import com.maersk.sd1.common.repository.BookingEdiRepository;
import com.maersk.sd1.common.repository.BookingEdiSettingRepository;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sds.dto.RegisterCoparnFileResult;
import com.maersk.sd1.sds.dto.RegisterCoparnLastBookingEdiDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.IntStream;

import static com.maersk.sd1.common.Parameter.*;
import static com.maersk.sd1.common.Parameter.IS_STATE_TO_PROCESS;

@Service
@RequiredArgsConstructor
public class RegisterCoparnFileService {


    private final GESCatalogService catalogService;


    private final  MessageLanguageService messageLanguageService;


    private final  BookingEdiSettingRepository bookingEdiSettingRepository;


    private final  BookingEdiRepository bookingEdiRepository;


    private final  BookingEdiFileRepository bookingEdiFileRepository;


    private final  BookingEdiFileRepository1 bookingEdiFileRepository1;

    private List<String> catalogAliases = Arrays.asList(
            IS_BKEDI_PENDING,
            IS_STATE_TO_PROCESS,
            IS_BKEDI_DONE,
            IS_BKEDI_REJECTED,
            IS_BKEDI_NOT_VALID,
            IS_BKEDI_REJECTED_DUPLICATE,
            IS_BOOKING_TYPE_UPDATE,
            IS_BOOKING_TYPE_UPDATE,
            CATALOG_IS_EDI_MESSAGE_TYPE_COPARN,
            CATALOG_IS_EDI_MESSAGE_TYPE_301
    );

    @Transactional
    public RegisterCoparnFileResult registerCoparnFileTransactional(Integer ediCoparnSettingId, String coparnFileName, String coparnFileContent, String azureUrl) {
        return registerCoparnFile(ediCoparnSettingId, coparnFileName, coparnFileContent, azureUrl);
    }

    public RegisterCoparnFileResult registerCoparnFileNonTransactional(Integer ediCoparnSettingId, String coparnFileName, String coparnFileContent, String azureUrl) {
        return registerCoparnFile(ediCoparnSettingId, coparnFileName, coparnFileContent, azureUrl);
    }

    private RegisterCoparnFileResult registerCoparnFile(Integer ediCoparnSettingId, String coparnFileName, String coparnFileContent, String azureUrl) {

        RegisterCoparnFileResult result = RegisterCoparnFileResult.builder()
                .respNewId(0)
                .respStatus(0)
                .respMessage("")
                .build();

        HashMap<String, Integer> catalogIds = catalogService.findIdsByAliases(this.catalogAliases);

        Pageable pageable = PageRequest.of(0, 1);

        String ediCoparnNombreArchivoOriginal = coparnFileName;
        String ediCoparnNombreArchivo = coparnFileName;
        String nombreArchivo = coparnFileName;
        String coparnContenidoArchivoEnter = "";
        String coparnContenidoArchivoCopia = "";
        String extensionArchivo = "";
        String extensionArchivoDescargar = "";
        String bookingNumber = "";
        String bookingNumberTemp = "";
        String ediCoparnSituacionReserva = null;
        String ediCoparnObsProcesado = "";
        String fechaCreacionEdi = null;
        Integer lineaNavieraId;
        Integer ownerCorrelative = 0;
        Integer idiomaId = 1;
        Integer catBkediMessageTypeId;
        Integer estado;
        List<String> tbContenido = new ArrayList<>();
        Boolean isMultiple = false;

        Optional<BookingEdiSetting> bookingEdiSetting = bookingEdiSettingRepository.findBybookingEdiSetting(ediCoparnSettingId);
        extensionArchivoDescargar = bookingEdiSetting.map(BookingEdiSetting::getDownloadFileExtension).orElse("");
        lineaNavieraId = bookingEdiSetting.map(BookingEdiSetting::getShippingLine).map(ShippingLine::getId).orElse(null);
        catBkediMessageTypeId = bookingEdiSetting.map(BookingEdiSetting::getCatBkEdiMessageType).map(Catalog::getId).orElse(catalogIds.get(CATALOG_IS_EDI_MESSAGE_TYPE_COPARN));

        Optional<RegisterCoparnLastBookingEdiDTO> bookingEdiDTO = getLastBookingEdiInfo(pageable, ediCoparnSettingId, catalogIds, extensionArchivoDescargar, ediCoparnNombreArchivoOriginal, nombreArchivo, extensionArchivo);
        estado = bookingEdiDTO.map(RegisterCoparnLastBookingEdiDTO::getStatus).orElse(catalogIds.get(IS_BKEDI_REJECTED_DUPLICATE));
        ediCoparnNombreArchivo = bookingEdiDTO.map(RegisterCoparnLastBookingEdiDTO::getFinalFileName).orElse(ediCoparnNombreArchivo);

        if (coparnFileContent.isEmpty()) {
            if (catalogIds.get(IS_BKEDI_PENDING).equals(estado)) {
                estado = catalogIds.get(IS_BKEDI_REJECTED);
                ediCoparnObsProcesado = messageLanguageService.getMessage("SERVICIO_COPARN", 1, idiomaId);
            }
        } else {
            if (catalogIds.get(CATALOG_IS_EDI_MESSAGE_TYPE_COPARN).equals(catBkediMessageTypeId)) {
                coparnFileContent = coparnFileContent.replace("UNA:+.? '", "")
                        .replace("UNA:+,? '", "");

                coparnContenidoArchivoCopia = getCoparnContenidoArchivoCopia(coparnFileContent);

                coparnContenidoArchivoEnter = getCoparnContenidoArchivoEnter(coparnFileContent);

                tbContenido.addAll(fnSplit(coparnContenidoArchivoCopia, '|').stream().filter(value -> !"".equals(value)).toList());

                fechaCreacionEdi = getFechaCreacionEdi(lineaNavieraId, tbContenido, fechaCreacionEdi);

                ediCoparnSituacionReserva = getReservationStatus(tbContenido, ediCoparnSituacionReserva);

                bookingNumber = getBookingNumber(tbContenido, bookingNumber);

                ownerCorrelative = getOwnerCorrelative(tbContenido, ownerCorrelative);
            }

            if (catalogIds.get(CATALOG_IS_EDI_MESSAGE_TYPE_301).equals(catBkediMessageTypeId)) {
                coparnContenidoArchivoEnter = coparnFileContent;

                coparnFileContent = coparnFileContent.replace("ST*", "|ST*")
                        .replace("GS*", "|GS*")
                        // .replace("ST*", "|ST*")
                        .replace("B1*", "|B1*")
                        .replace("Y3*", "|Y3*")
                        .replace("Y4*", "|Y4*")
                        .replace("N9*", "|N9*")
                        .replace("N1*", "|N1*")
                        .replace("N3*", "|N3*")
                        .replace("N4*", "|N4*")
                        .replace("R4*", "|R4*")
                        .replace("DTM*", "|DTM*")
                        .replace("LX*", "|LX*")
                        .replace("W09*", "|W09*")
                        .replace("H3*", "|H3*")
                        .replace("LX6*", "|LX6*")
                        .replace("N7*", "|N7*")
                        .replace("L0*", "|L0*")
                        .replace("L5*", "|L5*")
                        .replace("H1*", "|H1*")
                        .replace("V1*", "|V1*")
                        .replace("V9*", "|V9*")
                        .replace("SE*", "|SE*")
                        .replace("GE*", "|GE*")
                        .replace("IEA*", "|IEA*");

                tbContenido.addAll(fnSplit(coparnFileContent, '|'));

                String sSegmento;
                for (String value : tbContenido) {
                    List<String> tbv = new ArrayList<>();
                    tbv.addAll(fnSplit(value, '*'));
                    sSegmento = tbv.stream().findFirst().orElse(null);

                    switch (sSegmento) {
                        case "GS":
                            fechaCreacionEdi = formatFechaCreacionEdi(tbv);
                            break;
                        case "B1":
                            bookingNumberTemp = tbv.get(2);
                            ediCoparnSituacionReserva = tbv.get(4).trim();
                            ediCoparnSituacionReserva =
                                    switch (ediCoparnSituacionReserva) {
                                        case "D" -> "1";
                                        case "N" -> "9";
                                        case "U", "R" -> "5";
                                        default -> null;
                                    };
                            break;
                        case "N9":
                            bookingNumberTemp = tbv.get(2);
                            break;
                        case "ISA":
                            ownerCorrelative = processOwnerCorrelative(tbv, ownerCorrelative);
                            break;
                        default:
                            break;
                    }


                }

                List<HashMap<String, Object>> tbbkm = getBkmList(tbContenido);

                isMultiple = registerMultipleBookingEdi(ediCoparnSettingId, coparnFileName, coparnFileContent, azureUrl, tbContenido, isMultiple, tbbkm);
            }

            bookingNumber = getBookingNumber(bookingNumber, bookingNumberTemp);
        }

        registerSingleBookingEdi(ediCoparnSettingId, coparnFileContent, azureUrl, isMultiple, estado, lineaNavieraId, ediCoparnNombreArchivoOriginal, ediCoparnNombreArchivo, fechaCreacionEdi, ediCoparnObsProcesado, catalogIds, bookingNumber, ediCoparnSituacionReserva, ownerCorrelative, coparnContenidoArchivoEnter, result);

        return result;
    }

    private static String getBookingNumber(String bookingNumber, String bookingNumberTemp) {
        if (bookingNumber == null && bookingNumberTemp != null) {
            bookingNumber = bookingNumberTemp;
        }
        return bookingNumber;
    }

    private static Integer processOwnerCorrelative(List<String> tbv, Integer ownerCorrelative) {
        String sOwnerCorrelative;
        sOwnerCorrelative = tbv.get(13);
        if (StringUtils.isNumeric(sOwnerCorrelative))
            ownerCorrelative = Integer.parseInt(sOwnerCorrelative);
        return ownerCorrelative;
    }

    private static String formatFechaCreacionEdi(List<String> tbv) {
        String fechaCreacionEdi;
        fechaCreacionEdi = tbv.get(4) + tbv.get(5);
        if (fechaCreacionEdi.length() == 12) fechaCreacionEdi = fechaCreacionEdi + "00";
        return fechaCreacionEdi;
    }

    private static String getCoparnContenidoArchivoEnter(String coparnFileContent) {
        String coparnContenidoArchivoEnter;
        if (!coparnFileContent.contains("\r\n")) {
            coparnContenidoArchivoEnter = coparnFileContent.replace("'", "'\r\n");
        } else {
            coparnContenidoArchivoEnter = coparnFileContent;
        }
        return coparnContenidoArchivoEnter;
    }

    private static String getCoparnContenidoArchivoCopia(String coparnFileContent) {
        String coparnContenidoArchivoCopia;
        if (!coparnFileContent.contains("\r\n")) {
            coparnContenidoArchivoCopia = coparnFileContent.replace("'", "'|");
        } else {
            coparnContenidoArchivoCopia = coparnFileContent.replace("'\r\n", "'|");
        }
        return coparnContenidoArchivoCopia;
    }

    private static String getNombreArchivo(String extensionArchivoDescargar, String ediCoparnNombreArchivoOriginal, String nombreArchivo) {
        if (!extensionArchivoDescargar.isBlank()) {
            extensionArchivoDescargar = !extensionArchivoDescargar.substring(0, 1).equals(".") ? ("." + extensionArchivoDescargar) : extensionArchivoDescargar;
            extensionArchivoDescargar = ediCoparnNombreArchivoOriginal.substring(ediCoparnNombreArchivoOriginal.length() - extensionArchivoDescargar.length() - 1);
            nombreArchivo = ediCoparnNombreArchivoOriginal.substring(0, ediCoparnNombreArchivoOriginal.length() - extensionArchivoDescargar.length());
        }
        return nombreArchivo;
    }

    private String getBookingNumber(List<String> tbContenido, String bookingNumber) {
        String segRFF = tbContenido.stream().filter(value -> value.startsWith("RFF")).findFirst().orElse(null);
        if (segRFF != null && "".equals(bookingNumber)) {
            String coparnLine = fnLeeLineaCoparn(segRFF, 2, 1);
            if ("BN".equals(coparnLine) || "ANN".equals(coparnLine))
                bookingNumber = fnLeeLineaCoparn(segRFF, 2, 2);
        }
        return bookingNumber;
    }

    private Integer getOwnerCorrelative(List<String> tbContenido, Integer ownerCorrelative) {
        String sOwnerCorrelative;
        String segUNB2 = tbContenido.stream().filter(value -> value.startsWith("UNB")).findFirst().orElse(null);
        if (segUNB2 != null) {
            sOwnerCorrelative = fnLeeLineaCoparn(segUNB2, 6, 1);
            if (StringUtils.isNumeric(segUNB2)) ownerCorrelative = Integer.parseInt(sOwnerCorrelative);
        }
        return ownerCorrelative;
    }

    private static List<HashMap<String, Object>> getBkmList(List<String> tbContenido) {
        List<HashMap<String, Object>> tbbkm = new ArrayList<>();
        IntStream.range(0, tbContenido.size()).forEach(index -> {
            String element = tbContenido.get(index);
            if (element.startsWith("ST*"))
                tbbkm.add(new HashMap<>(
                        Map.of("posx", index, "valuex", element)
                ));
        });
        return tbbkm;
    }

    private void registerSingleBookingEdi(Integer ediCoparnSettingId, String coparnFileContent, String azureUrl, Boolean isMultiple, Integer estado, Integer lineaNavieraId, String ediCoparnNombreArchivoOriginal, String ediCoparnNombreArchivo, String fechaCreacionEdi, String ediCoparnObsProcesado, HashMap<String, Integer> catalogIds, String bookingNumber, String ediCoparnSituacionReserva, Integer ownerCorrelative, String coparnContenidoArchivoEnter, RegisterCoparnFileResult result) {
        if (!isMultiple) {
            Integer finalEstado = estado;
            BookingEdi newBookingEdi = bookingEdiRepository.save(BookingEdi.builder()
                    .bookingEdiSetting(BookingEdiSetting.builder().id(ediCoparnSettingId).build())
                    .shippingLine(ShippingLine.builder().id(lineaNavieraId).build())
                    .originalBkEdiFileName(ediCoparnNombreArchivoOriginal)
                    .bkEdiFileName(ediCoparnNombreArchivo)
                    .catBkEdiStatus(Catalog.builder().id(estado).build())
                    .registrationDate(LocalDateTime.now())
                    .registrationUser(User.builder().id(1).build())
                    .bkEdiCreationDate(fechaCreacionEdi)
                    .bkEdiProcessedComment(ediCoparnObsProcesado)
                    .active(Arrays.asList(catalogIds.get(IS_BKEDI_REJECTED), catalogIds.get(IS_BKEDI_REJECTED_DUPLICATE)).stream().anyMatch(value -> value.equals(finalEstado)))
                    .bookingNumber(bookingNumber)
                    .bkEdiReservationSituation(ediCoparnSituacionReserva)
                    .traceBkEdi2(bookingNumber)
                    .dateProcessedCoparn(Arrays.asList(catalogIds.get(IS_BKEDI_REJECTED), catalogIds.get(IS_BKEDI_REJECTED_DUPLICATE)).stream().anyMatch(value -> value.equals(finalEstado)) ? LocalDateTime.now() : null)
                    .ownerCorrelative(ownerCorrelative)
                    .build());

            if (coparnFileContent != null) {
                bookingEdiFileRepository1.save(BookingEdiFile.builder()
                        .bookingEdi(BookingEdi.builder().id(newBookingEdi.getId()).build())
                        .bkEdiContent(coparnContenidoArchivoEnter)
                        .registrationDate(LocalDateTime.now())
                        .registrationUser(User.builder().id(1).build())
                        .active(true)
                        .azureUrl(azureUrl)
                        .build());
            }

            result.setRespNewId(newBookingEdi.getId());
            result.setRespStatus(1);
        }
    }

    private Boolean registerMultipleBookingEdi(Integer ediCoparnSettingId, String coparnFileName, String coparnFileContent, String azureUrl, List<String> tbContenido, Boolean isMultiple, List<HashMap<String, Object>> tbbkm) {
        Integer y;
        y = tbContenido.stream().filter(value -> value.startsWith("ST*")).toList().size();
        if (y > 1) {
            isMultiple = true;
            String ediHeader = tbContenido.get(0) + tbContenido.get(1);
            String ediFooter = tbContenido.get(tbContenido.size() - 2) + tbContenido.get(tbContenido.size() - 1);

            Integer position = 1;
            while (position <= y) {
                String newEdi = "";
                String coparnNombreArvhivoM;
                Integer posTo = 0;
                Integer posFrom = 0;

                if (coparnFileName.contains("."))
                    coparnNombreArvhivoM = coparnFileName.substring(1, coparnFileName.indexOf('.') - 1) + "-" + String.format("%02d", position) + coparnFileName.substring(coparnFileName.indexOf('.'));
                else
                    coparnNombreArvhivoM = coparnFileName + "-" + String.format("%02d", position);

                posTo = Integer.parseInt(tbbkm.get(position).get("posx").toString());
                posFrom = position < y ? (Integer.parseInt(tbbkm.get(position).get("posx").toString() + 1)) : (tbContenido.size() - 1);

                Integer qq = posTo;
                while (qq <= posFrom - 1) {
                    newEdi += tbContenido.get(qq);
                    qq++;
                }

                newEdi = ediHeader + newEdi + ediFooter;

                RegisterCoparnFileResult multipleResult = registerCoparnFileNonTransactional(ediCoparnSettingId, coparnNombreArvhivoM, newEdi, azureUrl);
                bookingEdiRepository.updateFileOriginalName(multipleResult.getRespNewId(), coparnFileName);
                bookingEdiFileRepository.updateFileOriginalContent(multipleResult.getRespNewId(), coparnFileContent);

                position++;
            }
        }
        return isMultiple;
    }

    private String getReservationStatus(List<String> tbContenido, String ediCoparnSituacionReserva) {
        String segBGM2 = tbContenido.stream().filter(value -> value.startsWith("BGM")).findFirst().orElse(null);
        if (segBGM2 != null) {
            ediCoparnSituacionReserva = fnLeeLineaCoparn(segBGM2, 4, 1);
        }
        return ediCoparnSituacionReserva;
    }

    private String getFechaCreacionEdi(Integer lineaNavieraId, List<String> tbContenido, String fechaCreacionEdi) {
        if (!lineaNavieraId.equals(4104)) {
            String segUNB = tbContenido.stream().filter(value -> value.startsWith("UNB")).findFirst().orElse(null);
            fechaCreacionEdi = fnLeeLineaCoparn(segUNB, 5, 1) + fnLeeLineaCoparn(segUNB, 5, 2);
        } else {
            String segBGM = tbContenido.stream().filter(value -> value.startsWith("BGM")).findFirst().orElse(null);
            String fechaTemp = fnLeeLineaCoparn(segBGM, 3, 1);
            if (fechaTemp.length() > 12) fechaCreacionEdi = fechaTemp;
        }

        if (fechaCreacionEdi != null && fechaCreacionEdi.length() == 10) {
            fechaCreacionEdi = "20" + fechaCreacionEdi + "00";
        } else {
            if (fechaCreacionEdi != null && fechaCreacionEdi.length() > 12 && lineaNavieraId.equals(4102))
                fechaCreacionEdi = "20" + fechaCreacionEdi.substring(fechaCreacionEdi.length() - 10) + "00";
        }

        if (fechaCreacionEdi != null && fechaCreacionEdi.length() > 14)
            fechaCreacionEdi = fechaCreacionEdi.substring(0, 14);
        return fechaCreacionEdi;
    }

    private List<String> fnSplit(String list, char delimiter) {
        List<String> result = new ArrayList<>();
        int textPos = 0;
        String leftover = "";
        int chunkLen;
        String tmpStr;
        int startPos;
        int endPos;

        while (textPos < list.length()) {
            chunkLen = 4000 - leftover.length();
            tmpStr = leftover + list.substring(textPos, Math.min(textPos + chunkLen, list.length()));
            textPos += chunkLen;

            startPos = 0;
            endPos = tmpStr.indexOf(delimiter);

            while (endPos > 0) {
                String tmpVal = tmpStr.substring(startPos, endPos).trim();
                result.add(tmpVal);
                startPos = endPos + 1;
                endPos = tmpStr.indexOf(delimiter, startPos);
            }

            leftover = tmpStr.substring(startPos);
        }

        if (!leftover.isEmpty()) {
            result.add(leftover.trim());
        }

        return result;
    }

    private String fnLeeLineaCoparn(String psCadena, int piSegmento, int piSubSegmento) {
        psCadena = Optional.ofNullable(psCadena).orElse("").trim();

        int i = 0;
        int n = 0;
        int q = 0;
        String lsReturn = "";

        if (piSegmento == 1) {
            lsReturn = processSegment(psCadena, lsReturn, psCadena.substring(0, i));
        } else {
            n = 1;
            psCadena = getPsCadena(psCadena, piSegmento, n, i);

            if (piSubSegmento > 1) {
                i = psCadena.indexOf('+');
                if (i != -1) {
                    psCadena = psCadena.substring(0, i);
                }
                n = 1;
                while (n <= piSubSegmento - 1) {
                    i = psCadena.indexOf(':');
                    if (i != -1) {
                        q = 1;
                        psCadena = psCadena.substring(i + 1);
                    }
                    n++;
                }
            }

            lsReturn = getLsReturn(psCadena, piSegmento, piSubSegmento, q, lsReturn);
        }

        lsReturn = lsReturn.replace('&', ' ').replace('>', ' ').replace('<', ' ');
        return lsReturn;
    }

    private static String getPsCadena(String psCadena, int piSegmento, int n, int i) {
        while (n <= piSegmento - 1) {
            psCadena = processSegment(psCadena, psCadena, psCadena.substring(i + 1));
            n++;
        }
        return psCadena;
    }

    private static String getLsReturn(String psCadena, int piSegmento, int piSubSegmento, int q, String lsReturn) {
        int i;
        if ((piSegmento > 0 && piSubSegmento == 0) || piSubSegmento == 1 || (piSubSegmento > 1 && q == 1)) {
            int indexColon = psCadena.indexOf(':');
            int indexPlus = psCadena.indexOf('+');
            int indexQuote = psCadena.indexOf('\'');
            i = Math.min(Math.min(indexColon > 0 ? indexColon : Integer.MAX_VALUE, indexPlus > 0 ? indexPlus : Integer.MAX_VALUE), indexQuote > 0 ? indexQuote : Integer.MAX_VALUE);
            if (i != Integer.MAX_VALUE) {
                lsReturn = psCadena.substring(0, i);
            } else {
                lsReturn = psCadena;
            }
        }
        return lsReturn;
    }

    private static String processSegment(String psCadena, String lsReturn, String psCadena1) {
        int indexPlus = psCadena.indexOf('+');
        int indexQuote = psCadena.indexOf('\'');
        int i = Math.min(indexPlus > 0 ? indexPlus : Integer.MAX_VALUE, indexQuote > 0 ? indexQuote : Integer.MAX_VALUE);
        if (i != Integer.MAX_VALUE) {
            lsReturn = psCadena1;
        }
        return lsReturn;
    }

    private Optional<RegisterCoparnLastBookingEdiDTO> getLastBookingEdiInfo(Pageable pageable, Integer ediCoparnSettingId, HashMap<String, Integer> catalogIds, String extensionArchivoDescargar, String ediCoparnNombreArchivoOriginal, String nombreArchivo, String extensionArchivo) {
        List<BookingEdi> lastBookingEdi = bookingEdiRepository.findByOriginalCoparnFileNameAndBookingEdiSettingId(pageable, ediCoparnNombreArchivoOriginal, ediCoparnSettingId);
        if (!lastBookingEdi.isEmpty()) {
            return Optional.of(RegisterCoparnLastBookingEdiDTO.builder()
                    .status(catalogIds.get(IS_BKEDI_REJECTED_DUPLICATE))
                    .firstFileName(getNombreArchivo(extensionArchivoDescargar, ediCoparnNombreArchivoOriginal, nombreArchivo))
                    .finalFileName((nombreArchivo + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + extensionArchivo).substring(0, 200))
                    .build());
        }

        return Optional.empty();
    }
}