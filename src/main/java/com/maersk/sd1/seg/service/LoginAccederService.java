package com.maersk.sd1.seg.service;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.UserRepository;
import com.maersk.sd1.seg.controller.dto.LoginAccederOutput;
import com.maersk.sd1.seg.controller.dto.LoginAccederUserDto;
import com.maersk.sd1.seg.dto.LoginDataObtainOutput;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class LoginAccederService {

    private static final Logger logger = LogManager.getLogger(LoginAccederService.class);

    private final UserRepository userRepository;

    private final LoginDataObtainService loginDataObtainService;

    @Transactional
    public LoginAccederOutput loginAcceder(String user, String claveMd5, Long sistemaId, String origenLogin) {
        logger.info("Attempting to login. usuario={}, origenLogin={}", user, origenLogin);

        LoginAccederOutput output = new LoginAccederOutput();
        List<User> userDataOptional;
        Optional<User> userEntityOptional;

        if ("CMP".equalsIgnoreCase(origenLogin)) {
            userDataOptional = userRepository.findByAliasAndStatusAndCompany_Status(user, '1', true);
        } else {
            userDataOptional = userRepository.findByAliasAndKeyAndStatusInAndCompany_Status(user, claveMd5, List.of('1', '3'), true);
        }

        if (userDataOptional.isEmpty()) {
            logger.error("No user found with specified criteria.");
            output.setRespStatus(0);
            output.setRespMessage("Invalid credentials or user not found.");
            return output;
        }
        User userData = userDataOptional.getFirst();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dateChangeKey = userData.getDateChangeKey();

        long daysDifference = dateChangeKey != null ? ChronoUnit.DAYS.between(dateChangeKey, now) : 0;

        LoginAccederUserDto loginAccederUserDto = new LoginAccederUserDto(
                String.valueOf(userData.getId()),
                userData.getAlias(),
                userData.getNames(),
                userData.getFirstLastName(),
                userData.getSecondLastName() != null ? userData.getSecondLastName() : "",
                Optional.ofNullable(userData.getCompany()).map(Company::getLegalName).orElse(""),
                Optional.ofNullable(userData.getCompany()).map(Company::getDocument).orElse(""),
                dateChangeKey != null && daysDifference == 0 ? "1" : "0",
                (int) daysDifference,
                "IND"
        );

        userEntityOptional = userRepository.findByAlias(user);
        User userEntity = userEntityOptional.orElseThrow(() -> new EntityNotFoundException("User entity not found for update."));

        userEntity.setAccountantLogin(null);
        userEntity.setModificationDate(LocalDateTime.now());
        userRepository.save(userEntity);

        output.setRespStatus(1);
        output.setRespMessage("Success");
        output.setUserId(loginAccederUserDto.getUsuarioId());
        output.setCodigo(loginAccederUserDto.getCodigo());
        output.setNames(loginAccederUserDto.getNombre());
        output.setApellidoPaterno(loginAccederUserDto.getApellidoPaterno());
        output.setApellidoMaterno(loginAccederUserDto.getApellidoMaterno());
        output.setEmpresa(loginAccederUserDto.getEmpresa());
        output.setDocumento(loginAccederUserDto.getDocumento());
        output.setClaveCambiar(loginAccederUserDto.getClaveCambiar());
        output.setClaveDias(loginAccederUserDto.getClaveDias());
        output.setOrigen(loginAccederUserDto.getOrigen());

        Long userId;
        try {
            userId = Long.valueOf(loginAccederUserDto.getUsuarioId());
        } catch (NumberFormatException e) {
            logger.warn("Could not parse usuarioId as integer: {}", loginAccederUserDto.getUsuarioId());
            userId = null;
        }

        if (userId != null) {
            LoginDataObtainOutput loginDataObtainOutput = loginDataObtainService.getLoginData(userId, sistemaId);
            if(loginDataObtainOutput == null) {
                logger.error("No login data found for user with id: {}", userId);
                output.setLoginDataObtainOutput(null);
            }
            output.setLoginDataObtainOutput(loginDataObtainOutput);
        } else {
            output.setLoginDataObtainOutput(null);
        }

        return output;
    }
}