package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.EdiCodecoSendSftpInput;
import com.maersk.sd1.sde.dto.EdiCodecoSendSftpOutput;
import com.maersk.sd1.sde.service.EdiCodecoSendSftpService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEediCodecoSendSftp")
public class EdiCodecoSendSftpController {

    private static final Logger logger = LogManager.getLogger(EdiCodecoSendSftpController.class);

    private final EdiCodecoSendSftpService ediCodecoSendSftpService;

    @Autowired
    public EdiCodecoSendSftpController(EdiCodecoSendSftpService ediCodecoSendSftpService) {
        this.ediCodecoSendSftpService = ediCodecoSendSftpService;
    }

    @PostMapping("/servicioCodecoEnviarSftpEdi")
    public ResponseEntity<ResponseController<EdiCodecoSendSftpOutput>> sendCodecoToSftp(@RequestBody @Valid EdiCodecoSendSftpInput.Root request) {
        try {

            if(request.getPrefix() == null || request.getPrefix().getInput() == null){
                throw new InvalidRequestException("Invalid request");
            }

            EdiCodecoSendSftpInput.Input input = request.getPrefix().getInput();
            EdiCodecoSendSftpOutput output = ediCodecoSendSftpService.sendCodecoToSftp(
                    input.getGateTransmissionSettingId(),
                    input.getReferenceId(),
                    input.getRepositoryFiles()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (InvalidRequestException e) {
            logger.error("Invalid request: {}", e.getMessage());
            EdiCodecoSendSftpOutput output = new EdiCodecoSendSftpOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.badRequest().body(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing EDI CODECO send to SFTP.", e);
            EdiCodecoSendSftpOutput output = new EdiCodecoSendSftpOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.internalServerError().body(new ResponseController<>(output));
        }
    }

    public static class InvalidRequestException extends RuntimeException {
        public InvalidRequestException(String message) {
            super(message);
        }

        public InvalidRequestException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}