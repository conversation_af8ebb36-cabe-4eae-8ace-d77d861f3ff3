package com.maersk.sd1.sds.controller;


import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.ProductDeleteInput;
import com.maersk.sd1.sds.dto.ProductDeleteOutput;
import com.maersk.sd1.sds.service.ProductDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProductoService")
public class ProductDeleteController {

    private static final Logger logger = LogManager.getLogger(ProductDeleteController.class.getName());

    private final ProductDeleteService productDeleteService;

    @Autowired
    public ProductDeleteController(ProductDeleteService productDeleteService) {
        this.productDeleteService = productDeleteService;
    }

    @PostMapping("/sdsproductoEliminar")
    public ResponseEntity<ResponseController<ProductDeleteOutput>> deleteProduct(@RequestBody @Valid ProductDeleteInput.Root request) {
        try {
            ProductDeleteInput.Input input = request.getPrefix().getInput();
            ProductDeleteOutput output = productDeleteService.deleteProduct(
                    input.getProductId(),
                    input.getUserModificationId(),
                    input.getLanguageId()
            );
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while logically deleting the product.", e);
            ProductDeleteOutput output = new ProductDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}
