package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.InspectionShowCleaningSectionInput;
import com.maersk.sd1.sde.dto.InspectionShowCleaningSectionOutput;
import com.maersk.sd1.sde.service.InspectionShowCleaningSectionService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("ModuleSDE/module/sde/SDEInspeccionServiceImp")
public class InspectionShowCleaningSectionController {

    private static final Logger logger = LogManager.getLogger(InspectionShowCleaningSectionController.class);

    private final InspectionShowCleaningSectionService cleaningSectionService;

    @Autowired
    public InspectionShowCleaningSectionController(InspectionShowCleaningSectionService cleaningSectionService) {
        this.cleaningSectionService = cleaningSectionService;
    }

    @PostMapping("/sdeinspectionShowCleaningSectionGet")
    public ResponseEntity<ResponseController<InspectionShowCleaningSectionOutput>> getCleaningSection(@Valid @RequestBody InspectionShowCleaningSectionInput.Root request) {
        try {
            Integer catCleaningTypeId = request.getPrefix().getInput().getCatCleaningTypeId();
            Integer eirId = request.getPrefix().getInput().getEirId();

            InspectionShowCleaningSectionOutput output = cleaningSectionService.getShowCleaningSection(catCleaningTypeId, eirId);

            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            // Return error output
            InspectionShowCleaningSectionOutput errorOutput = new InspectionShowCleaningSectionOutput();
            InspectionShowCleaningSectionOutput.ResponseList responseList = new InspectionShowCleaningSectionOutput.ResponseList();
            responseList.setCedexMercId(null);
            responseList.setFlagShowCleaningSectionInterior(false);
            responseList.setFlagShowCleaningSectionBottom(false);
            responseList.setFlagShowCleaningSectionTop(false);
            responseList.setFlagShowCleaningSectionRight(false);
            responseList.setFlagShowCleaningSectionLeft(false);
            errorOutput.setResponse(responseList);
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}
