package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.ShippingLine;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "contenedor", schema = "sds", indexes = {
        @Index(name = "nci_msft_1_contenedor_1C4730378DC03E64A20AE2A25B8DA58C", columnList = "activo")
}, uniqueConstraints = {
        @UniqueConstraint(name = "UniqueContenedor", columnNames = {"numero_contenedor"})
})
public class Container {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "contenedor_id", nullable = false)
    private Integer id;

    @Size(max = 11)
    @NotNull
    @Column(name = "numero_contenedor", nullable = false, length = 11)
    private String containerNumber;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_familia_id", nullable = false)
    private Catalog catFamily;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_tamano_id", nullable = false)
    private Catalog catSize;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_tipo_contenedor_id", nullable = false)
    private Catalog catContainerType;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "linea_naviera_id", nullable = false)
    private ShippingLine shippingLine;

    @NotNull
    @Column(name = "tara", nullable = false)
    private Integer containerTare;

    @NotNull
    @Column(name = "carga_maxima", nullable = false)
    private Integer maximunPayload;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "codigo_iso_id")
    private IsoCode isoCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_clase_id")
    private Catalog catGrade;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_tipo_reefer_id")
    private Catalog catReeferType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_marca_motor_id")
    private Catalog catEngineBrand;

    @Column(name = "fecha_fabricacion")
    private LocalDateTime manufactureDate;

    @NotNull
    @Column(name = "shipper_own", nullable = false)
    private Boolean shipperOwn = false;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @Size(max = 20)
    @Column(name = "trace_contenedor", length = 20)
    private String traceContainer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_categoria_id")
    private Catalog catCategory;

    @Column(name = "para_venta")
    private Boolean forSale;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_origen_creacion_id")
    private Catalog catCreationOrigin;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_equipment_group_type")
    private Catalog catEquipmentGroupType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_equip_measure_tare_id")
    private Catalog catEquipMeasureTare;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_equip_measure_payload_id")
    private Catalog catEquipMeasurePayload;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_updatesource_cnt_id")
    private Catalog catUpdatesourceCnt;

    @Transient
    private String valueTare;

    public Container(Integer id) {
        this.id = id;
    }
}