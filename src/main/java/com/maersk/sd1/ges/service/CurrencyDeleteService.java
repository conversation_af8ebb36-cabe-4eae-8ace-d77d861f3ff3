package com.maersk.sd1.ges.service;

import com.maersk.sd1.ges.dto.CurrencyDeleteOutput;
import com.maersk.sd1.common.repository.CurrencyRepository;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CurrencyDeleteService {

    private static final Logger logger = LogManager.getLogger(CurrencyDeleteService.class);

    private final CurrencyRepository currencyRepository;

    @Autowired
    public CurrencyDeleteService(CurrencyRepository currencyRepository) {
        this.currencyRepository = currencyRepository;
    }

    @Transactional
    public CurrencyDeleteOutput deleteMoneda(Integer monedaId) {
        CurrencyDeleteOutput output = new CurrencyDeleteOutput();
        try {
            currencyRepository.deleteById(monedaId);
            output.setRespEstado(1);
            output.setRespMensaje("Registro eliminado correctamente");
        } catch (Exception e) {
            logger.error("Error deleting Moneda with ID ");
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
        }

        return output;
    }
}
