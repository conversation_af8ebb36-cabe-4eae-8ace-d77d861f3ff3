package com.maersk.sd1.sde.dto.listactivityzone;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ListActivityZoneCntDTO {
    private Integer x;
    private Integer subBusinessUnitId;
    private Integer subBusinessUnitLocalId;
    private LocalDateTime truckArrivalDate;
    private Integer catMovementId;
    private Integer catProcedenceId;
    private Integer eirId;
    private Integer shippingLineId;
    private String containerNumber;
    private Integer catContainerTypeId;
    private Integer catSizeId;
    private Integer isoCodeId;
    private Integer catClassId;
    private Integer tara;
    private Integer catReeferTypeId;
    private String activityCode;
    private String zoneCode;
    private LocalDateTime startActZone;
    private Integer eirActivityZoneId;
    private Integer eirZoneId;
    private Boolean structureWithDamage;
    private Boolean machineryWithDamage;
    private String nombreAct;
    private String nombreZona;
}