package com.maersk.sd1.common.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "programacion_nave_cutoff", schema = "sds", indexes = {
        @Index(name = "PRONAVCUTX_LINE", columnList = "linea_naviera_id"),
        @Index(name = "PRONAVCUTX_PRNADE", columnList = "programacion_nave_detalle_id")
})
public class VesselProgrammingCutoff {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "programacion_nave_cutoff_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "programacion_nave_detalle_id", nullable = false)
    private VesselProgrammingDetail vesselProgrammingDetail;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "linea_naviera_id")
    private ShippingLine shippingLine;

    @Column(name = "fecha_cutoff_retiro_vacio_dry")
    private LocalDateTime dateCutoffRetreatEmptyDry;

    @Column(name = "fecha_cutoff_retiro_vacio_reefer")
    private LocalDateTime dateCutoffRetreatEmptyReefer;

    @Column(name = "fecha_cutoff_puerto_dry")
    private LocalDateTime dateCutoffPortDry;

    @Column(name = "fecha_cutoff_puerto_reefer")
    private LocalDateTime dateCutoffPortReefer;

    @Column(name = "fecha_cutoff_deposito_dry")
    private LocalDateTime dateCutoffDepositDry;

    @Column(name = "fecha_cutoff_deposito_reefer")
    private LocalDateTime dateCutoffDepositReefer;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

}