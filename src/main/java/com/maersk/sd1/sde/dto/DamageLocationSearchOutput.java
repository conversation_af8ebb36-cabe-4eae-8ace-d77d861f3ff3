package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat (shape = JsonFormat.Shape.ARRAY)
public class DamageLocationSearchOutput {

    @JsonProperty("cat_damage_location_id")
    private Integer catDamageLocationId;

    @JsonProperty("damage_location")
    private String damageLocation;

    @JsonProperty("damage_location_code")
    private String damageLocationCode;
}
