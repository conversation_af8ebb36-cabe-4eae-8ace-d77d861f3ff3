package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BookingEdiProcessUpdatesInputDTO {

    @Data
    public static class Input{

        @JsonProperty("booking_id")
        private int bookingId;

        @JsonProperty("documento_carga_id")
        private int documentoCargaId;

        @JsonProperty("usuario_registro_id")
        private int usuarioRegistroId;

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private com.maersk.sd1.sds.dto.BookingEdiProcessUpdatesInputDTO.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private com.maersk.sd1.sds.dto.BookingEdiProcessUpdatesInputDTO.Prefix prefix;

        public Root() {
            this.prefix = new com.maersk.sd1.sds.dto.BookingEdiProcessUpdatesInputDTO.Prefix();
            this.prefix.setInput(new com.maersk.sd1.sds.dto.BookingEdiProcessUpdatesInputDTO.Input());
        }
    }
}
