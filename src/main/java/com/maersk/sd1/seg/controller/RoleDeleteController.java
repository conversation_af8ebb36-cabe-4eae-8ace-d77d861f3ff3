package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.RoleDeleteInput;
import com.maersk.sd1.seg.controller.dto.RoleDeleteOutput;
import com.maersk.sd1.seg.service.RoleDeleteService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMRolServiceImp")
public class RoleDeleteController {

    private static final Logger logger = LogManager.getLogger(RoleDeleteController.class);

    private final RoleDeleteService roleDeleteService;

    public RoleDeleteController(RoleDeleteService roleDeleteService) {
        this.roleDeleteService = roleDeleteService;
    }

    @PostMapping("/segrolEliminar")
    public ResponseEntity<ResponseController<RoleDeleteOutput>> deleteRole(@RequestBody @Valid RoleDeleteInput.Root request) {
        try {
            RoleDeleteInput.Input input = request.getPrefix().getInput();
            RoleDeleteOutput output = roleDeleteService.deleteRole(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing delete role request.", e);
            RoleDeleteOutput output = new RoleDeleteOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}