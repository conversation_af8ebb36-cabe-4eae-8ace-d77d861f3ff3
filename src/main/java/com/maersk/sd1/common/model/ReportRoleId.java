package com.maersk.sd1.common.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Objects;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReportRoleId {

    private Integer report;
    private Integer rol;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ReportRoleId that = (ReportRoleId) o;
        return rol.equals(that.rol) &&

                report.equals(that.report);
    }

    @Override
    public int hashCode() {
        return Objects.hash(report, rol);
    }
}