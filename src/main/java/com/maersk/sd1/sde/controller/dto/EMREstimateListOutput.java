package com.maersk.sd1.sde.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class EMREstimateListOutput {
    @JsonProperty
    private List<EMREstimateListDataOutput> finalResult;

    @JsonProperty
    private List<List<Integer>> totalRecords;
}