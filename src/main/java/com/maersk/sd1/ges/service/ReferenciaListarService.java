package com.maersk.sd1.ges.service;


import com.maersk.sd1.common.repository.ResourceRepository;
import com.maersk.sd1.ges.dto.ForeignKeyReferenceDTO;
import com.maersk.sd1.ges.dto.ReferenciaItem;
import com.maersk.sd1.ges.dto.ReferenciaListarInputDTO;
import com.maersk.sd1.ges.dto.ReferenciaListarOutputDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
public class ReferenciaListarService {
    private static final Logger logger = LogManager.getLogger(ReferenciaListarService.class);


    private final ResourceRepository referenciaListarRepository;

    @Autowired
    public ReferenciaListarService(ResourceRepository referenciaListarRepository) {
        this.referenciaListarRepository = referenciaListarRepository;
    }

    @Transactional
    public ReferenciaListarOutputDTO listarReferencias(ReferenciaListarInputDTO.Input input) {
        try {
            Integer rowsToProcess;
            Integer currentRow;
            String owner;
            String table;
            String column;
            String pkColumn;


            List<Object[]> referencias = referenciaListarRepository.listarReferencias(input.getTablaIn(), input.getOwnerIn());

            List<ForeignKeyReferenceDTO> foreignKeyReferences = new ArrayList<>();

            Integer i = 0;
            for (Object[] referencia : referencias) {
                ForeignKeyReferenceDTO dto = new ForeignKeyReferenceDTO();
                dto.setRowId(i++);
                dto.setPktableQualifier((String) referencia[0]);
                dto.setPktableOwner((String) referencia[1]);
                dto.setPktableName((String) referencia[2]);
                dto.setPkcolumnName((String) referencia[3]);
                dto.setFktableQualifier((String) referencia[4]);
                dto.setOwnerFk((String) referencia[5]);
                dto.setTablaFk((String) referencia[6]);
                dto.setColumnFk((String) referencia[7]);
                dto.setKeySeq((Short) referencia[8]);
                dto.setUpdateRule((Short) referencia[9]);
                dto.setDeleteRule((Short) referencia[10]);
                dto.setFkName((String) referencia[11]);
                dto.setPkName((String) referencia[12]);
                dto.setDeferrability((Short) referencia[13]);

                foreignKeyReferences.add(dto);
            }

            currentRow = 0;
            rowsToProcess = foreignKeyReferences.size();

            List<ReferenciaItem> referenciaItems = new ArrayList<>();
            while (currentRow < rowsToProcess) {
                ForeignKeyReferenceDTO foreignKeyReference = foreignKeyReferences.get(currentRow);
                owner = foreignKeyReference.getOwnerFk();
                table = foreignKeyReference.getTablaFk();
                column = foreignKeyReference.getColumnFk();
                pkColumn = foreignKeyReference.getPkcolumnName();

                if (input.getValueIn() != null) {
                    List<Object[]> referenciasFk = referenciaListarRepository.listReferencias(owner, table, column, input.getOwnerIn(), input.getTablaIn(), pkColumn, input.getValueIn());

                    for (Object[] referenciaFk : referenciasFk) {
                        ReferenciaItem referenciaItem = new ReferenciaItem();
                        referenciaItem.setEsquema((String) referenciaFk[0]);
                        referenciaItem.setTabla((String) referenciaFk[1]);
                        referenciaItem.setColumna((String) referenciaFk[2]);
                        referenciaItem.setValueId((Integer) referenciaFk[3]);
                        referenciaItems.add(referenciaItem);

                    }
                } else {
                    ReferenciaItem referenciaItem = new ReferenciaItem();
                    referenciaItem.setEsquema(owner);
                    referenciaItem.setTabla(table);
                    referenciaItem.setColumna(column);
                    referenciaItem.setValueId(null);
                    referenciaItems.add(referenciaItem);
                }
                currentRow++;
            }

            ReferenciaListarOutputDTO output = new ReferenciaListarOutputDTO();
            output.setRespEstado(1);
            output.setRespMensaje("OK");
            output.setListaReferencias(referenciaItems);

            return output;
        }
        catch (Exception e) {
            logger.error("Error al listar referencias", e);
            ReferenciaListarOutputDTO output = new ReferenciaListarOutputDTO();
            output.setRespEstado(0);
            output.setRespMensaje("Error al listar referencias");
            output.setListaReferencias(new ArrayList<>());
            return output;
        }
    }
}
