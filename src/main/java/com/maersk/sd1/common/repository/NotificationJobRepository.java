package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.NotificationJob;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface NotificationJobRepository extends JpaRepository<NotificationJob, Integer> {

    @Query("SELECT n " +
            "FROM NotificationJob n " +
            "WHERE n.catStatus.id = :activeState AND " +
            "((n.validitySince IS NULL AND n.validityUntil IS NULL) OR " +
            "(n.validitySince IS NOT NULL AND n.validityUntil IS NOT NULL AND CURRENT_TIMESTAMP BETWEEN n.validitySince AND n.validityUntil))")
    List<NotificationJob> findNotificationJobs(@Param("activeState") Integer activeState);
}