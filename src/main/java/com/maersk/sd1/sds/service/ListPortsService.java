package com.maersk.sd1.sds.service;

import com.maersk.sd1.sds.controller.dto.ListPortsOutput;
import com.maersk.sd1.common.repository.PortRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
@Service
public class ListPortsService {

    private static final Logger logger = LogManager.getLogger(ListPortsService.class);

    private final PortRepository portRepository;

    @Transactional(readOnly = true)
    public List<ListPortsOutput> getAllPorts() {
        try {
            return portRepository.findAllActivePorts();
        } catch (Exception e) {
            logger.error("Error retrieving ports.", e);
            throw e;
        }
    }
}