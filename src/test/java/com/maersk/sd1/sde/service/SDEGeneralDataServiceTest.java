package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Currency;
import com.maersk.sd1.common.repository.BusinessUnitConfigRepository;
import com.maersk.sd1.sde.dto.GeneralDataInput;
import com.maersk.sd1.sde.dto.GeneralDataOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

class SDEGeneralDataServiceTest {

    @Mock
    private BusinessUnitConfigRepository businessUnitConfigRepository;

    @InjectMocks
    private SDEGeneralDataService sdeGeneralDataService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getGeneralData_withValidInput_returnsExpectedOutput() {
        // Arrange
        GeneralDataInput.Input input = new GeneralDataInput.Input();
        input.setBusinessUnitId(1);
        input.setUserId(2);

        Integer zonaHoraria = 5;
        Integer impuestoVenta = 16;
        String userName = "Test User";

        Currency currency1 = new Currency();
        currency1.setId(1);
        currency1.setName("US Dollar");
        currency1.setAbbreviation("USD");
        currency1.setSymbol("$");
        currency1.setSeparatorMiles(",");
        currency1.setSeparatorDecimals(".");
        currency1.setPrecision("2");
        currency1.setIcu("USD");

        Currency currency2 = new Currency();
        currency2.setId(2);
        currency2.setName("Euro");
        currency2.setAbbreviation("EUR");
        currency2.setSymbol("€");
        currency2.setSeparatorMiles(".");
        currency2.setSeparatorDecimals(",");
        currency2.setPrecision("2");
        currency2.setIcu("EUR");

        List<Currency> currencies = Arrays.asList(currency1, currency2);

        when(businessUnitConfigRepository.findActiveValueByBusinessUnitIdAndCatConfigurationTypeId(1, 20251))
                .thenReturn(zonaHoraria);
        when(businessUnitConfigRepository.findActiveValueByBusinessUnitIdAndCatConfigurationTypeId(1, 183))
                .thenReturn(impuestoVenta);
        when(businessUnitConfigRepository.findUserNameById(2)).thenReturn(userName);
        when(businessUnitConfigRepository.findCurrenciesByBusinessUnitId(1)).thenReturn(currencies);

        // Act
        GeneralDataOutput.Output output = sdeGeneralDataService.getGeneralData(input);

        // Assert
        assertNotNull(output);
        assertNotNull(output.getTemp());
        assertEquals(1, output.getTemp().size());
        assertEquals(zonaHoraria, output.getTemp().get(0).getZonaHoraria());
        assertEquals(impuestoVenta.doubleValue(), output.getTemp().get(0).getImpuestoVenta());
        assertEquals(userName, output.getTemp().get(0).getUsuarioNombreCompleto());

        assertNotNull(output.getMonedas());
        assertEquals(2, output.getMonedas().size());

        assertEquals(1, output.getMonedas().get(0).getMonedaId());
        assertEquals("US Dollar", output.getMonedas().get(0).getNombre());
        assertEquals("USD", output.getMonedas().get(0).getAbreviatura());
        assertEquals("$", output.getMonedas().get(0).getSimbolo());

        assertEquals(2, output.getMonedas().get(1).getMonedaId());
        assertEquals("Euro", output.getMonedas().get(1).getNombre());
        assertEquals("EUR", output.getMonedas().get(1).getAbreviatura());
        assertEquals("€", output.getMonedas().get(1).getSimbolo());
    }

    @Test
    void getGeneralData_withNullTimeZone_usesDefaultValue() {
        // Arrange
        GeneralDataInput.Input input = new GeneralDataInput.Input();
        input.setBusinessUnitId(1);
        input.setUserId(2);

        Integer impuestoVenta = 16;
        String userName = "Test User";

        when(businessUnitConfigRepository.findActiveValueByBusinessUnitIdAndCatConfigurationTypeId(1, 20251))
                .thenReturn(null);
        when(businessUnitConfigRepository.findActiveValueByBusinessUnitIdAndCatConfigurationTypeId(1, 183))
                .thenReturn(impuestoVenta);
        when(businessUnitConfigRepository.findUserNameById(2)).thenReturn(userName);
        when(businessUnitConfigRepository.findCurrenciesByBusinessUnitId(1)).thenReturn(List.of());

        // Act
        GeneralDataOutput.Output output = sdeGeneralDataService.getGeneralData(input);

        // Assert
        assertNotNull(output);
        assertNotNull(output.getTemp());
        assertEquals(1, output.getTemp().size());
        assertEquals(0, output.getTemp().get(0).getZonaHoraria());
    }

    @Test
    void getGeneralData_withNullSalesTax_usesDefaultValue() {
        // Arrange
        GeneralDataInput.Input input = new GeneralDataInput.Input();
        input.setBusinessUnitId(1);
        input.setUserId(2);

        Integer zonaHoraria = 5;
        String userName = "Test User";

        when(businessUnitConfigRepository.findActiveValueByBusinessUnitIdAndCatConfigurationTypeId(1, 20251))
                .thenReturn(zonaHoraria);
        when(businessUnitConfigRepository.findActiveValueByBusinessUnitIdAndCatConfigurationTypeId(1, 183))
                .thenReturn(null);
        when(businessUnitConfigRepository.findUserNameById(2)).thenReturn(userName);
        when(businessUnitConfigRepository.findCurrenciesByBusinessUnitId(1)).thenReturn(List.of());

        // Act
        GeneralDataOutput.Output output = sdeGeneralDataService.getGeneralData(input);

        // Assert
        assertNotNull(output);
        assertNotNull(output.getTemp());
        assertEquals(1, output.getTemp().size());
        assertEquals(0.0, output.getTemp().get(0).getImpuestoVenta());
    }
}