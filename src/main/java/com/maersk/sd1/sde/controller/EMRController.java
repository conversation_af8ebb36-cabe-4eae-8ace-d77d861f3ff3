package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.*;
import com.maersk.sd1.sde.service.EMREstimateListService;
import com.maersk.sd1.sde.service.EMREstimaterRegisterService;
import com.maersk.sd1.sde.service.EMRSearchEstimateContainerService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEEMRServiceImp")
@Log4j2
public class EMRController {

    private final EMREstimateListService eMREstimateListService;

    private final EMREstimaterRegisterService eMREstimaterRegisterService;

    private final EMRSearchEstimateContainerService eMRSearchEstimateContainerService;

    @PostMapping("/sdelistarEstimadosEmr")
    public ResponseEntity<ResponseController<EMREstimateListOutput>> getEquipmentList(@Valid @RequestBody EMREstimateListInput.Root request) {
        log.info("Request received for equipment list: {}", request);
        EMREstimateListInput.Input inputDto = request.getPrefix().getInput();
        try {
            EMREstimateListOutput result = eMREstimateListService.execute(inputDto);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            log.error("An error occurred while processing the equipment list request.", e);
            return ResponseEntity.internalServerError().body(new ResponseController<>(e.getMessage()));
        }
    }

    @PostMapping("/sderegistrarEstimadoEmr")
    public ResponseEntity<ResponseController<EMREstimateRegisterOutput>> eMREstimateRegister(@Valid @RequestBody EMREstimateRegisterInput.Root request) {
        EMREstimateRegisterInput.Input inputDto = request.getPrefix().getInput();
        try {
            EMREstimateRegisterOutput result = eMREstimaterRegisterService.execute(inputDto);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            log.error("An error occurred while processing estimate register.", e);
            return ResponseEntity.internalServerError().body(new ResponseController<>(e.getMessage()));
        }
    }

    @PostMapping("/sdebuscarContenedorEstimados")
    public ResponseEntity<ResponseController<List<EMRSearchEstimateContainerOutput>>> eMRSearchEstimateContainer(@Valid @RequestBody EMRSearchEstimateContainerInput.Root request) {
        EMRSearchEstimateContainerInput.Input inputDto = request.getPrefix().getInput();
        try {
            List<EMRSearchEstimateContainerOutput> result = eMRSearchEstimateContainerService.execute(inputDto);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            log.error("An error occurred while processing search estimate container.", e);
            return ResponseEntity.internalServerError().body(new ResponseController<>(e.getMessage()));
        }
    }

}