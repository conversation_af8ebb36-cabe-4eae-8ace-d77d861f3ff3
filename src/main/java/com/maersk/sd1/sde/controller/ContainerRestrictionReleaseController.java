package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.ContainerRestrictionReleaseInput;
import com.maersk.sd1.sde.dto.ContainerRestrictionReleaseOutput;
import com.maersk.sd1.sde.service.ContainerRestrictionReleaseService;
import jakarta.validation.Valid;
import lombok.extern.log4j.Log4j2;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/ModuleSDE/module/sde/SDERestriccionContenedorServiceImp")
@Log4j2
public class ContainerRestrictionReleaseController {

    private final ContainerRestrictionReleaseService containerRestrictionReleaseService;

    public ContainerRestrictionReleaseController(ContainerRestrictionReleaseService containerRestrictionReleaseService) {
        this.containerRestrictionReleaseService = containerRestrictionReleaseService;
    }

    @PostMapping("/sdecontainerRestrictionRealeaseEdit")
    public ResponseEntity<ResponseController<ContainerRestrictionReleaseOutput>> releaseContainerRestriction(
            @RequestBody @Valid ContainerRestrictionReleaseInput.Root request) {
        try {
            log.info("Request received releaseContainerRestriction: {}", request);
            ContainerRestrictionReleaseInput.Input input = request.getPrefix().getInput();
            ContainerRestrictionReleaseOutput output = containerRestrictionReleaseService.releaseContainerRestriction(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            log.error("An error occurred while processing the request.", e);
            ContainerRestrictionReleaseOutput output = new ContainerRestrictionReleaseOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}