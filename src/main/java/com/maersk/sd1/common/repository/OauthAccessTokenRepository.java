package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.OauthAccessToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OauthAccessTokenRepository extends JpaRepository<OauthAccessToken, Long> {

    // find all tokens for a given user
    List<OauthAccessToken> findByUserName(String userName);
}
