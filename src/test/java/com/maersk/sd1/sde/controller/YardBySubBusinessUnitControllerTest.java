package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.YardBySubBusinessUnitInput;
import com.maersk.sd1.sde.dto.YardBySubBusinessUnitOutput;
import com.maersk.sd1.sde.service.YardBySubBusinessUnitService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class YardBySubBusinessUnitControllerTest {

    @Mock
    private YardBySubBusinessUnitService yardBySubBusinessUnitService;

    @InjectMocks
    private YardBySubBusinessUnitController controller;

    private YardBySubBusinessUnitInput.Root validRequest;
    private YardBySubBusinessUnitOutput validOutput;

    @BeforeEach
    void setUp() {
        // Setup valid request
        YardBySubBusinessUnitInput.Input input = new YardBySubBusinessUnitInput.Input();
        input.setSubUnidadNegocioLocalId(1);

        YardBySubBusinessUnitInput.Prefix prefix = new YardBySubBusinessUnitInput.Prefix();
        prefix.setInput(input);

        validRequest = new YardBySubBusinessUnitInput.Root();
        validRequest.setPrefix(prefix);

        // Setup valid output
        validOutput = new YardBySubBusinessUnitOutput();
        List<YardBySubBusinessUnitOutput.YardDto> yards = new ArrayList<>();

        YardBySubBusinessUnitOutput.YardDto yard = new YardBySubBusinessUnitOutput.YardDto();
        yard.setYardId(1);
        yard.setYardCode("YRD1");
        yard.setYardName("Test Yard");
        yards.add(yard);

        validOutput.setYards(yards);
    }

    @Test
    void givenValidRequest_whenObtenerPatioBySubUnidadNegocio_thenReturnSuccess() {
        // Arrange
        when(yardBySubBusinessUnitService.getYardsBySubBusinessUnit(anyInt())).thenReturn(validOutput);

        // Act
        ResponseEntity<ResponseController<List<List<Object>>>> response =
                controller.obtenerPatioBySubUnidadNegocio(validRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getResult());

        List<List<Object>> result = response.getBody().getResult();
        assertEquals(1, result.size());

        List<Object> yardData = result.get(0);
        assertEquals(3, yardData.size());
        assertEquals(1, yardData.get(0));
        assertEquals("YRD1", yardData.get(1));
        assertEquals("Test Yard", yardData.get(2));

        verify(yardBySubBusinessUnitService, times(1)).getYardsBySubBusinessUnit(1);
    }

    @Test
    void givenValidRequest_whenServiceReturnsEmptyYards_thenReturnEmptyList() {
        // Arrange
        YardBySubBusinessUnitOutput emptyOutput = new YardBySubBusinessUnitOutput();
        emptyOutput.setYards(new ArrayList<>());

        when(yardBySubBusinessUnitService.getYardsBySubBusinessUnit(anyInt())).thenReturn(emptyOutput);

        // Act
        ResponseEntity<ResponseController<List<List<Object>>>> response =
                controller.obtenerPatioBySubUnidadNegocio(validRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getResult());
        assertEquals(0, response.getBody().getResult().size());

        verify(yardBySubBusinessUnitService, times(1)).getYardsBySubBusinessUnit(1);
    }

    @Test
    void givenValidRequest_whenServiceReturnsNullYards_thenReturnEmptyList() {
        // Arrange
        YardBySubBusinessUnitOutput nullOutput = new YardBySubBusinessUnitOutput();
        nullOutput.setYards(null);

        when(yardBySubBusinessUnitService.getYardsBySubBusinessUnit(anyInt())).thenReturn(nullOutput);

        // Act
        ResponseEntity<ResponseController<List<List<Object>>>> response =
                controller.obtenerPatioBySubUnidadNegocio(validRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertNotNull(response.getBody().getResult());
        assertEquals(0, response.getBody().getResult().size());

        verify(yardBySubBusinessUnitService, times(1)).getYardsBySubBusinessUnit(1);
    }

    @Test
    void givenServiceThrowsException_whenObtenerPatioBySubUnidadNegocio_thenReturnInternalServerError() {
        // Arrange
        when(yardBySubBusinessUnitService.getYardsBySubBusinessUnit(anyInt())).thenThrow(new RuntimeException("Test exception"));

        // Act
        ResponseEntity<ResponseController<List<List<Object>>>> response =
                controller.obtenerPatioBySubUnidadNegocio(validRequest);

        // Assert
        assertNotNull(response);
        assertEquals(500, response.getStatusCode().value());
        assertNotNull(response.getBody());
        assertNull(response.getBody().getResult());

        verify(yardBySubBusinessUnitService, times(1)).getYardsBySubBusinessUnit(1);
    }
}