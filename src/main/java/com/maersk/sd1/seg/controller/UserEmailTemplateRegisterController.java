package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.controller.dto.UserEmailTemplateRegisterInput;
import com.maersk.sd1.seg.controller.dto.UserEmailTemplateRegisterOutput;
import com.maersk.sd1.seg.service.UserEmailTemplateRegisterService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMUsuarioServiceImp")
public class UserEmailTemplateRegisterController {

    private static final Logger logger = LogManager.getLogger(UserEmailTemplateRegisterController.class);

    private final UserEmailTemplateRegisterService userEmailTemplateRegisterService;

    @PostMapping("/segusuarioEmailPlantillaRegistrar")
    public ResponseEntity<ResponseController<UserEmailTemplateRegisterOutput>> registerUserEmailPlantilla(
            @Valid @RequestBody UserEmailTemplateRegisterInput.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure."));
            }
            UserEmailTemplateRegisterInput.Input input = request.getPrefix().getInput();
            UserEmailTemplateRegisterOutput output = userEmailTemplateRegisterService.registerUserEmailPlantilla(
                    input.getUserId(),
                    input.getTemplate());
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request", e);
            UserEmailTemplateRegisterOutput out = new UserEmailTemplateRegisterOutput();
            out.setRespStatus(0);
            out.setRespMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(out));
        }
    }
}