package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class ProductUpdateInput {

    @Data
    public static class Input {

        @JsonProperty("producto_id")
        @NotNull(message = "El campo producto_id no puede ser nulo")
        private Integer productId;

        @JsonProperty("unidad_negocio_id")
        @NotNull(message = "El campo unidad_negocio_id no puede ser nulo")
        private Integer businessUnitId;

        @JsonProperty("codigo_producto")
        @Size(max = 10, message = "El campo codigo_producto no debe exceder 10 caracteres")
        private String productCode;

        @JsonProperty("nombre_producto")
        @NotNull(message = "El campo nombre_producto no puede ser nulo")
        @Size(max = 100, message = "El campo nombre_producto no debe exceder 100 caracteres")
        private String productName;

        @JsonProperty("cat_grupo_producto_id")
        @NotNull(message = "El campo cat_grupo_producto_id no puede ser nulo")
        private Integer catProductGroupId;

        @JsonProperty("cat_unidad_medida_peso_id")
        @NotNull(message = "El campo cat_unidad_medida_peso_id no puede ser nulo")
        private Integer catWeightMeasureUnitId;

        @JsonProperty("cat_envase_id")
        @NotNull(message = "El campo cat_envase_id no puede ser nulo")
        private Integer catPackagingId;

        @JsonProperty("activo")
        @NotNull(message = "El campo activo no puede ser nulo")
        private Boolean active;

        @JsonProperty("usuario_modificacion_id")
        @NotNull(message = "El campo usuario_modificacion_id no puede ser nulo")
        private Integer modificationUserId;

        @JsonProperty("coparn_commodity1")
        @Size(max = 100, message = "El campo coparn_commodity1 no debe exceder 100 caracteres")
        private String bookingEdiCommodity1;

        @JsonProperty("coparn_commodity2")
        @Size(max = 100, message = "El campo coparn_commodity2 no debe exceder 100 caracteres")
        private String bookingEdiCommodity2;

        @JsonProperty("cat_tipo_refrigeracion_id")
        private Integer catRefrigerationTypeId;

        @JsonProperty("idioma_id")
        @NotNull(message = "El campo idioma_id no puede ser nulo")
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
