package com.maersk.sd1.sds.repository;

import com.maersk.sd1.common.model.BookingBlockCancellation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface BookingBlockCancellationGetRepository extends JpaRepository<BookingBlockCancellation, Integer> {

    @Query(value = """
        SELECT DISTINCT 
            CBB.cat_tipo,
            BKX.numero_booking + ' -> MN: ' + NNAVE.NAVE + ' / ' + prna.VIAJE + ' (' + operax.descripcion + ')' AS numero_booking,
            CBB.cat_motivo,
            CBB.comentario,
            [ges].[fn_HoraLocal](CBB.unidad_negocio_id, CBB.fecha_cancel_bloqueo) AS fecha_cancel_bloqueo,
            BKX.numero_booking
        FROM sds.cancelacion_bloqueo_booking AS CBB (NOLOCK)
        INNER JOIN sds.cancelacion_bloqueo_booking_detalle AS CBBD (NOLOCK) 
            ON CBB.cancel_bloqueo_booking_id = CBBD.cancel_bloqueo_booking_id
        INNER JOIN sds.documento_carga AS doccar (NOLOCK) 
            ON CBBD.documento_carga_id = doccar.documento_carga_id
        INNER JOIN sds.documento_carga_detalle AS doccarD (NOLOCK) 
            ON doccar.documento_carga_id = doccarD.documento_carga_id
        INNER JOIN sds.booking_detalle AS bkd (NOLOCK) 
            ON doccarD.booking_detalle_id = bkd.booking_detalle_id
        INNER JOIN sds.booking AS BKX (NOLOCK) 
            ON bkd.booking_id = BKX.booking_id
        INNER JOIN sds.programacion_nave_detalle AS prnade (NOLOCK) 
            ON BKX.programacion_nave_detalle_id = prnade.programacion_nave_detalle_id
        INNER JOIN sds.programacion_nave AS prna (NOLOCK) 
            ON prnade.programacion_nave_id = prna.programacion_nave_id
        INNER JOIN sds.nave AS NNAVE 
            ON prna.nave_id = NNAVE.nave_id
        INNER JOIN ges.catalogo AS operax (NOLOCK) 
            ON prnade.cat_operacion_id = operax.catalogo_id
        WHERE CBB.cancel_bloqueo_booking_id = :cancelBloqueoBookingId
    """, nativeQuery = true)
    List<Object[]> findBookingBlockCancellationById(
            @Param("cancelBloqueoBookingId") Integer cancelBloqueoBookingId);

}

