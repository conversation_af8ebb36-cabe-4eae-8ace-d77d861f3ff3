package com.maersk.sd1.sde.dto;

import java.time.LocalDateTime;

public interface DocumentationListEmptyTbListProjection {
    Integer getEmptyDocumentId();
    Integer getDocumentTypeId();
    String getDocumentTypeAlias();
    String getDocumentType();
    String getDocumentNumber();
    Integer getMovementTypeId();
    String getMovementType();
    String getShippingLine();
    String getCommodity();
    String getProduct();
    Integer getOperationTypeId();
    String getOperationType();
    String getConsignee();
    String getShipper();
    String getVessel();
    String getVoyage();
    Integer getUserRegistrationId();
    Integer getUserModificationId();
    LocalDateTime getUserRegistrationDate();
    LocalDateTime getUserModificationDate();
    String getUserRegistrationName();
    String getUserRegistrationLastName();
    String getUserModificationName();
    String getUserModificationLastName();
    String getCreationSource();
    Integer getQuantityRequested();
    Integer getQuantityAssigned();
    Integer getQuantityPending();
    String getDetail();
    Integer getStatusId();
    String getStatus();
    Integer getCatEstado();
    String getStatusAlias();
    String getMoveType();
    String getRemarkRule();
}
