package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class GeneralStockInventoryInput {

    @Data
    public static class Input {
        @JsonProperty("pf_usuario_id")
        private Integer userId;

        @JsonProperty("pf_sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("pf_idioma_id")
        private Integer languageId = 1;

        @JsonProperty("pd_equipment_number")
        private String equipmentNumber;

        @JsonProperty("pd_equipment_category")
        private Integer equipmentCategory;

        @JsonProperty("pd_empty_full_id")
        private Integer emptyFullId;

        @JsonProperty("pd_container_size_id")
        private Integer containerSizeId;

        @JsonProperty("pd_container_type_id")
        private Integer containerTypeId;

        @JsonProperty("pd_container_grade_id")
        private Integer containerGradeId;

        @JsonProperty("pd_container_shipping_line_id")
        private Integer containerShippingLineId;

        @JsonProperty("pd_chassis_owner_id")
        private Integer chassisOwnerId;

        @JsonProperty("pd_chassis_type_id")
        private Integer chassisTypeId;

        @JsonProperty("pd_shipper_name")
        private Integer shipperName;

        @JsonProperty("pd_non_maersk_flag")
        private String nonMaerskFlag;

        @JsonProperty("pagina")
        private Integer page;

        @JsonProperty("cantidad")
        private Integer recordCount;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("REP")
        private Prefix sdg;

        public Input getInput() {
            return sdg != null ? sdg.getInput() : null;
        }
    }
}
