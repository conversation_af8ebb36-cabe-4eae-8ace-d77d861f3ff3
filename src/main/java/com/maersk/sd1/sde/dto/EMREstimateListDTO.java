package com.maersk.sd1.sde.dto;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EMREstimateListDTO {

    private Integer estimateEmrId;
    private Integer catEstimateTypeId;
    private String containerNumber;
    private String catcntDesc;
    private String catsizDesc;
    private String shippingLineCompany;
    private Integer eirId;
    private String catmodDesc;
    private Integer catEstimateStatusId;
    private String currencyAbbreviation;
    private BigDecimal estimateTotalHours;
    private BigDecimal estimateTotalCost;
    private LocalDateTime estimateDateInspection;
    private LocalDateTime approveRejectEstimateDate;
    private Integer catRejectCodeEstimateId;
    private String rejectionObsEstimate;
    private LocalDateTime completedEstimateDate;
    private LocalDateTime gateInEirTruckArrivalDate;
    private LocalDateTime gateOutEirtruckArrivalDate;
    private String inspectorName;
    private Integer estimatorUserId;
    private String estimatorName;
    private String estimatorLastName;
    private Integer approveRejectUserId;
    private String approveName;
    private String approveLastName;
    private Integer completedUserId;
    private String completedName;
    private String completedLastName;
    private String businessUnit;
    private Integer estimateEMRShippingLineId;
    private Integer containerShippingLineId;
    private Boolean withoutGateinEstimate;
    private Short blockSendingCtrl;
    private Boolean blockSending;
    private LocalDateTime registrationDate;
    private Integer registrationUserId;
    private String registrationUserName;
    private String registrationUserLastName;
    private LocalDateTime modificationDate;
    private Integer modificationUserId;
    private String modificationUserName;
    private String modificationUserLastName;
    private Integer catApprovalSendEstimateStatusId;
    private Integer catCreationOriginEstimateId;
    private LocalDateTime estimateSubmissionDate;
    private Integer catApprovalRepBoxId;
    private Integer catApprovalRepMachineId;
    private Integer submissionUserId;
    private String submissionUserName;
    private String submissionUserLastName;
    private Boolean flagAutoApproval;
    private Integer mercPlusEstimateNumber;
    private Integer catCleaningStatusId;
    private LocalDateTime registrationCancelEstimateDate;
    private Integer catReasonCancelEstimateId;
    private String reasonCancelEstimateDescription;
    private Integer cancelUserId;
    private String cancelUserName;
    private String cancelUserLastName;

}