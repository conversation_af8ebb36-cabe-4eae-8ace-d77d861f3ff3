package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.ChassisDocumentRepository;
import com.maersk.sd1.common.service.CatalogService;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sdg.controller.dto.GateoutChassisSearchInput;
import com.maersk.sd1.sdg.controller.dto.GateoutChassisSearchOutput;
import com.maersk.sd1.sdg.dto.ChassisSearchResponse;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
public class GateoutSearchChassisService {

    private static final Logger logger = LogManager.getLogger(GateoutSearchChassisService.class);

    private final CatalogRepository catalogRepository;

    private final ChassisDocumentRepository chassisDocumentRepository;

    private final MessageLanguageService messageLanguageService;

    private final CatalogService catalogService;

    public GateoutChassisSearchOutput searchChassisGateout(GateoutChassisSearchInput.Root inputRoot) {
        int languageId = inputRoot.getSdg().getInput().getLanguageId();
        int subBusinessUnitId = inputRoot.getSdg().getInput().getSubBusinessUnitId();
        String documentChassisNumber = inputRoot.getSdg().getInput().getDocumentChassisNumber();
        int isGateOut = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);

        try {
            PageRequest pageRequest = PageRequest.of(0, 1);
            List<ChassisSearchResponse> chassisRecords = chassisDocumentRepository.findChassisDataRaw(
                    subBusinessUnitId, documentChassisNumber, isGateOut, pageRequest);

            if (!CollectionUtils.isEmpty(chassisRecords)) {
                ChassisSearchResponse chassisData = chassisRecords.getFirst();
                String referenceDesc = catalogService.getCatalogTranslationLongDesc(chassisData.getCatReferenceTypeId(), languageId);
                referenceDesc = referenceDesc + " : " + chassisData.getDocumentChassisNumber();

                return buildSuccessOutput(chassisData, referenceDesc);
            } else {
                return buildErrorOutput(documentChassisNumber, languageId);
            }

        } catch (Exception ex) {
            logger.error("Error occurred in searchCompanies method {}", ex.getMessage());
            return GateoutChassisSearchOutput.builder()
                    .statusCode(0)
                    .message("Internal Server Error")
                    .chassisData(Collections.emptyList())
                    .build();
        }
    }

    private GateoutChassisSearchOutput buildSuccessOutput(ChassisSearchResponse chassisData, String referenceDesc) {
        List<Object[]> chassisDetails = new ArrayList<>();
        chassisDetails.add(new Object[]{1, ""});

        List<Object[]> foundChassisData = new ArrayList<>();
        foundChassisData.add(new Object[]{
                chassisData.getDocumentChassisNumber(),
                referenceDesc,
                chassisData.getDocumentChassisId(),
                chassisData.getCatChassisTypeId(),
                chassisData.getChassisTypeDescription(),
                chassisData.getOperationTypeDescription()
        });

        List<List<Object[]>> chassisDataList = new ArrayList<>();
        chassisDataList.add(chassisDetails);
        chassisDataList.add(foundChassisData);

        return GateoutChassisSearchOutput.builder()
                .statusCode(1)
                .message(null)
                .chassisData(chassisDataList)
                .build();
    }



    private GateoutChassisSearchOutput buildErrorOutput(String documentChassisNumber, int languageId) {
        String untranslatedMessage = messageLanguageService.getMessage("GATE_OUT_CHASSIS", 1, languageId);
        String message = untranslatedMessage.replace("{CHAX}", documentChassisNumber);

        List<Object[]> errorDetails = new ArrayList<>();
        errorDetails.add(new Object[]{0, message});


        return GateoutChassisSearchOutput.builder()
                .statusCode(0)
                .message(message)
                .chassisData(List.of(errorDetails, Collections.emptyList()))
                .build();
    }
}

