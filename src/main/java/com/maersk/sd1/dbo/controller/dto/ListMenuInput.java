package com.maersk.sd1.dbo.controller.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ListMenuInput {

    @Data
    public static class Input {

        @JsonProperty("Origen")
        @NotNull(message = "El campo origen no puede ser nulo.")
        private String origin;

        @JsonProperty("Usuario")
        @NotNull(message = "El campo usuario no puede ser nulo.")
        private String userId;

        @JsonProperty("Empresa")
        @NotNull(message = "El campo empresa no puede ser nulo.")
        private String businessUnitId;

        @JsonProperty("Sistema")
        @NotNull(message = "El campo sistema no puede ser nulo.")
        private String systemId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEC")
        private Prefix prefix;
    }

    private ListMenuInput() {
        // Hide implicit public constructor
    }
}