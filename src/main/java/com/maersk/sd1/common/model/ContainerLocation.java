package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.Block;
import com.maersk.sd1.common.model.Cell;
import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "ubicacion_contenedor", schema = "sdy", indexes = {
        @Index(name = "nci_wi_ubicacion_contenedor_57B26F32B7ECD8FD1DAF06C0610CB7F6", columnList = "activo, nivel_id, celda_id"),
        @Index(name = "nci_msft_ubicacion_contenedor_443D2A6448BB0A5E6E67403AD63638CF", columnList = "contenedor_id, activo, bloque_id")
}, uniqueConstraints = {
        @UniqueConstraint(name = "SDY_UC_Posicion_Unica", columnNames = {"bloque_id", "celda_id", "nivel_id"})
})
public class ContainerLocation {
    @Id
    @Column(name = "ubicacion_contenedor_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "bloque_id", nullable = false)
    private Block block;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "celda_id", nullable = false)
    private Cell cell;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "nivel_id", nullable = false)
    private Level level;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contenedor_id")
    private Container container;

    @Column(name = "visit_id")
    private Integer visitId;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @NotNull
    @ColumnDefault("0")
    @Column(name = "cantidad_removidos", nullable = false)
    private Integer quantityRemoved;

}