package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.repository.CompanyRoleRepository;
import com.maersk.sd1.ges.dto.CompanySearchInput;
import com.maersk.sd1.ges.dto.CompanySearchOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class CompanySearchService {

    private static final Logger logger = LogManager.getLogger(CompanySearchService.class);

    private final CompanyRoleRepository companyRoleRepository;

    // Constructor injection
    public CompanySearchService(CompanyRoleRepository companyRoleRepository) {
        this.companyRoleRepository = companyRoleRepository;
    }

    @Transactional(readOnly = true)
    public CompanySearchOutput searchCompanies(CompanySearchInput.Input input) {
        CompanySearchOutput output = new CompanySearchOutput();
        try {
            // Prepare query parameters
            Integer businessUnitId = input.getBusinessUnitId();
            Boolean flagNull = input.getFlagNull();
            List<String> roles = input.getCompanyRoles();
            String companyName = input.getCompanyName();

            // Perform the paginated query (TOP 10 in the SP)
            PageRequest pageRequest = PageRequest.of(0, 10);
            Page<CompanySearchOutput.CompanySearchData> pageResult =
                    companyRoleRepository.findCompanies(businessUnitId, flagNull, roles, companyName, pageRequest);

            output.setRespEstado(1);
            output.setRespMensaje("Success");
            output.setCompanies(pageResult.getContent());
        } catch (Exception e) {
            logger.error("Error occurred in searchCompanies method", e);
            output.setRespEstado(0);
            output.setRespMensaje("Error: " + e.getMessage());
        }
        return output;
    }
}

