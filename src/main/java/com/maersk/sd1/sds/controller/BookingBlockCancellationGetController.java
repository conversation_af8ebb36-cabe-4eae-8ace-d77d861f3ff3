package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.BookingBlockCancellationGetInput;
import com.maersk.sd1.sds.dto.BookingBlockCancellationGetOutput;
import com.maersk.sd1.sds.service.BookingBlockCancellationGetService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSCancelacionBloqueoBookingServiceImp")
public class BookingBlockCancellationGetController {

    private static final Logger logger = LogManager.getLogger(BookingBlockCancellationGetController.class);

    private final BookingBlockCancellationGetService bookingBlockCancellationGetService;

    @Autowired
    public BookingBlockCancellationGetController(BookingBlockCancellationGetService bookingBlockCancellationGetService) {
        this.bookingBlockCancellationGetService = bookingBlockCancellationGetService;
    }

    @PostMapping("/sdebookingBlockCancelationGet")
    public ResponseEntity<ResponseController<List<BookingBlockCancellationGetOutput>>> getBookingBlockCancellation(@RequestBody @Valid BookingBlockCancellationGetInput.Root request) {
        try {
            Integer cancelBloqueoBookingId = request.getPrefix().getInput().getCancelBloqueoBookingId();
            List<BookingBlockCancellationGetOutput> result = bookingBlockCancellationGetService.getBookingBlockCancellation(cancelBloqueoBookingId);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while retrieving booking block cancellation data.", e);
            return ResponseEntity.status(500).body(new ResponseController<>(e.getMessage()));
        }
    }
}

