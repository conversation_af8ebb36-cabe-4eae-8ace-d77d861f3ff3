package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;

import com.maersk.sd1.sde.dto.GateOutEmptyExportReportInput;
import com.maersk.sd1.sde.dto.GateOutEmptyExportReportOutput;
import com.maersk.sd1.sde.service.GateOutEmptyExportReportService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/inlandnet/Inlandnet/gestion/ReporteConsultaServiceImp")
public class GateOutEmptyExportReportController {

    private static final Logger logger = LogManager.getLogger(GateOutEmptyExportReportController.class.getName());

    private final GateOutEmptyExportReportService gateOutEmptyExportReportService;

    @PostMapping("/consultarReporte")
    public ResponseEntity<ResponseController<GateOutEmptyExportReportOutput>> getGateOutEmptyExportReport(@RequestBody @Valid GateOutEmptyExportReportInput.Root request) {
        try {
            logger.info("Request received for GateOutEmptyExportReport: {}", request);
            GateOutEmptyExportReportInput.Input input = request.getPrefix().getInput();

            GateOutEmptyExportReportOutput output = gateOutEmptyExportReportService.getGateOutEmptyExportReport(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            GateOutEmptyExportReportOutput errorOutput = new GateOutEmptyExportReportOutput();
            errorOutput.setRows(null);
            errorOutput.setTotalRecords(0L);
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}

