package com.maersk.sd1.sde.controller;


import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.BuscarCedexMercXCodigoObservableInput;
import com.maersk.sd1.sde.dto.BuscarCedexMercXCodigoObservableOutput;
import com.maersk.sd1.sde.service.BuscarCedexMercXCodigoObservableService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controller that exposes the endpoint for replicating stored procedure buscar_cedex_merc_xcodigo_observable.
 * Follows the sample approach.
 */
@RestController
@RequestMapping("ModuleSDE/module/sde/SDEEMRServiceImp")
public class BuscarCedexMercXCodigoObservableController {

    private static final Logger logger = LogManager.getLogger(BuscarCedexMercXCodigoObservableController.class.getName());

    private final BuscarCedexMercXCodigoObservableService buscarCedexMercXCodigoObservableService;

    @Autowired
    public BuscarCedexMercXCodigoObservableController(BuscarCedexMercXCodigoObservableService buscarCedexMercXCodigoObservableService) {
        this.buscarCedexMercXCodigoObservableService = buscarCedexMercXCodigoObservableService;
    }

    /**
     * POST endpoint that receives the input wrapped under root/prefix structure.
     * Returns a standardized response with BuscarCedexMercOutput.
     */
    @PostMapping("/sdebuscarCedexMercXcodigoObservable")
    public ResponseEntity<ResponseController<List<BuscarCedexMercXCodigoObservableOutput>>> buscarCedexMerc(@RequestBody @Valid BuscarCedexMercXCodigoObservableInput.Root request) {
        try {
            BuscarCedexMercXCodigoObservableInput.Input input = request.getPrefix().getInput();
            List<BuscarCedexMercXCodigoObservableOutput> output = buscarCedexMercXCodigoObservableService.buscarCedexMerc(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            BuscarCedexMercXCodigoObservableOutput output = new BuscarCedexMercXCodigoObservableOutput();
            return ResponseEntity.status(500).body(new ResponseController<>(List.of(output)));
        }
    }
}

