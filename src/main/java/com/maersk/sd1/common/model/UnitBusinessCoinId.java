package com.maersk.sd1.common.model;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.Hibernate;

import java.io.Serializable;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Embeddable
public class UnitBusinessCoinId implements Serializable {
    private static final long serialVersionUID = 5187710886119246648L;
    @NotNull
    @Column(name = "unidad_negocio_id", nullable = false)
    private Integer unitBusinessId;

    @NotNull
    @Column(name = "moneda_id", nullable = false)
    private Integer coinId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        UnitBusinessCoinId entity = (UnitBusinessCoinId) o;
        return Objects.equals(this.coinId, entity.coinId) &&
                Objects.equals(this.unitBusinessId, entity.unitBusinessId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(coinId, unitBusinessId);
    }

}