package com.maersk.sd1.sde.service;

import com.maersk.sd1.sde.controller.dto.InspectionRegisterV2Input;
import com.maersk.sd1.sde.controller.dto.InspectionRegisterV2Output;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.SqlOutParameter;
import org.springframework.jdbc.core.SqlParameter;
import org.springframework.jdbc.core.simple.SimpleJdbcCall;
import org.springframework.stereotype.Service;

import java.sql.Types;
import java.util.HashMap;
import java.util.Map;

@Service
public class InspectionRegisterV2Service {

    private final Logger logger = LogManager.getLogger(InspectionRegisterV2Service.class);

    private final JdbcTemplate jdbcTemplate;

    public InspectionRegisterV2Service(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    public InspectionRegisterV2Output registerInspectionV2(InspectionRegisterV2Input.Root request) {

        logger.info("Starting registerInspectionV2 with request: {}", request);
        SimpleJdbcCall jdbcCall = new SimpleJdbcCall(jdbcTemplate)
                .withProcedureName("registrar_inspeccion_json_v2")
                .withSchemaName("sde")
                .declareParameters(
                        new SqlParameter("unidad_negocio_id", Types.NUMERIC),
                        new SqlParameter("sub_unidad_negocio_id", Types.NUMERIC),
                        new SqlParameter("sub_unidad_negocio_local_id", Types.NUMERIC),
                        new SqlParameter("eir_id", Types.INTEGER),
                        new SqlParameter("IDActividadZona", Types.INTEGER),
                        new SqlParameter("Contenedor", Types.VARCHAR),
                        new SqlParameter("TnoCnt_id", Types.NUMERIC),
                        new SqlParameter("TipoCnt_id", Types.NUMERIC),
                        new SqlParameter("ClaseCnt_id", Types.NUMERIC),
                        new SqlParameter("Tara", Types.INTEGER),
                        new SqlParameter("CargaMaxima", Types.INTEGER),
                        new SqlParameter("CodigoISO_id", Types.INTEGER),
                        new SqlParameter("CodigoLinea_id", Types.INTEGER),
                        new SqlParameter("FechaFabricacion", Types.VARCHAR),
                        new SqlParameter("TipoReefer_id", Types.NUMERIC),
                        new SqlParameter("MarcaMotor_id", Types.NUMERIC),
                        new SqlParameter("ConDano", Types.BIT),
                        new SqlParameter("SiguienteZona", Types.VARCHAR),
                        new SqlParameter("FlagInspCompleja", Types.BIT),
                        new SqlParameter("FlagBarrer", Types.BIT),
                        new SqlParameter("FlagLavadoEspecial", Types.BIT),
                        new SqlParameter("cat_complejidad_dano_id", Types.NUMERIC),
                        new SqlParameter("bloquear_contenedor", Types.BIT),
                        new SqlParameter("precinto_bloqueo", Types.VARCHAR),
                        new SqlParameter("cat_type_setting_material_id", Types.NUMERIC),
                        new SqlParameter("cat_color_id", Types.NUMERIC),
                        new SqlParameter("ObservacionesInspector", Types.NVARCHAR),
                        new SqlParameter("ObservacionInspOtros", Types.NVARCHAR),
                        new SqlParameter("FlagSensor", Types.VARCHAR),
                        new SqlParameter("FlagRCD", Types.BIT),
                        new SqlParameter("FlagPendRepuesto", Types.BIT),
                        new SqlParameter("DetalleEstimado", Types.NVARCHAR),
                        new SqlParameter("DeletedDamagesIds", Types.NVARCHAR),
                        new SqlParameter("Fotos", Types.NVARCHAR),
                        new SqlParameter("es_inspeccion_parcial", Types.BIT),
                        new SqlParameter("usuario_id", Types.INTEGER),
                        new SqlParameter("edit", Types.BIT),
                        new SqlParameter("inspection_comments", Types.VARCHAR),
                        new SqlParameter("flag_potencial_food_aid", Types.BIT),
                        new SqlParameter("observation_comments", Types.VARCHAR),
                        new SqlParameter("update_material_request_emr_detail", Types.NVARCHAR),
                        new SqlParameter("imo_registers", Types.NVARCHAR),
                        new SqlParameter("flag_cleaning_section_interior", Types.BIT),
                        new SqlParameter("flag_cleaning_section_bottom", Types.BIT),
                        new SqlParameter("flag_cleaning_section_right", Types.BIT),
                        new SqlParameter("flag_cleaning_section_left", Types.BIT),
                        new SqlParameter("flag_cleaning_section_top", Types.BIT),
                        new SqlParameter("cat_cleaning_type_id", Types.NUMERIC),

                        new SqlOutParameter("resp_new_id", Types.INTEGER),
                        new SqlOutParameter("resp_estado", Types.INTEGER),
                        new SqlOutParameter("resp_mensaje", Types.NVARCHAR),
                        new SqlOutParameter("SiguienteZonaGuardada", Types.VARCHAR),
                        new SqlOutParameter("type_product_integration", Types.VARCHAR),
                        new SqlOutParameter("yard_id", Types.INTEGER),
                        new SqlOutParameter("yard_code", Types.VARCHAR),
                        new SqlOutParameter("container_id", Types.INTEGER),
                        new SqlOutParameter("yard_location", Types.VARCHAR)
                );

        InspectionRegisterV2Input.Input input = request.getPrefix().getInput();

        logger.info("Input parameters: {}", input);


        Map<String, Object> inParams = new HashMap<>();
        inParams.put("unidad_negocio_id", input.getUnidadNegocioId());
        inParams.put("sub_unidad_negocio_id", input.getSubUnidadNegocioId());
        inParams.put("sub_unidad_negocio_local_id", input.getSubUnidadNegocioLocalId());
        inParams.put("eir_id", input.getEirId());
        inParams.put("IDActividadZona", input.getIdActividadZona());
        inParams.put("Contenedor", input.getContenedor());
        inParams.put("TnoCnt_id", input.getTnoCntId());
        inParams.put("TipoCnt_id", input.getTipoCntId());
        inParams.put("ClaseCnt_id", input.getClaseCntId());
        inParams.put("Tara", input.getTara());
        inParams.put("CargaMaxima", input.getCargaMaxima());
        inParams.put("CodigoISO_id", input.getCodigoISOId());
        inParams.put("CodigoLinea_id", input.getCodigoLineaId());
        inParams.put("FechaFabricacion", input.getFechaFabricacion());
        inParams.put("TipoReefer_id", input.getTipoReeferId());
        inParams.put("MarcaMotor_id", input.getMarcaMotorId());
        inParams.put("ConDano", input.getConDano());
        inParams.put("SiguienteZona", input.getSiguienteZona());
        inParams.put("FlagInspCompleja", input.getFlagInspCompleja());
        inParams.put("FlagBarrer", input.getFlagBarrer());
        inParams.put("FlagLavadoEspecial", input.getFlagLavadoEspecial());
        inParams.put("cat_complejidad_dano_id", input.getCatComplejidadDanoId());
        inParams.put("bloquear_contenedor", input.getBloquearContenedor());
        inParams.put("precinto_bloqueo", input.getPrecintoBloqueo());
        inParams.put("cat_type_setting_material_id", input.getCatTypeSettingMaterialId());
        inParams.put("cat_color_id", input.getCatColorId());
        inParams.put("ObservacionesInspector", input.getObservacionesInspector());
        inParams.put("ObservacionInspOtros", input.getObservacionInspOtros());
        inParams.put("FlagSensor", input.getFlagSensor());
        inParams.put("FlagRCD", input.getFlagRCD());
        inParams.put("FlagPendRepuesto", input.getFlagPendRepuesto());
        inParams.put("DetalleEstimado", input.getDetalleEstimado());
        inParams.put("DeletedDamagesIds", input.getDeletedDamagesIds());
        inParams.put("Fotos", input.getFotos());
        inParams.put("es_inspeccion_parcial", input.getEsInspeccionParcial());
        inParams.put("usuario_id", input.getUsuarioId());
        inParams.put("edit", input.getEdit());
        inParams.put("inspection_comments", input.getInspectionComments());
        inParams.put("flag_potencial_food_aid", input.getFlagPotencialFoodAid());
        inParams.put("observation_comments", input.getObservationComments());
        inParams.put("update_material_request_emr_detail", input.getUpdateMaterialRequestEmrDetail());
        inParams.put("imo_registers", input.getImoRegisters());
        inParams.put("flag_cleaning_section_interior", input.getFlagCleaningSectionInterior());
        inParams.put("flag_cleaning_section_bottom", input.getFlagCleaningSectionBottom());
        inParams.put("flag_cleaning_section_right", input.getFlagCleaningSectionRight());
        inParams.put("flag_cleaning_section_left", input.getFlagCleaningSectionLeft());
        inParams.put("flag_cleaning_section_top", input.getFlagCleaningSectionTop());
        inParams.put("cat_cleaning_type_id", input.getCatCleaningTypeId());

        Map<String, Object> outParams = jdbcCall.execute(inParams);

        InspectionRegisterV2Output response = new InspectionRegisterV2Output();
        response.setRespNewId((Integer) outParams.get("resp_new_id"));
        response.setRespEstado((Integer) outParams.get("resp_estado"));
        response.setRespMensaje((String) outParams.get("resp_mensaje"));
        response.setSiguienteZonaGuardada((String) outParams.get("SiguienteZonaGuardada"));
        response.setTypeProductIntegration((String) outParams.get("type_product_integration"));
        response.setYardId((Integer) outParams.get("yard_id"));
        response.setYardCode((String) outParams.get("yard_code"));
        response.setContainerId((Integer) outParams.get("container_id"));
        response.setYardLocation((String) outParams.get("yard_location"));

        logger.info("Response from stored procedure: {}", response);

        return response;
    }
}
