package com.maersk.sd1.sds.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class DepotEditInput {

    @Data
    public static class Input {

        @JsonProperty("deposito_id")
        @NotNull
        private Integer depositId;

        @JsonProperty("unidad_negocio_id")
        @NotNull
        private Integer businessUnitId;

        @JsonProperty("codigo_deposito")
        @NotNull
        @Size(max = 10)
        private String codeDeposit;

        @JsonProperty("nombre_deposito")
        @NotNull
        @Size(max = 100)
        private String nameDeposit;

        @JsonProperty("direccion_deposito")
        @Size(max = 250)
        private String addressDeposit;

        @JsonProperty("activo")
        @NotNull
        private Boolean active;

        @JsonProperty("usuario_modificacion_id")
        private Integer userModificationId;

        @JsonProperty("sub_unidad_negocio_id")
        private Integer subBusinessUnitId;

        @JsonProperty("deposito_default")
        @NotNull
        private Boolean depositDefault;

        @JsonProperty("cat_codigo_aduana_id")
        private Integer catCodigoAduanaId;

        @JsonProperty("cat_clase_operador_aduana_id")
        private Integer catClaseOperadorAduanaId;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDS")
        private Prefix prefix;
    }
}
