package com.maersk.sd1.sds.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sds.dto.VesselProgrammingDetailExportInput;
import com.maersk.sd1.sds.dto.VesselProgrammingDetailExportOutput;
import com.maersk.sd1.sds.service.VesselProgrammingDetailExportService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSProgramacionNaveDetalleServiceImp")
public class VesselProgrammingDetailExportController {

    private static final Logger logger = LogManager.getLogger(VesselProgrammingDetailExportController.class);

    private final VesselProgrammingDetailExportService exportService;

    @Autowired
    public VesselProgrammingDetailExportController(VesselProgrammingDetailExportService exportService) {
        this.exportService = exportService;
    }

    @PostMapping("/sdsprogramacionNaveDetalleExportacion")
    public ResponseEntity<ResponseController<VesselProgrammingDetailExportOutput>> getExportedVesselProgrammingDetail(
            @RequestBody @Valid VesselProgrammingDetailExportInput.Root request) {
        try {
            Integer unidadNegocioId = request.getPrefix().getInput().getUnidadNegocioId();
            VesselProgrammingDetailExportOutput output = exportService.exportDetails(unidadNegocioId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing getExportedVesselProgrammingDetail.", e);
            VesselProgrammingDetailExportOutput response = new VesselProgrammingDetailExportOutput();
            response.setTotalRegistros(0L);
            response.setDetalles(null);
            return ResponseEntity.status(500).body(new ResponseController<>(response));
        }
    }
}

