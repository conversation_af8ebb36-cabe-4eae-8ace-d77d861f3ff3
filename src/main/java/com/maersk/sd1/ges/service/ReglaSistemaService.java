package com.maersk.sd1.ges.service;

import com.maersk.sd1.common.repository.SystemRuleRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ReglaSistemaService {


    private final SystemRuleRepository reglaSistemaRepository;

    @Autowired
    public ReglaSistemaService(SystemRuleRepository reglaSistemaRepository) {
        this.reglaSistemaRepository = reglaSistemaRepository;
    }

}
