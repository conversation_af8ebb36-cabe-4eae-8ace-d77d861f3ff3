package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.controller.dto.InspectionRegisterInput;
import com.maersk.sd1.sde.controller.dto.InspectionRegisterOutput;
import com.maersk.sd1.sde.service.InspectionRegisterService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDE/module/sde/SDEInspeccionServiceImp")
public class InspectionRegisterController {

    private final InspectionRegisterService inspectionRegisterService;

    @PostMapping("/sderegistrarInspeccionJson")
    public ResponseEntity<ResponseController<InspectionRegisterOutput>> registerInspection(
            @RequestBody InspectionRegisterInput.Root request) {
        if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
            return ResponseEntity.badRequest().body(new ResponseController<>("Invalid input payload structure"));
        }
        try {
            InspectionRegisterOutput response = inspectionRegisterService.registerInspection(request);
            return ResponseEntity.ok(new ResponseController<>(response));
        } catch (Exception ex){
            return ResponseEntity.internalServerError().body(new ResponseController<>(ex.getMessage()));
        }
    }
}