package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.CatalogLanguageRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.ContainerRepository;
import com.maersk.sd1.common.repository.EirRepository;
import com.maersk.sd1.common.service.BusinessUnitConfigService;
import com.maersk.sd1.sde.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Log4j2
public class EquipmentListService {

    private final CatalogRepository catalogRepository;
    private final ContainerRepository containerRepository;
    private final EirRepository eirRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;
    private final BusinessUnitConfigService businessUnitConfigService;

    @Transactional(readOnly = true)
    public EquipmentListFinalOutput getEquipmentList(EquipmentListInput.Input input) {
        log.info("Request received for equipment list: {}", input);

        Integer isFull = catalogRepository.findIdByAlias("43084");
        Integer isEmpty = catalogRepository.findIdByAlias("43083");
        Integer isChassis = catalogRepository.findIdByAlias("sd1_equipment_category_chassis");
        Integer isContainer = catalogRepository.findIdByAlias("sd1_equipment_category_container");
        Integer isDocumentTypeBk = catalogRepository.findIdByAlias("cat_48734_bk");
        Integer isDocumentTypeBl = catalogRepository.findIdByAlias("cat_48734_bl");
        Integer isOperationExport = catalogRepository.findIdByAlias("cat_48789_export");
        Integer isOperationImport = catalogRepository.findIdByAlias("cat_48789_import");
        Integer isOperationStorage = catalogRepository.findIdByAlias("cat_48789_storage");
        Integer isCatalogoScac = catalogRepository.findIdByAlias("ges_company_configuration_scac");

        String equipmentNotApplicable = "NOT APPLICA";
        Integer equipmentNotApplicableId = containerRepository.findEquipmentNotApplicableId(equipmentNotApplicable);


        Integer equipmentCategory = input.getEquipmentCategory();

        List<EquipmentListTempInterface> tempEquipTable = new ArrayList<>();

        if (Objects.equals(equipmentCategory, isContainer) || equipmentCategory == null) {
            tempEquipTable = eirRepository.fetchGateInDetails(input.getSubBusinessUnitId(), input.getEirNumber(), equipmentNotApplicableId,
                    input.getEquipmentNumber(), input.getShippingLineChassisOwner(), input.getTruckInDateMin(), input.getTruckInDateMax(),
                    input.getLanguageId(), isContainer, isEmpty,input.getActive());
        }
        List<EquipmentListTempDTO> tempEquipTableDTO = mapToEquipmentListTempDTO(tempEquipTable);

        for (EquipmentListTempDTO temp : tempEquipTableDTO) {
            List<Object[]> cargotemp = eirRepository.findCargoDocumentByEirIdAndReferenceDocumentNumber(temp.getEirId(), input.getReferenceDocumentNumber());
            if (!cargotemp.isEmpty()) {
                for (Object[] cargo : cargotemp) {
                    if (cargo[0] != null) {
                        temp.setCatCargoDocumentTypeId(Long.parseLong(cargo[0].toString()));
                    }
                    if (cargo[1] != null) {
                        temp.setCargoDocumentNumber(cargo[1].toString());
                    }
                    if (cargo[2] != null) {
                        temp.setOperationTypeId(Long.parseLong(cargo[2].toString()));
                    }
                    temp.setIsShow(1);
                }
            }
        }
        tempEquipTableDTO.removeIf(temp -> temp.getIsShow() == 0);

        for (EquipmentListTempDTO temp : tempEquipTableDTO) {
            if (temp.getCatCargoDocumentTypeId() == null) {
                String operationGroupType = temp.getOperationGroupType();
                if ("I".equalsIgnoreCase(operationGroupType)) {
                    temp.setCatCargoDocumentTypeId(Long.valueOf(isDocumentTypeBl));
                } else if ("E".equalsIgnoreCase(operationGroupType)) {
                    temp.setCatCargoDocumentTypeId(Long.valueOf(isDocumentTypeBk));
                }
            }
        }

        for (EquipmentListTempDTO temp : tempEquipTableDTO) {
            if (temp.getOperationTypeId() == null) {
                String operationGroupType = temp.getOperationGroupType();

                if ("I".equalsIgnoreCase(operationGroupType)) {
                    temp.setOperationTypeId(Long.valueOf(isOperationImport));
                } else if ("E".equalsIgnoreCase(operationGroupType)) {
                    temp.setOperationTypeId(Long.valueOf(isOperationExport));
                } else {
                    temp.setOperationTypeId(Long.valueOf(isOperationStorage));
                }
            }
        }

        for (EquipmentListTempDTO temp : tempEquipTableDTO) {
            if (temp.getOperationTypeId() != null) {
                String operationTypeName = catalogRepository.findOperationTypeTranslation(temp.getOperationTypeId(), input.getLanguageId());
                temp.setOperationTypeName(operationTypeName);
            }
        }

        List<RestrictionTempDto> tempRestrictTableDTO = new ArrayList<>();
        for(EquipmentListTempDTO temp : tempEquipTableDTO) {
            List<RestrictionTempInterface> restrictionTemp = eirRepository.fetchRestrictionContainerDetails(temp.getContainerId(), temp.getCatEmptyFullId(),temp.getSubBusinessUnitId());
            if(!restrictionTemp.isEmpty()) {
                for (RestrictionTempInterface tempRestrict : restrictionTemp) {
                    RestrictionTempDto dto = new RestrictionTempDto();
                    dto.setCatEmptyFullId(tempRestrict.getCatEmptyFullId());
                    dto.setContainerId(tempRestrict.getContenedorId());
                    dto.setRestrictionRemark(tempRestrict.getAnotacionRestriccion());
                    dto.setReasons(tempRestrict.getMotivoRestriccionDescripcion());
                    tempRestrictTableDTO.add(dto);
                }
            }
        }
        List<ImoDto> tempImoTableDTO = new ArrayList<>();
        for(EquipmentListTempDTO temp : tempEquipTableDTO) {
            List<ImoInterface> imotemp = eirRepository.fetchImoValues(temp.getEirId(), temp.getContainerId(), temp.getProgramacionNaveDetalleId());
            if(!imotemp.isEmpty()) {
                for (ImoInterface tempRestrict : imotemp) {
                    ImoDto dto = new ImoDto();
                    dto.setEirId(tempRestrict.getEirId());
                    dto.setContainerId(tempRestrict.getContainerId());
                    dto.setProgramacionNaveDetalleId(tempRestrict.getProgramacionNaveDetalleId());
                    dto.setImoValue(tempRestrict.getImoValue());
                    tempImoTableDTO.add(dto);
                }
            }
        }

        if(Objects.equals(equipmentCategory, isChassis) || equipmentCategory == null){
            List<EquipmentListTempInterface> equipmentDetails = eirRepository.getEquipmentDetails(input.getSubBusinessUnitId(), input.getLanguageId(),
                    input.getEirNumber(), input.getEquipmentNumber(), input.getShippingLineChassisOwner(), input.getReferenceDocumentNumber(),
                    input.getTruckInDateMin(), input.getTruckInDateMax(), isChassis, input.getActive());
            List<EquipmentListTempDTO> equipmentListTempDTOS;
            if(!CollectionUtils.isEmpty(equipmentDetails)) {
                equipmentListTempDTOS = mapToEquipmentListTempDTO(equipmentDetails);
                tempEquipTableDTO.addAll(equipmentListTempDTOS);
            }
        }
        for(EquipmentListTempDTO temp : tempEquipTableDTO){
            String operationTypeName = eirRepository.getTransportCompanyName(temp.getTransportCompanyId(),isCatalogoScac);
            temp.setTransportCompanyName(operationTypeName);
        }

        EquipmentListFinalOutput output = new EquipmentListFinalOutput();

        List<Integer> totalRecords = new ArrayList<>();

        totalRecords.add(tempEquipTableDTO.size());


        List<List<Integer>> totalRecordsBase = new ArrayList<>();

        totalRecordsBase.add(totalRecords);

        output.setTotalRecords(totalRecordsBase);


        List<EquipmentListOutputDTO> equipOutput = mapToEquipmentListOutputDTO(tempEquipTableDTO, tempRestrictTableDTO, input, isContainer, isEmpty, isFull);
        output.setFinalResult(equipOutput);

        return output;
    }
    public List<EquipmentListTempDTO> mapToEquipmentListTempDTO(List<EquipmentListTempInterface> tempEquipTable) {
        List<EquipmentListTempDTO> tempEquipTableDTO = new ArrayList<>();
        for (EquipmentListTempInterface temp : tempEquipTable) {
            EquipmentListTempDTO dto = new EquipmentListTempDTO();
            dto.setBusinessUnitId(temp.getBusinessUnitId());
            dto.setSubBusinessUnitId(temp.getSubBusinessUnitId());
            dto.setProgramacionNaveDetalleId(temp.getProgramacionNaveDetalleId());
            dto.setContainerId(temp.getContainerId());
            dto.setChassisId(temp.getChassisId());
            dto.setCatEmptyFullId(temp.getCatEmptyFullId());
            dto.setTipoMov(temp.getTipoMov());
            dto.setLocal(temp.getLocal());
            dto.setEirId(temp.getEirId());
            dto.setFechaIngresoCamion(temp.getFechaIngresoCamion());
            dto.setFechaSalidaCamion(temp.getFechaSalidaCamion());
            dto.setEquipmentNumber(temp.getEquipmentNumber());
            dto.setEquipmentSizeType(temp.getEquipmentSizeType());
            dto.setCatEquipmentCategory(temp.getCatEquipmentCategory());
            dto.setIsoCode(temp.getIsoCode());
            dto.setOwnerPropietario(temp.getOwnerPropietario());
            dto.setPlateTruckNumber(temp.getPlateTruckNumber());
            dto.setTransportCompanyId(temp.getTransportCompanyId());
            dto.setTransportCompanyName(temp.getTransportCompanyName());
            dto.setDriverName(temp.getDriverName());
            dto.setUserModificationId(temp.getUserModificationId());
            dto.setUserModificationName(temp.getUserModificationName());
            dto.setUserModificationLastName(temp.getUserModificationLastName());
            dto.setFechaModificacion(temp.getFechaModificacion());
            dto.setUserRegistrationId(temp.getUserRegistrationId());
            dto.setUserRegistrationName(temp.getUserRegistrationName());
            dto.setUserRegistrationLastName(temp.getUserRegistrationLastName());
            dto.setFechaRegistro(temp.getFechaRegistro());
            dto.setSeals(temp.getSeals());
            dto.setCatCargoDocumentTypeId(temp.getCatCargoDocumentTypeId());
            dto.setCargoDocumentNumber(temp.getCargoDocumentNumber());
            dto.setOperationTypeId(temp.getOperationTypeId());
            dto.setOperationTypeName(temp.getOperationTypeName());
            dto.setIsShow(temp.getIsShow());
            dto.setEirChassisId(temp.getEirChassisId());
            dto.setCatStructureConditionId(temp.getCatStructureConditionId());
            dto.setCatMachineryConditionId(temp.getCatMachineryConditionId());
            dto.setOperationGroupType(temp.getOperationGroupType());
            dto.setGradeId(temp.getGradeId());
            dto.setContainerOperationType(temp.getContainerOperationType());
            dto.setVesselName(temp.getVesselName());
            dto.setVoyageName(temp.getVoyageName());
            dto.setDepotOperationType(temp.getDepotOperationType());
            dto.setEstructuaConDano(temp.getEstructuaConDano());
            dto.setMaquinariaConDano(temp.getMaquinariaConDano());
            dto.setParaVenta(temp.getParaVenta());
            dto.setCamionMultipleCarga(temp.getCamionMultipleCarga());
            dto.setUserDepartureId(temp.getUserDepartureId());
            dto.setUserDepartureName(temp.getUserDepartureName());
            dto.setUserDepartureLastName(temp.getUserDepartureLastName());
            dto.setOrigenCreacionEir(temp.getOrigenCreacionEir());
            dto.setCatMovimientoId(temp.getCatMovimientoId());
            dto.setCatOperacionDescripcion(temp.getCatOperacionDescripcion());
            dto.setTipoGate(temp.getTipoGate());
            dto.setInspectionPhotos(temp.getInspectionPhotos());
            dto.setInspectionsQuantity(temp.getInspectionsQuantity());
            dto.setGatePhotos(temp.getGatePhotos());
            dto.setActive(temp.getActive());
            dto.setUserDeleteId(temp.getUserDeleteId());
            dto.setUserDeleteName(temp.getUserDeleteName());
            dto.setUserDeleteLastName(temp.getUserDeleteLastName());
            dto.setDeleteDate(temp.getDeleteDate());
            tempEquipTableDTO.add(dto);
        }
        return tempEquipTableDTO;
    }
    private String getEquipmentRestriction(Integer catEquipmentCategory, Integer isContainer, Integer containerId, List<RestrictionTempDto> restrictions, Long isEmpty, Long isFull) {
        if (!Objects.equals(catEquipmentCategory, isContainer)) {
            return "";
        }

        String emptyRestriction = getRestrictionRemark(containerId, isEmpty, restrictions).map(r -> "[Empty] " + r.getRestrictionRemark() + ": " + r.getReasons()).orElse("");
        String fullRestriction = getRestrictionRemark(containerId, isFull, restrictions).map(r -> "[Full] " + r.getRestrictionRemark() + ": " + r.getReasons()).orElse("");

        return emptyRestriction + fullRestriction;
    }

    private Optional<RestrictionTempDto> getRestrictionRemark(Integer containerId, Long catEmptyFullId, List<RestrictionTempDto> restrictions) {
        return restrictions.stream()
                .filter(r -> r.getContainerId().equals(containerId) && r.getCatEmptyFullId().equals(catEmptyFullId))
                .findFirst();
    }
    public String getSeals(Integer catEquipmentCategory, Integer isContainer, String seals) {
        String result = "";
        if (Objects.equals(catEquipmentCategory, isContainer)) {
            result = (seals != null) ? seals : "";
        }
        return result;
    }
    public Integer getEirChassisId(Integer eirChassisId, Integer chassisId) {
        return (eirChassisId != null && chassisId != null) ? eirChassisId : null;
    }
    public List<EquipmentListOutputDTO> mapToEquipmentListOutputDTO(List<EquipmentListTempDTO> tempEquipTableDTO, List<RestrictionTempDto> tempRestrictTableDTO,
                                                                    EquipmentListInput.Input input, Integer isContainer, Integer isEmpty, Integer isFull) {
        List<EquipmentListOutputDTO> equipOutput = new ArrayList<>();

        for (EquipmentListTempDTO temp : tempEquipTableDTO) {
            EquipmentListOutputDTO dto = new EquipmentListOutputDTO();
            dto.setEirId(temp.getEirId());
            dto.setGateType(temp.getTipoGate());
            dto.setDepot(temp.getLocal());
            dto.setEquipmentNumber(temp.getEquipmentNumber());
            dto.setCatEquipmentCategory(temp.getCatEquipmentCategory());

            dto.setEquipmentCategoryDescription(
                    temp.getCatEquipmentCategory() != null
                            ? catalogLanguageRepository.fnCatalogTranslationDescLong(
                            Math.toIntExact(temp.getCatEquipmentCategory()), input.getLanguageId())
                            : ""
            );

            dto.setEquipmentSizeType(temp.getEquipmentSizeType());
            if (temp.getIsoCode() != null) {
                dto.setIsoCode(temp.getIsoCode());
            }

            if(temp.getGradeId() != null){
                dto.setEquipmentGrade(catalogLanguageRepository.fnCatalogTranslationDesc(Math.toIntExact(temp.getGradeId()), input.getLanguageId()));

            }


            if (temp.getOwnerPropietario() != null) {
                dto.setShippingLineChassisOwner(temp.getOwnerPropietario());
            }

            dto.setGateInTruckArrival(
                    Optional.ofNullable(temp.getFechaIngresoCamion())
                            .map(date -> businessUnitConfigService.getDateTimeStr(input.getSubBusinessUnitId(), date))
                            .orElse(null)
            );
            dto.setGateInTruckDeparture(
                    Optional.ofNullable(temp.getFechaSalidaCamion())
                            .map(date -> businessUnitConfigService.getDateTimeStr(input.getSubBusinessUnitId(), date))
                            .orElse(null)
            );

            if (temp.getOperationTypeId() != null) {
                dto.setOperationType(temp.getOperationTypeName());
            }
            if (temp.getCatCargoDocumentTypeId() != null) {
                dto.setDocumentType(catalogLanguageRepository.fnCatalogoTraducidoDes(Math.toIntExact(temp.getCatCargoDocumentTypeId()), input.getLanguageId()));
            }
            if (temp.getCargoDocumentNumber() != null) {
                dto.setDocumentNumber(temp.getCargoDocumentNumber());
            }
            if (temp.getTransportCompanyName() != null) {
                dto.setTruckingCompany(temp.getTransportCompanyName());
            }
            if (temp.getDriverName() != null) {
                dto.setDriverName(temp.getDriverName());
            }
            if (temp.getPlateTruckNumber() != null) {
                dto.setTruckIdentifier(temp.getPlateTruckNumber());
            }

            String chassisDropped = "";
            if (Objects.equals(temp.getCatEquipmentCategory(), (isContainer))) {
                chassisDropped = (temp.getEirChassisId() != null) ? "Yes" : "No";
            }
            dto.setChassisDropped(chassisDropped);

            dto.setEquipmentRestriction(getEquipmentRestriction(temp.getCatEquipmentCategory(), isContainer, temp.getContainerId(), tempRestrictTableDTO, Long.valueOf(isEmpty), Long.valueOf(isFull)));
            dto.setSeals(getSeals(temp.getCatEquipmentCategory(), isContainer, temp.getSeals()));
            dto.setModificationUserId(
                    Optional.ofNullable(temp.getUserModificationId())
                            .map(Math::toIntExact)
                            .orElse(null)
            );
            dto.setModificationUserName(temp.getUserModificationName());
            dto.setModificationUserLastname(temp.getUserModificationLastName());
            dto.setModificationDate(
                    Optional.ofNullable(temp.getFechaModificacion())
                            .map(fechaModificacion -> businessUnitConfigService.getDateTimeStr(input.getSubBusinessUnitId(), fechaModificacion))
                            .orElse(null)
            );
            dto.setRegisterUserId(Math.toIntExact(temp.getUserRegistrationId()));
            dto.setRegisterUserName(temp.getUserRegistrationName());
            dto.setRegisterUserLastname(temp.getUserRegistrationLastName());
            dto.setRegisterDate(
                    Optional.ofNullable(temp.getFechaRegistro())
                            .map(fechaRegistro -> businessUnitConfigService.getDateTimeStr(input.getSubBusinessUnitId(), fechaRegistro))
                            .orElse(null)
            );
            dto.setContainerOperationType(temp.getContainerOperationType());
            dto.setVesselVoyageOperation(temp.getVesselName() + " / " + temp.getVoyageName() + " / " + temp.getDepotOperationType());
            dto.setEstructuraDano(Objects.equals(temp.getEstructuaConDano(), true));
            dto.setMaquinariaDano(Objects.equals(temp.getMaquinariaConDano(), true));
            dto.setParaVenta(Objects.equals(temp.getParaVenta(), "true") ? "1" : "0");
            dto.setEquiposDobles(temp.getCamionMultipleCarga());
            dto.setTruckDepartureUserId(
                    Optional.ofNullable(temp.getUserDepartureId())
                            .map(Math::toIntExact)
                            .orElse(null)
            );
            dto.setTruckDepartureUserName(temp.getUserDepartureName());
            dto.setTruckDepartureUserLastname(temp.getUserDepartureLastName());
            dto.setOrigenCreacion(temp.getOrigenCreacionEir());
            if (temp.getCatMovimientoId() != null) {
                dto.setCatMovimientoId(Math.toIntExact(temp.getCatMovimientoId()));
            } else {
                dto.setCatMovimientoId(null);
            }
            dto.setCatEmptyFullId(Math.toIntExact(temp.getCatEmptyFullId()));
            dto.setCapOperacionDescripcion(temp.getCatOperacionDescripcion());
            dto.setTipoMovimiento(temp.getTipoMov());
            dto.setInspectionPhotos(temp.getInspectionPhotos());
            dto.setInspectionQuantity(String.valueOf(temp.getInspectionsQuantity()));
            dto.setGatePhotos(temp.getGatePhotos());
            dto.setEirChassisId(getEirChassisId(temp.getEirChassisId(), temp.getChassisId()));
            List<EirSendAppeirProjection> eirSendAppeir = eirRepository.findEirSendAppeir(temp.getEirId());

            if(eirSendAppeir != null && !eirSendAppeir.isEmpty()){
                dto.setFlagSend(eirSendAppeir.getFirst().getFlagSend());
                dto.setEirSendAppeirId(eirSendAppeir.getFirst().getEirSendAppeirId());
                dto.setResultMessage(eirSendAppeir.getFirst().getResultMessage());
                dto.setStatusCode(eirSendAppeir.getFirst().getStatusCode());
            }

            dto.setUserDeleteId(temp.getUserDeleteId());
            dto.setUserDeleteName(temp.getUserDeleteName());
            dto.setUserDeleteLastName(temp.getUserDeleteLastName());
            dto.setDeleteDate(temp.getDeleteDate());
            dto.setActive(temp.getActive());

            equipOutput.add(dto);
        }

        return equipOutput;
    }
}