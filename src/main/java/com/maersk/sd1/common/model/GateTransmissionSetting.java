package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "seteo_edi_codeco", schema = "sde", indexes = {
        @Index(name = "SET_CODECO_LINSUNX", columnList = "linea_naviera_id, sub_unidad_negocio_id")
})
public class GateTransmissionSetting {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "seteo_edi_codeco_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "unidad_negocio_id", nullable = false)
    private BusinessUnit businessUnit;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "sub_unidad_negocio_id", nullable = false)
    private BusinessUnit subBusinessUnit;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "linea_naviera_id", nullable = false)
    private ShippingLine shippingLine;

    @Size(max = 50)
    @NotNull
    @Column(name = "sistema_entrega", nullable = false, length = 50)
    private String systemDelivery;

    @Size(max = 200)
    @Column(name = "info_sistema_entrega", length = 200)
    private String infoSystemDelivery;

    @Size(max = 10)
    @NotNull
    @Column(name = "identificador_receptor", nullable = false, length = 10)
    private String receptorIdentifier;

    @NotNull
    @Column(name = "enviar_gate_in_empty", nullable = false)
    private Boolean sendGateInEmpty = false;

    @NotNull
    @Column(name = "enviar_gate_out_empty", nullable = false)
    private Boolean sendGateOutEmpty = false;

    @NotNull
    @Column(name = "enviar_gate_in_full", nullable = false)
    private Boolean sendGateInFull = false;

    @NotNull
    @Column(name = "enviar_gate_out_full", nullable = false)
    private Boolean sendGateOutFull = false;

    @NotNull
    @Column(name = "enviar_status_activity", nullable = false)
    private Boolean sendStatusActivity = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_formato_gate_out_empty")
    private Catalog catGateOutEmptyFormat;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_formato_gate_in_full")
    private Catalog catGateInFullFormat;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_formato_gate_out_full")
    private Catalog catGateOutFullFormat;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_formato_gate_in_empty")
    private Catalog catGateInEmptyFormat;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_formato_status_activity")
    private Catalog catStatusActivityFormat;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_canal_envio_id", nullable = false)
    private Catalog catDeliveryChannel;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "cat_modo_generar_archivo_id", nullable = false)
    private Catalog catGenerateFileMode;

    @Size(max = 200)
    @Column(name = "correo_codeco_destino", length = 200)
    private String gateTransmissionDestinationMail;

    @Size(max = 200)
    @Column(name = "correo_telex_destino", length = 200)
    private String telexDestinationMail;

    @Size(max = 10)
    @Column(name = "parametro_1", length = 10)
    private String parameter1;

    @Size(max = 10)
    @Column(name = "parametro_2", length = 10)
    private String parameter2;

    @Size(max = 10)
    @Column(name = "parametro_3", length = 10)
    private String parameter3;

    @Size(max = 10)
    @Column(name = "parametro_4", length = 10)
    private String parameter4;

    @NotNull
    @Column(name = "es_historico", nullable = false)
    private Boolean isHistorical = false;

    @Column(name = "fecha_debaja")
    private LocalDateTime deactivationDate;

    @Size(max = 200)
    @Column(name = "motivo_debaja", length = 200)
    private String deactivationReason;

    @NotNull
    @Column(name = "activo", nullable = false)
    private Boolean active = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @Size(max = 10)
    @Column(name = "parametro_5", length = 10)
    private String parameter5;

    @Size(max = 10)
    @Column(name = "parametro_6", length = 10)
    private String parameter6;

    @Size(max = 100)
    @Column(name = "sftp_id", length = 100)
    private String sftpId;

    @Column(name = "minutos_trancurridos")
    private Integer elapsedMinutes;

    @Size(max = 100)
    @Column(name = "extension_archivo_enviar", length = 100)
    private String extensionFileSend;

    @Size(max = 100)
    @Column(name = "azure_id_codeco", length = 100)
    private String azureIdGateTransmission;

    @Size(max = 100)
    @Column(name = "azure_id_telex", length = 100)
    private String azureIdTelex;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cat_owner_edi_gateout_empty_id")
    private Catalog catOwnerEdiGateoutEmpty;

}