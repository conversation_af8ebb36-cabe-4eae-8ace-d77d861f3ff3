package com.maersk.sd1.common.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "email_atributo", schema = "ges")
public class EmailAttribute {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "email_atributo_id", nullable = false)
    private Integer id;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "email_plantilla_id", nullable = false)
    private EmailTemplate emailTemplate;

    @Size(max = 80)
    @NotNull
    @Column(name = "campo", nullable = false, length = 80)
    private String field;

}