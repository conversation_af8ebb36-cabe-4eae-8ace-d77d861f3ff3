package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.repository.BookingEdiRepository;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.ShippingLineRepository;
import com.maersk.sd1.sds.dto.PendingEdiDTO;
import com.maersk.sd1.sds.dto.PendingEdiTempDTO;
import com.maersk.sd1.sds.dto.ServiceCopyListPendingProcessInput;
import com.maersk.sd1.sds.dto.ServiceCopyListPendingProcessOutput;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ServiceCopyListPendingProcessService {

    private static final Logger logger = LogManager.getLogger(ServiceCopyListPendingProcessService.class.getName());

    private final BookingEdiRepository bookingEdiRepository;
    private final CatalogRepository catalogRepository;
    private final ShippingLineRepository shippingLineRepository;

    @Transactional
    public ResponseEntity<ResponseController<List<ServiceCopyListPendingProcessOutput>>> serviceCopyListPendingProcessService(ServiceCopyListPendingProcessInput.Root request) {

        Integer isStateToRead=catalogRepository.findIdByAlias(Parameter.IS_STATE_TO_READ);
        Integer isStateToProcess=catalogRepository.findIdByAlias(Parameter.IS_STATE_TO_PROCESS);

        ServiceCopyListPendingProcessInput.Input input = request.getPrefix().getInput();
        Integer ediCoparnSetupId = input.getEdiCoparnSetupId();

        List<PendingEdiDTO> pendingEdiDTOList;

        pendingEdiDTOList = bookingEdiRepository.findPendingEdiDTOByEdiCoparnSetupId(ediCoparnSetupId, isStateToRead);


        List<PendingEdiDTO> additionalList1 = bookingEdiRepository.findPendingEdiDTOByEdiCoparnSetupIdAndIsstateProcess(ediCoparnSetupId, isStateToProcess);
        pendingEdiDTOList.addAll(additionalList1);

        List<PendingEdiDTO> additionalList2 = bookingEdiRepository.findPendingEdiDTOByEdiCoparnSetupIdAndIsstateProcess2(ediCoparnSetupId, isStateToProcess);
        pendingEdiDTOList.addAll(additionalList2);

        List<PendingEdiTempDTO> pendingEdiTempList = pendingEdiDTOList.stream()
                .collect(Collectors.groupingBy(
                        edi -> new AbstractMap.SimpleEntry<>(edi.getEdiCreationDate(), edi.getBookingNumber()),
                        Collectors.counting()
                ))
                .entrySet()
                .stream()
                .filter(entry -> entry.getValue() > 1)
                .map(entry -> new PendingEdiTempDTO(
                        entry.getKey().getKey(),
                        entry.getKey().getValue(),
                        entry.getValue().intValue()
                ))
                .toList();


        Map<String, Integer> tempMap = pendingEdiTempList.stream()
                .collect(Collectors.toMap(
                        temp -> temp.getEdiCreationDate() + "|" + temp.getBookingNumber(), // Composite key
                        PendingEdiTempDTO::getEdiCountByMinute
                ));

        pendingEdiDTOList.forEach(edi -> {
            String key = edi.getEdiCreationDate() + "|" + edi.getBookingNumber();
            if (tempMap.containsKey(key)) {
                edi.setEdiCountByMinute(tempMap.get(key));
            }
        });

        List<PendingEdiDTO> filteredList = pendingEdiDTOList.stream()
                .filter(edi -> edi.getEdiCountByMinute() == 2)
                .toList();

        Map<String, List<PendingEdiDTO>> groupedData = filteredList.stream()
                .collect(Collectors.groupingBy(edi -> edi.getBookingNumber() + "|" + edi.getEdiCreationDate()));

        List<String> matchingKeys = groupedData.entrySet().stream()
                .filter(entry -> {
                    List<PendingEdiDTO> group = entry.getValue();
                    boolean withN = group.stream().anyMatch(edi -> "9".equals(edi.getReservationStatus()));
                    boolean withC = group.stream().anyMatch(edi -> "1".equals(edi.getReservationStatus()));
                    return withN && withC;
                })
                .map(Map.Entry::getKey)
                .toList();

        pendingEdiDTOList.forEach(edi -> {
            String key = edi.getBookingNumber() + "|" + edi.getEdiCreationDate();
            if (matchingKeys.contains(key)) {
                edi.setGroupNumber(1);
            }
        });

        pendingEdiDTOList.forEach(edi -> {
            if (edi.getGroupNumber() != null && edi.getGroupNumber() == 1 && "1".equals(edi.getReservationStatus())) {
                edi.setOrder(2);
            }
        });

        List<PendingEdiDTO> filteredList2 = pendingEdiDTOList.stream()
                .filter(edi -> edi.getEdiCountByMinute() == 2)
                .toList();

        Map<String, List<PendingEdiDTO>> groupedData2 = filteredList2.stream()
                .collect(Collectors.groupingBy(
                        edi -> edi.getBookingNumber() + "|" + edi.getEdiCreationDate()
                ));

        Set<String> matchingKeys2 = groupedData2.entrySet().stream()
                .filter(entry -> {
                    List<PendingEdiDTO> group = entry.getValue();
                    boolean withN = group.stream().anyMatch(edi -> "9".equals(edi.getReservationStatus()));
                    boolean withC = group.stream().anyMatch(edi -> "1".equals(edi.getReservationStatus()));
                    boolean withU = group.stream().anyMatch(edi -> "5".equals(edi.getReservationStatus()));
                    return (withN || withC) && withU;
                })
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        pendingEdiDTOList.forEach(edi -> {
            String key = edi.getBookingNumber() + "|" + edi.getEdiCreationDate();
            if (matchingKeys2.contains(key)) {
                edi.setGroupNumber(2);
            }
        });

        for (PendingEdiDTO edi : pendingEdiDTOList) {

            if (edi.getEdiCountByMinute() > 2) {
                edi.setGroupNumber(4);

                if ("1".equals(edi.getReservationStatus())) {
                    edi.setOrder(2);
                }
            }
        }

        updateTraceEdiCoparn(pendingEdiDTOList);

        List<ServiceCopyListPendingProcessOutput> pendingProcesses = getPendingProcesses(pendingEdiDTOList);

        return ResponseEntity.ok(new ResponseController<>(pendingProcesses));
    }

    public void updateTraceEdiCoparn(List<PendingEdiDTO> pendingEdiDTOList) {
        List<Integer> ediIdsToUpdate = pendingEdiDTOList.stream()
                .filter(edi -> edi.getGroupNumber() != null && edi.getGroupNumber() == 4)
                .map(PendingEdiDTO::getEdiCoparnId)
                .toList();

        if (!ediIdsToUpdate.isEmpty()) {
            bookingEdiRepository.updateTraceEdiCoparn2ForEdiIds("[1°CANCEL +]", ediIdsToUpdate);
        }
    }

    public List<ServiceCopyListPendingProcessOutput> getPendingProcesses(List<PendingEdiDTO> tbPendingList) {
        List<ServiceCopyListPendingProcessOutput> resultList = new ArrayList<>();


        List<Integer> shippingLineIds = tbPendingList.stream()
                .map(PendingEdiDTO::getShippingLineId)
                .distinct()
                .toList();


        List<Object[]> shippingLineData = shippingLineRepository.findShippingLineNamesByIds(shippingLineIds);


        Map<Integer, String> shippingLineMap = new HashMap<>();
        for (Object[] data : shippingLineData) {
            Integer shippingLineId = (Integer) data[1];
            String shippingLineName = (String) data[0];
            shippingLineMap.put(shippingLineId, shippingLineName);
        }


        for (PendingEdiDTO pendingEdi : tbPendingList) {
            ServiceCopyListPendingProcessOutput output = new ServiceCopyListPendingProcessOutput();


            output.setEdiCoparnSetupId(pendingEdi.getEdiCoparnSetupId());
            output.setShippingLineId(pendingEdi.getShippingLineId());
            output.setEdiCoparnId(pendingEdi.getEdiCoparnId());
            output.setEdiCoparnOriginalFileName(pendingEdi.getOriginalFileName());
            output.setOrder(pendingEdi.getOrder());
            output.setBookingNumber(pendingEdi.getBookingNumber());
            output.setEdiCoparnCreationDate(pendingEdi.getEdiCreationDate());
            output.setRegistrationDate(pendingEdi.getRegistrationDate());
            output.setEdiCoparnReservationStatus(pendingEdi.getReservationStatus());


            String shippingLineName = shippingLineMap.get(pendingEdi.getShippingLineId());
            output.setShippingLine(shippingLineName);

            resultList.add(output);
        }
        resultList.sort(Comparator.comparing(ServiceCopyListPendingProcessOutput::getEdiCoparnCreationDate)
                .thenComparing(ServiceCopyListPendingProcessOutput::getOrder)
                .thenComparing(ServiceCopyListPendingProcessOutput::getRegistrationDate));

        return resultList;
    }
}
