package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdg.controller.dto.TruckDepartureListInput;
import com.maersk.sd1.sdg.controller.dto.TruckDepartureListOutput;
import com.maersk.sd1.sdg.service.TruckDepartureListService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDG/module/sdg/SDGtruckDepartureServiceImp")
public class TruckDepartureListController {

    private final TruckDepartureListService truckDepartureListService;

    @PostMapping("/sdgtruckDepartureList")
    public ResponseEntity<ResponseController<TruckDepartureListOutput.Output>> truckDepartureList(@RequestBody TruckDepartureListInput.Root request) {
        if (request != null && request.getSdg() != null && request.getSdg().getInput() != null &&
                request.getSdg().getInput().getLocalSubBusinessUnitId() != null && request.getSdg().getInput().getSubBusinessUnitId() != null &&
                request.getSdg().getInput().getSize() != null && request.getSdg().getInput().getPage() != null) {
            TruckDepartureListOutput.Output result = truckDepartureListService.truckDepartureList(request);
            return ResponseEntity.ok(new ResponseController<>(result));
        } else {
            return ResponseEntity.badRequest().build();
        }
    }
}
