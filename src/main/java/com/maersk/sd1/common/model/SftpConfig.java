package com.maersk.sd1.common.model;

import com.maersk.sd1.common.model.User;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import jakarta.persistence.*;
import org.hibernate.annotations.ColumnDefault;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "sftp_config", schema = "ges")
public class SftpConfig {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "sftp_config_id", nullable = false)
    private Integer id;

    @Size(max = 200)
    @Column(name = "id", length = 200)
    private String alias;

    @Size(max = 500)
    @Column(name = "sftp_host", length = 500)
    private String sftpHost;

    @Size(max = 200)
    @Column(name = "sftp_name", length = 200)
    private String sftpName;

    @Size(max = 100)
    @Column(name = "sftp_pass", length = 100)
    private String sftpPass;

    @Size(max = 10)
    @Column(name = "sftp_port", length = 10)
    private String sftpPort;

    @Size(max = 500)
    @Column(name = "sftp_path", length = 500)
    private String sftpPath;

    @Size(max = 200)
    @Column(name = "evento_despues_subir", length = 200)
    private String eventAfterUpload;

    @NotNull
    @ColumnDefault("1")
    @Column(name = "estado", nullable = false)
    private Boolean status = false;

    @NotNull
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "usuario_registro_id", nullable = false)
    private User registrationUser;

    @NotNull
    @ColumnDefault("getdate()")
    @Column(name = "fecha_registro", nullable = false)
    private LocalDateTime registrationDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "usuario_modificacion_id")
    private User modificationUser;

    @Column(name = "fecha_modificacion")
    private LocalDateTime modificationDate;

    @Column(name = "es_ftp")
    private Boolean isFtp;

    @Column(name = "requiere_private_key")
    private Boolean requiresPrivateKey;

    @Column(name = "requiere_passphrase")
    private Boolean requiresPassphrase;

    @Column(name = "public_key_adjunto_id")
    private Integer publicKeyAttachedId;

    @Column(name = "private_key_adjunto_id")
    private Integer privateKeyAttachedId;

    @Size(max = 50)
    @Column(name = "passphrase", length = 50)
    private String passphrase;

}