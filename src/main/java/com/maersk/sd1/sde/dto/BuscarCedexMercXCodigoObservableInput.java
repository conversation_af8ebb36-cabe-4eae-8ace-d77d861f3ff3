package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BuscarCedexMercXCodigoObservableInput {
    @Data
    public static class Input {

        @JsonProperty("sub_unidad_negocio_id")
        @NotNull
        private Long subUnidadNegocioId;

        @JsonProperty("cedex_merc_codigo")
        private String cedexMercCodigo;

        @JsonProperty("cedex_merc_descripcion")
        private String cedexMercDescripcion;

        @JsonProperty("cat_tipo_estimado_id")
        @NotNull
        private Long catTipoEstimadoId;

        @JsonProperty("es_reefer")
        @NotNull
        private String esReefer;

        @JsonProperty("moneda_id")
        @NotNull
        private Long monedaId;

        @JsonProperty("cat_estimado_ubicacion_dano_id")
        private Long catEstimadoUbicacionDanoId;

        @JsonProperty("cat_estimado_componente_id")
        private Long catEstimadoComponenteId;

        @JsonProperty("cat_estimado_metodo_rep_id")
        private Long catEstimadoMetodoRepId;

        @JsonProperty("cat_estimado_dimension_tipo_id")
        private Long catEstimadoDimensionTipoId;

        @JsonProperty("piezas_reparar")
        @Min(0)
        private Integer piezasReparar;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer idiomaId;

        @JsonProperty("shipping_line_id")
        private Integer shippingLineId;

    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}
