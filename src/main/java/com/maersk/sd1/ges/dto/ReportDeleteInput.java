package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

public class ReportDeleteInput {

    @Data
    public static class Input {
        @JsonProperty("reporte_id")
        @NotNull(message = "reporte_id cannot be null")
        private Integer reporteId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}