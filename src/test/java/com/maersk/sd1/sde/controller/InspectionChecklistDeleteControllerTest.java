package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.InspectionChecklistDeleteInput;
import com.maersk.sd1.sde.dto.InspectionChecklistDeleteOutput;
import com.maersk.sd1.sde.service.InspectionChecklistDeleteService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class InspectionChecklistDeleteControllerTest {

    @InjectMocks
    private InspectionChecklistDeleteController controller;

    @Mock
    private InspectionChecklistDeleteService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void Given_ValidRequest_When_DeleteInspectionChecklist_Then_ReturnSuccessResponse() {
        // Arrange
        InspectionChecklistDeleteInput.Root request = new InspectionChecklistDeleteInput.Root();
        InspectionChecklistDeleteInput.Prefix prefix = new InspectionChecklistDeleteInput.Prefix();
        InspectionChecklistDeleteInput.Input input = new InspectionChecklistDeleteInput.Input();
        input.setInspectionChecklistId(1);
        input.setUserModificationId(2);
        input.setLanguageId(3);
        prefix.setInput(input);
        request.setPrefix(prefix);

        InspectionChecklistDeleteOutput output = new InspectionChecklistDeleteOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Success");

        when(service.deleteInspectionChecklist(1, 2, 3)).thenReturn(output);

        // Act
        ResponseEntity<ResponseController<InspectionChecklistDeleteOutput>> response = controller.inspectionChecklistDelete(request);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, response.getBody().getResult().getRespEstado());
        assertEquals("Success", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void Given_ServiceThrowsException_When_DeleteInspectionChecklist_Then_ReturnInternalServerError() {
        // Arrange
        InspectionChecklistDeleteInput.Root request = new InspectionChecklistDeleteInput.Root();
        InspectionChecklistDeleteInput.Prefix prefix = new InspectionChecklistDeleteInput.Prefix();
        InspectionChecklistDeleteInput.Input input = new InspectionChecklistDeleteInput.Input();
        input.setInspectionChecklistId(1);
        input.setUserModificationId(2);
        input.setLanguageId(3);
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(service.deleteInspectionChecklist(1, 2, 3)).thenThrow(new RuntimeException("Service error"));

        // Act
        ResponseEntity<ResponseController<InspectionChecklistDeleteOutput>> response = controller.inspectionChecklistDelete(request);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals(0, response.getBody().getResult().getRespEstado());
        assertEquals("Service error", response.getBody().getResult().getRespMensaje());
    }

}