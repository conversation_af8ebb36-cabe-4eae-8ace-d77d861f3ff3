package com.maersk.sd1.common.model;

import lombok.*;
import jakarta.persistence.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "catalogo_unidad_negocio", schema = "ges")
public class CatalogBusinessUnit {
    @EmbeddedId
    private CatalogUnitBusinessId id;

    @MapsId("catalogId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "catalogo_id", nullable = false)
    private Catalog catalog;

    @MapsId("unitBusinessId")
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "unidad_negocio_id", nullable = false)
    private BusinessUnit businessUnit;

}