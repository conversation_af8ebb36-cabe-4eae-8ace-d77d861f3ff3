package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.controller.dto.SettingEdiCoparnObtainInputDTO;
import com.maersk.sd1.sds.controller.dto.SettingEdiCoparnObtainOutputDTO;
import com.maersk.sd1.sds.service.SettingEdiCoparnService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSEdiServiceImp")
public class SettingEdiCoparnController {

    private static final Logger logger = LogManager.getLogger(SettingEdiCoparnController.class);

    private final SettingEdiCoparnService settingEdiCoparnService;

    public SettingEdiCoparnController(SettingEdiCoparnService settingEdiCoparnService) {
        this.settingEdiCoparnService = settingEdiCoparnService;
    }

    @PostMapping("/sdsseteoEdiCoparnObtener")
    public ResponseEntity<ResponseController<SettingEdiCoparnObtainOutputDTO>> obtenerSeteoEdiCoparn(@RequestBody @Valid SettingEdiCoparnObtainInputDTO.Root request) {
        try {
            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null){
                return ResponseEntity.status(400).body(new ResponseController<>("Invalid input payload structure."));
            }

            Integer seteoEdiCoparnId = request.getPrefix().getInput().getSettingEdiCoparnId();

            if(seteoEdiCoparnId == null){
                return ResponseEntity.status(400).body(new ResponseController<>("settingEdiCoparnId cannot be null."));
            }

            SettingEdiCoparnObtainOutputDTO result = settingEdiCoparnService.obtainSettingEdiCoparn(seteoEdiCoparnId);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            SettingEdiCoparnObtainOutputDTO errorOutput = new SettingEdiCoparnObtainOutputDTO();
            errorOutput.setRespStatus(0);
            errorOutput.setRespMessage(e.getMessage());
            return ResponseEntity.status(500).body(new ResponseController<>(errorOutput));
        }
    }
}