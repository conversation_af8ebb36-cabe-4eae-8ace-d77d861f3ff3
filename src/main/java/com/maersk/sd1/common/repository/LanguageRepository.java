package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.Language;
import com.maersk.sd1.seg.dto.LanguageListOutput;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface LanguageRepository extends JpaRepository<Language, Integer> {
    @Modifying
    @Query(value = "INSERT INTO seg.idioma (codigo, nombre, activo, usuario_registro_id) " +
            "VALUES (:code, :name, :active, :registrationUserId); " +
            "SELECT SCOPE_IDENTITY()", nativeQuery = true)
    Integer insertLanguageAndGetId(
            @Param("code") String code,
            @Param("name") String name,
            @Param("active") Character active,
            @Param("registrationUserId") Integer registrationUserId
    );

    @Query("SELECT new com.maersk.sd1.seg.dto.LanguageListOutput$LanguageRecord(" +
            " l.id, " +
            " l.code, " +
            " l.name, " +
            " l.active, " +
            " l.registrationDate, " +
            " l.modificationDate, " +
            " regUser.id, " +
            " regUser.names, " +
            " CONCAT(regUser.firstLastName, ' ', COALESCE(regUser.secondLastName, '')), " +
            " modUser.id, " +
            " modUser.names, " +
            " CONCAT(modUser.firstLastName, ' ', COALESCE(modUser.secondLastName, ''))" +
            ")" +
            " FROM Language l " +
            " JOIN l.registrationUser regUser " +
            " LEFT JOIN l.modificationUser modUser " +
            " WHERE (:languageId IS NULL OR l.id = :languageId) " +
            "   AND (:code IS NULL OR l.code LIKE CONCAT('%', :code, '%')) " +
            "   AND (:name IS NULL OR l.name LIKE CONCAT('%', :name, '%')) " +
            "   AND (:active IS NULL OR CAST(l.active as string) LIKE CONCAT('%', :active, '%')) " +
            "   AND ((:registrationDateMin IS NULL AND :registrationDateMax IS NULL) OR (l.registrationDate >= :registrationDateMin AND l.registrationDate < :registrationDateMaxPlusOne)) " +
            "   AND ((:modificationDateMin IS NULL AND :modificationDateMax IS NULL) OR (l.modificationDate >= :modificationDateMin AND l.modificationDate < :modificationDateMaxPlusOne)) "
    )//            " ORDER BY l.id DESC")
    Page<LanguageListOutput.LanguageRecord> findLanguagesWithFilters(
            @Param("languageId") Integer languageId,
            @Param("code") String code,
            @Param("name") String name,
            @Param("active") Character active,
            @Param("registrationDateMin") LocalDateTime registrationDateMin,
            @Param("registrationDateMax") LocalDateTime registrationDateMax,
            @Param("registrationDateMaxPlusOne") LocalDateTime registrationDateMaxPlusOne,
            @Param("modificationDateMin") LocalDateTime modificationDateMin,
            @Param("modificationDateMax") LocalDateTime modificationDateMax,
            @Param("modificationDateMaxPlusOne") LocalDateTime modificationDateMaxPlusOne,
            Pageable pageable
    );

    @Query("SELECT l.id FROM Language l WHERE l.code = :code AND l.active = '1'")
    List<Integer> findActiveLanguageByCode(@Param("code") String code);

}