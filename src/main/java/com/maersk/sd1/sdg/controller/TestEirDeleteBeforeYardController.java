package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.sde.controller.dto.EirDeleteInput;
import com.maersk.sd1.sdg.dto.ResponseEirDeleteBeforeYard;
import com.maersk.sd1.sdg.service.EirDeleteBeforeYardService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDG/module/sdg/eirdeletebeforeyard")
public class TestEirDeleteBeforeYardController {

    private final EirDeleteBeforeYardService eirDeleteBeforeYardService;

    @PostMapping
    public ResponseEirDeleteBeforeYard eirDeleteBeforeYard(@RequestBody Map<String, Object> eirDeleteBeforeYard) throws Exception {

        return eirDeleteBeforeYardService.eirDeleteBeforeYard((EirDeleteInput.Input)eirDeleteBeforeYard);
    }
}
