package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.ServiceDisableInputDto;
import com.maersk.sd1.seg.dto.ServiceDisableOutputDto;
import com.maersk.sd1.seg.service.ServiceDisableService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleADM/module/adm/ADMServicioServiceImp")
public class ServiceDisableController {

    private final ServiceDisableService serviceDisableService;

    @PostMapping("/segservicioDisable")
    public ResponseEntity<ResponseController<ServiceDisableOutputDto>> disableService(
            @Valid @RequestBody ServiceDisableInputDto.Root request) {
        try {

            if(request==null || request.getPrefix()==null || request.getPrefix().getInput()==null){
                log.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
            }

            log.info("Request received disableService: {}", request);
            Integer serviceId = request.getPrefix().getInput().getServiceId();
            Integer userModificationId = request.getPrefix().getInput().getUserModificationId();

            ServiceDisableOutputDto output = serviceDisableService.disableService(serviceId, userModificationId);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            log.error("An error occurred while disabling service", e);
            ServiceDisableOutputDto output = new ServiceDisableOutputDto();
            output.setRespResult(0);
            output.setRespMessage(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseController<>(output));
        }
    }
}
