package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
public class ReportRegisterInput {
    @Data
    public static class Input {
        @JsonProperty("id")
        @NotBlank
        private String id;

        @JsonProperty("menu_id")
        @NotNull
        private Integer menuId;

        @JsonProperty("nombre")
        @NotBlank
        private String nombre;

        @JsonProperty("descripcion")
        @NotBlank
        private String descripcion;

        @JsonProperty("nombre_store")
        @NotBlank
        private String nombreStore;

        @JsonProperty("parametros")
        @NotBlank
        private String parametros;

        @JsonProperty("columns")
        private String columns;

        @JsonProperty("estado")
        @NotBlank
        private Boolean estado;

        @JsonProperty("usuario_id")
        @NotNull
        private Integer usuarioId;

        @JsonProperty("roles_id")
        private List<Role> rolesId;

        @JsonProperty("idioma_id")
        @NotNull
        private Integer idiomaId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private Prefix prefix;
    }
}
