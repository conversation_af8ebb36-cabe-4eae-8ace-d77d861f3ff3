package com.maersk.sd1.sde.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class InspectionChecklistDeleteInput {

    @Data
    public static class Input {

        @JsonProperty("inspection_checklist_id")
        @NotNull
        private Integer inspectionChecklistId;

        @JsonProperty("user_modification_id")
        @NotNull
        private Integer userModificationId;

        @JsonProperty("language_id")
        @NotNull
        private Integer languageId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}