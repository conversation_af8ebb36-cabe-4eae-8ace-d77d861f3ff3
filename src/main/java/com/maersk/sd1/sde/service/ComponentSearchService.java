package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.sde.dto.ComponentSearchOutput;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.maersk.sd1.common.model.Catalog;

import java.util.ArrayList;
import java.util.List;

@Service
public class ComponentSearchService {

    private static final Logger logger = LogManager.getLogger(ComponentSearchService.class);

    private final CatalogRepository catalogRepository;

    public ComponentSearchService(CatalogRepository catalogRepository) {
        this.catalogRepository = catalogRepository;
    }

    @Transactional(readOnly = true)
    public List<ComponentSearchOutput> searchComponents(String component) {

        List<ComponentSearchOutput> components=new ArrayList<>();
        logger.info("Starting searchComponents with component: {}", component);
        try {

            List<Catalog> catalogPage = catalogRepository.findCatalogsByComponent(
                    component,
                    PageRequest.of(0, 10)
            );

            List<Catalog> catalogs = catalogPage;


             components = catalogs.stream().map(cat -> {
                ComponentSearchOutput data = new ComponentSearchOutput();
                data.setCatComponentId(cat.getId());
                String joinedDescription = cat.getDescription();
                if (cat.getLongDescription() != null && !cat.getLongDescription().isEmpty()) {
                    joinedDescription += " - " + cat.getLongDescription();
                }
                data.setComponent(joinedDescription);
                data.setComponentCode(cat.getDescription());
                return data;
            }).toList();

        } catch (Exception e) {
            logger.error("Error in searchComponents: ", e);

        }
        return components;
    }
}
