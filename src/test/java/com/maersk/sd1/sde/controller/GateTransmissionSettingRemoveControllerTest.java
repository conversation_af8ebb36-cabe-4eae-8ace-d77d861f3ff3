package com.maersk.sd1.sde.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sde.dto.GateTransmissionSettingRemoveInput;
import com.maersk.sd1.sde.dto.GateTransmissionSettingRemoveOutput;
import com.maersk.sd1.sde.service.GateTransmissionSettingRemoveService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GateTransmissionSettingRemoveControllerTest {

    @Mock
    private GateTransmissionSettingRemoveService gateTransmissionSettingRemoveService;

    @InjectMocks
    private GateTransmissionSettingRemoveController gateTransmissionSettingRemoveController;

    private GateTransmissionSettingRemoveInput.Root validRequest;
    private GateTransmissionSettingRemoveInput.Root invalidRequest;

    @BeforeEach
    void setUp() {
        GateTransmissionSettingRemoveInput.Input input = new GateTransmissionSettingRemoveInput.Input();
        input.setSeteoEdiCodecoId(1);
        input.setUsuarioModificacionId(1);

        GateTransmissionSettingRemoveInput.Prefix prefix = new GateTransmissionSettingRemoveInput.Prefix();
        prefix.setInput(input);

        validRequest = new GateTransmissionSettingRemoveInput.Root();
        validRequest.setPrefix(prefix);

        invalidRequest = new GateTransmissionSettingRemoveInput.Root();
    }

    @Test
    void testRemoveGateTransmissionSettingSuccess() {
        GateTransmissionSettingRemoveOutput output = new GateTransmissionSettingRemoveOutput();
        output.setRespEstado(1);
        output.setRespMensaje("Record deleted successfully");

        when(gateTransmissionSettingRemoveService.removeGateTransmissionSetting(any(Integer.class), any(Integer.class)))
                .thenReturn(output);

        ResponseEntity<ResponseController<GateTransmissionSettingRemoveOutput>> response = gateTransmissionSettingRemoveController.removeGateTransmissionSetting(validRequest);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(1, Objects.requireNonNull(response.getBody()).getResult().getRespEstado());
        assertEquals("Record deleted successfully", response.getBody().getResult().getRespMensaje());
    }

    @Test
    void testRemoveGateTransmissionSettingInvalidRequest() {
        ResponseEntity<ResponseController<GateTransmissionSettingRemoveOutput>> response = gateTransmissionSettingRemoveController.removeGateTransmissionSetting(invalidRequest);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertEquals("Invalid request", Objects.requireNonNull(response.getBody()).getMessage());
    }

    @Test
    void testRemoveGateTransmissionSettingException() {
        when(gateTransmissionSettingRemoveService.removeGateTransmissionSetting(any(Integer.class), any(Integer.class)))
                .thenThrow(new RuntimeException("Database error"));

        ResponseEntity<ResponseController<GateTransmissionSettingRemoveOutput>> response = gateTransmissionSettingRemoveController.removeGateTransmissionSetting(validRequest);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertEquals("java.lang.RuntimeException: Database error", Objects.requireNonNull(response.getBody()).getResult().getRespMensaje());
    }
}