package com.maersk.sd1.common.repository;

import com.maersk.sd1.common.model.EirChassis;
import com.maersk.sd1.sdg.dto.EirChassisDetailsDto;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.query.Param;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

@Primary
public interface EirChassisRepository extends JpaRepository<EirChassis, Integer> {

    @Modifying
    @Query("""
        UPDATE EirChassis ec
        SET ec.active = false,
            ec.modificationDate = :modificationDate,
            ec.modificationUser.id = :userModificationId
        WHERE ec.id = :chassisId
    """)
    void deactivateChassis(Integer chassisId, Integer userModificationId, LocalDateTime modificationDate);

    @Query("SELECT COALESCE(e.structureWithDamage, false) FROM EirChassis e WHERE e.id = :eirChassisId")
    Boolean findStructureWithDamageByEirChassisId(@Param("eirChassisId") Integer eirChassisId);

    @Modifying
    @Query("UPDATE EirChassis e SET e.urlSignatureChassisInspector = :urlSignatureChassisInspector, " +
            "e.modificationUser.id = :userRegistrationId, e.modificationDate = :currentTimestamp " +
            "WHERE e.id IN :eirChassisIds")
    void updateEirChassis(@Param("eirChassisIds") List<Integer> eirChassisIds,
                          @Param("urlSignatureChassisInspector") String urlSignatureChassisInspector,
                          @Param("userRegistrationId") Integer userRegistrationId,
                          @Param("currentTimestamp") LocalDateTime currentTimestamp);

    @Query("SELECT new com.maersk.sd1.sdg.dto.EirChassisDetailsDto(e.chassisDocumentGo.id, d.documentChassisNumber) " +
            "FROM EirChassis e LEFT JOIN ChassisDocument d ON e.chassisDocumentGo.id = d.id " +
            "WHERE e.id = :eirChassisId")
    EirChassisDetailsDto findDocumentChassisDetailsByEirChassisId(@Param("eirChassisId") Integer eirChassisId);

    @Modifying
    @Query("UPDATE EirChassis e SET e.chassisDocumentDetail.id = :documentChassisDetailId, e.chassis.id = :chassisId, " +
            "e.chassisAllocationControl = \"1\", e.modificationUser.id = :userModificationId, e.modificationDate = CURRENT_TIMESTAMP, " +
            "e.traceEirChassis = 'asig_gral_gout2' WHERE e.id = :eirChassisId")
    void updateEirChassisData(@Param("eirChassisId") Integer eirChassisId, @Param("documentChassisDetailId") Integer documentChassisDetailId,
                              @Param("chassisId") Integer chassisId, @Param("userModificationId") Integer userModificationId);

    @Procedure(name = "EirChassis.gateoutGeneralAssignmentChassisRegister")
    HashMap<String, Object> gateoutGeneralAssignmentChassisRegister(@Param("eir_id") Integer eirId,
                                                                    @Param("chassis_id") Integer chassisId,
                                                                    @Param("user_registration_id") Integer userRegistrationId,
                                                                    @Param("languaje_id") Integer languageId
    );
}