package com.maersk.sd1.sds.controller;

import com.maersk.sd1.sds.dto.BookingEditFileInputDTO;
import com.maersk.sd1.sds.dto.BookingEditFileOutputDTO;
import com.maersk.sd1.sds.service.BookingEditFileService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleSDS/module/sds/SDSBookingServiceImp")
public class BookingEditFileController {

    private static final Logger logger = LoggerFactory.getLogger(BookingEditFileController.class);

    private final BookingEditFileService bookingEditService;

    @Autowired
    public BookingEditFileController(BookingEditFileService bookingEditService) {
        this.bookingEditService = bookingEditService;
    }

    @PostMapping("/sdsbookingEditar")
    public ResponseEntity<BookingEditFileOutputDTO.Root> editBooking(@RequestBody BookingEditFileInputDTO.Root request) {
        logger.info("Received request to edit booking with details: {}", request);

        try {
            BookingEditFileInputDTO.Input input = request.getPrefix().getInput();
            logger.debug("Extracted input data: {}", input);

            BookingEditFileOutputDTO.Root response = bookingEditService.editBooking(input);
            logger.info("Successfully processed the booking update for bookingId: {}", input.getBookingId());

            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid input received: {}", e.getMessage());
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            logger.error("Error occurred while processing the booking update", e);
            return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
