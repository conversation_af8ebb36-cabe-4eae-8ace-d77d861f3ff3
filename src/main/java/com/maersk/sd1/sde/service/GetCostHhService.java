package com.maersk.sd1.sde.service;


import com.maersk.sd1.sde.dto.GetCostHhOutput;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.EstimateEmrCostManHoursRepository;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
public class GetCostHhService {

    private static final Logger logger = LogManager.getLogger(GetCostHhService.class);

    private final EstimateEmrCostManHoursRepository estimateEmrCostManHoursRepository;

    private final CatalogRepository catalogRepository;

    @Transactional(readOnly = true)
    public GetCostHhOutput obtenerCostoHh(Integer subUnidadNegocioId, Integer lineaNavieraId,
                                          Integer catTipoEstimadoId, Integer monedaId) {
        GetCostHhOutput output = new GetCostHhOutput();
        try {
            Integer catEquipmentCategory = catalogRepository.findCatalogIdByAlias1("sd1_equipment_category_container")
                    .orElseThrow(() -> new IllegalArgumentException("Equipment category container not found."));

            Integer costManHour = estimateEmrCostManHoursRepository.findCostManHour1(
                            subUnidadNegocioId, lineaNavieraId, catTipoEstimadoId, monedaId, catEquipmentCategory)
                    .orElse(null);

            if (costManHour != null) {
                output.setRespCostoHoraHombre(List.of(costManHour));
            } else {
                output.setRespCostoHoraHombre(null);
            }

        } catch (Exception e) {
            logger.error("Error during obtenerCostoHh", e);
            output.setRespCostoHoraHombre(null);
        }
        return output;
    }
}