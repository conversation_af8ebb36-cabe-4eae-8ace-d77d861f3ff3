package com.maersk.sd1.sds.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class ShippingLineOutput {

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @JsonProperty("total_registros")
    private List<List<Long>> totalRecords;

    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    @JsonProperty("result_list")
    private List<ShippingLineResult> shippingLineResultList;

    @Data
    public static class ShippingLineResult {
        @JsonProperty("linea_naviera_id")
        private Integer shippingLineId;

        @JsonProperty("nombre")
        private String name;

        @JsonProperty("activo")
        private Boolean active;

        @JsonProperty("fecha_registro")
        private LocalDateTime registrationDate;

        @JsonProperty("fecha_modificacion")
        private LocalDateTime modificationDate;

        @JsonProperty("linea_naviera")
        private String shippingLine;

        @JsonProperty("color")
        private String color;

        @JsonProperty("usuario_registro_id")
        private Integer registrationUserId;

        @JsonProperty("usuario_modificacion_id")
        private Integer modificationUserId;

        @JsonProperty("usuario_registro_nombres")
        private String registrationUserNames;

        @JsonProperty("usuario_registro_apellidos")
        private String registrationUserLastNames;

        @JsonProperty("usuario_modificacion_nombres")
        private String modificationUserNames;

        @JsonProperty("usuario_modificacion_apellidos")
        private String modificationUserLastNames;

    }
}