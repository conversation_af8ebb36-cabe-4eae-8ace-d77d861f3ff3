package com.maersk.sd1.sdg.dto;

import org.springframework.beans.factory.annotation.Value;

public interface FoundContainerDetailsDTO {

    @Value("#{target.container_id}")
    Integer getReplaceContainerId();
    @Value("#{target.container_number}")
    String getReplaceContainerNumber();
    @Value("#{target.container_location_id}")
    Integer getContainerLocationId();
    @Value("#{target.lift_moves}")
    Integer getLiftMoves();
}
