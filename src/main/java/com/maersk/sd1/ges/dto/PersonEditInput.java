package com.maersk.sd1.ges.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.PastOrPresent;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PersonEditInput {

    @Data
    public static class Input {

        @JsonProperty("persona_id")
        @NotNull(message = "persona_id cannot be null")
        private Integer personaId;

        @JsonProperty("tipo_documento_identidad_id")
        @NotNull(message = "tipo_documento_identidad_id cannot be null")
        private Integer typeIdentificationDocumentId;

        @JsonProperty("documento_identidad")
        @NotNull(message = "documento_identidad cannot be null")
        @Size(max = 15)
        private String identificationDocument;

        @JsonProperty("apellido_parterno")
        @NotNull(message = "apellido_parterno cannot be null")
        @Size(max = 100)
        private String firstLastName;

        @JsonProperty("apellido_materno")
        @Size(max = 100)
        private String secondLastName;

        @JsonProperty("nombres")
        @NotNull(message = "nombres cannot be null")
        @Size(max = 100)
        private String names;

        @JsonProperty("fecha_nacimiento")
        @NotNull(message = "fecha_nacimiento cannot be null")
        @PastOrPresent(message = "fecha_nacimiento must be in the past or present")
        private LocalDateTime birthDate;

        @JsonProperty("correo")
        @Size(max = 100)
        private String mail;

        @JsonProperty("telefono")
        @Size(max = 50)
        private String phone;

        @JsonProperty("tipo_estado_persona_id")
        @NotNull(message = "tipo_estado_persona_id cannot be null")
        private Integer typePersonStatusId;

        @JsonProperty("tipo_area_trabajo_id")
        private Integer typeWorkAreaId;

        @JsonProperty("activo")
        @NotNull(message = "activo cannot be null")
        private Boolean active;

        @JsonProperty("usuario_modificacion_id")
        @NotNull(message = "usuario_modificacion_id cannot be null")
        private Integer userModificationId;

        @JsonProperty("licencia_conducir")
        @Size(max = 50)
        private String driversLicense;

        @JsonProperty("fecha_vigencia_licencia_conducir")
        private LocalDateTime driversLicenseValidityDate;

        @JsonProperty("carnet")
        @Size(max = 50)
        private String card;

        @JsonProperty("fecha_vigencia_carnet")
        private LocalDateTime cardValidityDate;

        @JsonProperty("genero")
        private Character gender;

        @JsonProperty("situacion_laboral")
        private Integer laborSituation;

        @JsonProperty("persona_rol_multiple")
        private String personMultipleRole;

        @JsonProperty("empresa_id")
        private Integer companyId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private PersonEditInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("GES")
        private PersonEditInput.Prefix prefix;
    }
}
