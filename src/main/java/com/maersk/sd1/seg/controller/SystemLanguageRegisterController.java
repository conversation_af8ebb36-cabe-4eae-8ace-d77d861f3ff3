package com.maersk.sd1.seg.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.seg.dto.SystemLanguageRegisterInput;
import com.maersk.sd1.seg.dto.SystemLanguageRegisterOutput;
import com.maersk.sd1.seg.service.SystemLanguageRegisterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;


@RestController
@RequestMapping("/ModuleSEG/module/seg/INDIdiomaServiceImp")
public class SystemLanguageRegisterController {

    private static final Logger logger = LogManager.getLogger(SystemLanguageRegisterController.class.getName());

    private final SystemLanguageRegisterService service;

    @Autowired
    public SystemLanguageRegisterController(SystemLanguageRegisterService service) {
        this.service = service;
    }

    @PostMapping("/segidiomaSistemaRegistrar")
    public ResponseEntity<ResponseController<SystemLanguageRegisterOutput>> systemLanguageRegisterService(@RequestBody SystemLanguageRegisterInput.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                logger.error("Received request for segidiomaSistemaRegistrar with invalid input.");
                return ResponseEntity.badRequest().body(new ResponseController<>(null));
            }

            return service.systemLanguageRegisterService(request);
        } catch (Exception e) {
            logger.error("An error occurred while processing the request.", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ResponseController<>("An error occurred while processing the request: " + e.getMessage()));
        }
    }
}
