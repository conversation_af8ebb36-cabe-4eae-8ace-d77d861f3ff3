package com.maersk.sd1.sde.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;

@UtilityClass
public class BuscarEstimadosAsociadosInput {
    @Data
    public static class Input {

        @JsonProperty("sub_business_unit_id")
        private BigDecimal subBusinessUnitId;

        @JsonProperty("container_number")
        @Size(max = 11)
        private String containerNumber;

        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("without_gate_in")
        private Boolean withoutGateIn = false;

        @JsonProperty("language_id")
        private Integer languageId = 1;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDE")
        private Prefix prefix;
    }
}