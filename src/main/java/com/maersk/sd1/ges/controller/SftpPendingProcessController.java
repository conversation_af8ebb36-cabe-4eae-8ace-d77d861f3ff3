package com.maersk.sd1.ges.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.ges.dto.SftpPendingProcessInput;
import com.maersk.sd1.ges.dto.SftpPendingProcessOutput;
import com.maersk.sd1.ges.service.SftpPendingProcessService;
import jakarta.validation.Valid;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/ModuleADM/module/adm/ADMSftpConfigServiceImp")
public class SftpPendingProcessController {

    private static final Logger logger = LogManager.getLogger(SftpPendingProcessController.class);

    private final SftpPendingProcessService sftpPendingProcessService;

    @Autowired
    public SftpPendingProcessController(SftpPendingProcessService sftpPendingProcessService) {
        this.sftpPendingProcessService = sftpPendingProcessService;
    }

    @PostMapping("/gessftpPendingProcess")
    public ResponseEntity<ResponseController<SftpPendingProcessOutput>> sdgSftpPendingProcess(
            @RequestBody @Valid SftpPendingProcessInput.Root request) {
        try {
            SftpPendingProcessOutput output = sftpPendingProcessService.getAllPendingRecords();
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while processing the SFTP pending records.", e);
            SftpPendingProcessOutput output = new SftpPendingProcessOutput();
            output.setRespEstado(0);
            output.setRespMensaje(e.toString());
            return ResponseEntity.status(500).body(new ResponseController<>(output));
        }
    }
}