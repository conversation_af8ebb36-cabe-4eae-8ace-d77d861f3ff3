package com.maersk.sd1.seg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ServiceDisableInputDto {

    @Data
    public static class Input {

        @JsonProperty("service_id")
        @NotNull
        private Integer serviceId;

        @JsonProperty("user_modification_id")
        @NotNull
        private Integer userModificationId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SEG")
        private Prefix prefix;
    }
}
