package com.maersk.sd1.sdf.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdf.dto.IncomeProgrammingGetInputDTO;
import com.maersk.sd1.sdf.dto.IncomeProgrammingGetOutputDTO;
import com.maersk.sd1.sdf.service.IncomeProgrammingGetService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ModuleSDF/module/sdf/SDFIncomeProgrammingServiceImp")
public class IncomeProgrammingGetController {

    private final IncomeProgrammingGetService incomeProgrammingGetService;

    // Constructor Injection
    public IncomeProgrammingGetController(IncomeProgrammingGetService incomeProgrammingGetService) {
        this.incomeProgrammingGetService = incomeProgrammingGetService;
    }

    @PostMapping("/sdfincomeProgrammingGet")
    public ResponseEntity<ResponseController<IncomeProgrammingGetOutputDTO>> incomeProgrammingGetService(@RequestBody IncomeProgrammingGetInputDTO.Root input) {
        return incomeProgrammingGetService.incomeProgrammingGetService(input);
    }
}

